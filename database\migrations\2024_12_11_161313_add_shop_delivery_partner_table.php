<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddShopDeliveryPartnerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shop_delivery_partners', function (Blueprint $table) {
            $table->uuid('id')->primary(); // UUID làm khóa chính
            $table->uuid('shop_id'); // UUID của shop
            $table->uuid('delivery_partner_id'); // UUID của đối tác giao hàng
            $table->boolean('is_enabled')->default(true); // Trạng thái bật/tắt
            $table->timestamps(); // created_at và updated_at

            // Ràng buộc khóa ngoại
            $table->foreign('shop_id')->references('id')->on('shops')->onDelete('cascade');
            $table->foreign('delivery_partner_id')->references('id')->on('delivery_partners')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shop_delivery_partners');
    }
}
