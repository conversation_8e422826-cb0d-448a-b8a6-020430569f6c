<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class CreatePasswordClientRequest extends BaseRequest
{

    public function rules()
    {
        return [

            'password' => 'bail|required|min:6|max:255|regex:/^\S*(?=\S{6,})(?=\S*[a-zA-Z])(?=\S*[\d])\S*$/',
        ];
    }
    // public function messages()
    // {

    //     return [

    //         'id.required' => 'User_001_E_030',
    //         'id.uuid' => 'User_002_E_031',
    //         'id.exists' => 'User_003_E_032',

    //         'password.required' => 'User_001_E_006',
    //         'password.confirmed' => 'User_010_E_007',
    //         'password.min' => 'User_004_E_008',
    //         'password.max'      => 'User_004_E_065',
    //         'password.regex'      => 'User_002_E_066'
    //     ];
    // }
}
