<?php

use Illuminate\Http\Request;
// use Illuminate\Routing\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
// ---------------------------------
Route::group([
    'middleware' => ['checkcors', 'xss', 'origin'],
], function () {
    Route::post('v1/login_jwt', 'Api\v1\AuthController@login')->name('loginJWT');
    Route::post('v1/forget_password', 'Api\v1\AuthController@forgetPassword');
    Route::post('v1/confirm_code', 'Api\v1\AuthController@confirmCode');
    Route::post('v1/change_password', 'Api\v1\AuthController@changeForgetPassword');
    Route::post('v1/logout', 'Api\v1\AuthController@logout')->middleware('jwt.verify');
});


//------------------api image--------------------------

Route::group(['prefix' => 'v1/image', 'namespace' => 'Api\v1', 'middleware' => ['jwt.verify', 'xss', 'origin']], function () {
    Route::post('insert', 'ImageController@insert');
    Route::post('delete', 'ImageController@delete');
    Route::post('rotate_image', 'ImageController@rotateImage');
});
Route::post('v1/client/image/ocr', 'Api\v1\ImageController@ocr');
Route::group(['prefix' => 'v1/image', 'namespace' => 'Api\v1'], function () {
    Route::get('detail/{id?}', 'ImageController@detail');
    Route::post('problem/report/insert', 'ImageController@problemReportInsert');
});

//---------------api setting--------------------
Route::group(['prefix' => 'v1/setting', 'namespace' => 'Api\v1', 'middleware' => ['jwt.verify', 'xss', 'origin']], function () {

    // Route::group(['middleware' => ['admin']], function () {
    Route::post('insert', 'SettingController@insert');
    Route::post('delete', 'SettingController@delete')->middleware('can:admin,App\User');
    Route::post('update', 'SettingController@update')->middleware('can:admin,App\User');
    // });
    Route::get('list', 'SettingController@list');
    Route::get('detail/{id?}', 'SettingController@detail');
});


Route::group(['prefix' => 'v1/mqtt', 'namespace' => 'Api\v1', 'middleware' => ['jwt.verify', 'xss', 'origin']], function () {

    Route::post('publish', 'MqttChatController@publish');
});


Route::group(['prefix' => 'v1/village', 'namespace' => 'Api\v1', 'middleware' => ['jwt.verify', 'xss', 'origin']], function () {

    // Route::post('insert', 'VillageController@insert');
    Route::post('delete', 'VillageController@delete');
    Route::post('update', 'VillageController@update');
    Route::get('list', 'VillageController@list');
    Route::get('detail/{id?}', 'VillageController@detail');
});

//----------------api category----------------------

Route::group(['prefix' => 'v1/category', 'namespace' => 'Api\v1', 'middleware' => ['jwt.verify', 'xss', 'origin']], function () {

    Route::post('create', 'CategoryController@create');
    Route::post('delete', 'CategoryController@delete');
    Route::post('update', 'CategoryController@update');
    Route::post('remove', 'CategoryController@remove');
    Route::post('updateListProduct', 'CategoryController@updateListProduct');
    Route::post('update_index', 'CategoryController@updateIndex');

    Route::get('detail/{slug?}', 'CategoryController@detail')->where('slug', '.*');
    // Route::post('check_uni', 'CategoryController@compareUni');
    Route::post('update_list_product', 'CategoryController@updateListProduct');
});
Route::get('v1/category/list', 'Api\v1\CategoryController@list');
Route::get('v1/category/list_level', 'Api\v1\CategoryController@listLevel');
Route::get('v1/category/list_by_shop_id/{id}', 'Api\v1\CategoryController@listByShopId');
// Route::post('v1/category/compare', 'Api\v1\CategoryController@compareUni');


//-----------------------view----------------------------
Route::group(['prefix' => 'v1/view', 'namespace' => 'Api\v1'], function () {
    // Route::post('insert', 'ViewController@insert')->middleware('xss', 'origin');
});


//-----------------------token----------------------------
Route::group(['prefix' => 'v1/token', 'namespace' => 'Api\v1', 'middleware' => ['jwt.verify', 'xss', 'origin']], function () {
    Route::get('list', 'TokenController@list');
    Route::post('list_by_user', 'TokenController@listByUser');
    Route::post('insert_or_update', 'TokenController@insertOrUpdate');
    Route::post('delete', 'TokenController@delete');
    Route::post('delete_token', 'TokenController@deleteToken');
    Route::post('delete_by_user', 'TokenController@deleteTokenByUser');
});
Route::post('v1/geocode/search/address', 'Api\v1\GeocodeController@searchAddress');

Route::group(['prefix' => 'v1/client/setting', 'namespace' => 'Api\v1\Client', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list', 'SettingClientController@list');
});
Route::group(['prefix' => 'v1/client', 'namespace' => 'Api\v1\Client', 'middleware' => ['xss', 'origin','throttle:360,1']], function () {
    Route::post('login', 'AuthClientController@login');
    Route::post('register', 'AuthClientController@register');
    Route::get('user/infor', 'AuthClientController@userInfor')->middleware('jwt.verify');
    Route::post('update', 'AuthClientController@update')->middleware('jwt.verify');
    Route::post('update/image', 'AuthClientController@updateImage')->middleware('jwt.verify');
    Route::post('update/password', 'AuthClientController@updatePassword')->middleware('jwt.verify');
    Route::post('change_email', 'AuthClientController@changeEmail')->middleware('jwt.verify');

    Route::post('forget_password', 'AuthClientController@forgetPassword');
    Route::post('confirm_code', 'AuthClientController@confirmCode');
    Route::post('change_password', 'AuthClientController@changeForgetPassword');
    Route::post('register_code', 'AuthClientController@registerCode');
    Route::post('confirm_register_code', 'AuthClientController@confirmRegisterCode');
    Route::post('check_exist', 'AuthClientController@checkExist');
    Route::post('check_exist/path', 'AuthClientController@checkExistPath');
    Route::post('login/driver', 'AuthClientController@loginDriver');
    // Route::get('user/detail/{id?}', 'AuthClientController@detail');
    Route::get('user/notify', 'AuthClientController@notify')->middleware('jwt.verify');
    Route::get('user/detail/notify/{id?}', 'AuthClientController@detailNotify')->middleware('jwt.verify');
    Route::get('user/device/list', 'AuthClientController@listDevice')->middleware('jwt.verify');
    Route::post('user/device/logout', 'AuthClientController@logoutDevice')->middleware('jwt.verify');

    Route::post('get_otp', 'AuthClientController@get_otp');
    Route::post('confirm_otp', 'AuthClientController@confirm_otp');
    Route::post('delete_account', 'AuthClientController@delete_account')->middleware('jwt.verify');

    Route::post("user/language", "AuthClientController@switchLang")->middleware('jwt.verify');
    
    // Referral routes
    Route::get('user/referral/code', 'AuthClientController@getReferralCode')->middleware('jwt.verify');
    Route::get('user/referral/stats', 'AuthClientController@getReferralStats')->middleware('jwt.verify');
});

// callback zalo access token
Route::group(['prefix' => 'callback', 'namespace' => 'Api\v1\Client', 'middleware' => ['xss','throttle:360,1']], function () {
    Route::get('zalo_auth_code', 'AuthClientController@zaloGetAuthCode');
});
//---------------------api addresss--------------------------------

Route::group(['prefix' => 'v1/client/address', 'namespace' => 'Api\v1\Client', 'middleware' => ['xss', 'origin']], function () {
    // Route::get('list', 'AddressClientController@List');
    Route::get('province', 'AddressClientController@provinceList');
    Route::get('district', 'AddressClientController@distrctList');
    Route::get('ward', 'AddressClientController@wardList');
    Route::get('detail/area/{slug}', 'AddressClientController@detailArea');
    Route::get('province/list', 'AddressClientController@province');
    Route::get('province/list/district', 'AddressClientController@provinceListDictrict');
    // Route::get('update/coordinate/{id}', 'AddressClientController@updateCoordinate');

});

// Route::group(['prefix' => 'v1/single_page', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
//     // Route::post('insert', 'SinglePageController@insert');
//     Route::post('update', 'SinglePageController@update');
//     Route::post('delete', 'SinglePageController@delete');
//     Route::get('detail/{id?}', 'SinglePageController@detail');
//     Route::post('detailBySlug', 'SinglePageController@detailBySlug');
//     Route::post('filter', 'SinglePageController@filter');
// });

Route::group(['prefix' => 'v1/order', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list', 'OrderController@list')->middleware('jwt.verify');
    Route::post('list_order_by_shop_id', 'OrderController@listOrderByShopId')->middleware('jwt.verify');
    Route::post('list_order_by_customer_id', 'OrderController@listOrderByCustomerId')->middleware('jwt.verify');
    Route::post('count_order_by_status', 'OrderController@countOrderByStatus')->middleware('jwt.verify');
    Route::get('detail/{id?}', 'OrderController@detail')->where('id', '.*')->middleware('jwt.verify');
    Route::post('create', 'OrderController@create');
    Route::post('update', 'OrderController@update')->middleware('jwt.verify');
    Route::post('remove', 'OrderController@remove')->middleware('jwt.verify');

    Route::post('list_order', 'OrderController@listOrders')->middleware('jwt.verify');
});

Route::group(['prefix' => 'v1/delivery', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('detail/{id}', 'DeliveryController@detail');
    Route::get('detail_by_order_id/{order_id}', 'DeliveryController@detail_by_order_id')->middleware('jwt.verify');
    Route::post('create', 'DeliveryController@create')->middleware('jwt.verify');
    Route::post('update', 'DeliveryController@update')->middleware('jwt.verify');
    Route::post('check_price', 'DeliveryController@check_price');
    Route::post('find_driver', 'DeliveryController@find_driver')->middleware('jwt.verify');
    Route::post('update_driver_id', 'DeliveryController@update_driver_id')->middleware('jwt.verify');
    Route::get('list_by_driver', 'DeliveryController@getDeliveriesByDriver')->middleware('jwt.verify');
});

Route::group(['prefix' => 'v1/driver', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin','jwt.verify']], function () {
    Route::post('action', 'DeliveryController@driverAction');
});

Route::group(['prefix' => 'v1/interaction', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::post('create_or_delete', 'InteractionController@create_or_delete')->middleware('jwt.verify');
    Route::get('list', 'InteractionController@list')->middleware('jwt.verify');
    Route::get('detail/{id}', 'InteractionController@detail')->middleware('jwt.verify');
    Route::post('check', 'InteractionController@checkInteraction')->middleware('jwt.verify');


    Route::post('increment_view', 'InteractionController@incrementView')->middleware('throttle:1000,1');
    Route::post('list')->middleware('jwt.verify');
});

Route::group(['prefix' => 'v1/voucher', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin', 'jwt.verify']], function () {
    Route::post('create', 'VoucherController@create');
    Route::post('update', 'VoucherController@update');
    Route::get('list', 'VoucherController@list');
    Route::get('detail/{id}', 'VoucherController@detail');
});

Route::group(['prefix' => 'v1/agent', 'namespace' => 'Api\v1\Agent', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list_shop', 'IndexController@list_shop')->middleware('jwt.verify');
    Route::get('shop_detail/{id?}', 'IndexController@shop_detail')->where('id', '.*')->middleware('jwt.verify');
    Route::post('create_shop', 'IndexController@create_shop')->middleware('jwt.verify');
    Route::post('copy_shop', 'IndexController@copy_shop')->middleware('jwt.verify');
    Route::post('update_shop', 'IndexController@update_shop')->middleware('jwt.verify');
    Route::get('shop_orders/{id?}', 'IndexController@shop_orders')->where('id', '.*')->middleware('jwt.verify');
    Route::post('list_order_by_agent', 'IndexController@list_order_by_agent')->middleware('jwt.verify');
    Route::post('order_update', 'IndexController@order_update')->middleware('jwt.verify');
    Route::post('disable_shop', 'IndexController@disable_shop')->middleware('jwt.verify');
    Route::post('delete_shop', 'IndexController@delete_shop')->middleware('jwt.verify');
    Route::get('order_detail/{id?}', 'IndexController@order_detail')->where('id', '.*')->middleware('jwt.verify');
    Route::post('create_private_product', 'IndexController@create_private_product')->middleware('jwt.verify');
    Route::post('add_product_from_system', 'IndexController@add_product_from_system')->middleware('jwt.verify');
    Route::post('update_product', 'IndexController@update_product')->middleware('jwt.verify');
    Route::post('category_update_list_product', 'IndexController@category_update_list_product')->middleware('jwt.verify');
    // Route::post('category_update_index', 'IndexController@category_update_index')->middleware('jwt.verify');
});

Route::group(['prefix' => 'v1/shop', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list', 'ShopController@list');
    Route::get('detail/{slug?}', 'ShopController@detail')->where('slug', '.*')->middleWare('etag');
    Route::get('my_shop', 'ShopController@myShop')->middleware('jwt.verify');
    Route::post('create', 'ShopController@create')->middleware('jwt.verify');
    Route::post('update', 'ShopController@update')->middleware('jwt.verify');
    // Route::post('filter', 'ShopController@filter');
    Route::post('remove', 'ShopController@remove')->middleware('jwt.verify');
    Route::post('get_products_by_shop_id', 'ShopController@getProductsByShopId');
    Route::post('search_products_in_shop', 'ShopController@searchProductsInShop')->middleware('jwt.verify');
    Route::get('super_menu/{slug?}', 'ShopController@superMenu')->where('slug', '.*')->middleWare('etag');
    Route::get('products_with_category/{id?}', 'ShopController@productWithCategory');

    Route::post('setting', 'ShopController@updateSetting')->middleware('jwt.verify');
});

Route::group(['prefix' => 'v1/client/public', 'namespace' => 'Api\v1\Client', 'middleware' => ['xss', 'origin']], function () {
    Route::post('filter', 'ClientPublicController@filter');
    Route::post('dashboard', 'ClientPublicController@dashboard');
    Route::post('add_zalo_hey_hash', 'ClientPublicController@addZaloHashkey');
    Route::post('search', 'ClientPublicController@search');
    Route::post('suggest_search', 'ClientPublicController@suggest_search');
    Route::post('reels', 'ClientPublicController@reels');
    Route::post('search_by_address', 'ClientPublicController@search_by_address');
    Route::post('search_by_latlong', 'ClientPublicController@search_by_latlong');
    Route::get('order/detail/{id}', 'ClientPublicController@orderDetail')->where('id', '.*');
    Route::get('product/detail/{id}', 'ClientPublicController@productDetail')->where('id', '.*');
    Route::post('search_products_in_shop', 'ClientPublicController@searchProductsInShop');
    Route::post("user/shops_interacted", 'ClientPublicController@listShop')->middleware('jwt.verify');
});


Route::group(['prefix' => 'v1/product', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list', 'ProductController@list')->middleWare('etag');
    Route::get('detail/{slug?}', 'ProductController@detail')->where('slug', '.*')->middleWare('etag');
    Route::get('relate/{slug?}', 'ProductController@relate')->where('slug', '.*')->middleWare('etag');
    // Route::post('insert', 'ProductController@insert')->middleware('jwt.verify');
    Route::post('update', 'ProductController@update')->middleware('jwt.verify');
    Route::post('create_private_product', 'ProductController@createPrivateProduct')->middleware('jwt.verify');
    // Route::post('filter', 'ProductController@filter');
    Route::post('search_by_name', 'ProductController@searchByName');
    Route::post('search_base', 'ProductController@searchBase');
    Route::post('remove', 'ProductController@remove')->middleware('jwt.verify');
    Route::post('delete', 'ProductController@delete')->middleware('jwt.verify');
    Route::post('add_product_from_system', 'ProductController@addProductFromSystem')->middleware('jwt.verify');
});
Route::group(['prefix' => 'v1/material', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list', 'MaterialController@list')->middleware('jwt.verify');
    Route::post('add', 'MaterialController@add')->middleware('jwt.verify');
    Route::post('import_history', 'MaterialController@importHistory')->middleware('jwt.verify');
});

//----------------api stock management----------------------
Route::group(['prefix' => 'v1/stock', 'namespace' => 'Api\v1', 'middleware' => ['jwt.verify', 'xss', 'origin']], function () {
    Route::post('import', 'StockController@import');
    Route::post('export', 'StockController@export');
    Route::post('waste', 'StockController@waste');
    Route::get('history/{product_id}', 'StockController@history')->where('product_id', '[0-9a-f-]+');
    Route::post('daily-summary', 'StockController@dailySummary');
    Route::get('my-shops-summary', 'StockController@myShopsSummary');
});
Route::group(['prefix' => 'v1/supplier', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list', 'SupplierController@list')->middleware('jwt.verify');
    Route::post('add', 'SupplierController@add')->middleware('jwt.verify');
});
Route::group(['prefix' => 'v1/quotation', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('detail', 'QuotationController@detail');
    Route::get('list', 'QuotationController@list')->middleware('jwt.verify');
    Route::post('filter', 'QuotationController@filter')->middleware('jwt.verify');
    Route::post('add', 'QuotationController@add')->middleware('jwt.verify');
    Route::post('update', 'QuotationController@update');
    Route::post('request', 'QuotationController@request')->middleware('jwt.verify');
    Route::post('read_excel', 'QuotationController@readExcel')->middleware('jwt.verify');

});
Route::group(['prefix' => 'v1/business_type', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list', 'BusinessTypeController@list');
    Route::get('list', 'BusinessTypeController@list');
    Route::get('detail/{id?}', 'BusinessTypeController@detail')->where('id', '.*');
    Route::post('create', 'BusinessTypeController@create')->middleware('jwt.verify');
    Route::post('update', 'BusinessTypeController@update')->middleware('jwt.verify');
    Route::post('remove', 'BusinessTypeController@remove')->middleware('jwt.verify');
});

Route::group(['prefix' => 'v1/custom', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('f1', 'CustomController@custom_1');
    Route::get('f2', 'CustomController@custom_2');
});

Route::group(['prefix' => 'v1/confirm_code', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::post('create', 'ConfirmCodeController@create');
    Route::post('check_code', 'ConfirmCodeController@checkCode');
});

Route::group(['prefix' => 'v1/rating', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list', 'RatingController@list');
    Route::post('list_by_object_id', 'RatingController@listByObjectId');
    Route::get('detail/{id?}', 'RatingController@detail')->where('id', '.*');
    Route::post('update', 'RatingController@update')->middleware('jwt.verify');
    Route::post('create', 'RatingController@create')->middleware('jwt.verify');
    Route::post('delete', 'RatingController@delete')->middleware('jwt.verify');
    Route::get('calc_object_rating/{object_id?}', 'RatingController@calcAverageRating');
    Route::get('user_rating/{object_id}', 'RatingController@detailRatingObjectByUser')->middleware('jwt.verify');
});

//---------------------api user addresses--------------------------------

Route::group(['prefix' => 'v1/user_address', 'namespace' => 'Api\v1', 'middleware' => ['xss', 'origin']], function () {

    Route::get('my_addresses', 'UserAddressController@myAddresses')->middleware('jwt.verify');
    Route::post('create', 'UserAddressController@create')->middleware('jwt.verify');
    Route::post('update', 'UserAddressController@update')->middleware('jwt.verify');
    Route::get('detail/{id}', 'UserAddressController@detail')->middleware('jwt.verify');
    Route::post('delete', 'UserAddressController@delete')->middleware('jwt.verify');
    Route::post('set_default', 'UserAddressController@setDefault')->middleware('jwt.verify');
});



 #############################################-----------V2----------#############################################
Route::group(['prefix' => 'v2/video', 'namespace' => 'Api\v2', 'middleware' => ['xss', 'origin']], function () {
    Route::post('upload', 'VideoController@upload')->middleware('jwt.verify');
    Route::post('list', 'VideoController@list');
    Route::get('list_review', 'VideoController@listProductReviewVideos');
    });
//---------------------api chat--------------------------------
Route::group(['prefix' => 'v2/chat', 'namespace' => 'Api\v2', 'middleware' => ['xss', 'origin', 'jwt.verify']], function () {
    Route::post('list', 'MessageController@getMessages');
    Route::post('send', 'MessageController@createMessage');
    Route::post('retract', 'MessageController@retractMessage');
    Route::post('update', 'MessageController@updateMessage' );
//    Route::post('list_by_member_id', 'MessageController@getMessagesWithReceiver');
});


//---------------------api channel--------------------------------
Route::group(['prefix' => 'v2/channel', 'namespace' => 'Api\v2', 'middleware' => ['xss', 'origin', 'jwt.verify']], function () {
    Route::post('list', 'ChannelController@list');
    Route::post('get_channel', 'ChannelController@getChannelIdByTwoMember');
    Route::get('count_unread', 'ChannelController@countChannelUnread');
});

//---------------------api device--------------------------------
Route::group(['prefix' => 'v2/device', 'namespace' => 'Api\v2', 'middleware' => ['xss', 'origin', 'jwt.verify']], function () {
    Route::get('devices_by_user', 'DeviceController@getDevicesByUser');
});

Route::group(['prefix' => 'v2/reel', 'namespace' => 'Api\v2', 'middleware' => ['xss', 'origin']], function () {
    Route::post("create", 'ReelController@createReel')->middleware('jwt.verify');
    Route::post("delete", 'ReelController@deleteReel')->middleware("jwt.verify");
});

//---------------- api pos -----------------------
Route::group(['prefix' => 'v2/pos', 'namespace' => 'Api\v2', 'middleware' => ['xss', 'origin','jwt.verify']], function () {
    Route::post('connect', 'POSController@connect');
    Route::post('products', 'POSController@listProduct');
    Route::group(['prefix' => 'order'], function (){
        Route::post('create', 'POSController@createOrder');
        Route::post('update', 'POSController@updateOrder');
    });

    Route::group(['prefix' => 'branch'], function (){
        Route::post('list', 'POSController@listBranch');
        Route::post('set', 'POSController@setBranch');
    });

});

//------------------ api delivery partner --------------------------

Route::group(['prefix' => 'v2/delivery', 'namespace' => 'Api\v2', 'middleware' => ['xss', 'origin']], function () {
    Route::post("check_price" , 'DeliveryPartnerController@checkPrice');
    Route::post("create" , 'DeliveryPartnerController@createDelivery');
    Route::post("detail", 'DeliveryPartnerController@detailDelivery');
    Route::post("cancel", 'DeliveryPartnerController@cancelDelivery')->middleware('jwt.verify');
});

Route::group(['prefix' => 'v2/delivery_partner', 'namespace' => 'Api\v2', 'middleware' => ['xss', 'origin']], function () {
    Route::get('list', 'DeliveryPartnerController@listDeliveryPartner');
    Route::post('callback/update_status/{partner}', 'DeliveryPartnerController@updateStatus');
    Route::group(['prefix' => 'shop'], function () {
        Route::post("add", 'ShopDeliveryPartnerController@addDeliveryPartner')->middleware('jwt.verify');
        Route::get("list", 'ShopDeliveryPartnerController@listDeliveryPartner');
        Route::post("update", 'ShopDeliveryPartnerController@update')->middleware('jwt.verify');
        Route::post("detail", 'ShopDeliveryPartnerController@detail')->middleware('jwt.verify');
    });
});

