<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Option extends Model
{
    protected $fillable = [
        'name',
        'enable',
        'created_by',
    ];
    protected $primaryKey = 'id';
    protected $table = 'options';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
    protected $hidden = ['updated_at', 'created_at'];

    public function created_by()
    {
        return $this->belongsTo(User::class,'created_by');
    }
}
