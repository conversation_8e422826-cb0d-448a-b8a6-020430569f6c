<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Interaction extends Model
{
    protected $fillable = [
        'object_type_a',
        'object_id_a',
        'object_type_b',
        'object_id_b',
        'interaction_type',
    ];

    protected $casts = [
        'object_id_a' => 'uuid',
        'object_id_b' => 'uuid',
        'interaction_type' => 'integer',
    ];
    protected $table = 'interactions';
    protected $primaryKey = ['object_id_a', 'object_id_b', 'interaction_type'];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model){
        });
    }
    public $timestamps = false;

    public $incrementing = false;
}



