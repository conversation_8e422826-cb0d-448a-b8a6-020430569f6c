<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\Address\AddressService;
use Illuminate\Http\JsonResponse;
use App\Http\Requests\Address\ProvinceConvertVN2000ToWGS84;
class AddressController extends Controller
{
    public function List()
    {
        $list = new AddressService();
        $input = $list->list();

        $reponse = [
          'status' => JsonResponse::HTTP_OK,
          'body'=> [
              'data' => $input,
          ],
        ];
        return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    /**
     * @OA\Get(
     *      path="/api/v1/address/province/list",
     *      operationId="province_list",
     *      tags={"Address"},
     *      summary="Get list of Province",
     *      security={
     *       {"api_key": {}},
     *     },
     *      @OA\Response(
     *          response=200,
     *          description="Successful",
     *          @OA\JsonContent(ref="#/components/schemas/ProvinceModel")
     *       ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad request"
     *      )
     *     )
     */
    public function province(){
        $list = new AddressService();
        $list = $list->getProvinceList();
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $list,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    /**
     * @OA\Get(
     *      path="/api/v1/address/province",
     *      operationId="province_detail",
     *      tags={"Address"},
     *      summary="Get list of district by province id",
     *      security={
     *       {"api_key": {}},
     *     },
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="province id",
     *         required=true,
     *         @OA\Schema(
     *             type="string",
     *         )
     *     ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="id", type="integer"
     *          ),
     *              @OA\Property(
     *                  property="name", type="string"
     *          ),
     *              @OA\Property(
     *                  property="district", type="list",ref="#/components/schemas/DistrictModel"
     *          ),
     *      )
     *       ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad request"
     *      )
     *     )
     */
    public function provinceList(Request $request)
    {
        if($this->checkIdDetail($request->id) == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Province_002_E_004'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $address = new AddressService();
        $address = $address->getProvince($request->id);
        if($address == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'=> [
                    'Province_003_E_003'
                ],
              ];
              return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $address,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    /**
     * @OA\Get(
     *      path="/api/v1/address/district",
     *      operationId="distrct_detail",
     *      tags={"Address"},
     *      summary="Get list of ward by district id",
     *      security={
     *       {"api_key": {}},
     *     },
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="district id",
     *         required=true,
     *         @OA\Schema(
     *             type="string",
     *         )
     *     ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="id", type="integer"
     *          ),
     *              @OA\Property(
     *                  property="name", type="string"
     *          ),
     *              @OA\Property(
     *                  property="province_id", type="integer"
     *          ),
     *              @OA\Property(
     *                  property="district", type="list",ref="#/components/schemas/WardModel"
     *          ),
     *      )
     *       ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad request"
     *      )
     *     )
     */
    public function distrctList(Request $request)
    {
        if($this->checkIdDetail($request->id) == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'District_002_E_002'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $address = new AddressService();
        $address = $address->getDistrict($request->id);
        if($address == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'=> [
                    'District_003_E_001'
                ],
              ];
              return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $address,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }

    public function wardList(Request $request)
    {
        if($this->checkIdDetail($request->id) == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Ward_002_E_002'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $address = new AddressService();
        $address = $address->getWard($request->id);
        if($address == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'=> [
                    'Ward_003_E_001'
                ],
              ];
              return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $address,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    public function wardDetail($id = null,AddressService $service)
    {
        if($this->checkIdDetail($id) == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Ward_002_E_002'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $result = $service->wardDetail($id);
        if($result == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'=> [
                    'Ward_003_E_001'
                ],
              ];
              return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    public function districtByArrId(Request $request, AddressService $service )
    {
        $data = (array)$request->only([
            'id'
        ]);

        $result = $service->districtByArrId($data);
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    public function wardByArrId(Request $request, AddressService $service )
    {
        $data = (array)$request->only([
            'id'
        ]);
        $result = $service->wardByArrId($data);
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    public function villageByArrId(Request $request, AddressService $service )
    {
        $data = (array)$request->only([
            'id'
        ]);
        $result = $service->villageByArrId($data);
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }


    public function updateLatLng(AddressService $service)
    {
        $result = $service->updateLatLng();
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => [],
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }

    public function provinceConvertVN2000ToWGS84(ProvinceConvertVN2000ToWGS84 $request, AddressService $service){
        $data = $request->only(
            'arrVN2000',
            'dataProvince'
        );
        $result = $service->provinceConvertVN2000ToWGS84($data['arrVN2000'], $data['dataProvince']);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function updateProvince(Request $request, AddressService $service){
        $data = (array)$request->only([
            'id','name',
            'slug','longitude','latitude',
            'profile_picture', 'bound',
            'address', 'administrative_unit',
            'created_at', 'updated_at','satellite',
            'streetmap','planning','a', 'b', 'f', 
            'LAM0','PHI0', 'E0', 'N0', 'F0', 'DX', 'DY', 'DZ',
            'X_Rotation', 'Y_Rotation', 'Z_Rotation', 'Scale'
        ]);
        $result = $service->updateProvince($data);

        if($result == false){
          $response = [
            'status' => JsonResponse::HTTP_BAD_REQUEST,
            'body'=> "Property_003_E_019",
          ];
          return response()->json($response,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
        ];
        return response()->json($reponse,JsonResponse::HTTP_OK);
        
    }
}
