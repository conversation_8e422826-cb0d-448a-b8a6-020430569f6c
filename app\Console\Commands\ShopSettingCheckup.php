<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Shop;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Overtrue\ChineseCalendar\Calendar;
use App\Jobs\CheckShopHolidayJob;

class ShopSettingCheckup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:shop-settings-checkup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check shop holiday settings and update shop status';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Starting shop settings checkup...');
        CheckShopHolidayJob::dispatch();
        
        // // Get shops with holiday mode enabled
        // $shops = Shop::whereRaw("settings::jsonb->'general'->'holiday_settings'->'holiday_mode'->>'enabled' = 'true'")->get();
        // $this->info('Found ' . $shops->count() . ' shops with holiday mode enabled');
        
        // $today = Carbon::today()->format('Y-m-d');
        // $dayOfWeek = Carbon::today()->format('l'); // Returns day name (Monday, Tuesday, etc.)
        
        // // Get lunar date for today
        // $calendar = new Calendar();
        // $lunarDate = $calendar->solar2lunar(Carbon::today()->year, Carbon::today()->month, Carbon::today()->day);
        // $lunarMonth = $lunarDate['lunar_month'] ?? null;
        // $lunarDay = $lunarDate['lunar_day'] ?? 'null';
        
        // foreach ($shops as $shop) {
        //     $settings = ($shop->settings);
        //     $daysOff = $settings['general']['holiday_settings']['days_off'] ?? [];
        //     $daysOfWeekOff = $settings['general']['holiday_settings']['days_of_week_off'] ?? [];
        //     $daysOfMonthLunar = $settings['general']['holiday_settings']['days_of_month_lunar'] ?? [];
            
        //     // Check if today is in days_off array
        //     $isClosed = false;
        //     $reason = null;
            
        //     // Check specific dates
        //     foreach ($daysOff as $dayOff) {
        //         $dayOff['date'];
        //         if (isset($dayOff['date']) && $dayOff['date'] === $today) {
        //             $isClosed = true;
        //             $reason = $dayOff['reason']['en'] ?? 'Holiday';
        //             break;
        //         }
        //     }
            
        //     // Check days of week
        //     if (!$isClosed && in_array($dayOfWeek, $daysOfWeekOff)) {
        //         $isClosed = true;
        //         $reason = "Weekly off day ($dayOfWeek)";
        //     }
            
        //     // Check lunar calendar dates
        //     // Check lunar calendar dates (recurring monthly lunar days)
        //     if (!$isClosed && $lunarMonth && $lunarDay && !empty($daysOfMonthLunar)) {
        //         // Check if today's lunar day is in the days_of_month_lunar array
        //         if (in_array((string)$lunarDay, $daysOfMonthLunar)) {
        //             $isClosed = true;
        //             $reason = "Lunar Calendar every month: (Day " . $lunarDay . ")";
        //         }
        //     }
            
        //     // Update shop's is_open status
        //     $settings['general']['is_open'] = !$isClosed;
        //     $shop->settings = ($settings);
        //     $shop->save();
            
        //     if ($isClosed) {
        //         $this->info("Shop {$shop->id} ({$shop->name}) is closed today. Reason: $reason");
        //     } else {
        //         $this->info("Shop {$shop->id} ({$shop->name}) is open today.");
        //     }
        // }
        
        // $this->info('Shop settings checkup completed successfully!');
        // return 0;
    }
}
