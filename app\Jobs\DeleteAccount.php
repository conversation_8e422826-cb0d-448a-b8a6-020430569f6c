<?php

namespace App\Jobs;

use App\Request;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;

class DeleteAccount implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $request;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request = null)
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $user = User::find($this->request->user_id);

            if ($user && $user->delete()) {
                    $this->request->update(['status' => 'success']);
            } else {
                $this->request->update(['status' => 'failed']);
            }
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
        }
    }
}
