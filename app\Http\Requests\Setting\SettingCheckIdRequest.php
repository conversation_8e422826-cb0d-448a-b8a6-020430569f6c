<?php

namespace App\Http\Requests\Setting;

use App\Http\Requests\BaseRequest;

class SettingCheckIdRequest extends BaseRequest
{
   
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:settings,id'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'Setting_001_E_004',
    //         'id.uuid' => 'Setting_002_E_005',
    //         'id.exists' => 'Setting_003_E_006',
    //     ];
    // }
}
