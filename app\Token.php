<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Token extends Model
{
    protected $fillable = ['token', 'user_id', 'device', 'device_id', 'token_expired','address_ip', 'token_type'];
    protected $table = 'tokens';
    protected $primaryKey = 'id';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model){
            $model->id = Str::uuid();
        });
    }

    protected $hidden = ['updated_at','address_ip'];
}
