<?php

namespace App\Services\Chat;

use App\ChannelConnect;
use App\Services\GeneralService;
use App\Shop;
use App\User;
use Illuminate\Support\Facades\Auth;

class ChannelConnectionService
{
    private $_memberId;
    private $_memberType;
    private $_model;

    public function __construct($_memberId, $_memberType = "user")
    {
        $this->_memberId = $_memberId;
        $this->_memberType = $_memberType;
        $this->_model = new ChannelConnect();
    }

    public function setMember($_memberId, $_memberType )
    {
        $this->_memberId = $_memberId;
        $this->_memberType = $_memberType;
    }

    static function checkAuthor($id, $type)
    {
        $userId = Auth::user()->id;
        if($type == "user" && $id == $userId) return true;
        if($type == "shop" && (GeneralService::checkShopOwner($userId, $id) || GeneralService::checkAgentShopRelate($userId, $id))) return true;
        return false;
    }

    // Lấy ngôn ngữ đang sử dụng của thành viên trong một channel bất kì
    static function getLanguageOfAMember($memberId, $memberType = "user"){
        if($memberType == "user"){
            $language = User::on("pgsqlReplica")->select('language')->find($memberId)['language'];
        }else{
            $language = Shop::on("pgsqlReplica")->select('language')->find($memberId)['language'];
        }
        return json_decode($language, true);
    }

    // Lấy danh sách các channel của một thành viên
    static function getListChannel($memberId)
    {
        return ChannelConnect::on("pgsqlReplica")->where("member_id", $memberId)->with("channel")->get();
    }

    public function create($channelId)
    {
        $this->_model->setConnection("pgsql");
        $this->_model->create([
            'channel_id' => $channelId,
            'member_id' => $this->_memberId,
            'member_type' => $this->_memberType,
        ]);
    }

    public function getListMember($channelId){
        $this -> _model->setConnection('pgsqlReplica');
        $result = $this->_model
            ->select('member_type', 'member_id')
            ->with('member')
            ->where('channel_id', $channelId)
            ->get();

        return $result;
    }

}
