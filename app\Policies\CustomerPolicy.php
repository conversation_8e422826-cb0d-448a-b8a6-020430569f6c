<?php

namespace App\Policies;

use App\User;
use App\Customer;
use App\Policies\BasePolicy;

use Illuminate\Auth\Access\HandlesAuthorization;

class CustomerPolicy extends BasePolicy
{
    use HandlesAuthorization;


    public function __construct()
    {
        //
    }

    public function show(User $user)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting = $this->setting('customer_view');

        return in_array($user->role_id, json_decode($setting['value'],true));
    }

    public function create(User $user)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

       $setting = $this->setting('customer_create');

       return in_array($user->role_id, json_decode($setting['value'],true));
    }

    public function update(User $user)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting = $this->setting('customer_update');

        return in_array($user->role_id, json_decode($setting['value'],true));
    }

    public function delete(User $user)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting = $this->setting('customer_delete');

        return in_array($user->role_id, json_decode($setting['value'],true));
    }
}
