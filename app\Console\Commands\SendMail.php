<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Mail;
use App\Mail\SendForgetPasswordMail;
use App\Services\Mqtt\MqttChatService;
use App\Helpers\S3Utils;

class SendMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sendmail:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Success';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Mail::to('<EMAIL>')->send(new SendForgetPasswordMail("testmail from clomart BE",now()->toDateTimeString()));
        // $this->info('Hello Bangladesh!!');\
        // $this->testPutImage();
        // $mqtt = new MqttChatService();
        // $mqtt->publish(['topic' => env('IMPORT_PROPERTY_TOPIC')."user_id", 'message' => "hello"]);
        // return 1;
    }
    public function testPutImage()
    {
        $uploadImageResult = S3Utils::upload(file_get_contents(public_path('disc.jpg')), 'uploads/test.jpg');
    }
}
