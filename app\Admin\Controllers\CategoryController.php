<?php

namespace App\Admin\Controllers;

use App\Category;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Illuminate\Support\Str;
use App\Admin\Actions\BatchDelete;

class CategoryController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Category';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Category());

        $grid->column('id', __('Id'));
        $grid->column('name', __('Name'))->sortable()->link(function () {
            return "categories/".$this->id;
        });
        $grid->parent_category()->display(function ($shop) {return $shop['name'] ?? "";});
        $grid->column('enable', __('Enable'))->icon([
            0 => 'toggle-off',
            1 => 'toggle-on',
        ], $default = '');
        $grid->column('profile_picture', __('Profile picture'))->image();
        // $grid->column('created_by', __('Created by'));
        $grid->column('created_at', __('Created at'));
        $grid->column('updated_at', __('Updated at'));
        $grid->column('slug', __('Slug'));


        $grid->quickCreate(function (Grid\Tools\QuickCreate $create) {
            $create->text('name', 'Name');
            $create->select('parent_id', __('parent'))->options(Category::where('enable',true)->get()->pluck('name','id'));
        });
        $grid->filter(function($filter){
            $filter->column(1/2, function ($filter) {
                // Remove the default id filter
                // $filter->disableIdFilter();
                
                // Add a column filter
                $filter->where( function ($query) {
                    $query->where('name', 'ilike', "%{$this->input}%");
                
                }, __('name'));
                $filter->in('shop_id', __('shop'))->multipleSelect('/admin_get/shops');
            });
            $filter->column(1/2, function ($filter) {
                $filter->equal('enable')->radio([
                    1 => 'YES',
                    0 => 'NO',
                ]);
                // $filter->in('shop_id', __('shop'))->multipleSelect(Shop::where('enable',true)->get()->pluck('name','id'));
            });
        
        });
        // Add batch delete action
        $grid->batchActions(function ($batch) {
            $batch->add(new BatchDelete('Category')); // Ensure 'Shop' is the correct model type
        });
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Category::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('name', __('Name'));
        $show->field('parent_id', __('Parent id'));
        $show->field('enable', __('Enable'));
        // $show->field('created_by', __('Created by'));
        $show->field('profile_picture', __('Profile picture'))->image();
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));
        $show->field('slug', __('Slug'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Category());

        $form->text('name', __('Name'));
        $form->select('parent_id', __('Parent'))->options(Category::where('enable',true)->get()->pluck('name','id'));
        $form->switch('enable', __('Enable'))->default(1);
        // $form->text('created_by', __('Created by'));
        $form->image('profile_picture', __('Profile picture'))
        ->removable()->downloadable()
        ->move("product/".date("Y")."/".date("m"), Str::slug($form->model()->name)."_".uniqid().'.jpg')
        ->rules('mimes:jpeg,png,jpg,gif,svg,webp')
        // ->thumbnail('small', 300, 300)
        ;
        $form->text('slug', __('Slug'));

        return $form;
    }
}
