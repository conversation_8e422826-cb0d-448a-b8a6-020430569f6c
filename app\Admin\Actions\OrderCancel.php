<?php

namespace App\Admin\Actions;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class OrderCancel extends RowAction
{
    public function name()
    {
        return __('admin.order.status_admin.6');
    }

    public function handle (Model $model)
    {
        $model->status = config('constants.order.status.cancel', 6);
        $model->notes = $model->notes . " Hủy đơn từ Admin.";
        $model->save();

        return $this->response()->success(__('admin.order.status_admin.6'))->refresh();
    }


}