<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use App\Image;
use App\Setting;
use App\Http\Controllers\Controller;
use App\Http\Requests\Image\ImageRequest;
use App\Http\Requests\Image\ImageFileRequest;
use App\Http\Requests\Image\ImageCheckIdRequest;
use App\Http\Requests\Image\ImageProblemReportRequest;
use App\Http\Requests\Tag\TagImageRequest;
use App\Services\Image\ImageService as Img;
use App\Helpers\S3Utils;
use Illuminate\Support\Facades\Auth;
use App\Services\Tag\TagService;
use App\Services\GeneralService;


class ImageController extends Controller
{
    public function insert(ImageRequest $request)
    {
        $data = (array)$request->only([
            'path',
            'title',
            'description',
            'parent_id',
            'object_type',
            'orientation',
            'style',
            'image_type',
            'index',
            'is_profile_picture',
        ]);
        $service = new Img();
        // if(isset($data['panorama']) && $data['panorama'] == true)
        // {
        //     $result = $service->addPanorama($data);
        // }
        // else {
            $result = $service->addBase64($data);
        // }

        // unset($data['path']);
        $general = new GeneralService(Auth::user()->id);
        $general->addLog(
            config('constants.log_action.create'),
            $result['images']['id'] ?? '',
            config('constants.object_type.image'),
            json_encode($result),
            json_encode($data)
        );
        

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'data' => "path min 350x350"
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }


        // if($result === 'panorama')
        // {
        //     $response = [
        //         'status' => JsonResponse::HTTP_BAD_REQUEST,
        //         'body' => [
        //             "Panorama isn't enought condition"
        //         ]
        //     ];

        //     return response()->json($response, JsonResponse::HTTP_OK);
        // }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];

       return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function uploadToS3(Request $request) {
        $files = $request->file('images');
        if($files)
        {
            // foreach ($files as $file) {
                $baseName = pathinfo($files->getClientOriginalName(), PATHINFO_FILENAME);
                $filePath = $files->getRealPath();
                $imgSize = filesize($filePath);
                if($imgSize > 3000000){
                    $response['body']['message'] .= $baseName . ' is more than 3MB!';
                    // continue;
                }
                $baseName = str_replace(" ","",$baseName);
                // $fileExtension = $images->getClientOriginalExtension();
                $fileExtension = 'jpg';
                $time = date(config('constants.UPLOAD_DAYTIME')) .'_' . uniqid();
                $fileName = sprintf(config('constants.UPLOAD_FILENAME'), $time,'', $baseName, $fileExtension);
                $fileMime = mime_content_type($filePath);
                $mimeAccept = array('image/png','image/jpg','image/jpeg','image/JPG','image/PNG','image/JPEG','image/webp');
                if(in_array($fileMime, $mimeAccept))
                {
                    $fileContent = file_get_contents($files->getRealPath());
                    // do upload file
                    $uploaded = S3Utils::upload($fileContent, 'uploads/' . $fileName);
                    if($uploaded){
                        $uploadedFile = env('S3_HOST_PATH') . 'uploads/' . $fileName;
                        // var_dump($uploadedFile);
                    }
                }else{
                    $response['body']['message'] .= $baseName . ' is not image file!';
                }
            // }
            // return imgs path
        }else{
            $response['body']['message'] = "not found any images";
        }



        return [];
    }

    //---------------delete image----------------
    /**
     * @OA\Post(
     *     path="/api/v1/image/delete",
     *     operationId="Image Delete",
     *     tags={"Image"},
     *     summary="Image Delete ",
     *     security={
     *       {"api_key": {}},
     *     },
     *     @OA\Response(
     *         response="200",
     *         description="Successful"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Bad request"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         description="Unauthorized"
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="Image id want delete",
     *         required=true,
     *         @OA\Schema(
     *             type="integer",
     *         )
     *     )
     * )
     */
    public function delete(ImageCheckIdRequest $request, Img $service)
    {
        $data = (array)$request->only([
            'id'
        ]);
        $result = $service->delete($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Image_003_E_013',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [

            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------------- add file------------------
    public function insertFile(ImageFileRequest $request)
    {

        $data = (array)$request->only([
            'path',
            'title',
            'parent_id',
            'object_type'
        ]);
        $service = new Img(Auth::user()->id);
        $result = $service->add($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'data' => "path min 350x350"
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);

    }
    public function detail($id, Img $service)
    {
        $result = $service->preview($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    public function rotateImage(Request $request)
    {
        $data = (array)$request->only([
            'path',
            'parent_id',
            'object_type',
            'angle',
        ]);
        $service = new Img(Auth::user()->id);
        $result = $service->rotateImage($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function problemReportInsert(ImageProblemReportRequest $request)
    {
        $data = (array)$request->only([
            'path',
            'title',
            'parent_id',
            'object_type',
            'orientation'
        ]);
        $service = new Img();
        $result = $service->problemReportInsert($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'data' => "path min 350x350"
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];

       return response()->json($response, JsonResponse::HTTP_OK);
    }

    //---------update avatar property---------------
    public function updateData()
    {
        $service = new Img();
        $result = $service->updateData();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [

            ]
        ];

       return response()->json($response, JsonResponse::HTTP_OK);
    }


    public function ocr(TagImageRequest $request,TagService $service)
    {
        $data = $request->only([
            'file',
            'orientation'
        ]);
        $result = $service->hanldOCR($data);

        if($result == false)
        {
            $response = [
            'status' => JsonResponse::HTTP_BAD_REQUEST,
            'body' => "OCR_001_E_001"
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
