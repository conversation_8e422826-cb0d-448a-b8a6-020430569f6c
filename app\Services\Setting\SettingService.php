<?php

namespace App\Services\Setting;

use App\Setting;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Auth;
use App\User;
use Illuminate\Support\Facades\Cache;

class SettingService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }
    //------process insert setting-----------
    public function add(array $setting)
    {
        $result = Setting::create($setting);

        //----add into log------------
        // $general = new GeneralService;
        // $general->addLog(Config::get('constants.log_action.create', 1),$result->id,Config::get('constants.object_type.setting', 17),$result, json_encode($setting));

        return $result;
    }

    //-------process list setting-------
    public function list()
    {
        $result = Setting::orderBy('created_at','asc')->get();
        return $result;
    }

    //-----process detail setting-------
    public function detail($id)
    {
        $result = Setting::find($id);

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //--------process update setting-----------
    public function update(array $setting)
    {
        $result = Setting::find($setting['id']);

        if(!$result)
        {
            return false;
        }

        $result->update($setting);
        //---------delete cache-----------
        try {
            Cache::forget('list-highlight-facility');
        } catch (\Throwable $th) {
            //throw $th;
        }


        //----add into log------------
        $general = new GeneralService;
        $general->addLog(Config::get('constants.log_action.update', 2),$result->id,Config::get('constants.object_type.setting', 17),$result, json_encode($setting));

        return $result;
    }

    //------process update setting---------
    public function delete(array $setting)
    {
        $result = Setting::find($setting['id']);

        if(!$result)
        {
            return false;
        }

        $result->delete();

        //----add into log------------
        $general = new GeneralService;
        $general->addLog(Config::get('constants.log_action.update', 2),$setting['id'],Config::get('constants.object_type.setting', 17),$result, json_encode($setting));

        return $result;
    }
}
