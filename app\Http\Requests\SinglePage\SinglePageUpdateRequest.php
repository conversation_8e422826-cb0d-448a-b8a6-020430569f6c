<?php

namespace App\Http\Requests\SinglePage;

use App\Http\Requests\BaseRequest;

class SinglePageUpdateRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:single_pages,id',
            'title' => 'required|max:255',
            'slug' => 'required|max:255',
            'content' => 'required',
        ];
    }


    public function messages()
    {

        return [
            
            'title.required' => 'SinglePage_001_E_001',
            'title.max'  => 'SinglePage_004_E_002',

            'content.required' => 'SinglePage_001_E_003',
            'content.max' => 'SinglePage_001_E_004',

            'slug.required'  => 'SinglePage_001_E_005',
            'slug.max' => 'SinglePage_001_E_006',

            'id.required' => 'SinglePage_002_E_007',
            'id.uuid' => 'SinglePage_002_E_008',
            'id.exists' => 'SinglePage_002_E_009'

        ];
    }
}
