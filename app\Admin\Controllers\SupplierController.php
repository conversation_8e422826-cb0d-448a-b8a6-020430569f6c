<?php

namespace App\Admin\Controllers;

use App\Category;
use App\Supplier;
use App\Shop;
use App\User;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Illuminate\Support\Str;
// Use Encore\Admin\Widgets\Table;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Log;
use phpDocumentor\Reflection\DocBlock\Tags\Var_;

class SupplierController extends AdminController
{


    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Supplier';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Supplier());

        $grid->column('id', __('Id'))->sortable();
        $grid->column('name', __('Name'))->sortable()->link(function () {
            return "suppliers/".$this->id;
        })->limit(100);
        $grid->shop()->display(function ($shop) {return $shop['name'] ?? "";});
        $grid->column('created_at', __('Created at'))->display(function () {
            return "<span class='label' style='color:blue'>$this->created_at</span>";
        })->sortable();
        
        // Operate on the `$grid` instance
        $grid->filter(function($filter){
            $filter->scope('new', 'Recently modified')
                ->whereDate('created_at', date('Y-m-d'))
                ->orWhere('updated_at', date('Y-m-d')); 
            $filter->scope('type', 'Base System product')->where('type', 1);

            $filter->column(1/2, function ($filter) {
                // Remove the default id filter
                // $filter->disableIdFilter();
                
                // Add a column filter
                $filter->where( function ($query) {
                    $query->where('name', 'ilike', "%{$this->input}%");
                
                }, __('name'));
            
                $filter->in('shop_id', __('shop'))->multipleSelect('/admin_get/shops');
            });
            $filter->column(1/2, function ($filter) {

                // $filter->in('shop_id', __('shop'))->multipleSelect(Shop::where('enable',true)->get()->pluck('name','id'));
            });
        
        });
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Supplier::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('name', __('Name'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Supplier());
        $form->text('name', __('Name'));
        $form->text('phone', __('phone'));
        $form->text('address', __('address'));
        $form->text('email', __('email'));
        $form->select('shop_id', __('Shop'))->options(Shop::where('enable',true)->get()->pluck('name','id'));
        
        // $form->saving(function (Form $form__) {
        //     Log::info($form__->model());
        // });
        return $form;
    }
}
