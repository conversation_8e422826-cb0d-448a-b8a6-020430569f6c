<?php
namespace App\Services\Image;
use App\Channel;
use App\Image;
use App\Property;
use App\Advertisment;
use App\Place;
use App\SinglePage;
use App\User;
use App\Product;
use App\Shop;
use App\Category;
use App\Rating;
use App\Order;
use App\File;
use Illuminate\Support\Facades\Storage;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;
use App\Helpers\S3Utils;
use Jenssegers\Agent\Agent;
// use Image as Im;
// use Intervention\Image\ImageManager as Im;
use Intervention\Image\ImageManagerStatic as Im;
use Illuminate\Support\Facades\Auth;
use App\Jobs\InsertImage;
use App\Jobs\UpdateAvatarProperty;
use App\Jobs\updateThumbnail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Http\UploadedFile;
use App\BrokerHandbook;
use GoogleCloudVision\GoogleCloudVision;
use GoogleCloudVision\Request\AnnotateImageRequest;



class ImageService
{
    private $_userId;
    private $objectName;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    //-------insert image------------------
    public function insert(array $image, $new_image, $fileName)
    {

        $log = $image;
        $url = '';//env('URL_IMAGE');
        $date = date('Ymd');
        switch ($image['object_type']) {
            case '1':

                $user = User::find($image['parent_id']);
                // $image['title'] = $user['name'];
                $fileName = Str::slug(Str::limit($user['name'], 100)).'-'.$fileName;
                S3Utils::upload(base64_decode($new_image), 'user/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'user/'.$image['parent_id'].'/'.$fileName;
                $image['path_thumbnail'] =$url.'user/'.$image['parent_id'].'/'.$fileName;
                $user['profile_picture'] = $image['path'];
                $user->save();
                break;

            case '2':
                $product = Product::find($image['parent_id']);
                // $image['title'] = $product['name'];
                $fileName = Str::slug(Str::limit($product['name'], 100)).'-'.$fileName;
                S3Utils::upload(base64_decode($new_image), 'product/' .date('Y').'/'.date('m').'/'. $fileName);
                $image['path'] =$url.'product/'.date('Y').'/'.date('m').'/'.$fileName;
                $image['path_thumbnail'] =$url.'product/'.date('Y').'/'.date('m').'/'.$fileName;
                if(isset($image['is_profile_picture']) && $image['is_profile_picture'] == true){
                    $product['profile_picture'] = $image['path'];
                    $product->save();
                }
                break;
            case '3':
                $shop = Shop::find($image['parent_id']);
                // $image['title'] = $shop['name'];
                $fileName = Str::slug(Str::limit($shop['name'], 100)).'-'.$fileName;
                S3Utils::upload(base64_decode($new_image), 'shop/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'shop/'.$image['parent_id'].'/'.$fileName;
                $image['path_thumbnail'] =$url.'shop/'.$image['parent_id'].'/'.$fileName;
                $result = Image::create($image);
                if(isset($image['image_type'])){
                    if($image['image_type'] == 'logo'){
                        $shop['logo_id'] = $result['id'];
                    }
                    elseif($image['image_type'] == 'banner'){
                        $shop['banner_id'] = $result['id'];
                    }
                }
                $shop->save();
                return $result;
                break;
            case '4':
                $category = Category::find($image['parent_id']);
                // $image['title'] = $objectName['name'];
                $fileName = Str::slug(Str::limit($category['name'], 100)).'-'.$fileName;
                S3Utils::upload(base64_decode($new_image), 'category/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'category/'.$image['parent_id'].'/'.$fileName;
                $image['path_thumbnail'] =$url.'category/'.$image['parent_id'].'/'.$fileName;
                $category['profile_picture'] = $image['path'];
                $category->save();
                break;
            case '9':
                $order = Order::find($image['parent_id']);
                // $image['title'] = $order['name'];
                $fileName = Str::slug(Str::limit($order['name'], 100)).'-'.$fileName;
                S3Utils::upload(base64_decode($new_image), 'order/' .date('Y').'/'.date('m').'/'. $fileName);
                $image['path'] =$url.'order/'.date('Y').'/'.date('m').'/'.$fileName;
                $image['path_thumbnail'] =$url.'order/'.date('Y').'/'.date('m').'/'.$fileName;
                break;
            case '10':
                $rating = Rating::find($image['parent_id']);
                // $image['title'] = $shop['name'];
                // $fileName = Str::slug(Str::limit($rating['object_id'], 100)).'-'.$fileName;
                S3Utils::upload(base64_decode($new_image), 'rating/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'rating/'.$image['parent_id'].'/'.$fileName;
                $image['path_thumbnail'] =$url.'rating/'.$image['parent_id'].'/'.$fileName;
                $result = Image::create($image);
                return $result;
                break;
            case '12':
                Image::where('parent_id', $image['parent_id'])->where('object_type', 12)->delete();
                S3Utils::upload(base64_decode($new_image), 'file/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'file/'.$image['parent_id'].'/'.$fileName;
                $image['path_thumbnail'] =$url.'file/'.$image['parent_id'].'/'.$fileName;
                $result = Image::create($image);
                return $result;
                break;
            case '13':
                S3Utils::upload(base64_decode($new_image), 'channel/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'channel/'.$image['parent_id'].'/'.$fileName;
                $image['path_thumbnail'] =$url.'channel/'.$image['parent_id'].'/'.$fileName;
                $result = Image::create($image);
                return $result;
                break;

            case '14':
                S3Utils::upload(base64_decode($new_image), 'user_address/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'user_address/'.$image['parent_id'].'/'.$fileName;
                $image['path_thumbnail'] =$url.'user_address/'.$image['parent_id'].'/'.$fileName;
                $result = Image::create($image);
                return $result;
                break;
            default:
                // S3Utils::upload(base64_decode($new_image), 'uploads/tmp/' .$image['parent_id'].'/'. $fileName);
                // $image['path'] =$url.'uploads/tmp/'.$image['parent_id'].'/'.$fileName;
                break;
        }
        if(isset($image['update']) && $image['update'] == true)
        {

            $result = $this->updateAfterRotate($image);
        }
        else {

            if($image['object_type'] == 1 && isset($image['index']))
            {
                $check = Image::where([
                    ['parent_id',$image['parent_id']],
                    ['object_type', config('constants.image_object_type.user')],
                    // ['image_type', config('constants.image_type.root')],
                    // ['is_profile_picture', true]
                    ['index', $image['index']]
                    ])->get('id');

                if($check)
                {
                    foreach ($check as $key => $value) {
                        Image::where('id',$value['id'])->update(['index' => 0]);
                    }
                }
            }
            $result = Image::create($image);
        }


        return $result;
    }

    //-------insert image thumbnail------------------
    public function insertThumbnail($image, $new_image, $fileName)
    {
        $result = null;
        $date = date('Ymd');
        if(isset($image->id))
        {
            $url = env('URL_IMAGE');
            switch ($image['object_type']) {
                case '1':
                    $objectName = User::select('name')->find($image['parent_id']);
                    // $image['title'] = $objectName['name'];
                    $fileName = Str::slug(Str::limit($objectName['name'], 100)).'-'.$fileName;
                    S3Utils::upload(base64_decode($new_image), 'uploads/user/'.$date."/" .$image->parent_id.'/' . $fileName);
                    $path_thumbnail =$url.'uploads/user/'.$date."/".$image->parent_id.'/'.$fileName;
                    break;

                case '2':
                    $objectName = Product::select('name')->find($image['parent_id']);
                    // $image['title'] = $objectName['name'];
                    $fileName = Str::slug(Str::limit($objectName['name'], 100)).'-'.$fileName;
                    S3Utils::upload(base64_decode($new_image), 'uploads/product/' .$image->parent_id.'/'. $fileName);
                    $path_thumbnail =$url.'uploads/product/'.$image->parent_id.'/'.$fileName;
                    break;
                case '3':
                    $objectName = Shop::select('name')->find($image['parent_id']);
                    // $image['title'] = $objectName['name'];
                    $fileName = Str::slug(Str::limit($objectName['name'], 100)).'-'.$fileName;
                    S3Utils::upload(base64_decode($new_image), 'uploads/shop/' .$image->parent_id.'/'. $fileName);
                    $path_thumbnail =$url.'uploads/shop/'.$image->parent_id.'/'.$fileName;
                    break;
                case '4':
                    $objectName = Category::select('name')->find($image['parent_id']);
                    // $image['title'] = $objectName['name'];
                    $fileName = Str::slug(Str::limit($objectName['name'], 100)).'-'.$fileName;
                    S3Utils::upload(base64_decode($new_image), 'uploads/category/' .$image->parent_id.'/'. $fileName);
                    $path_thumbnail =$url.'uploads/category/'.$image->parent_id.'/'.$fileName;
                    break;
                default:

                    break;
            }

            $result = $image->update(['path_thumbnail' => $path_thumbnail]);


            //----add into log---------------
            $general = new GeneralService($this->_userId);
            $general->addLog(Config::get('constants.log_action.update', 2),$image->id,Config::get('constants.object_type.image', 11),$image, json_encode($path_thumbnail));

            return $result;
        }

        return $result;
    }

    //-----------preview--------------------
    public function preview($id = null)
    {
        $result = Image::select('id','path')->find($id);
        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //------process add image---------------
    public function add(array $image)
    {
        if (function_exists('exif_read_data')) {
            $exif = exif_read_data($image['path']);
            if($exif && isset($exif['Orientation'])) {
                $orientation = $exif['Orientation'];
                if($orientation != 1){
                    $img = imagecreatefromjpeg($image['path']);
                    $deg = 0;
                    switch ($orientation) {
                        case 3:
                            $deg = 180;
                            break;
                        case 6:
                            $deg = 270;
                            break;
                        case 8:
                            $deg = 90;
                            break;
                    }
                    if ($deg) {
                        $img = imagerotate($img, $deg, 0);
                    }
                    imagejpeg($img,"imageRotate.jpeg",100);
                    $image['path'] = public_path('imageRotate.jpeg');
                    $tmpFile = new File($image['path']);
                    $image['path'] = new UploadedFile(
                        $tmpFile->getPathname(),
                        $tmpFile->getFilename(),
                        $tmpFile->getMimeType(),
                        0,
                        true
                    );
                    imagedestroy($img);
                }
            }
        }

        $user = User::find($this->_userId);
        if($user)
        {
            $image['created_by'] = $user->id;
        }
        $result = null;
        $nameImg = date('YmdHis').uniqid('_');
        if(isset($image['path']))
        {
            $name = $image['path']->getRealPath();
            //------add image---------------
            $img = Im::make(file_get_contents($name));

            if($img->width() < 350 && $img->height() < 350)
            {
                return false;
            }

            //----image medium-------------
            // if($img->width() > 800 && $img->height() > 800)
            // {
            //     $fileName = $nameImg."_medium.jpeg";
            //     $data['medium'] = $this->imagePro($image['path']->getRealPath(),$fileName,800,80,config('constants.image_type.medium'),$image);
            // }
            $widthTN = $img->width();
            $heightTN = $img->height();
            //----image original----------
            if($img->width() <= 2000 && $img->height() <= 2000 && $img->width() >= 350 && $img->height() >= 350)
            {
                $width = round($img->width() / 10);
                $height = round($img->height() / 10);
                $fitW = $width < $height ? $height : $width;
                // $image['have_medium'] =  isset($data['medium']) && $data['medium'] ? true : false;
                // $image['have_thumbnail'] = true;
                $fileName = $nameImg.".jpeg";
                // $data['images'] = $this->imagePro($image['path']->getRealPath(),$fileName,0,$fitW,config('constants.image_type.root'),$image);
                $result['images'] = $this->imagePro($img,$fileName,0,$fitW,config('constants.image_type.root'),$image);
            }
            else
            {
                // $image['have_medium'] =  isset($data['medium']) && $data['medium'] ? true : false;
                // $image['have_thumbnail'] =true;
                $fileName = $nameImg.".jpeg";
                // $data['images'] = $this->imagePro($image['path']->getRealPath(),$fileName,2000,200,config('constants.image_type.root'),$image);
                $result['images'] = $this->imagePro($img,$fileName,2000,200,config('constants.image_type.root'),$image);
            }


            //----image thumbnail-------------
            if($widthTN >= 350 && $heightTN >= 350)
            {
                $fileName = $nameImg."_thumbnail.jpeg";
                // $data['thumbnail'] = $this->imageProNotWatermark($image['path']->getRealPath(),$fileName,350,config('constants.image_type.thumbnail'),$image);
                $this->imagePro($img,$fileName,350,35,config('constants.image_type.thumbnail'),$result['images']);
            }

        }
        return $result;
    }

    //-----insert avatar--------------
    public function insertAvatar(array $image)
    {
        $image['path'] = $this->orientation($image);
        $user = User::find($this->_userId);
        if($user)
        {
            $image['created_by'] = $user->id;
        }

        $data = [];
        $nameImg = date('YmdHis').uniqid('_');
        list($extension, $content) = explode(';', $image['path']);
        $tmpExtension = explode('/', $extension);
        $img = Im::make($image['path']);

        if ($image['object_type'] == config('constants.image_object_type.user')) {
            if($img->width() < 50 && $img->height() < 50)
            {
                return false;
            }
        }

        //----image medium-------------
        // if($img->width() > 800 && $img->height() > 800)
        // {
        //     $fileName = $nameImg."_medium.jpeg";
        //     $data['medium'] = $this->imagePro($image['path']->getRealPath(),$fileName,800,80,config('constants.image_type.medium'),$image);
        // }
        $widthTN = $img->width();
        $heightTN = $img->height();
        //----image original----------
        if($img->width() <= 2000 && $img->height() <= 2000)
        {
            $width = round($img->width() / 10);
            $height = round($img->height() / 10);
            $fitW = $width < $height ? $height : $width;
            // $image['have_medium'] =  isset($data['medium']) && $data['medium'] ? true : false;
            // $image['have_thumbnail'] = true;
            $fileName = $nameImg.".webp";
            // $data['images'] = $this->imagePro($image['path']->getRealPath(),$fileName,0,$fitW,config('constants.image_type.root'),$image);
            $data['images'] = $this->imageProNotWatermark($img,$fileName,0,config('constants.image_type.root'),$image);
        }
        else
        {
            // $image['have_medium'] =  isset($data['medium']) && $data['medium'] ? true : false;
            // $image['have_thumbnail'] =true;
            $fileName = $nameImg.".webp";
            // $data['images'] = $this->imagePro($image['path']->getRealPath(),$fileName,2000,200,config('constants.image_type.root'),$image);
            $data['images'] = $this->imageProNotWatermark($img,$fileName,2000,config('constants.image_type.root'),$image);
        }

        //----image thumbnail-------------
        /*if($widthTN >= 350 && $heightTN >= 350)
        {
            $fileName = $nameImg."_thumbnail.webp";
            // $data['thumbnail'] = $this->imageProNotWatermark($image['path'],$fileName,350,config('constants.image_type.thumbnail'),$image);
            $data['thumbnail'] = $this->imageProNotWatermark($img,$fileName,350,config('constants.image_type.thumbnail'),$data['images']);
        }*/


        return $data;
    }

    //------process check image delete-------------
    // Note that this function maybe not work, cause the delay of checking exists file on minio. should retry later if fail.
    public function delete($imageID)
    {
        // $image = Image::find($imageID);
        // if(!$image)
        // {
        //     return false;
        // }
        // $path = $image->path;
        $path = "product/2024/10/bo-kobe-a1-20241004152243_66ffa5d34bc26.webp";
        // $ext = @pathinfo($original, PATHINFO_EXTENSION);

        // We remove extension from file name so we can append thumbnail type
        // $path = @Str::replaceLast('.'.$ext, '', $original).'.'.$ext;
        // $path = 'shop/2024/05/test_6638afb6a720d.jpg';
        // We merge original name + thumbnail name + extension
        // $path = $path.'-'.$name.'.'.$ext;
        $storage = Storage::disk('minio');
        // @unlink($path);
        // var_dump($storage->exists($path));
        if ($storage->exists($path)) {
            Log::info('File exists, process to <<<DELETE>>> file::: ' . $path);
            // $image->delete();
            $storage->delete($path);
            return true;
        }else{
            Log::info('Not found file to delete:: ' . $path);
            return false;
        }
        return true;
    }

    //-----delete image------------
    public function processDelete(array $image)
    {
        $result = Image::find($image['id']);
        if(!$result)
        {
            return false;
        }

        S3Utils::deleteIfExist($result['path']);
        $result->delete();

        //-----add into log----------
        $general = new GeneralService($this->_userId);
        $general->addLog(Config::get('constants.log_action.delete', 3),$image['id'],Config::get('constants.object_type.image', 11),$result, json_encode($image));

        return $result;
    }

    //------process add image base64-----------
    public function addBase64(array $image)
    {
        if(isset($image['orientation']) && $image['orientation'] != 1 && $image['orientation'] != 0) {
                // $img = imagecreatefromjpeg($image['path']);
                $exploded = explode(',', $image['path'], 2);
                $encoded = $exploded[1];
                $imgBase64 = base64_decode($encoded);
                $img = imagecreatefromstring($imgBase64);
                $deg = 0;
                switch ($image['orientation']) {
                    case 2:
                        $img = $this->imageFlip($img,2);
                        break;
                    case 3:
                        $deg = 180;
                        break;
                    case 4:
                        $img = $this->imageFlip($img,2);
                        $deg = 180;
                        break;
                    case 5:
                        $deg = 270;
                        $img = $this->imageFlip($img,1);
                        break;
                    case 6:
                        $deg = 270;
                        break;
                    case 7:
                        $deg = 90;
                        $img = $this->imageFlip($img,1);
                        break;
                    case 8:
                        $deg = 90;
                        break;
                }
                if ($deg != 0) {
                    $img = imagerotate($img, $deg, 0);
                }
                ob_start();
                imagejpeg($img,NULL,100);
                $contents = ob_get_contents();
                ob_end_clean();

                $image['path'] = "data:image/jpeg;base64," . base64_encode($contents);

        }
        $log = $image;
        $user = User::find($this->_userId);
        if($user)
        {
            $image['created_by'] = $user->id;
        }


        $data = [];
        $result = null;
        $nameImg = date('YmdHis').uniqid('_');
        list($extension, $content) = explode(';', $image['path']);
        $tmpExtension = explode('/', $extension);
        $img = Im::make($image['path']);
        if($img->width() < 350 && $img->height() < 350)
        {
            // return false;
        }

        //----image medium-------------
        // if($img->width() > 800 && $img->height() > 800)
        // {
        //     $fileName = $nameImg."_medium.jpeg";
        //     $data['medium'] = $this->imagePro($image['path'],$fileName,800,80,config('constants.image_type.medium'),$image);
        // }

        $widthTN = $img->width();
        $heightTN = $img->height();

        //----image original----------
        if($img->width() <= 2000 && $img->height() <= 2000)
        {
            $width = round($img->width() / 10);
            $height = round($img->height() / 10);
            $fitW = $width < $height ? $height : $width;
            // $image['have_medium'] =  isset($data['medium']) && $data['medium'] ? true : false;
            // $image['have_thumbnail'] =true;
            // $fileName = $nameImg.".jpeg";
            $fileName = $nameImg.".webp";
            // $data['images'] = $this->imagePro($image['path'],$fileName,0,$fitW,config('constants.image_type.root'),$image);
            // $data['images'] = $this->imagePro($img,$fileName,0,$fitW,config('constants.image_type.root'),$image);
            $result['images'] = $this->imageProNotWatermark($img,$fileName,0,config('constants.image_type.root'),$image);
        }
        else
        {
            // $image['have_medium'] =  isset($data['medium']) && $data['medium'] ? true : false;
            // $image['have_thumbnail'] =true;
            // $fileName = $nameImg.".jpeg";
            $fileName = $nameImg.".webp";
            // $data['images'] = $this->imagePro($image['path'],$fileName,2000,200,config('constants.image_type.root'),$image);
            // $data['images'] = $this->imagePro($img,$fileName,2000,200,config('constants.image_type.root'),$image);
            $result['images'] = $this->imageProNotWatermark($img,$fileName,2000,config('constants.image_type.root'),$image);
        }

        //----image thumbnail-------------
        // if($widthTN >= 350 && $heightTN >= 350)
        // if($img->width() >= 500 && $img->height() >= 500)
        // {
        //     // $fileName = $nameImg."_thumbnail.jpeg";
        //     $fileName = $nameImg."_thumbnail.webp";
        //     // $data['thumbnail'] = $this->imageProNotWatermark($image['path'],$fileName,350,config('constants.image_type.thumbnail'),$image);
        //     $this->imagePro($img,$fileName,350,35,config('constants.image_type.thumbnail'),$result['images']);

        // }

        return $result;
    }




    // -----process insert thumbnail-------------

    public function imageProNotWatermark($path, $name, $fit, $image_type, $image)
    {
        // $img = Im::make($path);
        $img = $path;
        $width = $img->width();
        $height = $img->height();
        if($fit != 0)
        {
            if ($width > $height) {
                $targetWidth = $fit;
                $targetHeight = $height / ($width / $fit);
                if ($targetHeight > $fit) {
                    $targetHeight = $fit;
                    $targetWidth = $width / ($height / $fit);
                }
            } else {
                $targetHeight = $fit;
                $targetWidth = $width / ($height / $fit);
                if ($targetWidth > $fit) {
                    $targetWidth = $fit;
                    $targetHeight = $height / ($width / $fit);
                }
            }

            if((int)$targetWidth < 500)
            {
                $size = 500 - (int)$targetWidth;
                $difference = (int)$targetHeight / (int)$targetWidth;
                $targetWidth = 500;
                $targetHeight = (int)($targetHeight + ($size * $difference));

            }elseif ((int)$targetHeight < 500) {
                $size = 500 - (int)$targetHeight;
                $difference = (int)$targetWidth / (int)$targetHeight;
                $targetHeight = 500;
                $targetWidth = (int)($targetWidth + ($size * $difference));
            }

            $img->fit((int)$targetWidth, (int)$targetHeight);
        }
        $img->save(public_path($name));
        $new_image = base64_encode($img);


        // if($image_type == config('constants.image_type.root'))
        // {
            // $image['image_type'] = $image_type;
        $result = $this->insert($image, $new_image, $name);
        // }
        // else
        // {
        //     $result = $this->insertThumbnail($image, $new_image, $name);
        // }

        unlink(public_path($name));

        return $result;

    }

    //----------process detail image-------------
    public function detail($parent_id, $object_type)
    {
        // $check = ($object_type == 1)? Property::find($parent_id): Place::find($parent_id);
        $check = isset($object_type) && $object_type ? $object_type : config('constants.properties.property_image');

        $result = Image::where([
            ['parent_id', $parent_id],
            ['object_type', $object_type]
        ])
        ->get();
        return $result;
    }


    //-----process update image-------------
    public function update(array $image)
    {

        $result = Image::where([
            // ['image_type', config('constants.image_type.root')],
            ['id',$image['id']]
            ])->first();

        if(!$result)
        {
            return false;
        }

        //-------------update avatar-----------------------
        $result->update($image);

        //--------add into log---------------
        $general = new GeneralService($this->_userId);
        $general->addLog(Config::get('constants.log_action.update', 2),$result->id,Config::get('constants.object_type.image', 11),$result, json_encode($image));
        $result->update($image);

        // if($result['object_type'] == config('constants.properties.property_image')  && isset($image['is_profile_picture']) && $image['is_profile_picture'] == true)
        if($result['object_type'] == config('constants.properties.property_image')  && isset($image['index']) && $image['index'] == 0)
        {

            // $path = str_replace(".jpeg", "_thumbnail.jpeg", $result->path);
            // $avatar = Image::where('path',$path)->first();
            // if($avatar)
            // {
            //     Property::where('id',$result['parent_id'])->update(['profile_picture' => $avatar->id]);
            //     // $avatar->update(['is_profile_picture' => true]);
            //     $avatar->update(['index' => 0]);
            // }
            // else {
                Property::where('id',$result['parent_id'])->update(['profile_picture' => $result->id]);
            // }

        }
        // if($result['object_type'] == config('constants.properties.broker_handbook')  && isset($image['index']) && $image['index'] == 0)
        // {
        //     BrokerHandbook::where('id',$result['parent_id'])->update(['profile_picture' => $result->id]);
        // }

        // if($result['object_type'] == config('constants.properties.advertisment_image')  && isset($image['index']) && $image['index'] == 0)
        // {
        //     Advertisment::where('id',$result['parent_id'])->update(['avatar_picture' => $result->id]);
        // }

        return $result;
    }

    //------------tmp---------------
    public function addBase64Tmp(array $image)
    {
        $log = $image;
        list($extension, $content) = explode(';', $image['path']);
        $tmpExtension = explode('/', $extension);
        $fileName = date('YmdHis').uniqid('_'). ".jpeg";
        $content = explode(',', $content)[1];
        $url = env('URL_IMAGE');
        $file = base64_decode($content);
        $date = date('Ymd');
        switch ($image['object_type']) {
            case '1':
                S3Utils::upload($file, 'uploads/property/' .$date."/".$image['parent_id'].'/' . $fileName);
                $image['path'] =$url.'uploads/property/'.$date."/".$image['parent_id'].'/'.$fileName;
                break;

            case '2':
                S3Utils::upload($file, 'uploads/place/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'uploads/place/'.$image['parent_id'].'/'.$fileName;
                break;
            case '3':
                S3Utils::upload($file, 'uploads/user/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'uploads/user/'.$image['parent_id'].'/'.$fileName;
                break;
            case '4':
                S3Utils::upload($file, 'uploads/category/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'uploads/category/'.$image['parent_id'].'/'.$fileName;
                break;
            case '5':
                S3Utils::upload($file, 'uploads/post/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'uploads/post/'.$image['parent_id'].'/'.$fileName;
                break;
            default:
                S3Utils::upload($file, 'uploads/tmp/' .$image['parent_id'].'/'. $fileName);
                $image['path'] =$url.'uploads/tmp/'.$image['parent_id'].'/'.$fileName;
                break;

        }

        $result = Image::create($image);

        $general = new GeneralService($this->_userId);
        $general->addLog(Config::get('constants.log_action.create', 1),$result->id,Config::get('constants.object_type.image', 11),$result, json_encode($log));

        return $result;
    }
    public function rotateImage(array $image)
    {
        $s3Path = env('MINIO_ENDPOINT');
        $path = str_replace($s3Path, "", $image['path']);
        $image['id'] = Image::where('path', $path)->select('id')->first();
        $source = imagecreatefromstring(file_get_contents($image['path']));
        $rotate = imagerotate($source, $image['angle'], 0);
        ob_start();
        imagejpeg($rotate,NULL,100);
        $contents = ob_get_contents();
        ob_end_clean();


        $image['path'] = "data:image/jpeg;base64," . base64_encode($contents);
        $image['update'] = true;
        $result = $this->addBase64($image);
        return $result;
    }
    public function updateAfterRotate(array $image)
    {

        $result = Image::find($image['id']);
        // die($result);
        // exit;
        if(!$result)
        {
            return false;
        }
        if(str_contains($image['path'],'_medium.jpeg'))
        {
            $mediumPath = str_replace(".jpeg", "_medium.jpeg", $result[0]->path);
            $medium = Image::where('path', $mediumPath)->first();
            if($medium)
            {
                $dataMedium = [
                    'id' => $medium->id,
                    'parent_id' => $medium->parent_id,
                    'path' => $image['path']
                ];
                $this->update($dataMedium);
            }
        }
        if (str_contains($image['path'],'_thumbnail.jpeg'))
        {
            $thumbnailPath = str_replace(".jpeg", "_thumbnail.jpeg", $result[0]->path);
            $thumbnail = Image::where('path', $thumbnailPath)->first();
            if($thumbnail)
            {
                $dataThumbnail = [
                    'id' => $thumbnail->id,
                    'parent_id' => $thumbnail->parent_id,
                    'path' => $image['path']
                ];
                $this->update($dataThumbnail);
            }
        }
        else {
            $dataRoot = [
                'id' => $result[0]->id,
                'parent_id' => $result[0]->parent_id,
                'path' => $image['path']
            ];
            $this->update($dataRoot);
        }
        return true;
    }
    public function imageFlip ( $imgsrc, $mode )
    {

        $width                        =    imagesx ( $imgsrc );
        $height                       =    imagesy ( $imgsrc );
        $src_x                        =    0;
        $src_y                        =    0;
        $src_width                    =    $width;
        $src_height                   =    $height;

        switch ( $mode )
        {
            case '1': //vertical
                $src_y                =    $height -1;
                $src_height           =    -$height;
            break;

            case '2': //horizontal
                $src_x                =    $width -1;
                $src_width            =    -$width;
            break;

            case '3': //both
                $src_x                =    $width -1;
                $src_y                =    $height -1;
                $src_width            =    -$width;
                $src_height           =    -$height;
            break;

            default:
                return $imgsrc;
        }

        $imgdest = imagecreatetruecolor ( $width, $height );

        if ( imagecopyresampled ( $imgdest, $imgsrc, 0, 0, $src_x, $src_y , $width, $height, $src_width, $src_height ) )
        {
            return $imgdest;
        }
        return $imgsrc;

    }


    public function problemReportInsert(array $image)
    {
        $result = $this->addBase64($image);

        return $result;
    }

    //-----update avatar property----------------
    public function updateData()
    {
       $updateAvatarProperty = new UpdateAvatarProperty();
       dispatch($updateAvatarProperty);

       return true;
    }


    //------------update thumbnail-----------------
    public function updateThumbnail()
    {
        $domain = env('MINIO_ENDPOINT');
        $result = new updateThumbnail($domain);
        dispatch($result);
        return [];

    }

    public function orientation($image)
    {
        if(isset($image['orientation']) && $image['orientation'] != 1 && $image['orientation'] != 0) {
            // $img = imagecreatefromjpeg($image['path']);
            $exploded = explode(',', $image['path'], 2);
            $encoded = $exploded[1];
            $imgBase64 = base64_decode($encoded);
            $img = imagecreatefromstring($imgBase64);
            $deg = 0;
            switch ($image['orientation']) {
                case 2:
                    $img = $this->imageFlip($img,2);
                    break;
                case 3:
                    $deg = 180;
                    break;
                case 4:
                    $img = $this->imageFlip($img,2);
                    $deg = 180;
                    break;
                case 5:
                    $deg = 270;
                    $img = $this->imageFlip($img,1);
                    break;
                case 6:
                    $deg = 270;
                    break;
                case 7:
                    $deg = 90;
                    $img = $this->imageFlip($img,1);
                    break;
                case 8:
                    $deg = 90;
                    break;
            }
            if ($deg != 0) {
                $img = imagerotate($img, $deg, 0);
            }
            ob_start();
            imagejpeg($img,NULL,100);
            $contents = ob_get_contents();
            ob_end_clean();

            $image['path'] = "data:image/jpeg;base64," . base64_encode($contents);

        }
        return $image['path'];
    }

    public function ggVisionRead($img)
    {
        try {

            $image = base64_encode((file_get_contents(public_path($img))));

            unlink(public_path($img));

            $request = new AnnotateImageRequest();
            $request->setImage($image);
            $request->setFeature("TEXT_DETECTION");

            $gcvRequest = new GoogleCloudVision([$request], env('GOOGLE_CLOUD_KEY'));
            $response = $gcvRequest->annotate();
            if(isset($response->responses[0]->textAnnotations[0]))
            {
                return $text = $response->responses[0]->textAnnotations[0]->description;
            }
            else{
                $request->setFeature("DOCUMENT_TEXT_DETECTION");
                $gcvRequest = new GoogleCloudVision([$request], env('GOOGLE_CLOUD_KEY'));
                $response = $gcvRequest->annotate();
            }
        }catch (Exception $e) {

        }
        return $response;
    }
}
