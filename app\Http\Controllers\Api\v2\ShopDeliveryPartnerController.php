<?php

namespace App\Http\Controllers\Api\v2;

use App\DeliveryPartner;
use App\Http\Controllers\Controller;
use App\Http\Requests\ShopDeliveryPartner\AddDeliveryPartnerRequest;
use App\Http\Requests\ShopDeliveryPartner\ShopDeliveryPartnerRequest;
use App\Services\DeliveryPartner\DeliveryPartnerService;
use App\Services\GeneralService;
use App\Services\ShopDeliveryPartner\ShopDeliveryPartnerService;
use App\Shop;
use App\ShopDeliveryPartner;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShopDeliveryPartnerController extends Controller
{
    function addDeliveryPartner(AddDeliveryPartnerRequest $request)
    {
        // Get shop_id and partners from request
        $data = $request->only('shop_id', 'partner', 'connect_data');

        // Get authenticated user ID
        $userId = Auth::user()->id;

        // Check if user is authorized to add delivery partner for this shop
        if (!GeneralService::checkShopOwner($userId, $data['shop_id']) && !GeneralService::checkAgentShopRelate($userId, $data['shop_id'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body' => [
                    'message' => 'You are not authorized to add delivery partner'
                ],
            ]);
        }

        $service = new DeliveryPartnerService();
        $service = $service ->setDeliveryPartner($data['partner'], $data['shop_id']);
        // Create shop delivery partner records for each partner
        $result = $service->connectDeliveryPartner($data['connect_data'] ?? null);
        // Return success response

        if(isset($result['error'])){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'   => [
                    "message" => $result['error']
                ]
            ];
        }
        else{
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ],
            ];
        }

        return response()->json($response);
    }

    function listDeliveryPartner(Request $request)
    {
        $shopId = $request->shop_id;
        $shop = Shop::find($shopId);
        if (! $shop) {
            return response()->json([
                'status' => JsonResponse::HTTP_NOT_FOUND,
                'body' => [
                    'message' => 'Shop not found'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $partners = DeliveryPartner::with(['shopDeliveryPartners' => function ($query) use ($shopId) {
            $query->where('shop_id', $shopId);
        }])->where('is_active', true)->select('id', 'name', 'is_active', 'information')->get();;

        $partners = $partners->map(function ($partner) use ($shopId) {
            $filteredPartner = $partner->shopDeliveryPartners->firstWhere('shop_id', $shopId);

            if ($filteredPartner) {
                $partner->is_connected = true;
                $partner->is_enabled = $filteredPartner->is_enabled;
                $partner->is_default = $filteredPartner->is_default;
            } else {
                $partner->is_connected = false;
            }

            // Loại bỏ quan hệ gốc để trả về dữ liệu gọn hơn
            unset($partner->shopDeliveryPartners);

            return $partner;
        });
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'partners' => $partners
            ]
        ], JsonResponse::HTTP_OK);
    }
    public function update(ShopDeliveryPartnerRequest $request)
    {
        // Get shop_id and partners from request
        $data = $request->only('shop_id', 'delivery_partner_id', 'is_enabled', 'connect_data', 'is_default');

        // Get authenticated user ID
        $userId = Auth::user()->id;

        // Check if user is authorized to add delivery partner for this shop
        if (! GeneralService::checkShopOwner($userId, $data['shop_id']) && !GeneralService::checkAgentShopRelate($userId, $data['shop_id'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body' => [
                    'message' => 'You are not authorized to add delivery partner'
                ],
            ]);
        }

        $dataUpdate = [];
        if(isset($data['is_enabled'])) $dataUpdate['is_enabled'] = $data['is_enabled'];
        if(isset($data['connect_data'])) $dataUpdate['connect_data'] = $data['connect_data'];
        if(isset($data['is_default'])) $dataUpdate['is_default'] = $data['is_default'];

        $service = new ShopDeliveryPartnerService($data['shop_id'], $data['delivery_partner_id']);
        $result = $service->update($dataUpdate);

        if(!$result){
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'   => [
                    'message' => 'Failed to update delivery partner'
                ]
            ], JsonResponse::HTTP_OK);
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ], JsonResponse::HTTP_OK);
    }

    public function detail(ShopDeliveryPartnerRequest $request)
    {
        $data = $request->only('shop_id', 'delivery_partner_id');
        $userId = Auth::user()->id;
        if (! GeneralService::checkShopOwner($userId, $data['shop_id']) && !GeneralService::checkAgentShopRelate($userId, $data['shop_id'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body'   => [
                    'message' => 'Not authorized'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $result = ShopDeliveryPartner::on('pgsqlReplica')
            ->with('deliveryPartner')
            ->where([
                'shop_id' => $data['shop_id'],
                'delivery_partner_id' => $data['delivery_partner_id']])
            ->first();

        if($result){
            $result = $result->makeVisible("connect_data");
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => [
                'data' => $result
            ]
        ], JsonResponse::HTTP_OK);
    }
}
