<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\User;
use App\Product;
use App\Shop;
use App\Category;
use App\ProductCategory;
use App\Image;
use App\Translation;
use App\Log as LogTable;
use App\Helpers\S3Utils;
use Intervention\Image\ImageManagerStatic as Im;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Imports\ProductImport;
use App\Services\GeneralService;
use App\Services\Product\ProductService;
use App\Helpers\Helper;
use Intervention\Image\Facades\Image as ImageLib;

class ProductImportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:importExcel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'importExcel';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // $t=time();
        // echo($t . "<br>");
        // echo(date("Y-m-d H:i:s",$t));die;
        // $this->generateShops();

        // $this->downloadImagesWithInvalidBanners();

        // $this->importCategories();
        $this->importFromKiotViet(true);
        // sleep(10);
        // $this->importFromKiotViet(false);
        // $this->readExcel();



        

        // Delete the temporary file
        // unlink($tempPath);


    }
    public function downloadImagesWithInvalidBanners()
    {
        // Get shops with banners not matching UUID format
        $shops = Shop::whereRaw("banner !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'")
        ->whereRaw("logo is null")
        // ->whereRaw("banner NOT LIKE '%chưa có tên%'")
        ->orderBy('name','asc')->get();

        // Download images for each shop
        foreach ($shops as $shop) {
            $fileName = 'shop_' . $shop->slug . '_' . time() . '.jpg';
            // file_put_contents(public_path('images/' . $filename), $imageContents);
            $filePath = env('S3_HOST_PATH').$shop->banner;
            $image = [];
            $image['parent_id'] = $shop->id;
            $image['object_type'] = 3;
            $image['title'] = $shop->name;
            $image['path'] = 'shop/'.$image['parent_id'].'/'.$fileName;
            $image['path_thumbnail'] = $image['path'];
            $profile_picture = $image['path'];
            $uploaded = $this->resizeAndUpload($filePath, $profile_picture);
            $result = Image::create($image);
            // dd($result);
            $shop = Shop::find($shop->id);
            $shop['logo'] = $result['id'];
            $shop->save();
        }

        return response()->json(['message' => 'Images downloaded successfully']);
    }
    
    public function readExcel()
    {
        $generalService = new GeneralService();
        $generalService->readExcel(public_path('moon_milk.xlsx'), 5, 6, 'A', 'M');
        // $generalService->readExcel(public_path('new_viet.xlsx'), 10, 12, 'A', 'M');
    }

    public function importCategories()
    {
        $data = Excel::toArray(new ProductImport, public_path('import/remagan_categories.xlsx'));
    
        // Loop through each row of data in the Excel file
        $i = 0;
        $category = new Category();
        $categoryArr = [];
        $parent_id = null;
        $parent_slug = null;
        foreach ($data[0] as $row) {
            // var_dump($row);die;
            $i++;
            if(isset($row['name']) && !empty($row['name'])){
                if(isset($row['is_parent']) && !empty($row['is_parent']) && $row['is_parent'] == 1){
                    $parent_id = Str::uuid();
                    $parent_slug = Str::slug($row['name']);
                    $categoryObj = [ 
                        "id" => $parent_id,
                        "name" => $row['name'],
                        "parent_id" => null,
                        "enable" => true,
                        "slug" => Str::slug($row['name']),
                        "profile_picture" => null,
                        "shop_id" => '9fbe5e90-b443-45d7-934a-b9daf3727a98',
                        "notes" => 'system'
                        
                    ];
                    $categoryArr[] = $categoryObj;

                }elseif($row['is_parent'] == 0){
                    $categoryObj = [ 
                        "id" => Str::uuid(),
                        "name" => $row['name'],
                        "parent_id" => $parent_id,
                        "enable" => true,
                        "slug" => Str::slug($row['name']),
                        "profile_picture" => null,
                        "shop_id" => '9fbe5e90-b443-45d7-934a-b9daf3727a98',
                        "notes" => 'system'
                        
                    ];
                    $categoryArr[] = $categoryObj;
                }


            }
        }
        // echo json_encode($categoryArr);die
        $category->insert($categoryArr);
        echo "Done";
    }


    public function import()
    {
        // $file = file_get_contents(public_path('azmall_items.xlsx')); // get the uploaded file
    
        // Read the contents of the Excel file using the Maatwebsite Excel package
        // $data = Excel::load($file)->get();
        // $data = Excel::toArray(new ProductImport, public_path('hot/30kitems/30000itemsexcel.xlsx'));
        // $data = Excel::toArray(new ProductImport, public_path('DanhSachSanPham_KV10102023-165206-342.xlsx'));
        $data = Excel::toArray(new ProductImport, public_path('import/part-28-sanpham.xlsx'));
    
        // Loop through each row of data in the Excel file
        $i = 0;
        $product = new Product();
        $productArr = [];
        foreach ($data[0] as $row) {
            // var_dump($row);die;
            $i++;
            if(isset($row['name']) && !empty($row['name'])){
                $latLong = $this->generateLatLong();
                $profile_picture = "product/".date("Y")."/".date("m")."/azmon/".preg_replace('/_v=.+/', '', $row['images']);
                $productObj = [ 
                    "id" => Str::uuid(),
                    "name" => $row['name'],
                    "unaccent_name" => Helper::unaccentVietnamese($row['name']),
                    "lowercase_name" => Str::lower($row['name']),
                    "price" => floatval(str_replace(["₫",".",","," ","\""],["","","","",""],$row['price'])),
                    "profile_picture" => $profile_picture,
                    "type" => 1,
                    "shop_id" => '9fbe5e90-b443-45d7-934a-b9daf3727a98',
                    "latitude" => $latLong['latitude'],
                    "longitude" => $latLong['longitude']
                ];

                $filePath = public_path('import/part-28-images/').$row['images'];
                if($row['images'] && file_exists($filePath)){
                    // $fileContent = file_get_contents($filePath);
                    
                    // $uploaded = S3Utils::upload($fileContent, $profile_picture);
                    $uploaded = $this->resizeAndUpload($filePath, $profile_picture);
                    if(!$uploaded){
                        Log::info("Upload fail: ".$profile_picture);
                        continue;
                    }else{
                        Log::info("Upload success: ".$profile_picture);
                        $productArr[] = $productObj;
                    }
                }else{
                    // $productArr[] = $productObj;
                    Log::info("File not found: ".$profile_picture);
                    // continue;
                }
                // $product->shop_id = '';
                
                // Save the product to the database
                // Log::info($product);
                //code...
                if($i%100 == 0 || $i == count($data[0])){
                    echo $i."\n";
                    // DB::beginTransaction();
                    // try {
                        $product->insert($productArr);
                        $productArr = [];
                        sleep(15);
                        if($i%1000 == 0){
                            sleep(30);

                        }
                        // DB::commit();
                    // } catch (\Throwable $th) {
                        
                        // DB::rollback();
                    // }
                }
            }
            // if($i == 10) break;
        }
    
        return 'Data imported successfully.';
    
    }    


    public function resizeAndUpload($filePath, $profile_picture) {
        echo "Upload: ".$profile_picture."\n";
        // Load the image
        $filePath = str_replace(' ', '%20', $filePath);
        // $baseUrl = "https://s3.remagan.com/pro.remagan.uploads/shop/";
        // $path = substr($filePath, strlen($baseUrl));

        // // Encode the path part of the URL
        // $encodedPath = rawurlencode($path);

        // // Combine the base URL and the encoded path
        // $filePath = $baseUrl . $encodedPath;

        $image = ImageLib::make($filePath);
        // Check file size
        // $fileSize = filesize($filePath); // Size in bytes
        // $maxFileSize = 1 * 1024 * 1024; // 1MB in bytes

        // if ($fileSize > $maxFileSize) {}

        // Resize if width or height exceeds 2000px
        if ($image->width() > 2000 || $image->height() > 2000) {
            echo "Resize: ".$profile_picture."\n";
            $image->resize(2000, 2000, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }
        // $profile_picture = "testResize.jpg";
        // Save the resized image to a temporary location
        $tempPath = storage_path('app/temp/' . "tempImg.jpg");
        $image->save($tempPath);

        // Upload the resized image
        $fileContent = file_get_contents($tempPath);
        unlink($tempPath);
        return $uploaded = S3Utils::upload($fileContent, $profile_picture);

    }



    public function importCustom()
    {
        // $file = file_get_contents(public_path('azmall_items.xlsx')); // get the uploaded file
    
        // Read the contents of the Excel file using the Maatwebsite Excel package
        // $data = Excel::load($file)->get();
        // $data = Excel::toArray(new ProductImport, public_path('hot/30kitems/30000itemsexcel.xlsx'));
        $data = Excel::toArray(new ProductImport, public_path('mon_an.xlsx'));
    
        // Loop through each row of data in the Excel file
        $i = 0;
        $product = new Product();
        $productArr = [];
        foreach ($data[0] as $row) {
            // var_dump($row);die;
            $i++;
            if(isset($row['name']) && !empty($row['name'])){
                $latLong = $this->generateLatLong();
                $profile_picture = "";
                $productObj = [ 
                "id" => Str::uuid(),
                "name" => $row['name'],
                "price" => floatval(str_replace(["₫",".",","," ","\""],["","","","",""],$row['price'])),
                "profile_picture" => $profile_picture,
                "type" => 1,
                "shop_id" => '9fbe5e90-b443-45d7-934a-b9daf3727a98',
                "latitude" => $latLong['latitude'],
                "longitude" => $latLong['longitude']];
                $productArr[] = $productObj; 
                ProductCategory::firstOrCreate(['product_id' => $productObj['id'], 'category_id' => '456c7cc9-6d7e-44b9-9636-e68b572d7868']);
                // $product->shop_id = '';
                
                // Save the product to the database
                // Log::info($product);
                //code...
                if($i%10 == 0 || $i == count($data[0])){
                    echo $i."\n";
                    // DB::beginTransaction();
                    // try {
                        $product->insert($productArr);
                        $productArr = [];
                        sleep(1);
                        // DB::commit();
                    // } catch (\Throwable $th) {
                        
                        // DB::rollback();
                    // }
                }
            }
            // if($i == 10) break;
        }
    
        return 'Data imported successfully.';
    
    }
    public function importFromKiotViet($isParent = true)
    {
        // $file = file_get_contents(public_path('azmall_items.xlsx')); // get the uploaded file
    
        // Read the contents of the Excel file using the Maatwebsite Excel package
        // $data = Excel::load($file)->get();
        // $data = Excel::toArray(new ProductImport, public_path('hot/30kitems/30000itemsexcel.xlsx'));
        $data = Excel::toArray(new ProductImport, public_path('import/kiotviet01.xlsx'));
        $shopId = '61a7e997-aed6-4e99-a14b-2c4a1442a773';
        $shopId = '41599cf2-f588-4603-907f-7f1bfdeb0058';//Viet Dev
        $shopId = '353f4377-249e-4b85-9023-484333845daa';//Giang A Pao
        $shopId = '8b32e608-c5bb-47de-89be-33531af8010c';//tester01
        $shopCategories = Category::where('shop_id', $shopId)->get();
        // var_dump($shopCategories);die;

        // Loop through each row of data in the Excel file
        $i = 0;
        $product = new Product();
        $productArr = [];
        foreach ($data[0] as $row) {
            echo $i++;
            if(isset($row['ten_hang_hoa']) && !empty($row['ten_hang_hoa'])){
                $profile_picture = "";
                $parentId = NULL;
                $isMain = 1;
                if(!$isParent){//is child
                    if(empty($row['ma_hh_lien_quan'])){
                        continue;
                    }else{
                        $parentId = Product::where('extra_id', str_replace([" "], [""], $row['ma_hh_lien_quan']))->where('shop_id', $shopId)->first()->id;
                        // $isMain = 0;
                    }

                }else{//is parent
                    if(!empty($row['ma_hh_lien_quan'])){
                        continue;
                    }
                }
                // $latLong = $this->generateLatLong();
                $productObj = [ 
                    "id" => Str::uuid(),
                    "name" => $row['ten_hang_hoa'],
                    "price" => floatval(str_replace(["₫",".",","," ","\""],["","","","",""],$row['gia_ban'])),
                    "profile_picture" => "",
                    "notes" => $row['thuoc_tinh'],
                    "parent_id" => $parentId,
                    "type" => config('constants.product.type.shop'),
                    "shop_id" => $shopId,
                    "extra_id" => str_replace([" "], [""], $row['ma_hang']),
                    "is_main" => $isMain,
                    "lowercase_name" => Helper::unaccentVietnamese($row['ten_hang_hoa']),
                    "unaccent_name" => Str::slug($row['ten_hang_hoa'],' '),
                    "latitude" => "12.273283",
                    "longitude" => "109.202859",
                    "created_at" => date("Y-m-d H:i:s",time()),
                    "updated_at" => date("Y-m-d H:i:s",time())
                ];
                Translation::updateOrCreate(
                    [
                        'object_type' => config('constants.object_type.product'),
                        'object_id' => $productObj['id'],
                        'language_code' => 'vi'
                    ],
                    [
                        'name' => $row['ten_hang_hoa'],
                        'description' => $row['thuoc_tinh']
                    ]
                );
                Log::info($row['ten_hang_hoa']);   
                if(isset($row['loai_thuc_don']) && !empty($row['loai_thuc_don']) && isset($row['nhom_hang3_cap']) && !empty($row['nhom_hang3_cap'])){
                    $cat1 = $this->checkExistsCategory($shopCategories, $row['loai_thuc_don']);
                    $cat2 = $this->checkExistsCategory($shopCategories, $row['nhom_hang3_cap']);
                    if($cat1){
                        ProductCategory::firstOrCreate(['product_id' => $productObj['id'], 'category_id' => $cat1]);
                    }else{
                        $newCat1 = Category::create([
                            'name' => $row['loai_thuc_don'],
                            'enable' => 1,
                            'slug' => Str::slug($row['loai_thuc_don']),
                            'parent_id' => NULL,
                            'shop_id' => $shopId
                        ]);
                        if($newCat1){
                            $cat1 = $newCat1->id;
                            $shopCategories[] = $newCat1;
                            ProductCategory::firstOrCreate(['product_id' => $productObj['id'], 'category_id' => $cat1]);
                        }
                    }
                    if($cat2){
                        ProductCategory::firstOrCreate(['product_id' => $productObj['id'], 'category_id' => $cat2]);
                    }else{
                        $newCat2 = Category::create([
                            'name' => $row['nhom_hang3_cap'],
                            'enable' => 1,
                            'slug' => Str::slug($row['nhom_hang3_cap']),
                            'parent_id' => $cat1,
                            'shop_id' => $shopId
                        ]);
                        if($newCat2){
                            $cat2 = $newCat2->id;
                            $shopCategories[] = $newCat2;
                            ProductCategory::firstOrCreate(['product_id' => $productObj['id'], 'category_id' => $cat2]);
                        }
                    }
                }


                $productArr[] = $productObj;
                    
                // $product->shop_id = '';
                
                // Save the product to the database
                // Log::info($product);
                //code...
                if($i%10 == 0 || $i == count($data[0])){
                    echo $i."\n";
                    // DB::beginTransaction();
                    // try {
                        $product->insert($productArr);
                        $productArr = [];
                        // sleep(3);
                        // DB::commit();
                    // } catch (\Throwable $th) {
                        
                        // DB::rollback();
                    // }
                }
            }
            // if($i == 10) break;
        }
    
        return 'Data imported successfully.';
    
    }
    
    public function checkExistsCategory($categories, $catName){
        foreach ($categories as $key => $value) {
            $normalizedString1 = strtolower(preg_replace('/\s+/', '', $value['name'] ));
            $normalizedString2 = strtolower(preg_replace('/\s+/', '', $catName));
            if($normalizedString1 == $normalizedString2) return $value['id'];
        }
        return false;
    }

    public function generateLatLong()
    {
        // Define the boundaries of Vietnam
        $minLat = 8.559557;
        $maxLat = 23.324255;
        $minLong = 102.144749;
        $maxLong = 109.469787;
        // Define the boundaries of Khanh Hoa
        $minLat = 11.820645045397452;
        $maxLat = 12.821509612836735;
        $minLong = 108.60534667968751;
        $maxLong = 109.38537597656251;

        // Generate a random latitude within the boundaries
        $latitude = $minLat + mt_rand() / mt_getrandmax() * ($maxLat - $minLat);

        // Generate a random longitude within the boundaries
        $longitude = $minLong + mt_rand() / mt_getrandmax() * ($maxLong - $minLong);

        // Output the results
        // echo "Latitude: $latitude \nLongitude: $longitude";
        return [
            "latitude" => $latitude,
            "longitude" => $longitude
        ];

    }

    public function generateShops(){
        for ($i=10000; $i <= 10100; $i++) { 
            
            $shop = new Shop();
            $shop->banner = "shop/shop_image (" . rand(1,17) .  ").jpg";
            $shop->name   = "Shop " . $i;
            $shop->slug   = "sample_shop_" . $i;
            $shop->province_id   = rand(1,96);
            $shop->user_id   = "8f54de4d-f9dc-4a6d-83d0-943c9defc23e";
            $latLong = $this->generateLatLong();
            $shop->latitude = $latLong['latitude'];
            $shop->longitude = $latLong['longitude'];
            $shop->save();
            $products = Product::where("type", 1)->offset(rand(0,9990))->limit(10)->get()->toArray();
            $productService = new ProductService();
            $productService->clone($products, $shop);
        }
    }
    

}
