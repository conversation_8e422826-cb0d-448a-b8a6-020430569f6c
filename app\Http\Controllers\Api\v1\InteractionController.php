<?php

namespace App\Http\Controllers\Api\v1;

use App\File;
use App\Http\Controllers\Controller;
use App\Product;
use App\Shop;
use Illuminate\Http\Request;
use App\Services\Interaction\InteractionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\User;

class InteractionController extends Controller
{
    protected $interactionService;

    public function __construct(InteractionService $interactionService)
    {
        $this->interactionService = $interactionService;
    }

    public function create_or_delete(Request $request)
    {
        try {
            $data = $request->validate([
                'object_type_a' => 'required|integer',
                'object_id_a' => 'required|uuid',
                'object_type_b' => 'required|integer',
                'object_id_b' => 'required|uuid',
                'interaction_type' => 'required|integer|between:1,5',
            ]);

            $result = $this->interactionService->createOrDelete($data);

            return response()->json([
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ]
            ], JsonResponse::HTTP_OK);
        } catch (ValidationException $e) {
            return response()->json([
                'status' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                'body' => [
                    'errors' => $e->errors()
                ]
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getInteractions(Request $request)
    {
        $data = $request->validate([
            'object_type' => 'required|integer',
            'object_id' => 'required|uuid',
            'interaction_type' => 'required|string|max:50',
        ]);

        $result = $this->interactionService->getInteractions($data);

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ], JsonResponse::HTTP_OK);
    }

    public function list(Request $request)
    {
        try {
            $result = [];
            $data = $request->validate([
                'shop_id' => 'uuid',
                'type' => 'integer',
            ]);
            $data['type'] = $data['type'] ?? 1;

            if(isset($data['shop_id'])){
                $result = $this->interactionService->getShopInteractions($data['shop_id'], $data['type']);
            }else{
                $result = $this->interactionService->getUserInteractions(Auth::user()->id, $data['type']);
            }

            return response()->json([
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ]
            ], JsonResponse::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json([
                'status' => JsonResponse::HTTP_INTERNAL_SERVER_ERROR,
                'body' => [
                    'error' => 'An error occurred while fetching user interactions.'
                ]
            ], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    // Kiểm tra thông tin tương tác giữa 2 đối tượng
    public function checkInteraction(Request $request)
    {
        try {

            $data = $request->validate([
                'object_id_a' => 'nullable|uuid',
                'object_id_b' => 'required|uuid',
                'interaction_type' => 'required|integer',
            ]);
            $objectIdA = $data['object_id_a'] ?? Auth::user()->id;
            $result = $this->interactionService->checkInteractionBetweenTwoObjects($objectIdA, $data['object_id_b'], $data['interaction_type']);

            return response()->json([
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ]
            ], JsonResponse::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json([
                'status' => JsonResponse::HTTP_INTERNAL_SERVER_ERROR,
                'body' => [
                    'error' => 'An error occurred while fetching user interactions.'
                ]
            ], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }


    function incrementView(Request $request){
        $data = $request->only(['object_id', 'object_type']);
        $object = null;

        switch ($data['object_type']){
            case 'shop':
                $object = Shop::find($data['object_id']);
                break;
            case 'product':
                $object = Product::find($data['object_id']);
                break;
            case 'video':
                $object = File::where(['id' => $data['object_id'], 'file_type' => $data['object_type']])->first();
                break;
        }

        if(!$object){
            return response()->json([
                'status' => JsonResponse::HTTP_NOT_FOUND,
                'message' => 'Object not found.'
            ], JsonResponse::HTTP_OK);
        }

        if($data['object_type'] == 'video'){
            $object->increment('view_total');
        }else $object->increment('views');

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'message' => 'Interaction updated successfully.'
        ], JsonResponse::HTTP_OK);
    }

}
