<?php
namespace App\Services\SinglePage;

use App\Services\Address\AddressClientService;
use App\PropertyType;
use App\Setting;
use App\SinglePage;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;

class SinglePageService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    // ------------process add singlePage-----------------
    public function add(array $singlePage)
    {
            $log = $singlePage;

            $result = SinglePage::create($singlePage);

            //--------add into log-----------
            $general = new GeneralService;
            $general->addLog(
                Config::get('constants.log_action.create',1),
                $result->id,
                Config::get('constants.object_type.estimate_option', 34),
                $result,
                json_encode($log));

            return $result;
    }

   //--------------process update singlePage---------------
   public function update(array $singlePage)
   {
        $result = SinglePage::find($singlePage['id']);

        if(!$result)
        {
            return false;
        }

        $result->update($singlePage);

        $general = new GeneralService;

        $general->addLog(
            Config::get('constants.log_action.update', 2),
            $result->id,
            Config::get('constants.object_type.single_page', 35),
            $result,
            json_encode($singlePage)
        );

        $result = $this->detail($result->id);
        return $result;
   }

   //--------------process delete singlePage---------------
   public function delete(array $singlePage)
    {

        $result = SinglePage::find($singlePage['id']);

        if(!$result){
            return false;
        }

        $result->delete();

        //------add into log--------------
        $general = new GeneralService($this->_userId);
        $general->addLog(
            Config::get('constants.log_action.delete', 3),
            $singlePage['id'],
            Config::get('constants.object_type.single_page', 35),
            $result,
            json_encode($singlePage));

        return $result;
    }

   //-------------process detail singlePage--------------

   public function detail($id)
   {
        $general = new GeneralService($this->_userId);
        $result = SinglePage::with('avatars')->find($id);

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
   }

   public function detailBySlug($slug)
   {
        
        $general = new GeneralService($this->_userId);
        $result = SinglePage::with('avatars')->where('slug',$slug)->first();
        $addressService = new AddressClientService;

        $property_type = null ;
        $type_request = null;
        $address = null;
        
        if(!$result)
        {
            if(strcmp($slug["slug"][0],"/") != 0){
                $params = explode("/", $slug["slug"]);    
            }
            else {
                $params = explode("/", substr($slug["slug"],1));    
            }
            
            // slug đầy đủ các thông số
            if(count($params) == 4){
                $type_param = explode("_", $params[count($params) - 1]);
    
                //Xác định loại bất động sản trong slug
                if(count($type_param)>1){
                    $property_type = PropertyType::where("slug",$type_param[count($type_param) - 1])->first();
                    if($property_type) {
                        $property_type = $property_type->name;
                    }
                    else {
                        $property_type = "Nhà đất";
                    }
                }
                else {
                    $property_type = "Nhà đất";
                }
    
                //Xác định loại request sản trong slug
                $type_request = ($type_param[0] == 'for-rent' || $type_param[0] == 'cho-thue') 
                    ? "Cho thuê"
                    : "Bán";
    
                //Xác định địa chỉ trong slug
                if(strcmp($params[count($params) - 2],'toan-quoc')==0){
                    $address = "toàn quốc";
                }
                else {
                    $address = $addressService->detailArea($params[count($params) - 2])->name;
                }
            }
    
            // slug chỉ có area
            else {
                $type_request = 'Mua Bán Cho thuê';
                $property_type = "Nhà đất";
                if(strcmp($params[count($params) - 1],'toan-quoc')==0){
                    $address = "toàn quốc";
                }
                else {
                    $address = $addressService->detailArea($params[count($params) - 1])->name;
                }
            }
            return $this->getDefaultDetail($type_request, $property_type, $address);
        }

        return $result;
   }

   public function getDefaultDetail($type_request, $property_type = null, $address = null){

        $property_type = $property_type ? $property_type : "";
        $address = $address ? $address : "";

        $defaultData = Setting::where('key','singlepage_default_data')->first();

        $data =  json_decode($defaultData->value);

        $data->keywords = str_replace("{property_type}", $property_type, $data->keywords);
        $data->keywords = str_replace("{type_request}", 
            str_replace("Cho,","Cho", str_replace(" ", ", ", $type_request)), 
            $data->keywords);
        $data->keywords = str_replace("{address}", $address, $data->keywords);

        $data->title = str_replace("{property_type}", $property_type, $data->title);
        $data->title = str_replace("{type_request}", $type_request, $data->title);
        $data->title = str_replace("{address}", $address, $data->title);

        $data->description = str_replace("{property_type}", $property_type, $data->description);
        $data->description = str_replace("{type_request}", $type_request, $data->description);
        $data->description = str_replace("{address}", $address, $data->description);

        $result = [
            "keywords" => $data->keywords,
            "title" => $data->title,
            "description" => $data->description,
            "content" => $data->description
        ];
        return $result;
        
   }


   // ------------process filter singlePage-----------------
   public function filter(array $request)
   {
        $offset = isset($request['offset'])?$request['offset']:0;
        $limit = isset($request['limit'])?$request['limit']:50;
        $result = SinglePage::query();

        if(isset($request['title']) && !empty($request['title']))
        {
            $result->where('title','ILIKE','%'.$request['title'].'%');
        }
        if(isset($request['content']) && !empty($request['content']))
        {
            $result->where('content','ILIKE','%'.$request['content'].'%');
        }
        if(isset($request['slug']) && !empty($request['slug']))
        {
            $result->where('slug',$request['slug']);
        }
        if(isset($request['keywords']) && !empty($request['keywords']))
        {
            $result->where('keywords','ILIKE','%'.$request['keywords'].'%');
        }

        $result->with(
            'avatars')
            ->orderBy('updated_at','desc');
        
        $count = $result->count();

        $data = [
            'count' => $count,
            'result' => $result->offset($offset)->limit($limit)->get()
        ];
        return $data;
   }


}
