<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Village\VillageUpdateRequest;
use App\Http\Requests\Village\VillageDeleteRequest;
use App\Http\Requests\Village\VillageRequest;
use App\Http\Requests\Village\VillageListRequest;
use App\Services\Village\VillageService;
use Illuminate\Http\JsonResponse;

class VillageController extends Controller
{
    //
    public function insert(VillageRequest $request,VillageService $service)
    {
        $data = (array)$request->only([
            'name',
            'ward_id',
            'longitude',
            'latitude',
            'postcode'
        ]);
        $result = $service->add($data);
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data'=> $result,
        ],
    ];
    return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    public function list(VillageListRequest $request,VillageService $service)
    {
        $data = (array)$request->only([
            'id',
            'offset',
            'limit',
        ]);
        $result = $service->list($data);
        if($result == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'data' => 'Village_003_E_010'
                ]
                ];
            return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }

    public function detail($id = null,VillageService $service)
    {
        $result = $service->detail($id);
        if($result == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'=> "Village_003_E_010",
              ];
              return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    public function update(VillageUpdateRequest $request,VillageService $service)
    {
        $data = (array)$request->only([
            'id',
            'name',
            'ward_id',
            'longitude',
            'latitude',
            'postcode'
        ]);
        $result = $service->update($data);
        if($result == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'=> "Village_003_E_010",
              ];
              return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    public function delete(VillageDeleteRequest $request,VillageService $service)
    {
        $data = (array)$request->only([
            'id'
        ]);
        $result = $service->delete($data);
        if($result == false){
            $reponse = [
              'status' => JsonResponse::HTTP_BAD_REQUEST,
              'body'=> "Village_003_E_010",
            ];
            return response()->json($reponse,JsonResponse::HTTP_OK);
          }

          $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
}
