<?php

namespace App\Services\DeliveryPartner;

use App\Order;
use App\ShopDeliveryPartner;

abstract class DeliveryPartner
{
    protected $shopId;
    protected $partner;

    protected $order;
    function __construct($shopId = null){}

    function getOrder($orderId)
    {
        if(!$this->order){
            $this->order = Order::find($orderId);
        }
        return $this->order;
    }
    function checkPrice($data){}

    // Tạo vận đơn
    function createDelivery($data, $orderId){}

    // Chi tiết vận đơn
    function detailDelivery($orderId){}

    // Huỷ vận đơn
    function cancelDelivery($data , $orderId){}

    // Kết nối đơn vị vận chuyển
    function connectDeliveryPartner($data){}

    function notification($data, $orderId){}

    protected function getShopDeliveryPartner()
    {
        return ShopDeliveryPartner::where([
            'shop_id' => $this->shopId,
            'delivery_partner_id' => $this->partner->id
        ])->first();
    }

    // Kiểm tra shop đã có kết nối tới đơn vị vận chuyển nào chưa?
    protected function hasConnectedToDeliveryPartner()
    {
        return ShopDeliveryPartner::where('shop_id', $this->shopId)->exists();
    }

    /**
     * Process delivery details and update order status accordingly.
     *
     * @param object $serviceDelivery
     * @param array $order
     * @param object $checkOrder
     * @return boolean
     */
    function validateDeliveryStatus($order)
    {
        // Get detail delivery
        $detailDelivery = $this->detailDelivery($order['id']);
        // Check detail delivery; if it contains a message, it indicates that retrieving the details failed.
        if (isset($detailDelivery['message'])) return true;
        $deliveryStatus = $detailDelivery['status'] ?? null;

        if ($deliveryStatus > config('constants.delivery.delivered')) return true;

        if ($deliveryStatus == config('constants.delivery.pending')) {
           $resultCancel = $this->cancelDelivery([], $order['id']);
           if ($resultCancel === true) {
               return true;
           }
        }
        return false;
    }
}
