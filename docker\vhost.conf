<VirtualHost *:80>
        ServerAdmin <EMAIL>
        DocumentRoot "/workspace/clomart-backend/public/"
        Header  set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "POST, PUT, GET, DELETE, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, timezone,Authorization,X-Device-Id"
        ServerName api.remagan.com
        ServerAlias api.remagan.com
        RewriteCond %{HTTP:X-Forwarded-Proto} !https
        RewriteCond %{HTTPS} off
        RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
        ErrorLog ${APACHE_LOG_DIR}/error.log
        CustomLog ${APACHE_LOG_DIR}/access.log combined
        <Directory "/workspace/clomart-backend/public/">
                Options Indexes FollowSymLinks
                AllowOverride All
                Require all granted
        </Directory>
</VirtualHost>
