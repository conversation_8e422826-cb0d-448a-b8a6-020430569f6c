<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddEnabledToPosConnectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pos_connections', function (Blueprint $table) {
            $table->boolean('enabled')->default(true);
            $table->string('branch_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pos_connections', function (Blueprint $table) {
            $table->dropColumn('enabled');
            $table->dropColumn('branch_id');
        });
    }
}
