<?php

namespace App\Http\Controllers\Api\v1\Client;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\Setting\SettingService;
use Illuminate\Http\JsonResponse;

class SettingClientController extends Controller
{
    public function list(SettingService $service)
    {
        $result = $service->list();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
