<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Services\Log\LogService;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function checkIdDetail($id)
    {   
        
        $min = 0;
        if(!is_numeric($id)  || is_null($id) || strlen(strval($id)) != strlen(strval((int)$id)) || (int)$id <= $min)
        {
            return false;
        }
        return true;
    }

    public function checkIdDelete($id)
    {
        $min = 0;
        if(!is_numeric($id)  || strlen(strval($id)) != strlen(strval((int)$id)) || (int)$id <= $min)
        {
            return false;
        }
        return true;
    }

    public function checkImage($exextension)
    {
        if(strcmp($exextension,"jpeg")  == 0 || strcmp($exextension,"png")  == 0 || strcmp($exextension,"bmp")  == 0 || strcmp($exextension,"gif")  == 0 || strcmp($exextension,"svg")  == 0 || strcmp($exextension,"webp")  == 0)
            return true;
        return false;
       
    }

    public function checkInt($num)
    {
        if(!is_numeric($num)  || is_null($num) || strlen(strval($num)) != strlen(strval((int)$num)))
        {
            return false;
        }
        return true;
    }

     
}
