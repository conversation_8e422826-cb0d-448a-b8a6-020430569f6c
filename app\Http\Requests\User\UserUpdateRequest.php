<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;
use Carbon\Carbon;

class UserUpdateRequest extends BaseRequest
{
    public function rules()
    {
        if(isset($this->id))
        {
            $id = 'required|uuid|exists:users,id';
            $email = 'required|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u|unique:users,email,'.$this->id;
            $user_name = 'bail|nullable|string|min:6|max:64|regex:/^([a-zA-Z0-9]+)(\_[a-zA-Z0-9]+)*$/u|unique:users,user_name,'.$this->id;
        }
        else{
            $id = 'required|uuid|exists:users,id';
            $email = 'required|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u|unique:users,email';
            $user_name = 'bail|nullable|string|min:6|max:64|regex:/^([a-zA-Z0-9]+)(\_[a-zA-Z0-9]+)*$/u|unique:users,user_name';
        }
        // $seventeen_years_prior_now = Carbon::now()->subYears(17);
        return [
            'id'                    => $id,
            'name'                  => 'required|max:255',
            'email'                 => $email,
            'gender'                => 'nullable|boolean',
            'phone'                 => 'nullable|digits_between:10,11',
            // 'date_of_birth'         => 'bail|nullable|date_format:Y-m-d|before:'.$seventeen_years_prior_now,
            'identity_card'         => 'nullable|regex:/^\d{9}(\d{3})?$/',
            'province_id'           => 'nullable|integer|exists:dvhc2021_tinh,id',
            'district_id'           => 'nullable|integer|exists:dvhc2021_huyen,id',
            'ward_id'               => 'nullable|integer|exists:dvhc2021_xa,id',
            'address'               => 'nullable|max:255',
            'role_id'               => 'bail|nullable|integer|exists:roles,id',
            'user_name'             => $user_name,
            // 'sell_area'             => 'nullable|array',
            // 'sell_area.*.district_id' => 'required|integer|exists:dvhc2021_huyen,id',
            // 'sell_area.*.priority' => 'nullable|integer|between:1,9999',
            // 'sell_area_delete' => 'nullable|array',
            // 'sell_area_delete.*' => 'required|integer|exists:dvhc2021_huyen,id',
            // 'organize_id' => 'nullable|uuid|exists:organizes,id',
            'custom_path' => 'bail|nullable|unique:users,custom_path,'.$this->id.'|string|min:4|max:50|regex:/^([a-zA-Z0-9]+)(\_[a-zA-Z0-9]+)*$/u',
            'description'           => 'nullable|max:5000',
        ];
    }
    // public function messages()
    // {

    //     return [



    //         'id.required' => 'User_001_E_009',
    //         'id.uuid' => 'User_002_E_010',
    //         'id.exists' => 'User_003_E_011',

    //         'name.required' => 'User_001_E_001',
    //         'name.regex' => 'User_002_E_002',

    //         'email.required' => 'User_001_E_003',
    //         'email.regex' => 'User_002_E_004',
    //         'email.unique' => 'User_005_E_005',

    //         'password.required' => 'User_001_E_006',
    //         'password.confirmed' => 'User_010_E_007',
    //         'password.min' => 'User_004_E_008',

    //         'role_id.required' => 'User_001_E_009',
    //         'role_id.integer' => 'User_002_E_010',
    //         'role_id.exists' => 'User_003_E_011',
    //         'role_id.in' => 'User_002_E_012',

    //         'gender.boolean' => 'User_002_E_013',

    //         'province_id.required' => 'User_001_E_014',
    //         'province_id.integer' => 'User_002_E_015',
    //         'province_id.exists' => 'User_003_E_016',

    //         'district_id.required' => 'User_001_E_017',
    //         'district_id.integer' => 'User_002_E_018',
    //         'district_id.exists' => 'User_003_E_019',

    //         'ward_id.required' => 'User_001_E_020',
    //         'ward_id.integer' => 'User_002_E_021',
    //         'ward_id.exists' => 'User_003_E_022',

    //         'address.required' => 'User_001_E_023',

    //         'phone.required' => 'User_001_E_024',
    //         'phone.digits_between' => 'User_004_E_025',

    //         'date_of_birth.required' => 'User_001_E_026',
    //         'date_of_birth.date_format' => 'User_008_E_027',
    //         'date_of_birth.before' => 'User_004_E_028',

    //         'identity_card.regex' => 'User_002_E_029',

    //         'user_name.required' => 'User_001_E_037',
    //         'user_name.unique' => 'User_005_E_038',
    //         'user_name.string' => 'User_002_E_039',
    //         'user_name.min' => 'User_004_E_040',
    //         'user_name.max' => 'User_004_E_041',
    //         'user_name.regex' => 'User_002_E_042',

    //         // 'sell_area.required_if' => 'User_001_E_045',
    //         'sell_area.array' => 'User_002_E_046',

    //         'sell_area.*.province_id.required' => 'User_001_E_047',
    //         'sell_area.*.province_id.integer' => 'User_002_E_048',
    //         'sell_area.*.province_id.exists' => 'User_003_E_049',

    //         'sell_area.*.priority.integer' => 'User_002_E_050',
    //         'sell_area.*.priority.between' => 'User_004_E_051',

    //         'sell_area_delete.array' => 'User_002_E_052',

    //         'sell_area_delete.*.required' => 'User_001_E_053',
    //         'sell_area_delete.*.integer' => 'User_002_E_054',
    //         'sell_area_delete.*.exists' => 'User_003_E_055',

    //         'sell_area.between' => 'User_002_E_056',

    //         'sell_area_delete.between' => 'User_002_E_057',

    //         'name.max'          => 'User_004_E_064',

    //         'organize_id.uuid'  => 'User_002_E_067',
    //         'organize_id.exists'=> 'User_003_E_068',


    //         'address.max'       => 'User_004_E_069',
    //         'email.max'         => 'User_004_E_070',
    //         'description.max'   => 'User_004_E_071',

    //         'custom_path.unique'    => 'User_005_E_072',
    //         'custom_path.string'    => 'User_002_E_073',
    //         'custom_path.min'       => 'User_004_E_074',
    //         'custom_path.max'       => 'User_004_E_075',
    //         'custom_path.regex'     => 'User_002_E_076',
    //     ];
    // }

}
