<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class HistoryMail extends Model
{
    protected $fillable = [
        'title',
        'content',
        'status',
        'description'
    ];

    protected $table = 'history_mails';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
}
