<?php
namespace App\Services\BusinessType;

use App\BusinessType;
use App\Image;
use App\PlaceType;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use App\Services\GeneralService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\User;

class BusinessTypeService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

     //-------process create businessType--------
    public function create(array $businessType)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $businessType['created_by'] = $user->id;
        }

        $result = BusinessType::create($businessType);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.create'),
        //                 $result->id,
        //                 config('constants.object_type.businessType'),
        //                 $result,
        //                 json_encode($businessType));

        return $result;
    }

    //-------process listing BusinessType--------------------
    public function list()
    {
        $result = BusinessType::where('enable', true)->orderBy('name','asc')->get();

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //------process detail BusinessType----------
    public function detail($id)
    {
        $result = BusinessType::where('id',$id)
            ->where('enable', true)
            ->first();

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //-------process update businessType--------
    public function update(array $businessType)
    {
        $result = BusinessType::find($businessType['id']);

        if(!$result)
        {
            return false;
        }

        $result->update($businessType);


        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.businessType'),
        //                 $result,
        //                 json_encode($businessType));

        return $result;

    }

    //-----process remove businessType----------
    public function remove(array $businessType)
    {
        $result = BusinessType::where([
            ['id', $businessType['id']],
            ['enable', true]
        ])->first();

        if(!$result)
        {
            return false;
        }

        $businessType['enable'] = false;
        $this->update($businessType);

        
        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.businessType'),
        //                 $result,
        //                 json_encode($businessType));

        return $result;
    }

    //-----process delete businessType----------
    public function delete(array $businessType)
    {
        $result = BusinessType::find($businessType['id']);

        if(!$result)
        {
            return false;
        }

        //----------- set 'parent_id = NULL' to sub-categories--------------

        $sub_parent = BusinessType::where('parent_id', $businessType['id'])->get();

        foreach ($sub_parent as $key => $value) {
            $data = [
                'id' => $value->id,
                'parent_id' => null,
            ];
            $this->update($data);
        }

        //----------- delete action ---------------
        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.businessType'),
        //                 $result,
        //                 json_encode($businessType));

        return true;
    }
}
