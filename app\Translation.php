<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Translation extends Model
{
    protected $fillable = ['id','object_id','object_type','language_code','name','description','tsv'];
    // object_type: 'user->1; product->2; shop->3; category->4; image->5; brand->6; option->7; role->8'
    // language_code: https://en.wikipedia.org/wiki/List_of_ISO_639_language_codes#kr
    # there is a  trigger in pgsql db which is auto create tsvector(combine from name & description) data when saving a record.
    protected $table = 'translations';
    protected $primaryKey = 'id';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model){
            $model->id = Str::uuid();
        });
    }

     protected $hidden = ['created_at','updated_at'];
    public $timestamps = true;

    function product()
    {
        return $this->hasOne(Product::class, 'id', 'object_id')            ->where('type','<>', 1)->where('enable', true);
    }
}
