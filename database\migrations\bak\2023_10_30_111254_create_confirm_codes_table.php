<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateConfirmCodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('confirm_codes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('code', 6);
            $table->uuid('user_id')->nullable();
            $table->enum('action_type', [1,2,3])->nullable()->comment('loại tác vụ: 1 - quên mật khẩu, 2 - x<PERSON>c nhận email, 3 - xác nhận sđt');
            $table->timestamp('expire_time')->nullable()->comment('thời hạn hiệu lực');

            $table->index('id');
            $table->index('expire_time');
            $table->index('user_id');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('confirm_codes');
    }
}
