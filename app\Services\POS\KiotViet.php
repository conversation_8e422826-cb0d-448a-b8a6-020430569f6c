<?php

namespace App\Services\POS;

use App\Order;
use App\POSConnection;
use App\Services\Order\OrderService;
use App\Services\POS\POS;
use Illuminate\Support\Facades\Log;

class KiotViet implements POS
{
    private $shopId;

    private $connection;

    public function __construct($shopId)
    {
        $this->shopId = $shopId;
        $connection = POSConnection::where([
            'shop_id' => $shopId,
            'pos_type'=> 'kiot_viet'
        ])->first();

        $this->connection = $connection;

        if($connection && $connection->token_expired < now()){
            $connect = $this->reconnect();
            if($connect) {
                $connection = POSConnection::where([
                    'shop_id' => $shopId,
                    'pos_type'=> 'kiot_viet'
                ])->first();
            }else{

                $connection = null;
            }
        }
        $this->connection = $connection;
    }

    function createOrder($data, OrderService $service)
    {
        $order = $service->detail($data['order_id']);
        $request = [
            'isApplyVoucher' => (bool)$data['voucher'],
            'branchId'       => (int)$this->connection->branch_id,
            'discount'       => $order->discount_amount,
            'description'    => $data['description'] ?? "",
            'totalPayment'   => 0,
            'makeInvoice'    => false,
            "method"         => 'cash',
            'orderDetails'   => [],
            'orderDelivery'  => [
                'deliveryCode' => $order->short_code,
                'receiver'     => $order->customer_name,
                'contactNumber'=> $order->customer_phone,
                'address'      => $order->address,
                'locationName' => '',
                'wardName'     => '',
                'weight'       => 0,
                "length"       => 0,
                "width"        => 0,
                "height"       => 0,
                "usingPriceCod"   => true,
                "expectedDelivery" => $order->delivery_time ?? now()->addMinutes(30),
                "partnerDelivery" => [
                    "code" => "",
                    "name" => "Rẻ mà gần",
                    "address" => "",
                    "contactNumber" => "",
                    "email" => ""
                ]
            ],
            'customer'       => [
                'id' => 0,
                'code' => '',
                'name' => $order->customer_name,
                'gender'  => false,
                'birthDate'=> '',
                'contactNumber' => $order->customer_phone,
                'address' => $order->address,
                'email' => "",
                'comments'=> $order->notes ?? "",
                'wardName'=> "",
            ],
            'surchages'      => [],
        ];

        foreach ($order->items as $item) {
            $orderItem = $item->pivot;
            $price_off = $orderItem->price_off ?? $orderItem->price;
            $discount = $orderItem->price - $price_off;
            $request['orderDetails'][] = [
                'productId'   => (int)$item->extra_id,
                'productCode' => $item->extra_code,
                'productName' => $item->name,
                'isMaster'    => true,
                'quantity'    => $orderItem->quantity,
                'price'       => $orderItem->price,
                'discount'    => $discount,
                'note'        => $orderItem->note,
            ];
        }

        $url = "https://public.kiotapi.com/orders";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->connection->token,
            'Retailer: ' . $this->connection->extra_data['retailer'],
        ]);

        $response = curl_exec($ch);
        curl_close($ch);
        $responseData = json_decode($response, true);
        if(isset($responseData['id'])){
            Order::find($data['order_id'])->update(['extra_data' => $responseData['id']]);
        }else{
            return false;
        }

        return $responseData;
    }

    function update($data, OrderService $service)
    {
        $order = $service->detail($data['order_id']);
        if($order->status < 2){
            $response = $this->updateOrder($order);
        }else if ($order->status == 2){
            $response = json_decode($this->updateOrder($order), true);
            if(isset($response['invoices'])){
                Order::find($data['order_id'])->update(['extra_data' => $response['invoices'][0]['invoiceId'] ]);
                $order = $service->detail($data['order_id']);
            };
            $response = $this->updateInvoice($order);
        }else{
            $response = $this->updateInvoice($order);
        }

        return json_decode($response, true);
    }


    function listBranch()
    {
        $url = "https://public.kiotapi.com/branches?pageSize=100";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPGET, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->connection->token,
            'Retailer: ' . $this->connection->extra_data['retailer'],
        ]);
        $response = curl_exec($ch);

        // Kiểm tra lỗi
        if (curl_errno($ch)) {
            Log::error( 'Lỗi cURL: ' . curl_error($ch));
            curl_close($ch);
            return false;
        }
        curl_close($ch);
        $responseData = json_decode($response, true);
        return $responseData;
    }

    function listProducts($offset = 0, $limit = 10)
    {
        $url = "https://public.kiotapi.com/products?pageSize=". $limit . "&currentItem=". $offset; ;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPGET, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->connection->token,
            'Retailer: ' . $this->connection->extra_data['retailer'],
        ]);
        $response = curl_exec($ch);

        // Kiểm tra lỗi
        if (curl_errno($ch)) {
            Log::error( 'Lỗi cURL: ' . curl_error($ch));
            curl_close($ch);
            return false;
        }
        curl_close($ch);
        $responseData = json_decode($response, true);
        return $responseData;
    }

    function connect($data)
    {
        $responseData = $this->getToken($data);

        if(!$responseData) return false;
        $connect = POSConnection::where("shop_id",  $data['shop_id'])->first();
        if(!$connect) {
            POSConnection::create([
                'token' => $responseData['access_token'],
                'token_expired' => now()->addSeconds($responseData['expires_in']),
                'data' => [
                    'client_id' => $data['client_id'],
                    'client_secret' => $data['client_secret'],
                ],
                'extra_data' => [
                    'retailer' => $data['retailer'],
                ],
                'pos_type' => 'kiot_viet',
                'shop_id' => $data['shop_id'],
            ]);
        }else {
            $connect->update([
               'token' => $responseData['access_token'],
               'token_expired' => now()->addSeconds($responseData['expires_in']),
            ]);
        }
        return true;
    }

    function reconnect(){
        $responseData = $this->getToken($this->connection->data);

        if(!$responseData) return false;

        $this->connection->update([
            'token' => $responseData['access_token'],
            'token_expired' => now()->addSeconds($responseData['expires_in']),
        ]);
        return true;

    }

    function getToken($data)
    {
        $request = [
            'scopes' => 'PublicApi.Access',
            'grant_type' => 'client_credentials',
            'client_id' => $data['client_id'],
            'client_secret' => $data['client_secret'],
        ];
        $url = "https://id.kiotviet.vn/connect/token";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);

        // Thực thi yêu cầu và nhận phản hồi
        $response = curl_exec($ch);

        // Kiểm tra lỗi
        if (curl_errno($ch)) {
            Log::error( 'Lỗi cURL: ' . curl_error($ch));
            curl_close($ch);
            return false;
        }
        curl_close($ch);

        return json_decode($response, true);
    }

    function updateOrder(Order $order)
    {
        $request = [
            'branchId'       => (int)$this->connection->branch_id,
            'discount'       => $order->discount_amount,
            'description'    => $data['description'] ?? "",
            'totalPayment'   => 0,
            'makeInvoice'    => $order->status >= 2,
            'orderDetails'   => [],
            'orderDelivery'  => [
                'deliveryCode' => $order->short_code,
                'receiver'     => $order->customer_name,
                'contactNumber'=> $order->customer_phone,
                'address'      => $order->address,
                'status'       => 1,
                'locationName' => '',
                'wardName'     => '',
                'weight'       => 0,
                "length"       => 0,
                "width"        => 0,
                "height"       => 0,
                "usingPriceCod"   => true,
                "expectedDelivery" => $order->delivery_time ?? now()->addMinutes(30),
                "partnerDelivery" => [
                    "code" => "",
                    "name" => "Rẻ mà gần",
                    "address" => "",
                    "contactNumber" => "",
                    "email" => ""
                ]
            ],
            'customer'       => [
                'id' => 0,
                'code' => '',
                'name' => $order->customer_name,
                'gender'  => false,
                'birthDate'=> '',
                'contactNumber' => $order->customer_phone,
                'address' => $order->address,
                'email' => "",
                'comments'=> $order->notes ?? "",
                'wardName'=> "",
            ],
            'surchages'      => [],
        ];


        foreach ($order->items as $item) {
            $orderItem = $item->pivot;
            $price_off = $orderItem->price_off ?? $orderItem->price;
            $discount = $orderItem->price - $price_off;
            $request['orderDetails'] [] = [
                'productId'   => (int)$item->extra_id,
                'productCode' => $item->extra_code,
                'productName' => $item->name,
                'isMaster'    => true,
                'quantity'    => $orderItem->quantity,
                'price'       => $orderItem->price,
                'discount'    => $discount,
                'note'        => $orderItem->note,
            ];
        }

        $url = "https://public.kiotapi.com/orders/". $order->extra_data;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->connection->token,
            'Retailer: ' . $this->connection->extra_data['retailer'],
        ]);

        $response = curl_exec($ch);
        $httpStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if($httpStatus >= 200 && $httpStatus <= 300){
            return $response;
        }
        return false;
    }

    function updateInvoice($order)
    {
        switch ($order->status) {
            case config('constants.order.status.in_process'):
            case config('constants.order.status.ready_to_deliver'): $status = 2; break;
            case config('constants.order.status.done'): $status = 3; break;
            default: $status = $order->status; break;
        }

        if($status == config('constants.order.status.cancel')) {
            $result = $this->deleteOrder($order);
            if($result) return true;
        }
        $request = [
            'deliveryDetail'  => [
                'deliveryCode' => $order->short_code,
                'status'       => $status,
                'receiver'     => $order->customer_name,
                'contactNumber'=> $order->customer_phone,
                'address'      => $order->address,
                'locationName' => '',
                'wardName'     => '',
                'weight'       => 0,
                "length"       => 0,
                "width"        => 0,
                "height"       => 0,
                "priceCodPayment" => $status == 3 ? $order->total_amount : 0,
                "usingPriceCod"   => true,
                "expectedDelivery" => $order->delivery_time ?? now()->addMinutes(30),
                "partnerDelivery" => [
                    "code" => "",
                    "name" => "Rẻ mà gần",
                    "address" => "",
                    "contactNumber" => "",
                    "email" => ""
                ]
            ],
            'customer'       => [
                'id' => 0,
                'code' => '',
                'name' => $order->customer_name,
                'gender'  => false,
                'birthDate'=> '',
                'contactNumber' => $order->customer_phone,
                'address' => $order->address,
                'email' => "",
                'comments'=> $order->notes ?? "",
                'wardName'=> "",
            ],
            'surchages'      => [],
        ];
        $url = "https://public.kiotapi.com/invoices/". $order->extra_data;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->connection->token,
            'Retailer: ' . $this->connection->extra_data['retailer'],
        ]);

        $response = curl_exec($ch);
        curl_close($ch);
        return $response;
    }

    function deleteOrder($order)
    {
        $url = "https://public.kiotapi.com/orders/". $order->extra_data;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->connection->token,
            'Retailer: ' . $this->connection->extra_data['retailer'],
        ]);

        curl_exec($ch);
        $httpStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if($httpStatus == 200) {
            return true;
        }
        return false;
    }
}
