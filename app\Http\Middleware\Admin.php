<?php

namespace App\Http\Middleware;

use Closure;
use App\Http\Requests\PropertyRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use <PERSON><PERSON>\JWTAuth\Http\Middleware\BaseMiddleware;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Config;
use Illuminate\Http\Exceptions\HttpResponseException;
use App\Property;
use App\User;
use App\Role;
use App\Setting;
use Illuminate\Contracts\Auth\Guard;
use Tymon\JWTAuth\Facades\JWtAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
class Admin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    // auth()->user()->role_id
    // Auth::user()->role_id
    public function handle($request, Closure $next)
    {
        // $key = Config::get('constants.properties.permission', 'property_roles');
        // $admin = Setting::where('key',$key)->select('value')->first()->value;

        // $temp = json_decode($admin,true);
        // foreach ($temp as $key => $value) {
        //    foreach ($value as $key => $value) {
        //       $permission =  $value['edit'];
        //    }
        // }

        // $check = 0;
        // for ($i=0; $i < count($permission) ; $i++) {
        //     if(Auth::user()->role_id == $permission[$i])
        //     {
        //         $check = 1;
        //         break;
        //     }
        // }


        if(Auth::user()->role_id != Config::get('constants.role_id.admin'))
        {
            $response = [
                        'status' => JsonResponse::HTTP_UNAUTHORIZED,
                        'body' => ['C_E_007'],
                    ];
            return response()->json($response,JsonResponse::HTTP_OK);
        }
        return $next($request);
    }
}
