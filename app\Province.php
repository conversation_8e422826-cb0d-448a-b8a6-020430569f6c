<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Province extends Model
{
    //
     //set time to false
    protected $fillable = [
        'name','slug','longitude','latitude','profile_picture','bound','address',
        'streetmap','satellite','a','b','f','LAM0','PHI0','E0','N0','F0','DX','DY',
        'DZ','X_Rotation','Y_Rotation','Z_Rotation','Scale','planning'

    ];
    protected $primaryKey = 'id';
    // protected $table = 'provinces';
    protected $table = 'dvhc2021_tinh';
    protected $hidden =['pivot', 'created_at', 'updated_at'];

    public function districts()
    {
        return $this->hasMany(District::class,'province_id')->orderBy('slug','asc');

    }

}
