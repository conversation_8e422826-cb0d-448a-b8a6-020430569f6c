<?php

namespace App\Http\Requests\Setting;

use App\Http\Requests\BaseRequest;

class SettingUpdateRequest extends BaseRequest
{

    public function rules()
    {
        if(isset($this->id))
        {
            $id = 'required|uuid|exists:settings,id';
            $key = 'required|max:255|unique:settings,key,'.$this->id;
        }
        else{
            $id = 'required|uuid|exists:settings,id';
            $key = 'required|max:255|unique:settings,key';
        }

        return [
            'id' => $id,
            'key' => $key,
            'value' => 'required',
            'description' => 'nullable|max:255',
            'reference_value'=> 'nullable|max:255',
            'object_type'   => 'nullable|integer',
        ];

    }

    // public function messages()
    // {
    //     return [
    //         'key.required' => 'Setting_001_E_001',
    //         'key.unique' => 'Setting_005_E_002',

    //         'value.required' => 'Setting_001_E_003',

    //         'id.required' => 'Setting_001_E_004',
    //         'id.uuid' => 'Setting_002_E_005',
    //         'id.exists' => 'Setting_003_E_006',

    //         'key.max' => 'Setting_004_E_007',
    //         'value.max' => 'Setting_004_E_008',
    //         'description.max' => 'Setting_004_E_009',
    //         'reference_value.max' => 'Setting_004_E_010',
    //         'object_type.integer' => 'Setting_002_E_011',
    //     ];
    // }
}
