<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDeliveriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('deliveries', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('order_id')->nullable();
            $table->uuid('driver_id')->nullable();
            $table->integer('status')->default(1)->comment('1 = pending; 2 = confirmed; 3 = prepared; 4 = picked_up; 5 = in_transit; 6 = delivered; 7 = cancelled; 8 = failed;');
            $table->integer('duration')->default(30)->comment('minutes require to deliver:30,45,60,120 from pickup time');
            $table->dateTime('pickup_time')->nullable()->comment('null =  now; time = thời gian l<PERSON>y hàng tại người gửi');
            $table->text('notes')->nullable();
            $table->float('latitude_from');
            $table->float('longitude_from');
            $table->float('latitude_to');
            $table->float('longitude_to');
            $table->float('distance')->comment('kilomet');

            $table->string('address_from', 500);
            $table->string('name_from', 500);
            $table->string('phone_from', 50);

            $table->string('address_to', 500);
            $table->string('name_to', 500);
            $table->string('phone_to', 50);

            
            $table->json('status_time')->nullable()->comment('time for each status update.');
            $table->json('package_info')->nullable();
            $table->json('special_require')->nullable();

            $table->float('total_amount')->comment('tổng giá trị vận đơn');
            $table->string('discount_id', 50)->nullable()->comment('mã giảm giá vận chuyển');
            $table->float('discount_amount')->default(0)->comment('số tiền giảm giá');
            $table->float('grand_total')->comment('tổng thanh toán sau khi đã áp giảm giá...');
          

            $table->timestamps();

            $table->index(['driver_id','order_id','status']);

            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            $table->foreign('driver_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('deliveries');
    }
}
