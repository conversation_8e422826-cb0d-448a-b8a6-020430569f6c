<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Ward extends Model
{
    //
     //set time to false
    protected $fillable = [
    	'name','district_id','slug','longitude','latitude','bound','address',
        'streetmap','satellite', 'planning'
    ];
    protected $primaryKey = 'id';
    protected $table = 'dvhc2021_xa';
    // protected $table = 'wards';
    protected $hidden = ['created_at', 'updated_at', 'pivot'];

    public function projectAreas()
    {
        return $this->hasMany(ProjectArea::class,'ward_id');
    }
    public function properties()
    {
        return $this->hasMany(Property::class,'ward_id');
    }
    public function requests()
    {
        return $this->hasMany(Request::class,'ward_id');
    }
    public function customers()
    {
        return $this->hasMany(Customer::class,'ward_id');
    }
    public function places()
    {
        return $this->hasMany(Place::class,'ward_id');
    }

    public function villages()
    {
        return $this->hasMany(Village::class, 'ward_id')->orderBy('slug','asc');
    }
    public function districts()
    {
        return $this->belongsTo(District::class, 'district_id');
    }
}

