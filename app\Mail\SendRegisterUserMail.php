<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Services\HistoryMail\HistoryMailService;

class SendRegisterUserMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $token;
    protected $id;
    protected $name;
    protected $email;
    public function __construct($token,$id,$name,$email)
    {
        //
        $this->token = $token;
        $this->id = $id;
        $this->name = $name;
        $this->email = $email;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {

        $historyMail = null;
        try {
            $historyMail = HistoryMailService::insert([
                'title' => 'Thông báo kích hoạt tài khoản',
                'content' => json_encode([
                    'token'=> $this->token,
                    'id'   => $this->id,
                    'name' => $this->name,
                    'email'=> $this->email
                ]),
                'status' => config('constants.status_history_mail.success')
            ]);
            return $this->subject('Thông báo kích hoạt tài khoản')
            ->view('emails.register_user_mail')
            ->with([
                'token'=> $this->token,
                'id'   => $this->id,
                'name' => $this->name,
                'email'=> $this->email
            ]);


        } catch (\Exception $e) {
            Log::info("Mail Error: ".$e);

            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

    }
}
