<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Exceptions\HttpResponseException;

class BaseRequest extends FormRequest
{
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        $mes = array();
        // foreach($errors as $key => $value){
        //     $a = implode($value);
        //     array_push($mes,$a);
        // }
        foreach ($errors as $field => $messages) {
            foreach ($messages as $message) {
                $mes[] = [
                    'error_message' => $message,
                    'field' => $field,
                ];
            }
        }

        throw new HttpResponseException(response()->json(
            [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'errors' => $mes,
               
            ], JsonResponse::HTTP_OK));
    }
    
    protected function checkIdRequest($id)
    {   
        
        if(!is_numeric($id)  || is_null($id) || strlen(strval($id)) != strlen(strval((int)$id)) )
        {
            return false;
        }
        return true;
    }

    public function validationData() {
        return array_merge(
            $this->all(),
            [
                'name' => preg_replace('([\s]+)', ' ', $this->name),
                'key_word' => preg_replace('([\s]+)', ' ', $this->key_word),
                'value' => preg_replace('([\s]+)', ' ', $this->value),
                'key' => preg_replace('([\s]+)', ' ', $this->key),
                'title' => preg_replace('([\s]+)', ' ', $this->title),
                
            ]
        );
    }
}
