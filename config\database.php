<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'pgsql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],

        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_CRAWL', '127.0.0.1'),
            'port' => env('DB_PORT_CRAWL', '3306'),
            'database' => env('DB_DATABASE_CRAWL', 'nomnomland_crawl'),
            'username' => env('DB_USERNAME_CRAWL', 'root'),
            'password' => env('DB_PASSWORD_CRAWL', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => array_filter([
                #PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]),
        ],
        'mysqlSP' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_SP', '*************'),
            'port' => env('DB_PORT_SP', '33060'),
            'database' => env('DB_DATABASE_SP', 'remagan_support'),
            'username' => env('DB_USERNAME_SP', 'root'),
            'password' => env('DB_PASSWORD_SP', 'sdgNRO4sdgs1job@712'),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'version' => 8,
            'modes' => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST', '199.241.138.585'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'nomnomland'),
            'username' => env('DB_USERNAME', 'postgres'),
            'password' => env('DB_PASSWORD', '12345678@Z'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],
        
        'pgsql_crawl' => [
            'driver' => 'pgsql',
            'host' => env('DB_CRAWL_HOST', '************'),
            'port' => env('DB_CRAWL_PORT', '5432'),
            'database' => env('DB_CRAWL_DATABASE', 'nomnomland_crawl'),
            'username' => env('DB_CRAWL_USERNAME', 'postgres'),
            'password' => env('DB_CRAWL_PASSWORD', '123456'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'pgsqlReplica' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST_REPLICA', '199.241.138.855'),
            'port' => env('DB_PORT_REPLICA', '5434'),
            'database' => env('DB_DATABASE_REPLICA', 'nomnomland'),
            'username' => env('DB_USERNAME_REPLICA', 'postgres'),
            'password' => env('DB_PASSWORD_REPLICA', '12345678@Z'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'pgsqlGeocode' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST_GEOCODE', '*************'),
            'port' => env('DB_PORT_GEOCODE', '5432'),
            'database' => env('DB_DATABASE_GEOCODE', 'nomnomland'),
            'username' => env('DB_USERNAME_GEOCODE', 'nomnomland_pro'),
            'password' => env('DB_PASSWORD_GEOCODE', 'ojasf927dfSF330oUSFJu'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '1433'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'predis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'predis'),
        ],

        'default' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_DB', 0),
        ],

        'cache' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_CACHE_DB', 1),
        ],

    ],

];
