<?php

namespace App\Http\Requests\DeliveryPartner;

use App\Http\Requests\BaseRequest;

class CreateDeliveryRequest extends BaseRequest
{
    public function rules(){
        return  [
            'partner' => 'required|string|in:ahamove,j&t,remagan',
            'order_id' => 'required|uuid|exists:orders,id',
            'shop_id'    => 'required|uuid|exists:shops,id',
//            'delivery_type' => 'required|string|in:fast,super_fast,super_cheap',
            'path' => 'required|array',
            'path.*.lat' => 'required|numeric',
            'path.*.lng' => 'required|numeric',
            'path.*.address' => 'required|string',
            'path.*.name' => 'required|string',
            'path.*.cod' => 'nullable|numeric',
            'path.*.phone' => 'nullable|string',
            'promo_code' => 'nullable|string',
            'payment_method' => 'required|string|in:cash,cash_by_recipient',
            'note'  => 'nullable|string',
            'delivery_service' => 'required_if:partner,ahamove',
            'pending_time' => 'nullable|numeric|min:0',
        ];
    }
}
