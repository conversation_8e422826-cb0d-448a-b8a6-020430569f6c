<?php

namespace App\Admin\Actions\User;

use Encore\Admin\Actions\BatchAction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class BatchGenerateReferralCode extends BatchAction
{
    public $name = 'Batch Generate Referral Codes';

    public function handle(Collection $collection, Request $request)
    {
        $generated = 0;
        $existing = 0;
        
        foreach ($collection as $user) {
            if ($user->referral_code) {
                $existing++;
            } else {
                $user->generateReferralCode();
                $generated++;
            }
        }

        $message = "Generated referral codes for {$generated} users. {$existing} users already had referral codes.";
        
        return $this->response()->success($message)->refresh();
    }

    public function dialog()
    {
        $this->confirm('Are you sure you want to generate referral codes for selected users?');
    }
}
