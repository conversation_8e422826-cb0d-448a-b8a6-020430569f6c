<?php

namespace App\Http\Requests\Channel;

use App\Http\Requests\BaseRequest;

class ChannelGetIdRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'member_a_id' => 'required|uuid',
            'member_a_type' => 'required|string|in:user,shop',
            'member_b_id' => [
                'required',
                'uuid',
                // Kiểm tra có sự khác nhau giữa Id bên A và bên B không?
                function ($attribute, $value, $fail) {
                    if ($value === request('member_a_id')) {
                        $fail('The opponent_id must be different from owner_id.');
                    }
                },
            ],
        ];
    }
}
