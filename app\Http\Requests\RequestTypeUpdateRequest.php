<?php

namespace App\Http\Requests;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class RequestTypeUpdateRequest extends BaseRequest
{
    public function rules()
    {
        $data = $this->all();
        if(!is_integer($data['id']))
        {
            return [
                'id' => 'required|integer',
            ];
        }
        return [
            'id' => 'exists:request_types,id',
            // 'name' => ['required',Rule::unique('request_types')->ignore($this->id)],
            // 'title' => ['required',Rule::unique('request_types')->ignore($this->id)],
            'name'=>'required|unique:request_types,name,'.$this->id,
            'title' =>'required|unique:request_types,title,'.$this->id,
        ];

    }

    public function messages()
    {

        return [
            'name.required' => 'Request_Type_001_E_001',
            'name.unique' => 'Request_Type_005_E_005',

            'title.required' => 'Request_Type_001_E_002',
            'title.unique' => 'Request_Type_005_E_006',

            'id.required' => 'Request_Type_001_E_007',
            'id.integer' => 'Request_Type_002_E_003',
            'id.exists' => 'Request_Type_003_E_004',


        ];
    }
}
