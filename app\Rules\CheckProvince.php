<?php

namespace App\Rules;

use App\Province;
use Illuminate\Contracts\Validation\Rule;

class CheckProvince implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     * 
     */
    protected $province_id;
    public function __construct($province_id)
    {
        //
        $this->province_id = $province_id;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $province = Province::find($value);
        return $province;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return "Province_001_E_001";
    }
}
