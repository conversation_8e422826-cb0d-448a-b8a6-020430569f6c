<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProductCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // bảng liên kết gi<PERSON>a <PERSON> mụ<PERSON> và Sản phẩm
        Schema::create('product_categories', function (Blueprint $table) {
            // $table->uuid('id')->primary();
            $table->uuid('product_id')->comment("ID sản phẩm");
            $table->uuid('category_id')->comment("ID danh mục");
            $table->index('product_id','category_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_categories');
    }
}
