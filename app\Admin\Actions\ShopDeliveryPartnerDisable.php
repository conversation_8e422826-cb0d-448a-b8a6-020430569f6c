<?php
namespace App\Admin\Actions;

use App\ShopDeliveryPartner;
use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class ShopDeliveryPartnerDisable extends RowAction
{

    public function handle($model){
        // Toggle the enabled status
        $actionName = $model->is_enabled ? __('admin.disable') : __('admin.enable');
        $model->is_enabled = !$model->is_enabled;
        $model->save();

        return $this->response()->success($actionName . ' Success!')->refresh();
    }

    public function display($value)
    {
        return $value ? "<i class=\"fa fa-check text-green\"></i>" : "<i class=\"fa fa-close text-red\"></i>";
    }
}

