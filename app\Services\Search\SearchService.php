<?php
namespace App\Services\Search;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\District;
use App\Province;
use App\Ward;
use App\Place;
use App\View;
use App\Search;
use App\User;
use App\Tag;
use App\Geocode;
use Illuminate\Support\Facades\Auth;
use DB;
use App\Services\GeneralService;
use App\Services\Geocode\GeocodeService;

class SearchService
{
    private $_province ;
    private $_slugWard;
    private $_success;
    private $_slugDistrict;
    private $_userId;
    private $_check;

    public function __construct($userId = null)
    {
        $this->_province        = null;
        $this->_slugWard        = null;
        $this->_success         = 'success';
        $this->_slugDistrict    = null;
        $this->_check          = false;
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }


    //---------insert search--------------
    public function insert(array $data)
    {
        if(isset($data['id']))
        $geocode = Geocode::find($data['id']);

        if(isset($geocode))
        {
            if($geocode->province_id != null)
            {
                // $this->insertHotsearch(['object_id' => $geocode->province_id, 'object_type' => config('constants.object_type_view.province'), 'description' => $geocode->title]);

                View::where([
                    ['object_type', config('constants.object_type_view.province')],
                    ['object_id', $geocode->province_id],
                    ])->increment('view',1);

                if($geocode->district_id != null)
                {
                    // $this->insertHotsearch(['object_id' => $geocode->district_id, 'object_type' => config('constants.object_type_view.district'), 'description' => $geocode->title]);

                    View::where([
                        ['object_type', config('constants.object_type_view.district')],
                        ['object_id', $geocode->district_id],
                        ])->increment('view',1);

                    if($geocode->ward_id != null)
                    {
                        // $this->insertHotsearch(['object_id' => $geocode->ward_id, 'object_type' => config('constants.object_type_view.ward'), 'description' => $geocode->title]);

                        View::where([
                            ['object_type', config('constants.object_type_view.ward')],
                            ['object_id', $geocode->ward_id],
                            ])->increment('view',1);

                    }
                }
            }

            // $geocode->update(['popular', $geocode->popular + 1]);
            $geocode->increment('popular',1);

            try {
                $geocode->searchable();
            } catch (\Throwable $th) {
                Log::info("server elasticsearch error");
            }
            // $geocode->searchable();
        }
        else
        {
            $address = array_reverse(explode(',' , $data['address']));
            $count = count($address);

            for ($i=0; $i < $count ; $i++) {
                $p = Str::slug(str_replace(['Thành phố', 'tỉnh', 'Tỉnh', 'Province', 'TP.', 'Hanoi','City', 'Tp.','HCM', 'TP', 'tp', 'Haiphong', 'thành phố', 'Thành Phố'],['', '', '','', '','Ha noi','','','ho-chi-minh','','','hai-phong', '', ''],preg_replace("/[0-9]/","",$address[$i])));

                if(isset($p))
                {
                    //--------province-------------------------
                    $province_id = Province::where('slug',$p)->orWhere('slug', 'like', "%".$p)->first(['id']);

                    if(isset($province_id->id))
                    // $this->insertHotsearch(['object_id' => $province_id->id, 'object_type' => config('constants.object_type_view.province'), 'description' => $data['address']]);
                    View::where([
                        ['object_type', config('constants.object_type_view.province')],
                        ['object_id', $province_id->id],
                        ])->increment('view',1);

                    //----------district----------------------
                    if(isset($province_id) && isset($address[$i +1]))
                    {
                        $d = Str::slug(str_replace(['Tp.', 'TP', 'District', 'Q.', 'TX.'],['','','', '', ''],$address[$i +1]));
                        $updateDis = str_replace(['quan-2', 'quan-9'], ['thu-duc', 'thu-duc'], $d);
                        $district_id = District::where([['slug', $updateDis],['province_id', $province_id['id']]])->orWhere([['slug', 'like', "%".$updateDis],['province_id', $province_id['id']]])->first(['id']);

                        if(isset($district_id->id))
                        // $this->insertHotsearch(['object_id' => $district_id->id, 'object_type' => config('constants.object_type_view.district'), 'description' => $data['address']]);
                        View::where([
                            ['object_type', config('constants.object_type_view.district')],
                            ['object_id', $district_id->id],
                            ])->increment('view',1);

                        //-------------ward--------------------
                        if(isset($district_id) && isset($address[$i +2]))
                        {
                            $w = Str::slug(str_replace(['TT.'],[''],$address[$i +2]));
                            $ward_id =Ward::where([['slug', $w],['district_id', $district_id['id']]])->orWhere([['slug', 'like', "%".$w],['district_id', $district_id['id']]])->first(['id']);

                            if(isset($ward_id->id))
                            // $this->insertHotsearch(['object_id' => $ward_id->id, 'object_type' => config('constants.object_type_view.ward'), 'description' => $data['address']]);
                            View::where([
                                ['object_type', config('constants.object_type_view.ward')],
                                ['object_id', $ward_id->id],
                                ])->increment('view',1);

                        }
                    }

                    break;
                }
            }
        }


        return true;
    }

    public function updateView(array $data)
    {
        DB::beginTransaction();
        for ($i=0; $i < count($data) ; $i++) {
            $geocode = Geocode::find($data[$i]);

            if(isset($geocode))
            {
                if($geocode->province_id != null)
                {
                    // $this->insertHotsearch(['object_id' => $geocode->province_id, 'object_type' => config('constants.object_type_view.province'), 'description' => $geocode->title]);

                    DB::table('views')->where([
                        ['object_type', config('constants.object_type_view.province')],
                        ['object_id', $geocode->province_id],
                        ])
                        ->update([
                            'view' => \DB::raw( 'view + 1' ), // increment
                            // 'column2' => \DB::raw( 'column2 - 1' ), // decrement
                        ]);

                    // View::where([
                    //     ['object_type', config('constants.object_type_view.province')],
                    //     ['object_id', $geocode->province_id],
                    //     ])->increment('view',1);

                    if($geocode->district_id != null)
                    {
                        // $this->insertHotsearch(['object_id' => $geocode->district_id, 'object_type' => config('constants.object_type_view.district'), 'description' => $geocode->title]);

                        // View::where([
                        //     ['object_type', config('constants.object_type_view.district')],
                        //     ['object_id', $geocode->district_id],
                        //     ])->increment('view',1);

                            DB::table('views')->where([
                                ['object_type', config('constants.object_type_view.district')],
                                ['object_id', $geocode->district_id],
                                ])
                                ->update([
                                    'view' => \DB::raw( 'view + 1' ), // increment
                                    // 'column2' => \DB::raw( 'column2 - 1' ), // decrement
                                ]);

                        if($geocode->ward_id != null)
                        {
                            // $this->insertHotsearch(['object_id' => $geocode->ward_id, 'object_type' => config('constants.object_type_view.ward'), 'description' => $geocode->title]);

                            // View::where([
                            //     ['object_type', config('constants.object_type_view.ward')],
                            //     ['object_id', $geocode->ward_id],
                            //     ])->increment('view',1);

                            DB::table('views')->where([
                                ['object_type', config('constants.object_type_view.ward')],
                                ['object_id', $geocode->ward_id],
                                ])
                                ->update([
                                    'view' => \DB::raw( 'view + 1' ), // increment
                                    // 'column2' => \DB::raw( 'column2 - 1' ), // decrement
                                ]);
                        }
                    }
                }
                // $geocode->update(['popular', $geocode->popular + 1]);
                $geocode->increment('popular',1);
                try {
                    $geocode->searchable();
                } catch (\Throwable $th) {
                    Log::info("server elasticsearch error");
                }
                // $geocode->searchable();
            }
        }
        DB::commit();

        return true;
    }
    //---------geocode google--------------
    public function searchGG(array $data)
    {

        $geocode = $this->geocode($data);
        // $geocode = json_decode(json_encode($data['search'][0]));
        // die(json_encode($geocode));
        if($geocode->results[0])
        {
            $address = $geocode->results[0]->address_components;
            $count = count($address);

            foreach ($address as $key => $value)
            {

                    if($this->_province !== $this->_success)
                    {
                        $this->handleProvince($value->long_name,$data);
                    }
                    else
                    {
                        break;
                    }

            }

            return [];
        }

        return [];
    }

    //---------Ward---------------
    public function slugWard($text)
    {
        $search = ['Phường','Thị trấn', 'Xã', 'Ward', 'tt'];
        $replace = ['','','','',''];

        $text = Str::slug(str_replace($search, $replace, $text));

        $check = Ward::where('slug', 'ILIKE', '%'.$text.'%')->get();

        if($check)
        {
            $this->_slugWard = &$text;
            // var_dump('ward:'.$text);
        }

        // if($check->count() > 0)
        // {
        //     foreach ($tring as $key ) {
        //         $text2 = Str::slug($key.$text);
        //         $result = Ward::where('slug', $text2)->first();

        //         if($result)
        //         {
        //             $this->$_slugWard = $text2;
        //         }
        //     }
        // }
    }

    public function handleWard($district_id, $data)
    {
        $text = $this->_slugWard;
        $tring = ['Phường ', 'Thị trấn ','Xã '];
        if($text !== null)
        {
            $check = Ward::where([
                ['slug', $text],
                ['district_id', $district_id]
                ])->first();

            if($check)
            {
                $ward = [
                    'object_id'     => $check['id'],
                    'object_type'   => config('constants.object_type_view.ward'),
                    'description'   => $data['search']
                ];

                $this->insertHotsearch($ward);
                // $this->_ward = 'success';

                return true;
            }
            else
            {
                foreach ($tring as $key )
                {
                    $text2 = Str::slug($key.$text);
                    $result = Ward::where([
                        ['slug', $text2],
                        ['district_id', $district_id]
                        ])->first();

                    if($result)
                    {
                        $ward = [
                            'object_id'     => $result['id'],
                            'object_type'   => config('constants.object_type_view.ward'),
                            'description'   => $data['search']
                        ];

                        $this->insertHotsearch($ward);
                        // $this->_ward = 'success';

                        return true;
                    }
                }
            }

        }

        return [];
    }

    //-----------------District----------------

    public function slugDistrict($text)
    {
        $search = ['City','District', 'Dalat'];
        $replace = ['','','Da lat'];

        $slug = Str::slug(str_replace($search, $replace, $text));

        $check = District::where('slug', 'ILIKE', '%'.$slug.'%')->get();

        if($check->count() > 0)
        {
            if($this->_slugDistrict !== null)
            {
                $this->_slugWard = $this->_slugDistrict;
                // var_dump('ward1:'.$text);
            }

            $this->_slugDistrict = &$slug;
            // var_dump('district:'.$text);
        }
        else
        {

            $this->slugWard($text);
        }

        return [];

    }
    public function handleDistrict($province_id, $data)
    {
        $text = $this->_slugDistrict;
        $tring = ['Quận ', 'Thành phố ','Thị xã ', 'Huyện '];

        if($text !== null)
        {
            $check = District::where([
                ['slug', $text],
                ['province_id', $province_id]
                ])->first();

            if($check)
            {
                $district = [
                    'object_id'     => $check['id'],
                    'object_type'   => config('constants.object_type_view.district'),
                    'description'   => $data['search']
                ];

                $this->insertHotsearch($district);

                $this->handleWard($check->id, $data);
                return true;
            }
            else
            {
                foreach ($tring as $key )
                {
                    $text2 = Str::slug($key.$text);
                    $result = District::where([
                                ['slug', $text2],
                                ['province_id', $province_id]
                                ])->first();

                    if($result)
                    {
                        $district = [
                            'object_id'     => $result['id'],
                            'object_type'   => config('constants.object_type_view.district'),
                            'description'   => $data['search']
                        ];

                        $this->insertHotsearch($district);

                        $this->handleWard($result->id, $data);
                        return true;
                    }

                }
            }

        }

        return [];
    }
    //--------Pronvice----------------------
    public function handleProvince($text, $data)
    {
        $search = ['City','Hanoi','Province'];
        $replace = ['','Ha noi',''];

        $slug = Str::slug(str_replace($search, $replace, $text));

        $result = Province::where('slug', 'ILIKE', '%'.$slug.'%')->get();
        if($result->count() == 1)
        {
            // var_dump('province:'.$text);
            $province = [
                'object_id'     => $result[0]->id,
                'object_type'   => config('constants.object_type_view.province'),
                'description'   => $data['search']
            ];
            $this->insertHotsearch($province);
            $succ = 'success';
            $this->_province = &$succ;
            $this->handleDistrict($result[0]->id, $data);
            return true;
        }
        else
        {
            $this->slugDistrict($text);
        }
        return false;
    }
    //-----------Insert hotsearch------------
    public function insertSearch(array $data)
    {
        $device = request()->header('User-Agent');
        $general = new GeneralService();
        $ip = $general->getUserIpAddr();
        $hotsearch = [
            'raw'           => $data['search'],
            'device'        => $device,
            'address_ip'    => $ip,
            'user_id'       => $this->_userId
        ];
        $result = Search::create($hotsearch);
        return $result;



    }
    //-----------geocode google map----------------------
    public function geocode(array $data)
    {
        $baseURL = 'https://maps.google.com/maps/api/geocode/json?address=';
        $url = $baseURL.urlencode($data['search']).'&key=' . env('GOOGLE_KEY');
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        $geocode = json_decode(curl_exec($curl));

        return $geocode;
    }
    //------------check hotsearch-----------------------
    // public function checkHotSearch()
    // {
    //     $result = Search::with(['view','view.province', 'view.district','view.ward'])
    //                         // ->where('object_type', config('constants.object_type_follow_search.search'))
    //                         ->orderBy('updated_at','desc')
    //                         ->limit(10)
    //                         ->get();

    //     return $result;
    // }


    //------------geocode nomnomland--------------
    // public function geocoder(array $data)
    // {
    //     if(empty($data['search']))
    //     {
    //         return null;
    //     }
    //     $result = Search::
    //     // ->where([
    //     //     ['description','ILIKE', $data['search'].'%'],
    //     //     ['object_type',2]
    //     //     ])
    //     // select(['id','object_type'])
    //     where([
    //         ['raw','ILIKE', $data['search'].'%'],
    //         ])
    //     ->whereIn('object_type',[3,4])

    //     ->selectRaw('object_type, count(*) as total, object_id')
    //     ->groupBy(['object_id','object_type'])
    //     ->limit(10)
    //     ->get();

    //     $geocoder = [];
    //     foreach ($result as $key => $value) {
    //         switch ($value->object_type) {
    //             case 3:
    //                 $geocoder[]= Province::find($value->object_id);
    //                 break;
    //             case 4:
    //                 $geocoder[] = District::find($value->object_id);
    //                 break;
    //             case 5:
    //                 $geocoder[] = Ward::find($value->object_id);
    //                 break;
    //             default:
    //                 break;
    //         }
    //     }
    //     return $geocoder;
    // }


    //-------list hotsearch------------------
    public function listHotSearch()
    {
        // $result = Search::
        // where('object_type',4)
        // ->selectRaw('object_type, count(*) as total, object_id')
        // ->orderBy('total', 'desc')
        // ->groupBy(['object_id','object_type'])
        // ->limit(10)
        // ->get();

        $result = View::where('object_type',4)->orderBy('view', 'desc')->limit(10)->get();

        $hotsearch = [];
        foreach ($result as $key => $value) {
            // switch ($value->object_type) {
            //     case 3:
            //         $hotsearch[]= Province::find($value->object_id);
            //         break;
            //     case 4:
                    $district = District::find($value->object_id);

                    $province = Province::find($district->province_id);

                    $district->slug = $province->slug.'_'.$district->slug;
                    $hotsearch[] = $district;
            //         break;
            //     case 5:
            //         $hotsearch[] = Ward::find($value->object_id);
            //         break;
            //     default:
            //         break;
            // }
        }
        return $hotsearch;

    }

    public function _search(array $data)
    {

        if(empty($data['search']))
        {
            return [];
        }

        // $address = [];
        // $have = 0;
        $cent = isset($data['cent']) &&  $data['cent'] > 0 && $data['cent'] < 1? $data['cent'] : 0.7;
        $result = [];
        $province = Province::get();
        $arrSearch = explode('-',preg_replace('/[,-]/','-',$data['search']));
        // $arrSearch = array($data['search']);
        if(count($arrSearch) < 2)
        {
            $result = array_merge($result,$this->handleSearchP($arrSearch, $province, $cent));

            $district = District::get();
            $result = array_merge($result,$this->handleSearchD($arrSearch, $district, $cent));

            $ward = Ward::get();
            $result = array_merge($result,$this->handleSearchW($arrSearch, $ward, $cent));

        }
        else {
            $arrProvince = $this->handleSearchPU($arrSearch, $province, $cent);

            //---------found province-----------------
            $collectionP = collect($arrProvince);
            $sortedP = $collectionP->sortByDesc('similar')->values()->take(5)->toArray();

            $arrDistrict = [];
            if(count($sortedP) > 0 && $sortedP[0]['similar'] == 2)
            {
                $district = District::where('province_id',$sortedP[0]['id'])->get();
                $arrDistrict = $this->handleSearchDU($arrSearch, $district, $cent);
            }
            else {
                $district = District::get();
                $arrDistrict = $this->handleSearchDU($arrSearch, $district, $cent);
            }
            //-------------found district------------------------
            $collectionD = collect($arrDistrict);
            $sortedD = $collectionD->sortByDesc('similar')->values()->take(5)->toArray();

            $arrWard = [];
            if(count($sortedD) > 0  && $sortedD[0]['similar'] == 2)
            {
                $ward = Ward::where('district_id',$sortedD[0]['id'])->get();
                $arrWard = $this->handleSearchWU($arrSearch, $ward, $cent);
            }
            else {
                $ward = Ward::get();
                $arrWard = $this->handleSearchWU($arrSearch, $ward, $cent);
            }
            //-------------found ward------------------------
            $collectionW = collect($arrWard);
            $sortedW = $collectionW->sortByDesc('similar')->values()->take(5)->toArray();
            $result = array_merge($result,$sortedW,$sortedD,$sortedP);
        }

        //---------case C-----------------
        if(count($result) == 0)
        {
            $arrProvince = $this->handleSearchPC($arrSearch, $province, $cent);

            //---------found province-----------------
            $collectionP = collect($arrProvince);
            $sortedP = $collectionP->sortByDesc('similar')->values()->all();

            $arrDistrict = [];
            if(count($sortedP) > 0 && $sortedP[0]['similar'] == 2)
            {
                $district = District::where('province_id',$sortedP[0]['id'])->get();
                $arrDistrict = $this->handleSearchDC($arrSearch, $district, $cent);
            }
            else {
                $test = collect($sortedP);
                $test->filter(function ($value) {
                    return $value->similar == 1.5;
                })->all();
                $arrIdProvince = [];

                foreach ($test as $key => $value) {
                    $arrIdProvince[] = $value['id'];
                }

                if(count($arrIdProvince) > 0){
                    $district = District::whereIn('province_id', $arrIdProvince)->get();
                }
                else {
                    $district = District::get();
                }
                $district = District::get();
                $arrDistrict = $this->handleSearchDC($arrSearch, $district, $cent);
            }

            //-------------found district------------------------
            $collectionD = collect($arrDistrict);
            $sortedD = $collectionD->sortByDesc('similar')->values()->all();


            $arrWard = [];
            if(count($sortedD) > 0  && $sortedD[0]['similar'] == 2)
            {
                $ward = Ward::where('district_id',$sortedD[0]['id'])->get();
                $arrWard = $this->handleSearchWC($arrSearch, $ward, $cent);
            }
            else {
                $test = collect($sortedD);
                $test->filter(function ($value) {
                    return $value->similar == 1.5;
                })->all();
                $arrIdDistrict = [];

                foreach ($test as $key => $value) {
                    $arrIdDistrict[] = $value['id'];
                }

                if(count($arrIdDistrict) > 0){
                    $ward = Ward::whereIn('district_id', $arrIdDistrict)->get();
                }
                else {
                    $ward = Ward::get();
                }

                $arrWard = $this->handleSearchWC($arrSearch, $ward, $cent);
            }

            //-------------found ward------------------------
            $collectionW = collect($arrWard);
            $sortedW = $collectionW->sortByDesc('similar')->values()->all();
            $result = array_merge($result,$sortedW,$sortedD,$sortedP);

        }

        //-------sort result-----------------
        $collection = collect($result);
        $sorted = json_decode(json_encode($collection->sortByDesc('similar')->values()->take(10)));

        $search = [];
        foreach ($sorted as $key => $value) {
            if(isset($value->province_id))
            {
                $pro = Province::select('name')->find($value->province_id);
                $value->address = $value->name.', '.$pro->name;
                $search[] = $value;

            }
            elseif(isset($value->district_id)) {
                $dis = District::select('name','province_id')->find($value->district_id);
                $pro = Province::select('name')->find($dis->province_id);
                $value->address   = $value->name.', '.$dis->name.', '.$pro->name;

                $search[] = $value;

            } else {
                $value->address = $value->name;
                $search[] = $value;
            }

        }
        return $search;
        // if($address)
        // {
        //     switch ($have) {
        //         case 1:
        //             $sortedP[0]->address = implode(',',array_reverse($address));
        //             $result = $sortedP;
        //             break;
        //         case 2:
        //             $sortedD[0]->address = implode(',',array_reverse($address));
        //             $result = $sortedD;
        //             break;
        //         case 3:
        //             // $sortedW[0]->address = implode(',',array_reverse($address));
        //             // $result = $sortedW;
        //             $sortedD[0]->address = implode(',',array_reverse($address));
        //             $result = $sortedD;
        //             break;
        //         default:
        //             $result = [];
        //             break;
        //     }
        //     return $result;
        // }

        // return [];

    }
    //-------------province------------------------
    public function handleSearchP(array $arr, $address, $cent = 0.7)
    {
        // $check = false;
        $result = [];
        $priority = [];
        for ($i=0; $i < count($arr) ; $i++) {
            $slug = Str::slug($arr[$i]);
            $len = strlen($slug);
            $odd = $len * 0.3;
            $percent = $len * $cent;

            foreach ($address as $key => $value) {
                // if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd) && !in_array($value->id, $priority))

                $num = similar_text($slug,$value->slug);
                if($num == $len && $num == strlen($value->slug))
                {
                    $value['similar'] = 2;
                    $result[] = $value;
                    $priority[] = $value->id;
                    break;
                }
                if($num >= $percent)
                {
                    $s = explode("\\", addcslashes($slug, 'a..z-'));
                    $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                    $i = 1;
                    $j = 1;
                    $dem = 0;

                    while($i < count($s) && $j < count($s1) && $dem <= $len)
                    {

                        // if($dem == 0 && $j > count($s)/2)
                        // {
                        //     break;
                        // }

                        if($s[$i] === $s1[$j])
                        {
                            $i++;
                            $j++;
                            $dem++;
                        }else
                        {
                            if($dem > 0)
                            {
                                break;
                            }
                            $j++;
                        }

                    }
                    if($dem == $len)
                    {
                        $value['similar'] = 1.5;
                        $result[] = $value;
                    }
                    else {
                        $value['similar'] = round($num/$len,5,PHP_ROUND_HALF_DOWN);
                        $result[] = $value;
                    }

                }

            }
            // if($check == true)
            // {
            //     break;
            // }
        }

        // if(empty($result) && empty($priority))
        if(empty($result))
        {
            $tring = ['Thành phố '];
            for ($i=0; $i < count($arr) ; $i++) {
                foreach ($tring as $key ) {
                    $slug = Str::slug($key.$arr[$i]);
                    $lenKey = strlen($key);
                    $len = strlen($slug);
                    $odd = $len * 0.3;
                    $percent = ($len - $lenKey) * $cent;

                    foreach ($address as $key => $value) {
                        // if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd) && !in_array($value->id, $priority))

                        $num = similar_text($slug,$value->slug);
                        if($num == $len && $num == strlen($value->slug))
                        {
                            $value['similar'] = 2;
                            $result[] = $value;
                            $priority[] = $value->id;
                            // $check = true;
                            break;
                        }
                        if(($num - $lenKey) >= $percent)
                        {
                            $s = explode("\\", addcslashes($slug, 'a..z-'));
                            $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                            $i = $lenKey + 1;
                            $j = $lenKey + 1;
                            $dem = $lenKey;

                            while($i < count($s) && $j < count($s1) && $dem < count($s))
                            {

                                // if($dem == 0 && ($j - $lenKey) > (count($s) - $lenKey)/2)
                                // {
                                //     break;
                                // }

                                if($s[$i] === $s1[$j])
                                {
                                    $i++;
                                    $j++;
                                    $dem++;
                                }else
                                {
                                    if($dem > 0)
                                    {
                                        break;
                                    }
                                    $j++;
                                }

                            }
                            if($dem == $len)
                            {
                                $value['similar'] = 1.5;
                                $result[] = $value;
                            }
                            else {
                                $value['similar'] = round(($num - $lenKey)/($len - $lenKey),5,PHP_ROUND_HALF_DOWN);
                                $result[] = $value;
                            }
                            // $value['similar'] = ($num - $lenKey)/($len - $lenKey);
                            // $value['similar'] = round(($num - $lenKey)/($len - $lenKey),5,PHP_ROUND_HALF_DOWN);
                            // $result[] = $value;
                        }

                    }
                    // if($check == true)
                    // {
                    //     break;
                    // }
                }

                // if($check == true)
                // {
                //     break;
                // }
            }
        }

        return $result;
    }
    //-------------province upgrade------------------------
    public function handleSearchPU(array $arr, $address, $cent = 0.7)
    {
        $check = false;
        $result = [];
        $priority = [];

        for ($k=0; $k < count($arr) ; $k++)
        {
            $slug = Str::slug($arr[$k]);
            $len = strlen($slug);
            $odd = $len * 0.3;
            $percent = $len * $cent;
            foreach ($address as $key => $value)
            {
                if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
                {

                    $num = similar_text($slug,$value->slug);
                    if($num == $len && $num == strlen($value->slug))
                    {
                        $value['similar'] = 2;
                        $priority[] = $value;
                        $check = true;
                        break;
                    }
                    elseif($num >= $percent)
                    {

                        // $s = explode("\\", addcslashes($slug, 'a..z-'));
                        // $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                        // $i = 1;
                        // $j = 1;
                        // $dem = 0;

                        // while($i < count($s) && $j < count($s1) && $dem <= $len)
                        // {

                        //     if($dem == 0 && $j > count($s)/2)
                        //     {
                        //         break;
                        //     }

                        //     if($s[$i] === $s1[$j])
                        //     {
                        //         $i++;
                        //         $j++;
                        //         $dem++;
                        //     }else
                        //     {
                        //         if($dem > 0)
                        //         {
                        //             break;
                        //         }
                        //         $j++;
                        //     }

                        // }
                        // if($dem == $len)
                        // {
                        //     $value['similar'] = 1.5;
                        //     $result[] = $value;
                        // }
                        // else {
                        //     $value['similar'] = round($num/$len,5,PHP_ROUND_HALF_DOWN);
                        //     $result[] = $value;
                        // }
                        $value['similar'] = $num/$len;
                        $result[] = $value;
                    }
                }
            }

            if($check == true)
            {

                break;
            }
        }
        if(empty($result) && empty($priority))
        {
            $tring = ['Thành phố '];
            for ($k=0; $k < count($arr) ; $k++) {
                foreach ($tring as $key ) {
                    $slug = Str::slug($key.$arr[$k]);
                    $lenKey = strlen($key);
                    $len = strlen($slug);
                    $odd = $len * 0.3;
                    $percent = ($len - $lenKey) * $cent;

                    foreach ($address as $key => $value) {
                        if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
                        {
                            $num = similar_text($slug,$value->slug);
                            if($num == $len && $num == strlen($value->slug))
                            {
                                $value['similar'] = 2;
                                $priority[] = $value;
                                $check = true;
                                break;
                            }
                            elseif(($num - $lenKey) >= $percent)
                            {
                                // $s = explode("\\", addcslashes($slug, 'a..z-'));
                                // $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                                // $i = $lenKey + 1;
                                // $j = $lenKey + 1;
                                // $dem = $lenKey;

                                // while($i < count($s) && $j < count($s1) && $dem < count($s))
                                // {

                                //     if($dem == 0 && ($j - $lenKey) > (count($s) - $lenKey)/2)
                                //     {
                                //         break;
                                //     }

                                //     if($s[$i] === $s1[$j])
                                //     {
                                //         $i++;
                                //         $j++;
                                //         $dem++;
                                //     }else
                                //     {
                                //         if($dem > 0)
                                //         {
                                //             break;
                                //         }
                                //         $j++;
                                //     }

                                // }
                                // if($dem == $len)
                                // {
                                //     $value['similar'] = 1.5;
                                //     $result[] = $value;
                                // }
                                // else {
                                //     $value['similar'] = ($num - $lenKey)/($len - $lenKey);
                                //     $result[] = $value;
                                // }
                                $value['similar'] = ($num - $lenKey)/($len - $lenKey);
                                $result[] = $value;
                            }
                        }
                    }
                    if($check == true)
                    {
                        break;
                    }
                }

                if($check == true)
                {
                    break;
                }
            }
        }

        return $priority ? $priority : $result;
    }

    //-------------province upgrade case C------------------------
    public function handleSearchPC(array $arr, $address, $cent = 0.7)
    {
        $check = false;
        $result = [];
        $priority = [];
        $result2 = [];
        $priority2 = [];
        $slug = Str::slug($arr[0]);
        $string ='thanh-pho';
        $len = strlen($slug);
        $odd = $len * 0.3;
        $percent = $len * $cent;

        foreach ($address as $key => $value)
        {

            $checkBase = false;
            if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
            {
                //case 1
                $checkBase = true;
                $num = similar_text($slug,$value->slug);
                if($num == $len && $num == strlen($value->slug))
                {
                    $value['similar'] = 2;
                    $priority[] = $value;
                    $check = true;
                    $this->_check = true;

                    break;
                }
                elseif($num >= $percent)
                {
                    $this->_check = true;
                    $value['similar'] = $num/$len;
                    $result[] = $value;

                }

            }

            if($checkBase === false)
            {
                $text = 'Thành phố ';
                // case 2
                $slugB = Str::slug($text.$slug);
                $lenKey = strlen($text);
                $lenB = strlen($slugB);
                $oddB = $lenB * 0.3;
                $percentB = ($lenB - $lenKey) * $cent;

                if(strlen($value->slug) > ($lenB - (int)$oddB)  && strlen($value->slug) < ($lenB + (int)$oddB))
                {
                    //case 2
                    $checkBase = true;
                    $numB = similar_text($slugB,$value->slug);
                    if($numB == $lenB && $numB == strlen($value->slug))
                    {
                        $value['similar'] = 2;
                        $priority[] = $value;
                        $check = true;
                        $this->_check = true;

                        break;
                    }
                    elseif(($numB - $lenKey) >= $percentB)
                    {
                        $this->_check = true;
                        $value['similar'] = ($numB - $lenKey)/($lenB - $lenKey);
                        $result[] = $value;

                    }

                }
            }

            if($this->_check === false){
                $arrSlug = explode('-',$value->slug);
                $checkSlug = strpos($arrSlug[0].'-'.$arrSlug[1], $string);

                if($checkSlug === false)
                {
                    $slugV = $value->slug;
                }
                else {
                    unset($arrSlug[0],$arrSlug[1]);
                    $slugV = implode('-',$arrSlug);
                }

                $lenV = strlen($slugV);
                $num = similar_text($slugV,$slug);

                if($num >= ($lenV * $cent))
                {

                    $s = explode("\\", addcslashes($slug, 'a..z-'));
                    $s1 =  explode("\\", addcslashes($slugV, 'a..z-'));
                    $i = 1;
                    $j = 1;
                    $dem = 0;

                    while($i < count($s) && $j < count($s1) && $dem < $lenV)
                    {

                        if($s[$i] === $s1[$j])
                        {
                            $i++;
                            $j++;
                            $dem++;
                        }else
                        {
                            if($dem > 0)
                            {
                                $i++;
                                $j = 1;
                                $dem = 0;
                            }
                            $i++;
                        }

                    }
                    if($dem == $lenV)
                    {
                        $value['similar'] = 1.5;
                        $priority2[] = $value;
                    }
                    else {
                        $value['similar'] = round($num/$lenV,5,PHP_ROUND_HALF_DOWN);
                        $result2[] = $value;
                    }
                }

            }
            if($check == true)
            {
                break;
            }
        }

        if($this->_check === true)
        {
            return $priority ? $priority : $result;
        }
        else {
            return $priority2 ? $priority2 : $result2;
        }
    }

    //-----------district----------------------------
    public function handleSearchD(array $arr, $address, $cent = 0.7)
    {
        // $check = false;
        $result = [];
        $priority = [];

        for ($i=0; $i < count($arr) ; $i++) {
            $slug = Str::slug($arr[$i]);
            $len = strlen($slug);
            $odd = $len * 0.3;
            $percent = $len * $cent;

            foreach ($address as $key => $value) {
                // if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd) && !in_array($value->id, $priority))
                // {
                    $num = similar_text($slug,$value->slug);
                    if($num == $len && $num == strlen($value->slug))
                    {
                        $value['similar'] = 2;
                        $result[] = $value;
                        // $priority[] = $value->id;
                        // $check = true;
                        // break;
                    }
                    elseif($num >= $percent)
                    {
                        $s = explode("\\", addcslashes($slug, 'a..z-'));
                        $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                        $i = 1;
                        $j = 1;
                        $dem = 0;

                        while($i < count($s) && $j < count($s1) && $dem <= $len)
                        {

                            // if($dem == 0 && $j > count($s)/2)
                            // {
                            //     break;
                            // }

                            if($s[$i] === $s1[$j])
                            {
                                $i++;
                                $j++;
                                $dem++;
                            }else
                            {
                                if($dem > 0)
                                {
                                    break;
                                }
                                $j++;
                            }

                        }
                        if($dem == $len)
                        {
                            $value['similar'] = 1.5;
                            $result[] = $value;
                        }
                        else {
                            $value['similar'] = round($num/$len,5,PHP_ROUND_HALF_DOWN);
                            $result[] = $value;
                        }
                        // $value['similar'] = round($num/$len,5,PHP_ROUND_HALF_DOWN);
                        // $result[] = $value;
                    }
                // }
            }
            // if($check == true)
            // {
            //     break;
            // }
        }

        if(empty($result))
        {
            $tring = ['Quận ', 'Thành phố ','Thị xã ', 'Huyện '];
            for ($i=0; $i < count($arr) ; $i++) {

                foreach ($tring as $key ) {
                    $slug = Str::slug($key.$arr[$i]);
                    $lenKey = strlen($key);
                    $len = strlen($slug);
                    $odd = $len * 0.3;
                    $percent = ($len - $lenKey) * $cent;

                    foreach ($address as $key => $value) {
                        // if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd) && !in_array($value->id, $priority))
                        // {
                            $num = similar_text($slug,$value->slug);
                            if($num == $len && $num == strlen($value->slug))
                            {
                                $value['similar'] = 2;
                                $result[] = $value;
                                // $priority[] = $value->id;
                                // $check = true;
                                // break;
                            }
                            elseif(($num - $lenKey) >= $percent)
                            {
                                $s = explode("\\", addcslashes($slug, 'a..z-'));
                                $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                                $i = $lenKey + 1;
                                $j = $lenKey + 1;
                                $dem = $lenKey;

                                while($i < count($s) && $j < count($s1) && $dem < count($s))
                                {

                                    // if($dem == 0 && ($j - $lenKey) > (count($s) - $lenKey)/2)
                                    // {
                                    //     break;
                                    // }

                                    if($s[$i] === $s1[$j])
                                    {
                                        $i++;
                                        $j++;
                                        $dem++;
                                    }else
                                    {
                                        if($dem > 0)
                                        {
                                            break;
                                        }
                                        $j++;
                                    }

                                }
                                if($dem == $len)
                                {
                                    $value['similar'] = 1.5;
                                    $result[] = $value;
                                }
                                else {
                                    $value['similar'] = round(($num - $lenKey)/($len - $lenKey),5,PHP_ROUND_HALF_DOWN);
                                    $result[] = $value;
                                }
                                // $value['similar'] = round(($num - $lenKey)/($len - $lenKey),5,PHP_ROUND_HALF_DOWN);
                                // $result[] = $value;
                            }
                        // }
                    }
                    // if($check == true)
                    // {
                    //     break;
                    // }
                }

                // if($check == true)
                // {
                //     break;
                // }
            }
        }

        // return $priority ? $priority : $result;
        return $result;
    }
    //-----------district upgrade----------------------------
    public function handleSearchDU(array $arr, $address, $cent = 0.7)
    {
        $check = false;
        $result = [];
        $priority = [];

        for ($k=0; $k < count($arr) ; $k++) {
            $slug = Str::slug($arr[$k]);
            $len = strlen($slug);
            $odd = $len * 0.3;
            $percent = $len * $cent;
            foreach ($address as $key => $value) {
                if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
                {
                    $num = similar_text($slug,$value->slug);
                    if($num == $len && $num == strlen($value->slug))
                    {
                        $value['similar'] = 2;
                        $priority[] = $value;
                        $check = true;
                        break;
                    }
                    elseif($num >= $percent)
                    {
                        // $s = explode("\\", addcslashes($slug, 'a..z-'));
                        // $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                        // $i = 1;
                        // $j = 1;
                        // $dem = 0;

                        // while($i < count($s) && $j < count($s1) && $dem <= $len)
                        // {

                        //     if($dem == 0 && $j > count($s)/2)
                        //     {
                        //         break;
                        //     }

                        //     if($s[$i] === $s1[$j])
                        //     {
                        //         $i++;
                        //         $j++;
                        //         $dem++;
                        //     }else
                        //     {
                        //         if($dem > 0)
                        //         {
                        //             break;
                        //         }
                        //         $j++;
                        //     }

                        // }
                        // if($dem == $len)
                        // {
                        //     $value['similar'] = 1.5;
                        //     $result[] = $value;
                        // }
                        // else {
                        //     $value['similar'] = round($num/$len,5,PHP_ROUND_HALF_DOWN);
                        //     $result[] = $value;
                        // }
                        $value['similar'] = $num/$len;
                        $result[] = $value;
                    }
                }
            }
            if($check == true)
            {
                break;
            }
        }
        if(empty($result) && empty($priority))
        {
            $tring = ['Quận ', 'Thành phố ','Thị xã ', 'Huyện '];
            for ($k=0; $k < count($arr) ; $k++) {

                foreach ($tring as $key ) {
                    $slug = Str::slug($key.$arr[$k]);
                    $lenKey = strlen($key);
                    $len = strlen($slug);
                    $odd = $len * 0.3;
                    $percent = ($len - $lenKey) * $cent;

                    foreach ($address as $key => $value) {
                        if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
                        {
                            $num = similar_text($slug,$value->slug);
                            if($num == $len && $num == strlen($value->slug))
                            {
                                $value['similar'] = 2;
                                $priority[] = $value;
                                $check = true;
                                break;
                            }
                            elseif(($num - $lenKey) >= $percent)
                            {
                                // $s = explode("\\", addcslashes($slug, 'a..z-'));
                                // $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                                // $i = $lenKey + 1;
                                // $j = $lenKey + 1;
                                // $dem = $lenKey;

                                // while($i < count($s) && $j < count($s1) && $dem < count($s))
                                // {

                                //     if($dem == 0 && ($j - $lenKey) > (count($s) - $lenKey)/2)
                                //     {
                                //         break;
                                //     }

                                //     if($s[$i] === $s1[$j])
                                //     {
                                //         $i++;
                                //         $j++;
                                //         $dem++;
                                //     }else
                                //     {
                                //         if($dem > 0)
                                //         {
                                //             break;
                                //         }
                                //         $j++;
                                //     }

                                // }
                                // if($dem == $len)
                                // {
                                //     $value['similar'] = 1.5;
                                //     $result[] = $value;
                                // }
                                // else {
                                //     $value['similar'] = ($num - $lenKey)/($len - $lenKey);
                                //     $result[] = $value;
                                // }
                                $value['similar'] = ($num - $lenKey)/($len - $lenKey);
                                $result[] = $value;
                            }
                        }
                    }
                    if($check == true)
                    {
                        break;
                    }
                }

                if($check == true)
                {
                    break;
                }
            }
        }

        return $priority ? $priority : $result;
    }

    //-----------district upgrade case C----------------------------
    public function handleSearchDC(array $arr, $address, $cent = 0.7)
    {
        $check = false;
        $result = [];
        $priority = [];
        $result2 = [];
        $priority2 = [];
        $slug =  Str::slug($arr[0]);
        $len = strlen($slug);
        $odd = $len * 0.3;
        $percent = $len * $cent;
        $string1 = ['quan', 'huyen'];
        $string2 = ['thanh-pho','thi-xa'];

        foreach ($address as $key => $value)
        {

            $checkBase = false;
            if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
            {
                //case 1

                $checkBase = true;
                $num = similar_text($slug,$value->slug);
                if($num == $len && $num == strlen($value->slug))
                {
                    $value['similar'] = 2;
                    $priority[] = $value;
                    $check = true;
                    $this->_check = true;
                    break;
                }
                elseif($num >= $percent)
                {
                    $this->_check = true;
                    $value['similar'] = $num/$len;
                    $result[] = $value;
                }

            }

            if($checkBase === false)
            {

                $text = ['Quận ', 'Thành phố ','Thị xã ', 'Huyện '];

                foreach ($text as $key )
                {
                    $slugB = Str::slug($key.$slug);
                    $lenKey = strlen($key);
                    $lenB = strlen($slugB);
                    $oddB = $lenB * 0.3;
                    $percentB = ($lenB - $lenKey) * $cent;

                    if(strlen($value->slug) > ($lenB - (int)$oddB)  && strlen($value->slug) < ($lenB + (int)$oddB))
                    {
                        //case 2

                        $numB = similar_text($slugB,$value->slug);
                        if($numB == $lenB && $numB == strlen($value->slug))
                        {
                            $value['similar'] = 2;
                            $priority[] = $value;
                            $check = true;
                            $this->_check = true;
                            break;
                        }
                        elseif(($numB - $lenKey) >= $percentB)
                        {

                            $this->_check = true;
                            $value['similar'] = ($numB - $lenKey)/($lenB - $lenKey);
                            $result[] = $value;
                        }

                    }

                }
            }

            if($this->_check === false)
            {
                $arrSlug = explode('-',$value->slug);

                $checkSlug = false;
                foreach ($string1 as $key ) {

                    if(strpos($arrSlug[0], $key) !== false)
                    {
                        unset($arrSlug[0]);
                        $checkSlug = true;
                        break;
                    }

                }

                if($checkSlug == false)
                {
                    foreach ($string2 as $key ) {
                        if(strpos($arrSlug[0].'-'.$arrSlug[1], $key) !== false)
                        {
                            unset($arrSlug[0],$arrSlug[1]);
                            // $checkSlug = true;
                            break;
                        }
                    }
                }
                $slugV = implode('-',$arrSlug);
                $lenV = strlen($slugV);
                $num = similar_text($slugV,$slug);


                if($num >= ($lenV * $cent))
                {
                    $s = explode("\\", addcslashes($slug, 'a..z-'));
                    $s1 =  explode("\\", addcslashes($slugV, 'a..z-'));
                    $i = 1;
                    $j = 1;
                    $dem = 0;

                    while($i < count($s) && $j < count($s1) && $dem < $lenV)
                    {

                        if($s[$i] === $s1[$j])
                        {
                            $i++;
                            $j++;
                            $dem++;
                        }else
                        {
                            if($dem > 0)
                            {
                                $i++;
                                $j = 1;
                                $dem = 0;
                            }
                            $i++;
                        }

                    }
                    if($dem == $lenV)
                    {
                        $value['similar'] = 1.5;
                        $priority2[] = $value;
                        // $check = true;
                    }
                    else {
                        $value['similar'] = round($num/$lenV,5,PHP_ROUND_HALF_DOWN);
                        $result2[] = $value;
                    }
                    // $value['similar'] = $num/$len;
                    // $result[] = $value;
                }
            }


            if($check == true)
            {
                break;
            }
        }
        die(json_encode($result2));
        if($this->_check === true)
        {
            return $priority ? $priority : $result;
        }
        else {
            return $priority2 ? $priority2 : $result2;
        }
    }

    //-------------ward------------------------
    public function handleSearchW(array $arr, $address, $cent = 0.7)
    {
        // $check = false;
        $result = [];
        $priority = [];
        for ($i=0; $i < count($arr) ; $i++) {
            $slug = Str::slug($arr[$i]);
            $len = strlen($slug);
            $odd = $len * 0.3;
            $percent = $len * $cent;
            // var_dump($len,$slug);
            foreach ($address as $key => $value) {
                // if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd) && !in_array($value->id, $priority))
                // {
                    $num = similar_text($slug,$value->slug);
                    if($num == $len && $num == strlen($value->slug))
                    {

                        $value['similar'] = 2;
                        $result[] = $value;
                        // $priority[] = $value->id;
                        // $check = true;
                        // break;
                    }elseif($num >= $percent)
                    {
                        // var_dump($num);
                        // var_dump($value->slug);
                        $s = explode("\\", addcslashes($slug, 'a..z-'));
                        $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                        $i = 1;
                        $j = 1;
                        $dem = 0;

                        while($i < count($s) && $j < count($s1) && $dem <= $len)
                        {

                            // if($dem == 0 && $j > count($s)/2)
                            // {
                            //     break;
                            // }

                            if($s[$i] === $s1[$j])
                            {
                                $i++;
                                $j++;
                                $dem++;
                            }else
                            {
                                if($dem > 0)
                                {
                                    break;
                                }
                                $j++;
                            }

                        }
                        if($dem == $len)
                        {
                            $value['similar'] = 1.5;
                            $result[] = $value;
                        }
                        else {
                            $value['similar'] = round($num/$len,5,PHP_ROUND_HALF_DOWN);
                            $result[] = $value;
                        }
                        // $value['similar'] = round($num/$len,5,PHP_ROUND_HALF_DOWN);
                        // $result[] = $value;
                    }
                // }
            }
            // if($check == true)
            // {
            //     break;
            // }
        }

        if(empty($result))
        {
            $tring = ['Phường ', 'Thị trấn ','Xã '];
            for ($i=0; $i < count($arr) ; $i++) {
                foreach ($tring as $key ) {
                    $slug = Str::slug($key.$arr[$i]);
                    $lenKey = strlen($key);
                    $len = strlen($slug);
                    $odd = $len * 0.3;
                    $percent = ($len - $lenKey) * $cent;

                    foreach ($address as $key => $value) {
                        // if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd) && !in_array($value->id, $priority))
                        // {
                            $num = similar_text($slug,$value->slug);
                            if($num == $len && $num == strlen($value->slug))
                            {
                                $value['similar'] = 2;
                                $result[] = $value;
                                // $priority[] = $value->id;
                                // $check = true;
                                // break;
                            }
                            elseif(($num - $lenKey) >= $percent)
                            {
                                $s = explode("\\", addcslashes($slug, 'a..z-'));
                                $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                                $i = $lenKey + 1;
                                $j = $lenKey + 1;
                                $dem = $lenKey;

                                while($i < count($s) && $j < count($s1) && $dem < count($s))
                                {

                                    // if($dem == 0 && ($j - $lenKey) > (count($s) - $lenKey)/2)
                                    // {
                                    //     break;
                                    // }

                                    if($s[$i] === $s1[$j])
                                    {
                                        $i++;
                                        $j++;
                                        $dem++;
                                    }else
                                    {
                                        if($dem > 0)
                                        {
                                            break;
                                        }
                                        $j++;
                                    }

                                }
                                if($dem == $len)
                                {
                                    $value['similar'] = 1.5;
                                    $result[] = $value;
                                }
                                else {
                                    $value['similar'] = round(($num - $lenKey)/($len - $lenKey),5,PHP_ROUND_HALF_DOWN);
                                    $result[] = $value;
                                }
                                $value['similar'] = round(($num - $lenKey)/($len - $lenKey),5,PHP_ROUND_HALF_DOWN);
                                $result[] = $value;
                            }
                        // }
                    }
                    // if($check == true)
                    // {
                    //     break;
                    // }
                }

                // if($check == true)
                // {
                //     break;
                // }
            }
        }
        return $result;
    }
    //-------------ward upgrade------------------------
    public function handleSearchWU(array $arr, $address, $cent = 0.7)
    {
        $check = false;
        $result = [];
        $priority = [];

        for ($k=0; $k < count($arr) ; $k++) {
            $slug = Str::slug($arr[$k]);
            $len = strlen($slug);
            $odd = $len * 0.3;
            $percent = $len * $cent;
            foreach ($address as $key => $value) {
                if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
                {
                    $num = similar_text($slug,$value->slug);
                    if($num == $len && $num == strlen($value->slug))
                    {
                        $value['similar'] = 2;
                        $priority[] = $value;
                        // $check = true;
                        // break;
                    }
                    elseif($num >= $percent)
                    {
                        // $s = explode("\\", addcslashes($slug, 'a..z-'));
                        // $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                        // $i = 1;
                        // $j = 1;
                        // $dem = 0;

                        // while($i < count($s) && $j < count($s1) && $dem <= $len)
                        // {

                        //     if($dem == 0 && $j > count($s)/2)
                        //     {
                        //         break;
                        //     }

                        //     if($s[$i] === $s1[$j])
                        //     {
                        //         $i++;
                        //         $j++;
                        //         $dem++;
                        //     }else
                        //     {
                        //         if($dem > 0)
                        //         {
                        //             break;
                        //         }
                        //         $j++;
                        //     }

                        // }
                        // if($dem == $len)
                        // {
                        //     $value['similar'] = 1.5;
                        //     $result[] = $value;
                        // }
                        // else {
                        //     $value['similar'] = round($num/$len,5,PHP_ROUND_HALF_DOWN);
                        //     $result[] = $value;
                        // }
                        $value['similar'] = $num/$len;
                        $result[] = $value;
                    }
                }
            }
            // if($check == true)
            // {
            //     break;
            // }
        }

        if(empty($result) && empty($priority))
        {
            $tring = ['Phường ', 'Thị trấn ','Xã '];
            for ($k=0; $k < count($arr) ; $k++) {
                foreach ($tring as $key ) {
                    $slug = Str::slug($key.$arr[$k]);
                    $lenKey = strlen($key);
                    $len = strlen($slug);
                    $odd = $len * 0.3;
                    $percent = ($len - $lenKey) * $cent;

                    foreach ($address as $key => $value) {
                        if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
                        {
                            $num = similar_text($slug,$value->slug);
                            if($num == $len && $num == strlen($value->slug))
                            {
                                $value['similar'] = 2;
                                $priority[] = $value;
                                // $check = true;
                                // break;
                            }
                            elseif(($num - $lenKey) >= $percent)
                            {
                                // $s = explode("\\", addcslashes($slug, 'a..z-'));
                                // $s1 =  explode("\\", addcslashes($value->slug, 'a..z-'));
                                // $i = $lenKey + 1;
                                // $j = $lenKey + 1;
                                // $dem = $lenKey;

                                // while($i < count($s) && $j < count($s1) && $dem < count($s))
                                // {

                                //     if($dem == 0 && ($j - $lenKey) > (count($s) - $lenKey)/2)
                                //     {
                                //         break;
                                //     }

                                //     if($s[$i] === $s1[$j])
                                //     {
                                //         $i++;
                                //         $j++;
                                //         $dem++;
                                //     }else
                                //     {
                                //         if($dem > 0)
                                //         {
                                //             break;
                                //         }
                                //         $j++;
                                //     }

                                // }
                                // if($dem == $len)
                                // {
                                //     $value['similar'] = 1.5;
                                //     $result[] = $value;
                                // }
                                // else {
                                //     $value['similar'] = ($num - $lenKey)/($len - $lenKey);
                                //     $result[] = $value;
                                // }
                                $value['similar'] = ($num - $lenKey)/($len - $lenKey);
                                $result[] = $value;
                            }
                        }
                    }
                    // if($check == true)
                    // {
                    //     break;
                    // }
                }

                // if($check == true)
                // {
                //     break;
                // }
            }
        }
        return $priority ? $priority : $result;
    }

    //-------------ward upgrade case C------------------------
    public function handleSearchWC(array $arr, $address, $cent = 0.7)
    {
        $check = false;
        $result = [];
        $priority = [];
        $result2 = [];
        $priority2 = [];
        $slug = Str::slug($arr[0]);
        $len = strlen($slug);
        $odd = $len * 0.3;
        $percent = $len * $cent;
        $string1 = ['phuong', 'xa'];
        $string2 = 'thi-tran';

        foreach ($address as $key => $value)
        {
            $checkBase = false;
            if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
            {
                //case 1
                $checkBase = true;
                $num = similar_text($slug,$value->slug);
                if($num == $len && $num == strlen($value->slug))
                {
                    $value['similar'] = 2;
                    $priority[] = $value;
                    // $check = true;
                    $this->_check = true;
                    break;
                }
                elseif($num >= $percent)
                {
                    $this->_check = true;
                    $value['similar'] = $num/$len;
                    $result[] = $value;
                }

            }

            if($checkBase === false)
            {

                $text = ['Phường ', 'Thị trấn ','Xã '];

                foreach ($text as $key )
                {
                    $slugB = Str::slug($key.$slug);
                    $lenKey = strlen($key);
                    $lenB = strlen($slugB);
                    $oddB = $lenB * 0.3;
                    $percentB = ($lenB - $lenKey) * $cent;


                    if(strlen($value->slug) > ($lenB - (int)$oddB)  && strlen($value->slug) < ($lenB + (int)$oddB))
                    {
                        //case 2
                        $numB = similar_text($slugB,$value->slug);
                        if($numB == $lenB && $numB == strlen($value->slug))
                        {
                            $value['similar'] = 2;
                            $priority[] = $value;
                            // $check = true;
                            $this->_check = true;
                            break;
                        }
                        elseif(($numB - $lenKey) >= $percentB)
                        {
                            $this->_check = true;
                            $value['similar'] = ($numB - $lenKey)/($lenB - $lenKey);
                            $result[] = $value;
                        }

                    }


                }
            }

            if($this->_check === false)
            {
                $arrSlug = explode('-',$value->slug);

                $checkSlug = false;
                foreach ($string1 as $key ) {
                    if(strpos($arrSlug[0], $key) !== false)
                    {
                        unset($arrSlug[0]);
                        $checkSlug = true;
                        break;
                    }
                }

                if($checkSlug == false)
                {
                    if(strpos($arrSlug[0].'-'.$arrSlug[1], $string2) !== false)
                    {
                        unset($arrSlug[0],$arrSlug[1]);
                    }
                }

                $slugV = implode('-',$arrSlug);
                // $slugV = $value->slug;
                $lenV = strlen($slugV);
                $num = similar_text($slugV,$slug);

                if($num >= ($lenV * $cent))
                {
                    $s = explode("\\", addcslashes($slug, 'a..z-'));
                    $s1 =  explode("\\", addcslashes($slugV, 'a..z-'));
                    $i = 1;
                    $j = 1;
                    $dem = 0;

                    while($i < count($s) && $j < count($s1) && $dem < $lenV)
                    {

                        if($s[$i] === $s1[$j])
                        {
                            $i++;
                            $j++;
                            $dem++;
                        }else
                        {
                            if($dem > 0)
                            {
                                $i++;
                                $j = 1;
                                $dem = 0;
                            }
                            $i++;
                        }

                    }
                    if($dem == $lenV)
                    {
                        $value['similar'] = 1.5;
                        $priority2[] = $value;
                    }
                    else {
                        $value['similar'] = round($num/$lenV,5,PHP_ROUND_HALF_DOWN);
                        $result2[] = $value;
                    }

                }
            }

            // if($check == true)
            // {
            //     break;
            // }
        }

        if($this->_check === true)
        {
            return $priority ? $priority : $result;
        }
        else {
            return $priority2 ? $priority2 : $result2;
        }
    }

    // search new v2
    public function searchV2(array $data)
    {

        if(empty($data['search']))
        {
            return [];
        }
        $cent = isset($data['cent']) &&  $data['cent'] > 0 && $data['cent'] < 1? $data['cent'] : 0.7;

        $result = Tag::where('slug', 'ILIKE', Str::slug($data['search']).'%')
        ->selectRaw('count(*) as total, object_id')
        ->selectRaw('count(*) as totalType, object_type')
        ->groupBy(['object_id', 'object_type'])->limit(10)->get();

        // $result = Search::where('slug', 'ILIKE', Str::slug($data['search']).'%')
        // ->selectRaw('count(*) as total, object_id')
        // ->selectRaw('count(*) as totalType, object_type')
        // ->groupBy(['object_id', 'object_type'])->limit(10)->get();

        $arr = [];

        if($result->count() == 0)
        {
            $tag = Tag::whereNotNull('object_type')->get();
            $slugSearch = Str::slug($data['search']);
            $len = strlen($slugSearch);
            $odd = $len * 0.3;
            $percent = $len * $cent;

            foreach ($tag as $key => $value)
            {
                if(strlen($value->slug) > ($len - (int)$odd)  && strlen($value->slug) < ($len + (int)$odd))
                {
                    $num = similar_text($slugSearch,$value->slug);
                    if($num >= $percent)
                    {
                        $value['similar'] = $num/$len;
                        $arr[] = $value;
                    }
                }
            }

            $collection = collect($arr);
            $result = $collection->sortByDesc('similar')->values()->take(10);

        }
        $search = [];
        foreach ($result as $key => $value) {

            switch ($value->object_type) {
                case config('constants.object_type_tag.province'):
                // case config('constants.object_type_view.province'):
                    $search[] = Province::find($value->object_id);
                    break;
                // case config('constants.object_type_view.district'):
                case config('constants.object_type_tag.district'):
                    $search[] = District::find($value->object_id);
                    break;
                // case config('constants.object_type_view.ward'):
                case config('constants.object_type_tag.ward'):
                    $search[] = Ward::find($value->object_id);
                    break;
                case config('constants.object_type_tag.place'):
                    $search[] = Place::find($value->object_id);
                    break;
                default:

                    break;
            }
        }

        return $search;
    }


}
