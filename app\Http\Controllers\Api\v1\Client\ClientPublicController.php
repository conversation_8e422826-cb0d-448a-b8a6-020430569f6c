<?php

namespace App\Http\Controllers\Api\v1\Client;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Role;
use App\Setting;
use Hash;
use App\Mail\SendMail;
use Mail;
use App\Notifications\NotificationUser;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\RoutesNotifications;
use App\Http\Requests\User\UserDeleteRequest;
use App\Http\Requests\User\UserResetRequest;
use App\Http\Requests\User\ConfirmEmailRequest;
use App\Services\Auth\AuthService;
use App\Services\Auth\AuthClientService;
use App\Services\Auth\RegisterService;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Illuminate\Http\JsonResponse;
// use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Services\Token\TokenService;
use App\Services\GeneralService;
use App\Services\ClientPublic\ClientPublicService;
use App\Services\Geocode\GeocodeService;
use App\Services\Product\ProductService;
use App\Services\Shop\ShopService;
use Illuminate\Support\Facades\Redis;
use App\Helpers\Helper;


class ClientPublicController extends Controller
{
    // ------------ Filter ---------------
    public function filter(Request $request, ClientPublicService $service)
    {
        $data = (array)$request->only([
            'search',
            'category_ids',
            'sortBy',
            // 'latitude_s',
            // 'longitude_s',
            // 'latitude_b',
            // 'longitude_b',
            'latitude_user',
            'longitude_user',
            'latitude',
            'longitude',
            'filter_type',
            'price_min',
            'price_max',
            'child_num',
            'map_data',
            'is_feature',
            'offset',
            'limit',
            'business_type_id',
            'is_sale_off',
            'is_suggest',
        ]);

        $result = $service->filterAll($data);
        if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => 'User_003_E_011'
           ];
           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => [
               'data' => $result,
           ],
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }


    //-----------zalo hashkey---------------
    public function addZaloHashkey(Request $request){
        $data = $request->only([
            'key_hash'
        ]);

        $service = new ClientPublicService();
        $result = $service->addZaloHashkey($data);

        if ($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => ['User_003_E_032'],
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function dashboard(Request $request, ClientPublicService $service){
        $data = (array)$request->only([
            'section',
            'search',
            'latitude_s',
            'longitude_s',
            'latitude_b',
            'longitude_b',
            'latitude_user',
            'longitude_user',
            'radius',
            'limit',
            'offset'
        ]);

        $session_key = substr($data['latitude_user'], 0, 4) . '_' . substr($data['longitude_user'], 0, 5) . '_' . $data['section']. '_' . $data['offset'];

        $tags = [
            env('APP_ENV').":".'api_responses',
            env('APP_ENV').":".'dashboard',
            env('APP_ENV').":".'dashboard_' . $session_key
        ];

        $cacheKey = env('APP_ENV').":"."dashboard_".$session_key;
        $result = null;
        $flushCache = $request->get('flush_cache') ?? 'false';
        if($flushCache == 'true'){
            // Redis::del($cacheKey);
            Helper::flushCacheByTag(env('APP_ENV').":".'dashboard');
        }
        if(!Redis::exists($cacheKey)){
            // echo 'no cache';
            $result = $service->get_dashboard($data);
            // $cacheRes = Redis::setex($cacheKey, 86400, json_encode($result));
            if($data['section'] != "hot_sale"){
                Helper::storeCacheWithTags($cacheKey, $result, $tags, 3600);
            }

            // var_dump($cacheRes);
        }else{
            // echo 'has cache';
            $result = json_decode(Redis::get($cacheKey));

        }

        // $result = $service->get_dashboard($data);
        //     if($result == false)
        //    {
        //        $response = [
        //            'status' => JsonResponse::HTTP_BAD_REQUEST,
        //            'body' => 'Something went wrong'
        //        ];
        //        return response()->json($response, JsonResponse::HTTP_OK);
        //    }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => [
               'data' => $result,
           ],
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------search-----------------
    public function search(Request $request, ClientPublicService $service)
    {
        $search = $request->only([
            'search',
            'limit',
            'offset'
        ]);
        $limit = isset($search['limit'])? $search['limit'] : 25;
        $offset = isset($search['offset'])? $search['offset'] : 0;
        $searchText = isset($search['search'])? $search['search'] : '';
        $result = $service->search($searchText, $limit, $offset, 'similarity_score' ,'DESC');

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //--------suggest search-----------------
    public function suggest_search(Request $request, ClientPublicService $service)
    {
        $data = $request->only([
            'search','latitude_user','longitude_user','radius',
        ]);
        $data['search'] = $data['search'] ?? '';
        $data['latitude_user'] = $data['latitude_user']  ?? '';
        $data['longitude_user'] = $data['longitude_user'] ?? '';

        $result = $service->suggest_search($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //--------search_by_address-----------------
    public function search_by_address(Request $request, GeocodeService $service)
    {
        $data = $request->only([
            'search','latitude_user','longitude_user','radius',
        ]);
        $data['search'] = $data['search'] ?? '';
        $data['latitude_user'] = $data['latitude_user']  ?? '';
        $data['longitude_user'] = $data['longitude_user'] ?? '';
        $result = $service->search_by_address($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //--------search_by_latlong-----------------
    public function search_by_latlong(Request $request, GeocodeService $service)
    {
        $data = $request->only([
            'search','latitude','longitude','radius',
        ]);
        $data['search'] = $data['search'] ?? '';
        $data['latitude'] = $data['latitude']  ?? '';
        $data['longitude'] = $data['longitude'] ?? '';
        $result = $service->search_by_latlong($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------reels-----------------
    public function reels(Request $request, ClientPublicService $service)
    {
        $data = $request->only([
            'search','latitude_user','longitude_user','radius',
        ]);
        $data['search'] = $data['search'] ?? '';
        $data['latitude_user'] = $data['latitude_user']  ?? '';
        $data['longitude_user'] = $data['longitude_user'] ?? '';

        $result = $service->reels($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }


    //--------reels-----------------
    public function orderDetail($id, ClientPublicService $service)
    {
        $result = $service->OrderDetailByShortCode($id);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------reels-----------------
    public function productDetail($id, ProductService $service)
    {
        $result = $service->detail($id, true);
        if ($result === false) {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                "message" => "product not found or disabled.",
                "message_code" => "Product_001_E_001",
                "errors" => [
                        "message" => "product not found or disabled.",
                        "field" => "id",
                        "message_code" => "Product_001_E_001",
                ],
                'body' => ''
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function searchProductsInShop(Request $request, ShopService $service)
    {
        $data = $request->only([
            'shop_id',
            'search',
            'category_ids',
            'limit',
            'offset',
            'sort_by',
            'product_type',
            'is_owner',
            'is_saleoff',
            'is_feature',
            'is_suggest'
        ]);
        $result = $service->searchProductsInShop($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => false
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }


    //-----------list shop------------------
    public function listShop(Request $request)
    {
        $data = $request->all();
        $id = Auth::user()->id;
        $service = new ClientPublicService($id);
        $result = $service->listShopByUser($data);
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => $result
        ], JsonResponse::HTTP_OK);
    }

}
