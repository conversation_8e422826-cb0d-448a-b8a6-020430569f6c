APP_NAME="Clomart Develop"
APP_ENV=develop
APP_KEY=base64:uG+eO1PsEDZep4YaO3NeKQtA/kwX848NJ29/w3KpGj0=
APP_DEBUG=true
APP_URL=https://api-dev.remagan.com/
CLIENT_SITE=https://dev.remagan.com/
ADMIN_EMAIL=<EMAIL>

PW_SALT=eO1PsEDZep4YaO3N
PW_IV=********@ArKrvso

ADMIN_HTTPS=true

DB_QUERY_LOG=true
LOG_CHANNEL=stack

DB_CONNECTION=pgsql
DB_HOST=*************
DB_PORT=5435
DB_DATABASE=remagan_dev
DB_USERNAME='account_dev'
DB_PASSWORD= 'ueH7eLndv639OQpo'

DB_CONNECTION_REPLICA=pgsqlReplica
DB_HOST_REPLICA=*************
DB_PORT_REPLICA=5436
DB_DATABASE_REPLICA=remagan_dev
DB_USERNAME_REPLICA='account_dev'
DB_PASSWORD_REPLICA= 'ueH7eLndv639OQpo'

DB_CONNECTION_GEOCODE=pgsqlGeocode
DB_HOST_GEOCODE=**************
DB_PORT_GEOCODE=5434
DB_DATABASE_GEOCODE=clomart
DB_USERNAME_GEOCODE='remagan_pro'
DB_PASSWORD_GEOCODE='ojasf9dehjert_j35f_kjhbUSFJu'



DB_CONNECTION_SP=mysqlSP
DB_HOST_SP=*************
DB_PORT_SP=33060
DB_DATABASE_SP=remagan_support
DB_USERNAME_SP='root'
DB_PASSWORD_SP='sdgNRO4sdgs1job@712'

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=*************
REDIS_PASSWORD=98we3y54fdfgG4y1I5xd
REDIS_PORT=6378

MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=adendmueouodtbft
MAIL_ENCRYPTION=tls

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=QASyFuqlQdQw5pBJe8SuqndZVIipBADYTat6RYmxUZRBeqTEyyZ2mKv23PIttffN

FILESYSTEM_DRIVER=minio

STORAGE_SERVICE=minio
MINIO_KEY=NOMNOMACCESSKEYAIUSF87q8934
MINIO_SECRET=NOMNOMSECRETKEY76a5sf7ytfdTFUtdutUToutd
MINIO_BUCKET=remagan.uploads
MINIO_ENDPOINT=https://s3-dev.nomnomland.net/
S3_HOST_PATH=https://s3-dev.nomnomland.net/remagan.uploads/

WATERMARK=https://s3-clomart.nomnomland.net/dev.nomnom.uploads/watermark-low.png
WATERMARK_SELECT=2
URL_IMAGE=remagan.uploads/

GOOGLE_KEY=AIzaSyAEAhp8pQH3iMmY-y0Qkb1TCqP0bn4j9HE
GOOGLE_KEY_CAPTCHA=6LfKHAAcAAAAAJJ3IB-XIsPOe3mAIm6Z3P4n3TPt

QUEUE_CONNECTION=database
QUEUE_DRIVER=database

CLIENT_URL=https://clomart.nomnomland.net/verify-account/
CLIENT_URL_FORGET_PASSWORD=https://clomart.nomnomland.net/verify-password/
DOMAIN=https://clomart.nomnomland.net/


CLIENT_DEV_URL=https://clomart.nomnomland.net/
VIEW_PROPERTY=home/
VIEW_BLOGS=blogs/detail/
VIEW_LOCATION=home/mua-ban-nha-dat-khu-vuc/
ZIP_SITEMAP_PATH=uploads/sitemap/sitemap.zip
SITEMAP_FOLDER=sitemap/
PROPERTY_EXPORT_FILE=uploads/export/property.xlsx
PROPERTY_CSV_EXPORT_FILE=uploads/export/property.xlsx
EXPORT_PROPERTY_TOPIC=exportPropertyDev/
JSON_FILE_PATH=uploads/jsonData/JsonData.json
JWT_TTL = 43200
CLIENT_SECURITY_URL=https://clomart.nomnomland.net/security
CLIENT_POLICY_URL=https://clomart.nomnomland.net/policy

SCOUT_ELASTIC_HOST=elastic:hY8KDMp6XpnnGMd@**************:9201
SCOUT_DRIVER=elastic
SCOUT_ELASTIC_INDEXER=bulk

ORIGIN_CLIENT=https://clomart.nomnomland.net
ORIGIN_ADMIN=https://admin-clomart.nomnomland.net
SECRET_KEY_ZALO=g20Zl1QHbZZDW9U7DpVU

IMPORT_PROPERTY_TOPIC=importPropertyDev/
IMPORT_PLACE_TOPIC=importPlaceDev/
IMPORT_POST_TOPIC=importPostDev/

GOOGLE_CLOUD_KEY=AIzaSyDzxh5Bnz_Hc9_AzQEiV-XJ2QUTe5thPd0
OCR_SELECT=2 # 1 - tessaract , 2 - annoration
CHECK_CRAWL=off

OTP_HOST=http://sandbox.sms.fpt.net/
OTP_CLIENT_ID=888a1ad38442ea0d8dcc6B75f843b32FD5d10c88
OTP_CLIENT_SECRET=f13F32a055082062d9c508ce81292880EF4987e4763e2D55509073872e8423dC4a6ca42d

ZALO_APP_ID=1735716509130650710
ZALO_OTP_HOST=https://oauth.zaloapp.com/
ZALO_CODE_VERIFER=oBUjuxslHKdpMrPPTa05mIN2chqwiudkjKKBWELSDFJ

# Ahamove
AHAMOVE_PARTNER_MOBILE=84879555799
AHAMOVE_PARTNER_NAME=Partner+Test
