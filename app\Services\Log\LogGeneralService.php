<?php
namespace App\Services\Log;

use App\LogGeneral;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\User;

class LogGeneralService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId)
        {
            $this->_userId = $userId;
        }
        else
        {
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    //---process insert log --------
    public function addLogImport(array $log)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $log['created_by'] = $user->id;
        }

        $result = LogGeneral::create($log);

        return $result;
    }
    public function add(array $log)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $log['created_by'] = $user->id;
        }

        $check = LogGeneral::where('object_id',$log['object_id'])->where('action',$log['action'])->first();

        if($check)
        {
            $log['id'] = $check->id;
            $result = $check->update($log);
        }
        else {
            $result = LogGeneral::create($log);
        }
        
        return $result;
    }
    // //-----process list log---------

    // public function list()
    // {
    //     $result = Log::with('created_by:id,name')->orderBy('created_at', 'desc')->get();

    //     return $result;
    // }

    // // -----process delete-----------

    // public function delete($id)
    // {
    //     $result = Log::find($id);

    //     $result->delete();

    //     return $result;
    // }
    public function update(array $log)
    {
        $result = LogGeneral::find($log['id']);
        $user = User::find($this->_userId);
        if($user)
        {
            $log['created_by'] = $user->id;
        }
        $result->update($log);
        return $result;
    }
}
