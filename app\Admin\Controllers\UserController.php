<?php

namespace App\Admin\Controllers;

use App\District;
use App\Province;
use App\User;
use App\Role;
use App\Ward;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Facades\Admin;

use App\Admin\Forms\Steps;
use App\Http\Controllers\Controller;
use Encore\Admin\Layout\Content;
use Encore\Admin\Widgets\MultipleSteps;

use App\Admin\Actions\User\Restore;
use App\Admin\Actions\Replicate;
use App\Admin\Actions\User\SetAgent;
use App\Admin\Actions\User\SetDriver;
use App\Admin\Actions\User\GenerateReferralCode;
use App\Admin\Actions\User\BatchGenerateReferralCode;
use App\Admin\Actions\EnableDisable;
use App\Admin\Actions\EnableDisable2;
use App\Admin\Actions\BatchDelete;

class UserController extends AdminController
{

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'User';
    protected $description = [
            'index'  => 'Index',
            'show'   => 'Show:',
            'edit'   => 'Edit',
            'create' => 'Create',
    ];

    // public function create(Content $content)
    // {
    //     var_dump($content);
    //     die;
    // }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new User());

        $grid->column('id', __('Id'))->limit(6);
        $grid->column('name', __('admin.user.name'))->sortable()->link(function () {
            return "users/".$this->id."/edit";
        });
        $grid->column('email', __('Email'));
        // $grid->column('email_verified_at', __('Email verified at'));
        // $grid->column('password', __('Password'));
        // $grid->column('token', __('Token'));

        // $grid->column('role_id', __('admin.user.role'))->display(function () {
        //     return "<span class='label label-warning'>{$this->roles}</span>";
        // });
        $grid->roles()->display(function ($role) {
            switch ($role['id']) {
                case '1':
                case '2':
                case '3':
                    return '<span class="label-danger" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.$role['description'];
                    break;
                case '4':
                    return '<span class="label-success" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.$role['description'];
                    break;
                
                case '6':
                case '7':
                    return '<span class="label-info" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.$role['description'];
                    break;
                
                default:
                    return '<span class="label-info" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.$role['name'];
                    break;
            }
        });

        // $grid->column('created_by', __('Created by'));
        $grid->column('phone', __('Phone'));
        $grid->column('user_name', __('admin.user.user_name'));
        $grid->column('gender', __('admin.user.gender'))->display(function ($gender) {
            return $gender == 1 ? 'Nam' : 'Nữ';        });
        $grid->column('notes', __('admin.user.notes'));
        $grid->column('date_of_birth', __('Date of birth'));
        // $grid->column('identity_card', __('Identity card'));
        // $grid->provinces()->display(function ($province) {return $province['name'] ?? "";});
        // $grid->column('district_id', __('District id'));
        // $grid->column('ward_id', __('Ward id'));
        $grid->column('referral_code', __('Referral Code'))->display(function ($referralCode) {
            if ($referralCode) {
                // Get referral stats for tooltip
                $stats = $this->getReferralStats();
                $tooltip = 'Total Referrals: ' . $stats['total_referrals'] . ' | Referred Orders: ' . $stats['total_referred_orders'] . ' | Total Value: ' . number_format($stats['total_referred_order_value'], 2);
                return "<span class='label label-info' title='" . htmlspecialchars($tooltip) . "' data-toggle='tooltip'>{$referralCode}</span>";
            } else {
                return "<span class='label label-default' title='No referral code generated' data-toggle='tooltip'>No Code</span>";
            }
        });
        $grid->column('address', __('admin.user.address'));
        $grid->column('provider_name', __('admin.user.provider'));
        // $grid->column('provider_id', __('Provider id'));
        $grid->column('profile_picture', __('admin.user.profile_picture'))->image('', 75, 75);
        // $grid->column('profile_picture', __('admin.user.profile_picture'))->display(function () {
        //         return "<span class='label label-warning'>{" .$this->Avatar."}</span>";
        //     });
        // $grid->column('is_new', __('Is new'))->bool();
        $grid->column('enable', __('admin.enable'))->action(new EnableDisable2);
        // $grid->column('description', __('Description'));
        $grid->column('total_rating', __('admin.rating.title'))->progressBar($style = 'primary', $size = 'sm', $max = 5);
        // $grid->column('background_picture', __('Background picture'));
        // $grid->column('organize_id', __('Organize id'));
        // $grid->column('custom_path', __('Custom path'));
        // $grid->column('remember_token', __('Remember token'));
        if (!request()->has('_sort')) {
            // Apply default sorting by created_at column in descending order
            $grid->model()->orderBy('created_at', 'desc');
        }
        $grid->column('created_at', __('Created at'))->display(function () {
            return "<span class='label' style='color:blue'>$this->created_at</span>";
        })->sortable();
        // $grid->column('updated_at', __('Updated at'));


        $grid->quickSearch('name','email','user_name','address','referral_code');


        $grid->quickCreate(function (Grid\Tools\QuickCreate $create) {
            $create->text('name', 'Name');
            $create->email('email', 'Email');
        });

        $grid->filter(function($filter){    
            $filter->column(1/2, function ($filter) {
                $filter->scope('provider_name', 'Zalo')->where('provider_name', 'ZALO');
                $filter->scope('new', 'Recently modified')
                    ->whereDate('created_at', date('Y-m-d'))
                    ->orWhere('updated_at', date('Y-m-d')); 
                $filter->scope('disabled', 'Disabled')->where('enable', 0);                $filter->scope('trashed', 'Recycle Bin')->onlyTrashed();
                $filter->scope('agent', 'AGENT')->where('role_id', 4);
                $filter->scope('no_referral', 'No Referral Code')->whereNull('referral_code');
                $filter->scope('with_referral', 'Has Referral Code')->whereNotNull('referral_code');

                $filter->like('name', 'name');
                $filter->like('email', 'email');
                $filter->like('user_name', 'user_name');                $filter->like('address', 'address');
                $filter->like('phone', 'phone');
                $filter->like('referral_code', 'referral_code');
            });

            $filter->column(1/2, function ($filter) {
                $filter->in('role_id', 'role_id')->select(Role::all()->pluck('name','id'));
                






            });
        });

        $grid->actions(function ($actions) {
            $scope = request('_scope_');
            // if ($scope === 'trashed') 
            {
                $actions->add(new Restore());
            }
        });        $grid->actions(function ($actions) {
            $actions->add(new Replicate);
            $actions->add(new SetAgent);
            $actions->add(new SetDriver);
            $actions->add(new GenerateReferralCode);
            // $actions->add(new Disable);
            $actions->add(new EnableDisable);
        });        // Add batch delete action
        $grid->batchActions(function ($batch) {
            $batch->add(new BatchDelete('User')); // Ensure 'Shop' is the correct model type
            $batch->add(new BatchGenerateReferralCode());
        });

        // Enable horizontal scroll with persistent footer scroll bar
        $grid->scrollX(true);
        
        // Add custom CSS for always visible horizontal scroll
        Admin::style('
            /* Force horizontal scroll and make it always visible */
            .grid-table .table-responsive,
            .box .box-body .table-responsive,
            .table-responsive {
                overflow-x: scroll !important;
                overflow-y: visible !important;
                min-height: 400px;
            }
            
            /* WebKit browsers (Chrome, Safari, Edge) - Big scrollbar */
            .grid-table .table-responsive::-webkit-scrollbar,
            .box .box-body .table-responsive::-webkit-scrollbar,
            .table-responsive::-webkit-scrollbar {
                height: 24px !important;
                width: 24px !important;
            }
            
            .grid-table .table-responsive::-webkit-scrollbar-track,
            .box .box-body .table-responsive::-webkit-scrollbar-track,
            .table-responsive::-webkit-scrollbar-track {
                background: #f1f1f1 !important;
                border-radius: 12px !important;
                border: 1px solid #ddd !important;
            }
            
            .grid-table .table-responsive::-webkit-scrollbar-thumb,
            .box .box-body .table-responsive::-webkit-scrollbar-thumb,
            .table-responsive::-webkit-scrollbar-thumb {
                background: #888 !important;
                border-radius: 12px !important;
                cursor: grab !important;
                border: 2px solid #f1f1f1 !important;
            }
            
            .grid-table .table-responsive::-webkit-scrollbar-thumb:hover,
            .box .box-body .table-responsive::-webkit-scrollbar-thumb:hover,
            .table-responsive::-webkit-scrollbar-thumb:hover {
                background: #555 !important;
                cursor: grab !important;
            }
            
            .grid-table .table-responsive::-webkit-scrollbar-thumb:active,
            .box .box-body .table-responsive::-webkit-scrollbar-thumb:active,
            .table-responsive::-webkit-scrollbar-thumb:active {
                cursor: grabbing !important;
                background: #333 !important;
            }
            
            /* Firefox - Big scrollbar */
            .grid-table .table-responsive,
            .box .box-body .table-responsive,
            .table-responsive {
                scrollbar-width: thick !important;
                scrollbar-color: #888 #f1f1f1 !important;
            }
            
            /* Additional padding for the scrollbar space */
            .content-wrapper .box .box-body {
                position: relative;
            }
            
            .content-wrapper .box .box-body .table-responsive {
                position: relative;
                padding-bottom: 35px !important;
            }
            
            /* Make sure the table doesnt hide the scrollbar */
            .grid-table table {
                margin-bottom: 30px !important;
            }
        ');
        
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $this->description['show'] = "Show detail";
        $show = new Show(User::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('name', __('Name'));
        $show->field('email', __('Email'));
        $show->field('email_verified_at', __('Email verified at'));
        // $show->field('password', __('Password'));
        // $show->field('token', __('Token'));
        $show->field('role_id', __('Role id'));
        $show->field('created_by', __('Created by'));
        $show->field('phone', __('Phone'));
        $show->field('user_name', __('User name'));
        $show->field('gender', __('Gender'))
            // ->using([0 => 'Female', 1 => 'Male'])
        ;        $show->field('date_of_birth', __('Date of birth'));
        $show->field('identity_card', __('Identity card'));        $show->field('referral_code', __('Referral Code'));
        
        // Add referral statistics section
        $user = User::findOrFail($id);
        if ($user->referral_code) {
            $stats = $user->getReferralStats();
            $show->html('
                <div class="box box-solid">
                    <div class="box-header with-border">
                        <h3 class="box-title">Referral Statistics</h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="info-box bg-green">
                                    <span class="info-box-icon"><i class="fa fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Referrals</span>
                                        <span class="info-box-number">' . $stats['total_referrals'] . '</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-blue">
                                    <span class="info-box-icon"><i class="fa fa-shopping-cart"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Referred Orders</span>
                                        <span class="info-box-number">' . $stats['total_referred_orders'] . '</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-yellow">
                                    <span class="info-box-icon"><i class="fa fa-check"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Successful Referrals</span>
                                        <span class="info-box-number">' . $stats['successful_referrals'] . '</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-red">
                                    <span class="info-box-icon"><i class="fa fa-money"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Order Value</span>
                                        <span class="info-box-number">$' . number_format($stats['total_referred_order_value'], 2) . '</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            ');
        }
        
        $show->field('province_id', __('Province id'));
        $show->field('district_id', __('District id'));
        $show->field('ward_id', __('Ward id'));
        $show->field('address', __('Address'));
        $show->field('provider_name', __('Provider name'));
        $show->field('provider_id', __('Provider id'));
        $show->field('profile_picture', __('Profile picture'))->image();
        $show->field('is_new', __('Is new'));
        $show->field('property_like', __('Property like'));
        $show->field('enable', __('Enable'));
        $show->field('description', __('Description'));
        $show->field('notes', __('admin.user.notes'));
        // $show->field('rating', __('Rating'));
        $show->field('background_picture', __('Background picture'));
        // $show->field('organize_id', __('Organize id'));
        $show->field('custom_path', __('Custom path'));
        // $show->field('remember_token', __('Remember token'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new User());

        $form->text('id', __('id'))->disable();
        $form->text('name', __('Name'));
        $form->email('email', __('Email'))->rules('required')
        ->creationRules(['required', "unique:users"], [
            'unique' => __('email đã tồn tại trong hệ thống.'),
        ])
        ->updateRules(['required', "unique:users,email,{{id}}"]);
        // $form->datetime('email_verified_at', __('Email verified at'))->default(date('Y-m-d H:i:s'));
        $form->password('password', __('Password'))->creationRules('required')->rules('nullable|min:6', [
            'required' => __('Vui lòng nhập password.'),
        ]);
        // $form->textarea('token', __('Token'));
        $form->select('role_id', __('admin.user.role'))->options(Role::all()->pluck('name','id'))->default(6)->rules('required');
        // $form->text('created_by', __('Created by'));
        $form->text('phone', __('Phone'));
        $form->text('user_name', __('admin.user.user_name'));
        // $form->switch('gender', __('Gender'));
        $form->radio('gender', __('admin.user.gender'))->options(['0' => 'Female', '1'=> 'Male'])->default('0');        $form->date('date_of_birth', __('admin.user.date_of_birth'))->default(date('Y-m-d'));
        $form->text('identity_card', __('admin.user.identify_card'));
        $form->text('referral_code', __('Referral Code'))
            ->help('Leave empty to auto-generate on save')
            ->creationRules(['nullable', 'unique:users,referral_code'])
            ->updateRules(['nullable', 'unique:users,referral_code,{{id}}']);
        $form->select('province_id', __('admin.address.province'))->options(Province::all()->pluck('name','id'));
        $form->select('district_id', __('admin.address.district'))->options(District::all()->pluck('name','id'));
        $form->select('ward_id', __('admin.address.ward'))->options(Ward::all()->pluck('name','id'));
        $form->text('address', __('Address'));
        $form->text('provider_name', __('Provider name'));
        $form->text('provider_id', __('Provider id'));
        $form->image('profile_picture', __('Profile picture'))->removable()->downloadable()->move("user")
        ->rules('mimes:jpeg,png,jpg,gif,svg,webp')
        // ->thumbnail('small', $width = 300, $height = 300)
        ;
        $form->switch('is_new', __('Is new'));
        $form->switch('enable', __('Enable'))->default(1);
        $form->textarea('description', __('Description'));
        $form->textarea('notes', __('admin.user.notes'));
        // $form->number('rating', __('Rating'))->default(10);
        $form->text('background_picture', __('Background picture'));
        // $form->text('organize_id', __('Organize id'));
        $form->text('custom_path', __('Custom path'));
        $form->text('remember_token', __('Remember token'));        $form->saving(function (Form $form__) {
            if($form__->password) {
                $form__->password = bcrypt($form__->password);
            } else {
                unset($form__->password);
            }
            
            // Auto-generate referral code if not provided
            if (empty($form__->referral_code)) {
                do {
                    $code = 'REF' . strtoupper(\Illuminate\Support\Str::random(6));
                } while (User::where('referral_code', $code)->exists());
                $form__->referral_code = $code;
            } else {
                // Normalize referral code to uppercase
                $form__->referral_code = strtoupper(trim($form__->referral_code));
            }
        });

        return $form;
    }
}
