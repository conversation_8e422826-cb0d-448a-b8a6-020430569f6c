<?php

namespace App\Http\Requests\Geocode;

use App\Http\Requests\BaseRequest;

class GeocodeRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'longitude' => 'required|numeric',
            'latitude' => 'required|numeric',
            'radius'    => 'nullable|integer|between:1,5'
        ];
    }

    public function messages()
    {
        return [
            'longitude.required'    => 'Geocode_001_001',
            'longitude.numeric'     => 'Geocode_002_002',

            'latitude.required'     => 'Geocode_001_003',
            'latitude.numeric'      => 'Geocode_002_004',

            'radius.integer'        => 'Geocode_002_005',
            'radius.between'        => 'Geocode_004_006',

        ];
    }
}
