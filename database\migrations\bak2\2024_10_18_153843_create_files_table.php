<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFilesTable extends Migration
{
    public function up()
    {
        Schema::create('files', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('file_name'); // Name of the file
            $table->string('file_type')->default('video')->comment('video, document,'); // Type of the file (video, document, etc.)
            $table->string('file_path'); // Path to the file
            $table->integer('view_total')->default(0); // Path to the file
            $table->integer('like_total')->default(0); // Path to the file
            $table->uuid('user_id')->nullable(); // Optional user association
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('files');
    }
}