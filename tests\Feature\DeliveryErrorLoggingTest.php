<?php

namespace Tests\Feature;

use App\Services\DeliveryPartner\DeliveryErrorService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class DeliveryErrorLoggingTest extends TestCase
{
    use RefreshDatabase;

    protected $deliveryErrorService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->deliveryErrorService = new DeliveryErrorService();
        
        // Fake mail to prevent actual emails during testing
        Mail::fake();
    }

    /** @test */
    public function it_logs_delivery_errors_to_separate_log_file()
    {
        // Clear any existing logs
        $logFile = storage_path('logs/delivery.log');
        if (file_exists($logFile)) {
            file_put_contents($logFile, '');
        }

        // Test logging an error
        $this->deliveryErrorService->logAndNotify(
            'TEST_ERROR',
            'This is a test error message',
            'ahamove',
            'test-order-123',
            [
                'test_data' => 'test_value',
                'request_id' => 'req-123'
            ]
        );

        // Check if log file exists and contains our error
        $this->assertFileExists($logFile);
        
        $logContent = file_get_contents($logFile);
        $this->assertStringContainsString('This is a test error message', $logContent);
        $this->assertStringContainsString('ahamove', $logContent);
        $this->assertStringContainsString('test-order-123', $logContent);
        $this->assertStringContainsString('TEST_ERROR', $logContent);
    }

    /** @test */
    public function it_sends_email_notification_for_delivery_errors()
    {
        // Set admin email in config for testing
        config(['app.env' => 'testing']);
        putenv('ADMIN_EMAIL=<EMAIL>');

        // Test sending email notification
        $this->deliveryErrorService->logAndNotify(
            'EMAIL_TEST_ERROR',
            'This is a test error for email notification',
            'remagan',
            'email-test-order-456',
            [
                'email_test' => true,
                'priority' => 'high'
            ]
        );

        // Assert that an email was sent
        Mail::assertSent(\App\Mail\DeliveryErrorMail::class, function ($mail) {
            return $mail->hasTo('<EMAIL>');
        });
    }

    /** @test */
    public function it_logs_successful_delivery_creation()
    {
        // Clear any existing logs
        $logFile = storage_path('logs/delivery.log');
        if (file_exists($logFile)) {
            file_put_contents($logFile, '');
        }

        // Test logging success
        $this->deliveryErrorService->logSuccess(
            'ahamove',
            'success-order-789',
            'delivery-123',
            [
                'delivery_price' => 50000,
                'service_id' => 'BIKE'
            ]
        );

        // Check if log file contains success message
        $logContent = file_get_contents($logFile);
        $this->assertStringContainsString('Delivery created successfully', $logContent);
        $this->assertStringContainsString('success-order-789', $logContent);
        $this->assertStringContainsString('delivery-123', $logContent);
    }

    /** @test */
    public function it_logs_api_calls_for_debugging()
    {
        // Clear any existing logs
        $logFile = storage_path('logs/delivery.log');
        if (file_exists($logFile)) {
            file_put_contents($logFile, '');
        }

        // Test logging API call
        $this->deliveryErrorService->logApiCall(
            'ahamove',
            'api-test-order-999',
            '/v3/orders',
            ['service_id' => 'BIKE', 'payment_method' => 'CASH'],
            ['order_id' => 'AH123456', 'status' => 'PENDING'],
            'POST'
        );

        // Check if log file contains API call information
        $logContent = file_get_contents($logFile);
        $this->assertStringContainsString('Delivery API Call', $logContent);
        $this->assertStringContainsString('api-test-order-999', $logContent);
        $this->assertStringContainsString('/v3/orders', $logContent);
        $this->assertStringContainsString('POST', $logContent);
    }

    /** @test */
    public function it_handles_exceptions_gracefully()
    {
        // Test with invalid data that might cause exceptions
        $this->deliveryErrorService->logAndNotify(
            'EXCEPTION_TEST',
            'Testing exception handling',
            'invalid-partner',
            null, // null order ID
            []
        );

        // The method should not throw exceptions and should handle gracefully
        $this->assertTrue(true); // If we reach here, no exception was thrown
    }

    protected function tearDown(): void
    {
        // Clean up test environment
        putenv('ADMIN_EMAIL');
        parent::tearDown();
    }
}
