<?php

namespace App\Http\Requests\UserAddress;

use App\Http\Requests\BaseRequest;

class UserAddressCheckIdRequest extends BaseRequest
{
   
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:addresses,id'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'Product_001_E_012',
    //         'id.uuid' => 'Product_002_E_013',
    //         'id.exists' => 'Product_003_E_014',
    //     ];
    // }
}
