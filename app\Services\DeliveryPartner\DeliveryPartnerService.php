<?php

namespace App\Services\DeliveryPartner;

use App\Notifications\NotificationUser;
use App\Order;
use App\Services\Mqtt\MqttChatService;
use App\Services\Notification\NotificationService;
use App\Services\Token\TokenService;
use App\Shop;
use App\User;

class DeliveryPartnerService
{
    function __construct(){}

    function setDeliveryPartner($deliveryPartnerType, $shopId = null)
    {
        switch ($deliveryPartnerType) {
            case 'ahamove': return new Ahamove($shopId);
            case 'j_and_t':
            case 'j&t': break;
            case 'remagan': return new Remagan($shopId);
        }
    }

    // Thông báo trạng thái vận đơn tới cửa hàng
    function notification($status, $orderId)
    {
        $order = Order::find($orderId);
        $shop = Shop::find($order->shop_id);
        $mqtt = new MqttChatService();
        if(!$order || !$shop) return ;
        $this->autoUpdateStatusOrder($status, $order);
        $msgShop = [
            'title' => "Trạng thái vận đơn đã được cập nhật",
            'body'  => "Đơn hàng ". $order->short_code,
            'url'   => $order->id,
            'extra' => json_encode([
                'shop_id'   => $order->shop_id
            ]),
            'target_url' => '/my-shop/orders/' . $order->id,
            'type'  => "delivery_update",
        ];
        $msgAgent = [
            'title' => "Trạng thái vận đơn đã được cập nhật",
            'body'  => "Đơn hàng ". $order->short_code . " của shop " . $shop->name,
            'url'   => $order->id,
            'extra' => json_encode([
                'shop_id'   => $order->shop_id
            ]),
            'target_url' => '/agent/order/' . $order->id,
            'type'  => "delivery_update",
        ];

        // Get status message based on delivery status
        $statusMessages = [
            config('constants.delivery.pending') => " đang chờ xử lý",
            config('constants.delivery.confirmed') => " đã được xác nhận và đang chờ lấy hàng",
            config('constants.delivery.prepared') => " đã được đóng gói và sẵn sàng giao",
            config('constants.delivery.picked_up') => " đã được tài xế lấy hàng",
            config('constants.delivery.in_transit') => " đang trên đường giao đến khách hàng",
            config('constants.delivery.delivered') => " đã giao hàng thành công",
            config('constants.delivery.cancelled') => " đã bị hủy vận chuyển",
            config('constants.delivery.failed') => " giao hàng không thành công"
        ];

        $msg = $statusMessages[$status] ?? "";

        $msgAgent['body'] .= $msg;
        $msgShop['body'] .= $msg;

        // Initialize services
        $tokenService = new TokenService();
        $notiService = new NotificationService;

        // Get shop and agent tokens
        $shopTokens = $tokenService->listByUser($shop->user_id, 2);
        $agentTokens = $tokenService->listByUser($shop->agent_id, 2);

        // Send notifications if tokens exist
        if(count($shopTokens) > 0){
            $notiService->sendBatchNotification($shopTokens, $msgShop);
        }
        if(count($agentTokens) > 0){
            $notiService->sendBatchNotification($agentTokens, $msgAgent);
        }

        // Gửi tới shop
        $mqtt->publish(['topic' => 'remagan_shop/'. $shop->id , 'message' => $msgShop]);
        // Gửi tới order
        $mqtt->publish(['topic' => 'remagan_order/'. $order->id , 'message' => $msgShop]);

        # nhận thông báo và lưu vào table notifications
        $shop_obj = User::find($shop->user_id);
        if($shop_obj){
            $shop_obj->notify(new NotificationUser($shop->user_id, $msgShop));
        }
        $shop_agent_obj = User::find($shop->agent_id);
        if($shop_agent_obj){
            $shop_agent_obj->notify(new NotificationUser($shop->agent_id, $msgAgent));
        }
    }

    function autoUpdateStatusOrder($status, &$order)
    {
        $autoStatusOrder = [
            config('constants.delivery.picked_up') => config('constants.order.status.in_process'),
            config('constants.delivery.delivered') => config('constants.order.status.done'),
        ];
        $order->update([
            'status' => $autoStatusOrder[$status] ?? $order->status
        ]);
    }
}
