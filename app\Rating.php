<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\User;

class Rating extends Model
{
    protected $fillable = [
        'id',
        'object_id',
        'object_type',
        'rating',
        'review',
        'user_id'
    ];
    protected $primaryKey = 'id';
    protected $table = 'ratings';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }

    public function users()
    {
        return $this->belongsTo(User::class,'user_id');
    }

    public function images()
    {
        return $this->hasMany(Image::class,'parent_id')->select('id','parent_id','object_type','path','title','description', 'orientation','style','index');
    }

    public function video()
    {
        return $this->hasMany(File::class, 'parent_id', 'id')->where('file_type', 'video')->where('object_type', 10);
    }
}
