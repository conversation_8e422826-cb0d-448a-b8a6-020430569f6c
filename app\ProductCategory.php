<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ProductCategory extends Model
{
    protected $fillable = [
        'product_id',
        'category_id',
        'index'
    ];
    // protected $primaryKey = 'id';
    protected $table = 'product_categories';
    public $incrementing = false;
    public $timestamps = false;

    protected static function boot()
    {
        parent::boot();
        // static::creating(function ($model) {
        //     $model->id = Str::uuid();
        // });
    }
    // protected $hidden = ['updated_at', 'created_at'];

    // public function created_by()
    // {
    //     return $this->belongsTo(User::class,'created_by');
    // }

    public function products()
    {
        return $this->belongsTo(Product::class,'product_id')->with('translation');
    }
    public function products_enable()
    {
        return $this->belongsTo(Product::class,'product_id')->where('enable', true)
        ->whereExists(function ($query) {
            $query->selectRaw(1)->from('product_categories')->whereRaw('products.id = product_categories.product_id');
        })->with('translation');
    }

    public function categories()
    {
        return $this->belongsTo(Category::class,'category_id')->with('translation');
    }
}
