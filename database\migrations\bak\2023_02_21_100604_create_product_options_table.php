<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProductOptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // bảng liên kết giữa Sản phẩm và Tùy chọn
        Schema::create('product_options', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('product_id');
            $table->uuid('option_id');
            $table->integer('min_select')->nullable()->comment("số lượng chọn tối thiểu");
            $table->integer('max_select')->nullable()->comment("số lượng chọn tối đa");
            $table->boolean('enable')->default(1);
            $table->uuid('created_by')->nullable();

            $table->index('product_id');
            $table->index('option_id');
            $table->index('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_options');
    }
}
