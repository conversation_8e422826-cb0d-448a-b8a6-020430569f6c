<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */
    'file_extension' => ' :attribute phải thuộc định dạng: :values.',
    'base64size' => ' :attribute vượt quá dung lượng cho phép.',
    'base64image' => ' :attribute phải là ảnh',
    'current_password' => ' :attribute không đúng.',

    'accepted' => ' :attribute không được phép.',
    'active_url' => ' :attribute không đúng.',
    'after' => ' :attribute phải là 1 ngày sau :date.',
    'after_or_equal' => ' :attribute phải là 1 ngày sau hoặc bằng :date.',
    'alpha' => ' :attribute chỉ bao gồm ký tự.',
    'alpha_dash' => ' :attribute chỉ bao gồm ký tự, số, dấu gạch ngang và dấu gạch dưới.',
    'alpha_num' => ' :attribute chỉ bao gồm chữ và số.',
    'array' => ' :attribute phải là 1 mảng.',
    'before' => ' :attribute phải là 1 ngày trước :date.',
    'before_or_equal' => ' :attribute phải là 1 ngày sau hoặc bằng :date.',
    'between' => [
        'numeric' => ' :attribute phải nằm giữa :min và :max.',
        'file' => ' :attribute có kích thước từ :min tới :max KB.',
        'string' => ' :attribute phải có độ dài từ :min tới :max ký tự.',
        'array' => ' :attribute phải có số lượng từ :min tới :max phần tử.',
    ],
    'boolean' => 'Trường :attribute phải là true hoặc false.',
    'confirmed' => ' :attribute chưa được xác định.',
    'date' => ' :attribute không phải là 1 ngày.',
    'date_equals' => ' :attribute phải là ngày :date.',
    'date_format' => ' :attribute không đúng định dạng :format.',
    'different' => ' :attribute và :other phải khác nhau.',
    'digits' => ' :attribute phải là :digits chữ số.',
    'digits_between' => ' :attribute phải thuộc khoảng từ :min tới :max chữ số.',
    'dimensions' => ' :attribute kích thước không hợp lệ.',
    'distinct' => 'Trường :attribute có giá trị trùng lặp.',
    'email' => ' :attribute không đúng.',
    'exists' => ' :attribute không tồn tại.',
    'file' => ' :attribute phải là 1 file.',
    'filled' => 'Trường :attribute phải có giá trị.',
    'gt' => [
        'numeric' => ' :attribute phải lớn hơn :value.',
        'file' => ' :attribute phải lớn hơn :value KB.',
        'string' => ' :attribute phải nhiều hơn :value ký tự.',
        'array' => ' :attribute phải có nhiều hơn :value phần tử.',
    ],
    'gte' => [
        'numeric' => ' :attribute phải lớn hơn hoặc bằng :value.',
        'file' => ' :attribute phải lớn hơn hoặc bằng :value KB.',
        'string' => ' :attribute phải có tối thiểu :value ký tự.',
        'array' => ' :attribute phải có tối thiểu :value phần tử.',
    ],
    'image' => ' :attribute phải là 1 ảnh.',
    'in' => ' :attribute không đúng.',
    'in_array' => ' :attribute không tồn tại trong :other.',
    'integer' => ' :attribute phải thuộc kiểu Integer.',
    'ip' => ' :attribute phải là địa chỉ IP.',
    'ipv4' => ' :attribute phải là địa chỉ IPv4.',
    'ipv6' => ' :attribute phải là địa chỉ IPv6.',
    'json' => ' :attribute phải là chuỗi JSON.',
    'lt' => [
        'numeric' => ' :attribute phải bé hơn :value.',
        'file' => ' :attribute phải bé hơn :value KB.',
        'string' => ' :attribute phải ngắn hơn :value ký tự.',
        'array' => ' :attribute phải ít hơn :value phần tử.',
    ],
    'lte' => [
        'numeric' => ' :attribute phải bé hơn hoặc bằng :value.',
        'file' => ' :attribute phải bé hơn hoặc bằng :value KB.',
        'string' => ' :attribute tối đa :value ký tự.',
        'array' => ' :attribute tối đa :value phần tử.',
    ],
    'max' => [
        'numeric' => ' :attribute không được lớn hơn :max.',
        'file' => ' :attribute có kích thước không quá :max KB.',
        'string' => ' :attribute không nhiều hơn :max ký tự.',
        'array' => ' :attribute không được nhiều hơn :max phần tử.',
    ],
    'mimes' => ' :attribute phải có định dạng file: :values.',
    'mimetypes' => ' :attribute phải có định dạng file: :values.',
    'min' => [
        'numeric' => ' :attribute không được bé hơn :min.',
        'file' => ' :attribute có kích thước tối thiểu :min KB.',
        'string' => ' :attribute phải có ít nhất :min ký tự.',
        'array' => ' :attribute phải có ít nhất :min phần tử.',
    ],
    'not_in' => ' :attribute được chọn không đúng.',
    'not_regex' => 'Định dạng :attribute không đúng.',
    'numeric' => ' :attribute phải là 1 số.',
    'present' => 'Trường :attribute phải tồn tại.',
    'regex' => 'Định dạng :attribute không đúng.',
    'required' => 'Trường :attribute là bắt buộc.',
    'required_if' => 'Trường :attribute bắt buộc khi :other là :value.',
    'required_unless' => 'Trường :attribute là bắt buộc nếu :other không nằm trong :values.',
    'required_with' => 'Trường :attribute là bắt buộc khi :values đang tồn tại.',
    'required_with_all' => 'Trường :attribute là bắt buộc khi :values đang tồn tại.',
    'required_without' => 'Trường :attribute là bắt buộc khi :values không tồn tại.',
    'required_without_all' => 'Trường :attribute là bắt buộc khi không có :values nào đang tồn tại.',
    'same' => ' :attribute và :other phải giống nhau.',
    'size' => [
        'numeric' => ' :attribute phải là :size.',
        'file' => ' :attribute phải là :size KB.',
        'string' => ' :attribute phải có :size ký tự.',
        'array' => ' :attribute phải chứa :size phần tử.',
    ],
    'starts_with' => ' :attribute phải bắt đầu bằng 1 trong các giá trị: :values',
    'string' => ' :attribute phải là chuỗi ký tự.',
    'timezone' => ' :attribute không phải là múi giờ đúng.',
    'unique' => ' :attribute đã được sử dụng.',
    'uploaded' => ' :attribute tải lên thất bại.',
    'url' => ' :attribute định dạng không đúng.',
    'uuid' => ' :attribute phải là chuỗi UUID.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [],

];
