<?php
namespace App\Admin\Actions;

use Encore\Admin\Actions\BatchAction;
use Illuminate\Database\Eloquent\Model;
use App\Shop;
use App\Product;

class BatchDelete extends BatchAction
{
    public $name = '<PERSON><PERSON><PERSON> nhiều thì bấm vào đây';
    protected $_modelType;


    public function handle($ids)
    {
        $modelType = request()->input('_model'); // Get the model type from the payload
        $modelType = str_replace('App_', '', $modelType); // Clean up the model type string
        $model = 'App\\' . $modelType; // Dynamically resolve the model
        $ids = collect($ids)->pluck('id')->toArray();
        $model::whereIn('id', $ids)->delete();

        return $this->response()->success('Items deleted successfully.')->refresh();
    }
}