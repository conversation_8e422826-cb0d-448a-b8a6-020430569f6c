<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePOSConnectionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pos_connections', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->text('token')->nullable();
            $table->timestamp('token_expired')->nullable();
            $table->json('data');
            $table->json('extra_data')->nullable();
            $table->string('pos_type');
            $table->uuid('shop_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pos_connections');
    }
}
