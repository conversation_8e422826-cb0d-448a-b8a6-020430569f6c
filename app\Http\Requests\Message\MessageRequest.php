<?php

namespace App\Http\Requests\Message;

use App\ChannelConnect;
use App\Http\Requests\BaseRequest;

class MessageRequest extends BaseRequest
{
    function rules()
    {
        return [
            'channel_id' => [
                'required_without:receiver',
                'uuid',
                'exists:channels,id',
                // Kiểm tra người gửi có quyền gửi tin vào channel hay không?
                function ($attribute, $value, $fail) {
                    if (!ChannelConnect::where('channel_id', $value)->where('member_id', request('sender_id'))->exists()) {
                        $fail("The sender does not have permission to send content to this channel.");
                    }
                }
            ],
            'sender_id' => 'required|uuid',
            'sender_type' => 'required|string|in:user,shop',
            'receiver' => 'required_without:channel_id',
            'receiver.id' => 'required_with:receiver|uuid',
            'receiver.type' => 'required_with:receiver|string|in:user,shop',
            'content' => 'required',
            'language' => 'required|string',
        ];
    }
}
