<?php

namespace App\Http\Requests\Category;

use App\Http\Requests\BaseRequest;

class CategoryRequest extends BaseRequest
{

    public function rules()
    {
        return [
            // 'title' => 'required|unique:categories,title',
            // 'category_id' => 'nullable|uuid|exists:categories,id',
            // 'featured_image' => 'nullable|base64size|base64image',
            // 'images' => 'nullable|array',
            // 'images.*.path' => 'required|base64size|base64image'
            'title' => 'required|max:255|unique:place_types,name',
            // 'name' => 'required|max:255',
            'mark' => 'bail|nullable|integer|min:-1000|max:1000',
            'parent_id' => 'nullable|uuid|exists:place_types,id'
        ];
    }

    public function messages()
    {
        return [
            // // 'title.required' => 'Category_001_E_001',
            // // // 'title.regex' => 'Category_002_E_002',
            // // 'title.unique' => 'Category_005_E_003',

            // // 'category_id.uuid' => 'Category_002_E_004',
            // // 'category_id.exists' => 'Category_003_E_005',

            // // 'featured_image.base64size' => 'Category_004_E_006',
            // // 'featured_image.base64image' => 'Category_002_E_007',

            // // 'images.array' => 'Category_002_E_008',

            // // 'images.*.path.required' => 'Category_001_E_009',
            // // 'images.*.path.base64size' => 'Category_004_E_010',
            // // 'images.*.path.base64image' => 'Category_002_E_011',
            // 'name.required' => 'Category_001_E_001',
            // 'title.unique' => 'Category_005_E_002',

            // 'title.required' => 'Category_001_E_003',

            // 'mark.integer' => 'Category_002_E_007',
            // 'mark.min' => 'Category_004_E_008',
            // 'mark.max' => 'Category_004_E_009',

            // 'parent_id.uuid' => 'Category_002_E_010',
            // 'parent_id.exists' => 'Category_003_E_011',

            // 'title.max' => 'Category_004_E_012',
            // 'name.max'  => 'Category_004_E_013'
        ];
    }
}
