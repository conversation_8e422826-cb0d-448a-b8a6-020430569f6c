<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class CheckUniRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'status'=> 'required|in:1,2,3,4,5'
        ];
    }
    // public function messages()
    // {
    //     return [
    //         'status.required'=> 'User_001_E_001',
    //         'status.in'      => 'User_002_E_002'
    //     ];
    // }

}
