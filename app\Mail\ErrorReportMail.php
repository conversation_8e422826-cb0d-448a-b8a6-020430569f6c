<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class ErrorReportMail extends Mailable
{
    use Queueable, SerializesModels;

    protected $errorType;
    protected $errorMessage;
    protected $context;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($errorType, $errorMessage, $context = [])
    {
        $this->errorType = $errorType;
        $this->errorMessage = $errorMessage;
        $this->context = $context;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        try {
            return $this->subject('Remagan OTP Error Report - ' . $this->errorType)
                ->view('emails.error_report')
                ->with([
                    'errorType' => $this->errorType,
                    'errorMessage' => $this->errorMessage,
                    'context' => $this->context,
                    'timestamp' => now(),
                ]);
        } catch (\Exception $e) {
            Log::error("Error Report Mail Error: " . $e->getMessage());
        }
    }
}
