<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
// JWT
// use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function report(Exception $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Exception $exception)
    {
        // if ($exception instanceof UnauthorizedHttpException) {

        //     $preException = $exception->getPrevious();

        //     if ($preException instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException)
        //     {
        //         return response()->json(['error' => 'TOKEN_EXPIRED']);
        //     }
        //     else if ($preException instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException)
        //     {

        //         return response()->json(['error' => 'TOKEN_INVALID']);
        //     }
        //     else if ($preException instanceof \Tymon\JWTAuth\Exceptions\TokenBlacklistedException) {

        //         return response()->json(['error' => 'TOKEN_BLACKLISTED']);
        //     }
        // }

        // if ($exception->getMessage() === 'Token not provided')
        // {
        //     // return response()->json(['error' => 'Token not provided']);
        //     return Redirect('login');
        // }
        return parent::render($request, $exception);
    }
}
