<?php

namespace App\Services\ClientPublic;
use App\Interaction;
use App\Province;
use App\Translation;
use App\Ward;
use App\District;
use App\Shop;
use App\Product;
use App\Order;
use App\Http\Requests\PropertyRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Setting;
use Illuminate\Support\Facades\Cache;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use App\Helpers\Helper;
use PHPUnit\Util\Json;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClientPublicService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if ($userId) {
            $this->_userId = $userId;
        } else {
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }
    // ------------ filter service -----------
    public function filterAll(array $filter)
    {
        if(isset($filter['filter_type'])){
            switch ($filter['filter_type']) {
                case 1:
                    $result = $this->filterShopWithProduct($filter);
                    break;
                case 2:
                    $result = $this->filterProduct($filter);
                    break;

                default:
                    $result = $this->filterProduct($filter);
                    break;
            }
        }
        else {
            $result = $this->filterProduct($filter);
        }

        return $result;
    }

    public function filterShopWithProduct(array $filter, $searchLevel = 1)
    {
        $count = 0;
        $modelShop = new Shop();
        $modelShop->setConnection('pgsqlReplica');
        $filter['radius'] = $filter['radius'] ?? 35;
        $limitMap = 200;
        $result = $modelShop->select('id','name','address','slug','user_id','description','business_type_id','enable','latitude','longitude','province_id','district_id','ward_id','created_by','order_type',
        'agent_id','phone','currency','language','banner_id','logo_id','logo_id','qr_code','open_hours','views','likes','follows','ratings');

        $result->where('enable', true);
        if(isset($filter['business_type_id']) && $filter['business_type_id'] != ''){
            $result->where('business_type_id', $filter['business_type_id']);
        }

        $filter['limit']        = $filter['limit'] ?? 100;
        $filter['latitude']     = $filter['latitude'] ?? $filter['latitude_user'];
        $filter['longitude']    = $filter['longitude'] ?? $filter['longitude_user'];
        $latitude_s   = $filter['latitude_s'] ?? null;
        $latitude_b   = $filter['latitude_b'] ?? null;
        $longitude_s  = $filter['longitude_s'] ?? null;
        $longitude_b  = $filter['longitude_b'] ?? null;

        // Log::info($filter);

        // $result->where('id', '670392c8-991e-4b5a-ae48-e3f713fdd941');
        // $result->whereIn('id', ['056cb5ff-fcb2-41c8-8e9e-22b455b076d4','06e3fe61-baef-435c-96ae-990936674f2d']);

        $result->with(['products' => function ($query) use ($filter,$searchLevel) {
            if(isset($filter['search']) && !empty($filter['search']))
            {
                // $query->where('name',  'ILIKE', '%'.$filter['search'].'%');
                // echo $searchLevel;
                switch ($searchLevel) {
                    case 1:
                        $searchText = Str::lower($filter['search']);
                        $query->where('lowercase_name','like', "%".$searchText."%");
                        $query->orderByRaw("CASE
                            WHEN lowercase_name LIKE '".$searchText."' THEN 1
                            WHEN lowercase_name LIKE '".$searchText."%' THEN 2
                            WHEN lowercase_name LIKE '%".$searchText."%' THEN 3
                            WHEN lowercase_name LIKE '%".$searchText."' THEN 4
                            ELSE 5
                        END");
                        break;
                    case 2:
                        $searchText = Str::slug($filter['search']," ");
                        $query->where('unaccent_name','like', "%".$searchText."%");
                        $query->orderByRaw("CASE
                            WHEN unaccent_name LIKE '".$searchText."' THEN 1
                            WHEN unaccent_name LIKE '".$searchText."%' THEN 2
                            WHEN unaccent_name LIKE '%".$searchText."%' THEN 3
                            WHEN unaccent_name LIKE '%".$searchText."' THEN 4
                            ELSE 5
                        END");
                        break;
                    case 3:
                        $searchText = ($filter['search']);
                        $query->where('notes','LIKE', "%".$searchText."%");
                        break;

                    default:
                        $searchText = Helper::unaccentVietnamese($filter['search']);
                        $query->selectRaw("*, similarity(unaccent_name, ?) AS similarity_score", [$searchText])
                        ->whereRaw("similarity(unaccent_name, ?) > 0.3", [$searchText]);
                        $query->raw("order by similarity_score desc");
                        break;
                }
            }

            if(isset($filter['is_suggest']) && $filter['is_suggest'] == true){
                $query->where('is_suggest', true);
            }
            if(isset($filter['is_sale_off']) && $filter['is_sale_off'] == true){
                $query->where('price_off', ">",0);
            }
            if (isset($filter['is_feature']) && $filter['is_feature'] == true) {
                $query->where('is_feature', true);
            }

            if (isset($filter['price_min'], $filter['price_max'])
            && !empty($filter['price_min'])
            && !empty($filter['price_max']))
            {
                if ($filter['price_min'] > $filter['price_max']) {
                    $temp = $filter['price_min'];
                    $filter['price_min'] = $filter['price_max'];
                    $filter['price_max'] = $temp;
                }

                $query->whereBetween('price', [$filter['price_min'], $filter['price_max']]);
            }
            $query->where('type', "<>", 1);
            $query->where('price', ">=", 0);
            $query->where('enable', true);

            // filter categories
            if (isset($filter['category_ids']) && !empty($filter['category_ids'])) {
                $query->whereHas('categories', function (Builder $query2) use ($filter) {
                    $query2->whereIn('category_id', $filter['category_ids']);
                });
            }

            if(isset($filter['sortBy']) && !empty(isset($filter['sortBy']))){
                switch($filter['sortBy']){
                    case 1:
                        $query->orderBy('price', 'asc');
                        break;
                    case 2:
                        // $query->orderBy('price', 'desc');
                        $query->orderByDesc('price');
                        break;
                    case 3:
                        $query->orderBy('created_at', 'desc');
                        break;
                    // case 4:
                    //     $query->orderBy('name', 'desc');
                    //     break;
                    case 6:
                        $query->orderBy('is_feature', 'desc');
                        break;
                    default:
                        $query->orderBy('price', 'asc');
                        break;
                }
            }
            else {
                $query->orderBy('price', 'asc');
            }

            $query->take(isset($filter['child_num']) ? $filter['child_num'] : 8);
        }]);
        // $result->whereHas('products', function (Builder $query) use ($filter){

        // });

        if ( $longitude_s && $latitude_s && $longitude_b && $latitude_b){}
        else
        {
            $aroundBound = $this->getBoundByRadius($filter['latitude'], $filter['longitude'], $filter['radius']);
            $latitude_s  = $aroundBound['latitude_s'];
            $latitude_b  = $aroundBound['latitude_b'];
            $longitude_s = $aroundBound['longitude_s'];
            $longitude_b = $aroundBound['longitude_b'];
        }

        $result->where([
            ['longitude', '>=', $longitude_s],
            ['longitude', '<=', $longitude_b],
            ['latitude', '>=', $latitude_s],
            ['latitude', '<=', $latitude_b]
        ]);


        if($filter['offset'] >= $limitMap) $limitMap = $limitMap + $filter['offset'];
        if(isset($filter['map_data']) && $filter['map_data'] == true){
            $limitMap = $filter['limit'];
        }
        $limitMap = $limitMap < 200 ? 200 : $limitMap;
        // echo $filter['longitude_b'];
        $result->where('name','<>', 'Base Shop');


        $result->selectRaw(
            "(1000 * 6371 * acos(
                LEAST(1, GREATEST(-1,
                    cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) +
                    sin(radians(?)) * sin(radians(latitude))
                ))
            )) AS distance", [
                $filter['latitude'],
                $filter['longitude'],
                $filter['latitude']
            ]
        )
        ->orderBy('distance', 'asc'); // Order by calculated distance
        $result->with('owner:id,name','banner','logo');
        $responseArr = $result
        ->offset($filter['offset'])
        ->limit(2000)->take(200)
        // ->inRandomOrder()
        ->orderBy("created_at",'desc')
        ->get()->toArray();


        $resultMap = [];
        foreach ($responseArr as $key => $item) {
            if(!empty($item['products']))
            {
                $resultMap[] = $item;
            }
        }
       $count = count($resultMap);

        // Check if count is 0 and expand the boundary
        if ($count == 0 && $searchLevel <= 4 && isset($filter['radius']) && $filter['radius'] < 500) {
            // Expand the boundary by 10 km
            $filter['radius'] += 100; // Increase radius
            return $this->filterShopWithProduct($filter, 1); // Recall the function with updated filter
        }
        //search again with searchText similarity
        if($count == 0 && $searchLevel <= 4 && isset($filter['search']) && !empty($filter['search'])){

            return $this->filterShopWithProduct($filter, $searchLevel + 1);
        }

        //search Shop by name, slug, address & description
        if(isset($filter['search']) && !empty($filter['search'])){
            $filter['radius'] = 1600;
            $shopResult = $this->filterShop($filter, true, 1);
            $count += $shopResult['count'];
            $resultMap = (array_merge($resultMap,$shopResult['result']));
        }

        // calculate distance
        if(isset($filter['latitude_user']) && isset($filter['longitude_user'])){
            foreach ($resultMap as $key => $shop) {
                // echo $key."\n";
                $resultMap[$key]['distance'] = round($shop['distance'],2);
                // $resultMap[$key]['distance_haversine'] = Helper::distance($filter['latitude_user'], $filter['longitude_user'], $shop['latitude'], $shop['longitude']);
            }
        }
        // Explode products if section is product_around
        if (isset($filter['section']) && $filter['section'] == 'product_around') {
            $newResultMap = [];
            foreach ($resultMap as $shop) {
                if (!empty($shop['products'])) {
                    foreach ($shop['products'] as $product) {
                        if($product['profile_picture'] == null || $product['profile_picture'] == ''){
                            continue;
                        }
                        $product['shop_name'] = $shop['name'];
                        $product['shop_id'] = $shop['id'];
                        $product['distance'] = $shop['distance'];
                        $newResultMap[] = $product;
                    }
                }
            }
            $resultMap = $newResultMap;
        }
        if(isset($filter['map_data']) && $filter['map_data'] == true){
            return $data = [
                'count' => $count,
                'result' => $resultMap
            ];

        }else{

            $resultList = array_slice($resultMap, $filter['offset'], $filter['limit']);
            return $data = [
                'count' => $count,
                'resultMap' => $resultMap,
                'resultList' => $resultList
            ];
        }

        // return $data;
    }
    public function filterShop(array $filter, $listOnly = false, $searchLevel = 1)
    {
        $limitMap = $listOnly == false ? 200 : $filter['limit'];
        $modelShop = new Shop();
        $modelShop->setConnection('pgsqlReplica');

        $result = $modelShop->select('id','name','address','slug','user_id','description','business_type_id','enable','latitude','longitude','province_id','district_id','ward_id','created_by','order_type',
        'agent_id','phone','currency','language','banner_id','logo_id','qr_code','open_hours','views','likes','follows','ratings')
            ->with('owner:id,name','banner','logo')
            ->where('enable', true);
        if(isset($filter['search']) && !empty($filter['search']))
        {

            $searchText = Str::lower($filter['search']);
            switch ($searchLevel) {
                case 1:
                    $result->where('name','ilike', "%".$searchText."%");
                    $result->orderByRaw("CASE
                        WHEN name ILIKE '".$searchText."' THEN 1
                        WHEN name ILIKE '".$searchText."%' THEN 2
                        WHEN name ILIKE '%".$searchText."%' THEN 3
                        WHEN name ILIKE '%".$searchText."' THEN 4
                        ELSE 5
                    END");
                    break;
                case 2:
                    $searchText = Str::slug($filter['search']);
                    $result->where('slug','ilike', "%".$searchText."%");
                    break;
                case 3:
                    $searchText = Str::lower($filter['search']);
                    $result->where('description','ilike', "%".$searchText."%");
                    break;
                case 4:
                    // $searchText = Str::lower($filter['search']);
                    $result->where('address','ilike', "%".$searchText."%");
                    break;

                default:
                    $searchText = Helper::unaccentVietnamese($searchText);
                    $result->selectRaw("*, similarity(name, ?) AS similarity_score", [$searchText])
                    ->whereRaw("similarity(name, ?) > 0.16", [$searchText]);
                    $result->raw("order by similarity_score desc");
                    break;
            }
        }

        if (
            isset(
                $filter['longitude_s'],
                $filter['latitude_s'],
                $filter['longitude_b'],
                $filter['latitude_b']
            ) &&
            !empty($filter['longitude_s']) &&
            !empty($filter['latitude_s']) &&
            !empty($filter['longitude_b']) &&
            !empty($filter['latitude_b'])
        ) {

        }else{
            $aroundBound = $this->getBoundByRadius($filter['latitude_user'], $filter['longitude_user'], $filter['radius'] ?? 900);
            $filter['latitude_s']  = $aroundBound['latitude_s'];
            $filter['latitude_b']  = $aroundBound['latitude_b'];
            $filter['longitude_s'] = $aroundBound['longitude_s'];
            $filter['longitude_b'] = $aroundBound['longitude_b'];
        }

        $result->where([
            ['longitude', '>=', $filter['longitude_s']],
            ['longitude', '<=', $filter['longitude_b']],
            ['latitude', '>=', $filter['latitude_s']],
            ['latitude', '<=', $filter['latitude_b']]
        ]);
        // calculate distance
        $result->selectRaw(
            "(1000 * 6371 * acos(
                LEAST(1, GREATEST(-1,
                    cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) +
                    sin(radians(?)) * sin(radians(latitude))
                ))
            )) AS distance", [
                $filter['latitude_user'],
                $filter['longitude_user'],
                $filter['latitude_user']
            ]
        );

        if(isset($filter['sortBy']) && !empty(isset($filter['sortBy']))){
            switch($filter['sortBy']){
                case 1:
                    $result->orderBy('name', 'asc');
                    break;
                case 3:
                    $result->orderBy('created_at', 'desc');
                    break;
                case 5:
                    $result->orderBy('similarity_score', 'desc');
                    break;
                case 'distance':
                    $result->orderBy('distance', 'asc');
                    break;
                default:
                    $result->inRandomOrder();
                    break;
            }
        }
        else {
            $result->inRandomOrder();
        }

        $result->whereHas('products', function($q){
            $q->where('enable', true);
        });
        if(isset($filter['business_type_id']) && $filter['business_type_id'] != ''){
            $result->where('business_type_id', $filter['business_type_id']);
        }
        $count = $result->count();

        if($count == 0 && $listOnly == true && $searchLevel <= 5 && isset($filter['search']) && !empty($filter['search'])){
            return $this->filterShop($filter, true, $searchLevel + 1);
        }

        $result->where('name','<>', 'Base Shop');


        $resultMap = $result
        ->offset($filter['offset'])
        ->limit($limitMap)
        ->get()->toArray();

        // calculate distance
        if(isset($filter['latitude_user']) && isset($filter['longitude_user'])){
            foreach ($resultMap as $key => $shop) {
                $resultMap[$key]['distance'] = round($shop['distance'],2);
                // $resultMap[$key]['distance_herachy'] = Helper::distance($filter['latitude_user'], $filter['longitude_user'], $shop['latitude'], $shop['longitude']);
            }
        }

        if($listOnly) {
            return $data = [
                'count' => $count,
                'result' => $resultMap
            ];
        }
        $resultList = array_slice($resultMap, $filter['offset'], $filter['limit']);
        $data = [
            'count' => $count,
            'resultMap' => $resultMap,
            'resultList' => $resultList
        ];

        return $data;
    }


    public function filterProduct(array $filter, $searchLevel = 1)
    {
        $filterByBounty = true;
        $filter['section'] = $filter['section'] ?? 'default';


        $modelProduct = new Product();
        $modelProduct->setConnection('pgsqlReplica');
        $result = $modelProduct->select('id','name','is_main','type','brand_id','profile_picture','latitude','longitude','notes','shop_id',
        'price','price_off','enable','created_by','parent_id','extra_id','unaccent_name','is_feature','lowercase_name','stock','sold_count',
        'slug','is_suggest','views','likes','follows','ratings','extra_code')
        ->with('categories:id,name', 'shop:id,name,user_id,currency,language,banner_id,logo_id','translation')
        ->where('enable', true)
        ->whereNotNull('profile_picture')
        ->where('profile_picture', '<>','')
        ->where('type','<>', 1);

        $result->whereHas('shop', function (Builder $query) {
            $query->where('enable', true);
        });
        if(isset($filter['is_suggest']) && $filter['is_suggest'] == true){
            $result->where('is_suggest', true);
        }
        if(isset($filter['is_sale_off']) && $filter['is_sale_off'] == true){
            $result->where('price_off', ">",0);
        }

        if (isset($filter['is_feature']) && $filter['is_feature'] == true) {
            $result->where('is_feature', true);
        }

        if (isset($filter['except']) && !empty($filter['except'])) {
            $result->whereNotin('id', $filter['except']);
        }

        if (isset($filter['category_ids']) && !empty($filter['category_ids'])) {
            $result->whereHas('categories', function (Builder $query) use ($filter) {
                $query->whereIn('category_id', $filter['category_ids']);
            });
        }


        if(isset($filter['search']) && !empty($filter['search']))
            {
                // $query->where('name',  'ILIKE', '%'.$filter['search'].'%');
                // echo $searchLevel;
                switch ($searchLevel) {
                    case 1:
                        $searchText = Str::lower($filter['search']);
                        $result->where('lowercase_name','like', "%".$searchText."%");
                        $result->orderByRaw("CASE
                            WHEN lowercase_name LIKE '".$searchText."' THEN 1
                            WHEN lowercase_name LIKE '".$searchText."%' THEN 2
                            WHEN lowercase_name LIKE '%".$searchText."%' THEN 3
                            WHEN lowercase_name LIKE '%".$searchText."' THEN 4
                            ELSE 5
                        END");
                        break;
                    case 2:
                        $searchText = Str::slug($filter['search']," ");
                        $result->where('unaccent_name','like', "%".$searchText."%");
                        $result->orderByRaw("CASE
                            WHEN unaccent_name LIKE '".$searchText."' THEN 1
                            WHEN unaccent_name LIKE '".$searchText."%' THEN 2
                            WHEN unaccent_name LIKE '%".$searchText."%' THEN 3
                            WHEN unaccent_name LIKE '%".$searchText."' THEN 4
                            ELSE 5
                        END");
                        break;
                    case 3:
                        $searchText = ($filter['search']);
                        $result->where('notes','LIKE', "%".$searchText."%");
                        break;

                    default:
                        $searchText = Helper::unaccentVietnamese($filter['search']);
                        $result->selectRaw("*, similarity(unaccent_name, ?) AS similarity_score", [$searchText])
                        ->whereRaw("similarity(unaccent_name, ?) > 0.3", [$searchText]);
                        $result->raw("order by similarity_score desc");
                        break;
                }
            }


        if (isset($filter['price_min'], $filter['price_max'])
            && !empty($filter['price_min'])
            && !empty($filter['price_max']))
        {
            if ($filter['price_min'] > $filter['price_max']) {
                $temp = $filter['price_min'];
                $filter['price_min'] = $filter['price_max'];
                $filter['price_max'] = $temp;
            }

            $result->whereBetween('price', [$filter['price_min'], $filter['price_max']]);
        }



        if($filter['section'] == 'suggest' || $filter['section'] == 'new'){
            if($filter['section'] == 'suggest'){
                $result->where('is_suggest', true);
            }
            $filterByBounty = false;
        }

//        if($filter['section'] == 'hot_sale'){
//            $result->where('is_posted', true);
//        }
        // filter by around
        if($filterByBounty == true){
            if (
                isset(
                    $filter['longitude_s'],
                    $filter['latitude_s'],
                    $filter['longitude_b'],
                    $filter['latitude_b']
                ) &&
                !empty($filter['longitude_s']) &&
                !empty($filter['latitude_s']) &&
                !empty($filter['longitude_b']) &&
                !empty($filter['latitude_b'])
                ) {}else{
                $aroundBound = $this->getBoundByRadius($filter['latitude_user'], $filter['longitude_user'], $filter['radius'] ?? 900);
                $filter['latitude_s']  = $aroundBound['latitude_s'];
                $filter['latitude_b']  = $aroundBound['latitude_b'];
                $filter['longitude_s'] = $aroundBound['longitude_s'];
                $filter['longitude_b'] = $aroundBound['longitude_b'];
            }
            $result->where([
                ['longitude', '>=', $filter['longitude_s']],
                ['longitude', '<=', $filter['longitude_b']],
                ['latitude', '>=', $filter['latitude_s']],
                ['latitude', '<=', $filter['latitude_b']]
            ]);
        }

        // calculate distance
        $result->selectRaw(
            "(1000 * 6371 * acos(
                LEAST(1, GREATEST(-1,
                    cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) +
                    sin(radians(?)) * sin(radians(latitude))
                ))
            )) AS distance", [
                $filter['latitude_user'],
                $filter['longitude_user'],
                $filter['latitude_user']
            ]
        );


        if(isset($filter['sortBy']) && !empty(isset($filter['sortBy']))){
            switch($filter['sortBy']){
                case 1:
                    $result->orderBy('price', 'asc');
                    break;
                case 2:
                    $result->orderBy('price', 'desc');
                    break;
                case 3:
                    $result->orderBy('created_at', 'desc');
                    break;
                case 4:
                    $result->whereNotNull('price_off')
                    ->where('price_off', '>', 0)
                    ->whereColumn('price_off', '<', 'price')
                    // ->orderByDesc('price_off');
                    // ->orderByRaw('price - price_off DESC');
                    ->orderBy('updated_at', 'DESC');
                    break;
                case 5:
                    $result->orderBy('similarity_score', 'desc');
                    break;
                case "distance" :
                    $result->orderBy('distance', 'asc');
                default:
                    $result->inRandomOrder();
                    break;
            }
        }
        else {
            $result->inRandomOrder();
        }

        $limitMap = 250;
        $filter['limit'] = $filter['limit'] ?? 20;
        $filter['offset'] = $filter['offset'] ?? 0;
        if($filter['offset'] > $limitMap) $limitMap = $limitMap + $filter['offset'];
        if(isset($filter['map_data']) && $filter['map_data'] == true){
            $limitMap = $filter['limit'];
        }
        $count = $result->limit(100000)->count();

        // search again with similarity
        if($count == 0 && $searchLevel <= 4 && isset($filter['search']) && !empty($filter['search'])){
            return $this->filterProduct($filter, true);
        }
        // die(json_encode($filter));
        $resultMap = $result
        ->offset($filter['offset'])
        ->limit($limitMap)
        ->get()->toArray();

        // calculate distance
        // if(isset($filter['latitude_user']) && isset($filter['longitude_user'])){
        //     foreach ($resultMap as $key => $product) {
        //         $resultMap[$key]['distance'] = Helper::distance($filter['latitude_user'], $filter['longitude_user'], $product['latitude'], $product['longitude']);
        //     }
        // }

       //search again with searchText similarity


        if(isset($filter['map_data']) && $filter['map_data'] == true){
            return $data = [
                'count' => $count,
                'result' => $resultMap
            ];

        }else{

            $resultList = array_slice($resultMap, $filter['offset'], $filter['limit']);
            return $data = [
                'count' => $count,
                'resultMap' => $resultMap,
                'resultList' => $resultList
            ];
        }
    }

    public function addZaloHashkey(array $data){
        $hashkeySetting = Setting::where('key', 'hash_key_zalo')->first();
        $result;

        if($hashkeySetting){

            $array_hash_key = json_decode($hashkeySetting->value);
            $indexKey = array_search($data['key_hash'], $array_hash_key);
            if($indexKey === false){
                array_push($array_hash_key, $data['key_hash']);
            }
            $hashkeySetting->value = json_encode($array_hash_key);
            $hashkeySetting->save();

            $result = $hashkeySetting = Setting::where('key', 'hash_key_zalo')->first();
        }
        else {
            $array_hash_key = [];
            array_push($array_hash_key, $data['key_hash']);
            $hashkeyNewSetting = [
                'key'   => 'hash_key_zalo',
                'value' =>  json_encode($array_hash_key),
                'description'   =>  'Hash key để đăng nhập zalo',
                'show_on_site'  => 3,
                'object_type'   => 1
            ];

            $result = Setting::create($hashkeyNewSetting);
        }
        return $result;
    }

    public function get_dashboard(array $data){

        
        $result = [];
        switch ($data['section']) {
            case 'best_around':
            case 'product_around':
                $aroundBound = $this->getBoundByRadius($data['latitude_user'], $data['longitude_user'], $data['radius'] ?? 95);
                $data['latitude_s']  = $aroundBound['latitude_s'];
                $data['latitude_b']  = $aroundBound['latitude_b'];
                $data['longitude_s'] = $aroundBound['longitude_s'];
                $data['longitude_b'] = $aroundBound['longitude_b'];
                break;
        }
        switch ($data['section']) {
            case 'suggest':
                $data['limit'] = 10;
                $data['offset'] = 0;
                $data['map_data'] = true;
                $data['sortBy'] = 'random';
                $result = $this->filterProduct($data);
                break;
            case 'sale_off':
                $data['limit'] = 10;
                $data['offset'] = 0;
                $data['map_data'] = true;
                $data['sortBy'] = 4;
                $result = $this->filterProduct($data);
                break;
            case 'best_around':
                $data['limit'] = 10;
                $data['offset'] = 0;
                $data['sortBy'] = 'distance';
                $result = $this->filterShop($data, true);
                break;
            case 'product_around':
                $data['limit'] = 10;
                $data['child_num'] = 2;
                $data['offset'] = 0;
                $data['sortBy'] = 'distance';
                $data['map_data'] = true;
                $result = $this->filterShopWithProduct($data);
                break;
            case 'hot_deal':
                $data['limit'] = 10;
                $data['offset'] = 0;
                $data['sortBy'] = 'random';
                $result = $this->filterShop($data, true);
                break;
            case 'hot_sale':
                $data['limit'] = $data['limit'] ?? 10;
                $data['offset'] = $data['offset'] ?? 3;
                $data['sortBy'] = 3;
                $data['map_data'] = true;
                $result = $this->filterProduct($data);
                break;
            case 'new':
                $data['limit'] = $data['limit'] ?? 20;
                $data['offset'] = $data['offset'] ?? 0;
                $data['sortBy'] = 3;
                $data['map_data'] = true;
                $result = $this->filterProduct($data);
                break;

            default:
                # code...
                break;
        }
        return $result;

    }

    public function getBoundByRadius($latitude, $longitude, $radius = 7){
        // 111.18957696 = 60 * 1.1515 * 1.609344: để quy đổi km ra độ lệch của tọa độ map
        $r = $radius / 111.18957696 ;
        $data = [];
        $data['latitude_s']  = $latitude - $r;
        $data['latitude_b']  = $latitude + $r;
        $data['longitude_s'] = $longitude - $r;
        $data['longitude_b'] = $longitude + $r;
        return $data;
    }


    // select with  pg_trgm extention
    public function search($searchText, $limit, $offset, $sortType = 'similarity_score', $sortDirect = 'DESC')
    {
        if(!$searchText) return [];

        $count = 0;
        if($offset == 0){

            $countQuery = "
            SELECT COUNT(*) as count
            FROM products
            WHERE similarity(name, ?) > 0.4
            AND type = 1
            AND enable = true
            ";
            $count = DB::select($countQuery, [$searchText])[0]->count;
        }
        $query2 = "
            SELECT id, name, profile_picture, price, similarity(name, ?) AS similarity_score
            FROM products
            WHERE similarity(name, ?) > 0.4
            AND type = 1
            AND enable = true
            ORDER BY similarity_score DESC
            LIMIT ?
            OFFSET ?";
        // $results = DB::connection('pgsqlReplica')->select($query2, [$searchText, $searchText, $limit, $offset]);
        $results = DB::select($query2, [$searchText, $searchText, $limit, $offset]);


        if($count == 0){
            if($offset == 0){

                $countQuery = "
                SELECT COUNT(*) as count
                FROM products
                WHERE similarity(name, ?) > 0.15
                AND type = 1
                AND enable = true
                ";
                $count = DB::select($countQuery, [$searchText])[0]->count;
            }
            $query2 = "
                SELECT id, name, profile_picture, price, similarity(name, ?) AS similarity_score
                FROM products
                WHERE similarity(name, ?) > 0.15
                AND type = 1
                AND enable = true
                ORDER BY similarity_score DESC
                LIMIT ?
                OFFSET ?";
            // $results = DB::connection('pgsqlReplica')->select($query2, [$searchText, $searchText, $limit, $offset]);
            $results = DB::select($query2, [$searchText, $searchText, $limit, $offset]);
        }
        // $count = 0;
        // $result = $results->limit($limit)->offset($offset)->get();

        return [
            'count' => $count,
            'result' => $results
        ];
    }
    public function suggest_search($data)
    {
        $productModel = Product::on('pgsqlReplica');
        $shopModel = Shop::on('pgsqlReplica');
        $searchText = Str::lower($data['search']);
        $translationModel = Translation::on('pgsqlReplica');


        # Search level 1
        $startWithQuery = $productModel->select('lowercase_name')
            ->where('lowercase_name','like', "%".$searchText."%")
            ->where('type','!=', 1)
            ->where('enable', true)
            ->orderByRaw("CASE
                WHEN lowercase_name LIKE '".$searchText."' THEN 1
                WHEN lowercase_name LIKE '".$searchText."%' THEN 2
                WHEN lowercase_name LIKE '%".$searchText."%' THEN 3
                WHEN lowercase_name LIKE '%".$searchText."' THEN 4
                ELSE 5
            END")
            ->groupBy('lowercase_name')
            // ->where([
            //     ['longitude', '>=', $data['longitude_s']],
            //     ['longitude', '<=', $data['longitude_b']],
            //     ['latitude', '>=', $data['latitude_s']],
            //     ['latitude', '<=', $data['latitude_b']]
            // ])
            // ->with('shop')
            // ->orderByDesc('similarity_score')
            ->limit(5)
            ->offset(0)
            ->get();

        # Search level 2
        if($startWithQuery->count() < 1){

            $searchText = Str::slug($searchText," ");
            $startWithQuery = $productModel->select('lowercase_name','unaccent_name')
            ->where('unaccent_name','like', "%".$searchText."%")
            ->where('type','!=', 1)
            ->where('enable', true)
            ->orderByRaw("CASE
                WHEN unaccent_name LIKE '".$searchText."' THEN 1
                WHEN unaccent_name LIKE '".$searchText."%' THEN 2
                WHEN unaccent_name LIKE '%".$searchText."%' THEN 3
                WHEN unaccent_name LIKE '%".$searchText."' THEN 4
                ELSE 5
            END")
            ->groupBy('lowercase_name','unaccent_name')
            ->limit(5)
            ->offset(0)
            ->get();
        }


        # Search level 3
        if($startWithQuery->count() < 1){
            $startWithQuery = $productModel->select('lowercase_name','unaccent_name')
            ->selectRaw("similarity(unaccent_name, ?) AS similarity_score", [$searchText])
            ->whereRaw("similarity(unaccent_name, ?) > 0.25", [$searchText])
            ->where('type','!=', 1)
            ->where('enable', true)
            // ->where([
                //     ['longitude', '>=', $data['longitude_s']],
                //     ['longitude', '<=', $data['longitude_b']],
                //     ['latitude', '>=', $data['latitude_s']],
                //     ['latitude', '<=', $data['latitude_b']]
                // ])
                ->with('shop')
                ->groupBy('lowercase_name','unaccent_name')
                ->orderByDesc('similarity_score')
                ->limit(5)
                ->offset(0)
                ->get();
            }


        $containQuery =  [];
        $containQuery = $translationModel
            ->select('id', 'object_id')
            ->where('object_type', config('constants.object_type.product'))
            ->whereRaw('tsv @@ plainto_tsquery(?)', $searchText)
            ->with('product')
            ->whereHas('product')
            ->orderBy('object_id')
            ->limit(5)
            ->get()
            ->pluck('product');
        /*
        $aroundBound = $this->getBoundByRadius($data['latitude_user'], $data['longitude_user'], $data['radius'] ?? 15);
        $data['latitude_s']  = $aroundBound['latitude_s'];
        $data['latitude_b']  = $aroundBound['latitude_b'];
        $data['longitude_s'] = $aroundBound['longitude_s'];
        $data['longitude_b'] = $aroundBound['longitude_b'];
        Product::select('*')
        ->selectRaw("similarity(unaccent_name, ?) AS similarity_score", [$data['search']])
        ->whereRaw("similarity(unaccent_name, ?) > 0.16", [$data['search']])
        ->where('type','<>', 1)
        ->where('enable', true)
        ->where([
            ['longitude', '>=', $data['longitude_s']],
            ['longitude', '<=', $data['longitude_b']],
            ['latitude', '>=', $data['latitude_s']],
            ['latitude', '<=', $data['latitude_b']]
        ])
        ->with('shop')
        ->orderByDesc('similarity_score')
        ->limit(10)
        ->offset(0)
        ->get();*/

        $results = [
            "strings" => $startWithQuery->pluck('lowercase_name'),
            "products" => $containQuery

        ];


        $resultShop = $shopModel->select('id','name','slug','logo_id','banner_id','business_type_id')
            ->where('name','ilike', "%".$searchText."%")
            ->where('enable', true)
            ->where('name','<>', 'Base Shop')
            ->orderByDesc('name')
            // ->inRandomOrder()
            ->limit(5)
            ->offset(0)
            ->get();

        $results['shops'] = ($resultShop);

        return $results;
    }

    public function reels(array $filter)
    {
        $productModel = new Product;
        $productModel->setConnection('pgsqlReplica');
        $result = $productModel->select('id','name','is_main','type','brand_id','profile_picture','latitude','longitude','notes','shop_id','price','price_off',
        'enable','created_by','parent_id','extra_id','unaccent_name','is_feature','lowercase_name','stock','sold_count','slug','is_suggest','views','likes',
        'follows','ratings','extra_code')
        ->with('categories:id,name', 'shop:id,name,user_id,currency,language,banner_id,logo_id')
        ->where('enable', true)
        ->whereNotNull('profile_picture')
        ->where('profile_picture', '<>','')
        ->where('type','<>', 1)
        ->inRandomOrder()
        ->limit(1);

        // if (
        //     isset(
        //         $filter['longitude_s'],
        //         $filter['latitude_s'],
        //         $filter['longitude_b'],
        //         $filter['latitude_b']
        //     ) &&
        //     !empty($filter['longitude_s']) &&
        //     !empty($filter['latitude_s']) &&
        //     !empty($filter['longitude_b']) &&
        //     !empty($filter['latitude_b'])
        // ) {

        // }{
        //     $aroundBound = $this->getBoundByRadius($filter['latitude_user'], $filter['longitude_user'], $filter['radius'] ?? 15);
        //     $filter['latitude_s']  = $aroundBound['latitude_s'];
        //     $filter['latitude_b']  = $aroundBound['latitude_b'];
        //     $filter['longitude_s'] = $aroundBound['longitude_s'];
        //     $filter['longitude_b'] = $aroundBound['longitude_b'];
        // }
        // $result->where([
        //     ['longitude', '>=', $filter['longitude_s']],
        //     ['longitude', '<=', $filter['longitude_b']],
        //     ['latitude', '>=', $filter['latitude_s']],
        //     ['latitude', '<=', $filter['latitude_b']]
        // ]);

        $result = $result->get();

        return $result;
    }

    //------process detail Order----------
    public function OrderDetailByShortCode($id)
    {
        $result = Order::where('short_code', $id)
            ->with('items', 'shops', 'customer','delivery','items','images', 'deliveryPartner')
            ->first();

        if(!$result)
        {
            return false;
        }
        $result['price'] = $result['grand_total'];
        $result['extra_data'] = $result['extra_data'] ? json_decode($result['extra_data']) : null;
        return $result;
    }


    // Danh sách các shop được user tương tác
    public function listShopByUser(array $filter)
    {
        $interactions = Interaction::on("pgsqlReplica")
            ->where([
                'object_id_a' =>  $this->_userId,
                'object_type_a' => config('constants.object_type.user'),
                'object_type_b' => config('constants.object_type.shop')
            ]);

        if(isset($filter['type'])){
            $interactions = $interactions->where('interaction_type', $filter['type']);
        }

        $shopIds = $interactions->get()->pluck('object_id_b')->toArray();
        $result = Shop::on('pgsqlReplica')->whereIn('id', $shopIds);

        $count = $result->count();
        $result = $result->limit($filter['limit'] ?? 20)->offset($filter['offset'] ?? 0)->get();
        return [
            'count' => $count,
            'result' => $result
        ];
    }
}
