<?php

namespace App\Http\Requests\Image;

use App\Http\Requests\BaseRequest;

class ImageFileRequest extends BaseRequest
{
    public function rules()
    {
        switch ($this->object_type) {
            case '1':
                $parent_id = 'required|uuid|exists:shops,id';
                break;
            case '2':
                $parent_id = 'required|uuid|exists:products,id';
                break;
            case '3':
                $parent_id = 'required|uuid|exists:brands,id';
                break;
            case '4':
                $parent_id = 'required|uuid|exists:categories,id';
                break;
            default:
                $parent_id = 'required|uuid|exists:products,id';
                break;
        }

        return [
            // 'path' => 'required|image|mimes:jpg,jpeg,png|between:0,100000000',
            'path' => [
                    'required',
                    // 'image',
                    'file_extension:jpeg,png,jpg',
                    'dimensions:min_width=350,min_height=350',
                    // 'max:100000000'
                    'max:10000'
                    // 'mimes:jpeg,png'
                ],
            'object_type'           => 'required|in:1,2,3,4,5,6,7,8,9',
            'parent_id'             => $parent_id,
            // 'is_profile_picture'    => 'nullable|boolean',
            // 'index'                 => 'nullable|integer|min:0'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'path.required' => 'Image_001_E_001',

    //         'object_type.required' => 'Image_001_E_004',
    //         'object_type.in' => 'Image_002_E_005',

    //         'parent_id.required' => 'Image_001_E_008',
    //         'parent_id.uuid' => 'Image_002_E_009',
    //         'parent_id.exists' => 'Image_003_E_010',

    //         'path.image' => 'Image_002_E_014',
    //         'path.file_extension' => 'Image_002_E_015',
    //         'path.max' => 'Image_004_E_016',

    //         'is_profile_picture.boolean' => 'Image_002_E_017',

    //         'index.integer'             => 'Image_002_E_018',
    //         'index.min'                 => 'Image_004_E_019'

    //     ];
    // }
}
