<?php
namespace App\Services;

use App\Delivery;
use App\Services\Log\LogService;
use App\Services\MasterHistory\MasterHistoryService;
use App\Services\Master\MasterService;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use App\User;
use App\Image;
use App\Setting;
use App\Order;
use App\Shop;
use App\Product;
use Illuminate\Support\Facades\Log;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Intervention\Image\ImageManagerStatic as Im;
use App\Log as LogTable;
use App\Helpers\S3Utils;
use Illuminate\Http\JsonResponse;

// use Intervention\Image\Facades\Image as ImageLib;

class GeneralService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId)
        {
            $this->_userId = $userId;
        }
        else
        {
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    //----add log----------
    public function addLog($action, $object_id, $object_type, $data_response, $data_request)
    {
        $settings = Setting::where('key', 'log_object_active')->first();
        if ($settings && in_array($object_type, explode(',', $settings->value))) {
            $data = [
                'action' => $action,
                'object_id' => $object_id,
                'object_type' => $object_type,
                'data_response' => $data_response,
                'data_request' => $data_request
            ];

            $service = new LogService($this->_userId);
            $service->add($data);
        }
    }

    //--------add history---------------
    public function addHistory($master_id, $latitude, $longitude)
    {
        $data = [
            'master_id' => $master_id,
            'latitude' => $latitude,
            'longitude' => $longitude,
        ];

        $service = new MasterHistoryService;
        $service->add($data);

    }

    //---------add master-------------------
    public function addMaster($id, $latitude_begin, $longitude_begin, $status, $type, $created_by, $owner)
    {
        $data = [
            'id' => $id,
            'latitude_begin' => $latitude_begin,
            'longitude_begin' => $longitude_begin,
            'latitude_current' => $latitude_begin,
            'longitude_current' => $longitude_begin,
            'status' => $status,
            'type' => $type,
            'created_by' => $created_by,
            'owner' => $owner
        ];

        $service = new MasterService;
        $service->add($data);
    }
    //----------update master---------------
    public function updateMaster($id, $latitude_current, $longitude_current, $status)
    {
        $data = [
            'id' => $id,
            'latitude_current' => $latitude_current,
            'longitude_current' => $longitude_current,
            'status' => $status,
        ];

        $service = new MasterService;
        $service->update($data);

    }

    /**
     * Connect to Redis. Throw RedisException if it can't connect.
     *
     * @throws \RedisException
     */
    public static function redisConnection()
    {
        $redis = null;
        try {
            $redis = Redis::connection();
            if ($redis == null) {
                throw new \RedisException("Can't connect to Redis.");
            }
        } catch (\RedisException $e) {
            throw new \RedisException($e->getMessage());
        }
        return $redis;
    }

    //---------process a image-------------
    public function avatar($imageId, $imageType = null)
    {
        $imageType = isset($imageType) ? $imageType : config('constants.image_type.thumbnail');
        switch ($imageType) {
            case config('constants.image_type.medium'):
                $check = Image::find($imageId);

                if($check && $check->image_type == config('constants.image_type.root'))
                {
                    // if($check->have_medium == true)
                    // {
                    //     $check->path = str_replace(".jpeg", "_medium.jpeg", $check->path);
                    // }
                    $check = [
                        'id' => $check->id,
                        'path' => $check->path,
                        'parent_id' => $check->parent_id,
                        'title' => $check->title
                    ];
                }
                else
                {
                    $check = null;
                }
                $result = $check;
                break;

            default:
                $check = Image::find($imageId);

                if($check && $check->image_type == config('constants.image_type.root'))
                {

                    if($check->have_thumbnail == true)
                    {
                        $check->path = str_replace(".jpeg", "_thumbnail.jpeg", $check->path);
                    }

                    $check = [
                        'id' => $check->id,
                        'path' => $check->path,
                        'parent_id' => $check->parent_id,
                        'title' => $check->title
                    ];
                }
                else
                {
                    $check = null;
                }
                $result = $check;
                break;
        }
        return $result;
    }

    //-------process list image-------------
    public function listImage($data, $imageType = null)
    {
        $data = isset($data) ? $data : [];
        $imageType = isset($imageType) ? $imageType : config('constants.image_type.medium');
        $root = config('constants.image_type.root');

        foreach ($data as $key => $value) {
            if($value->image_type == $root)
            {
                $path = $this->avatar($value->id, $imageType);
                $value['path'] = $path['path'];
            }
        }

        return $data;
    }

    //-----------process price-----------------
    public function handelPrice($price, $currency, $site_area = 1)
    {
        $set = Setting::where('key', 'usd_to_vnd')->first();
        $usdToVnd = json_decode($set->value, true);
        switch ($currency) {
            case config('constants.currency.VNĐ'):
                if($price === null)
                {
                    $result = null;
                }
                else {
                    $result = $price * $site_area;
                }
                break;

            case config('constants.currency.USD'):
                if($price === null)
                {
                    $result = null;
                }
                else {
                    $result = $price * $site_area * $usdToVnd[0];
                }

                break;
            default:
                $result = 0;
                break;
        }
        return $result;
    }

    //---------process user have image-----------------
    public function handleUser($id, $imageType = null)
    {
        $imageType = isset($imageType) ? $imageType : config('constants.image_type.thumbnail');

        $result = User::select('id','name','profile_picture')->find($id);

        if($result)
        {
            $result['avatar'] = $this->avatar($result->profile_picture, $imageType);
            return $result;
        }

        return $result;
    }

    //----------ListAvatar---------------
    public function listAvatar($result, $profile_picture = null)
    {
        $image = $result->images()->where('image_type', config('constants.image_type.root'))->select('id', 'path', 'parent_id','title')->get();
        $avatar = [];
        $listimg = [];
        foreach ($image as $key => $value) {
            if($value['id'] == $profile_picture)
            {
                $avatar[] = $value;
            }
            else
            {
                $listimg[] = $value;
            }
        }

        $result = array_merge($avatar,$listimg);
        return $result;
    }

    public function getUserIpAddr(){
        $ipaddress = '';
        if (isset($_SERVER['HTTP_CLIENT_IP']))
        {

            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        }

        else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        {

            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        }

        else if(isset($_SERVER['HTTP_X_FORWARDED']))
        {

            $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
        }

        else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
        {

            $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
        }

        else if(isset($_SERVER['HTTP_FORWARDED']))
        {

            $ipaddress = $_SERVER['HTTP_FORWARDED'];
        }

        else if(isset($_SERVER['REMOTE_ADDR']))
        {

            $ipaddress = $_SERVER['REMOTE_ADDR'];
        }

        else
        {

            $ipaddress = 'UNKNOWN';
        }

        return $ipaddress;
     }


    public function readExcel($file, $columnNameRow = 5, $startDataRow = 6, $startColumn = "A", $endColumn = "K"){
        // $file = public_path('moon_milk.xlsx');

        // Get the uploaded file from the request
        // $file = $request->file('file');

        $spreadsheet = IOFactory::load($file);
        $sheetNames = $spreadsheet->getSheetNames();

        $result = [];
        foreach ($sheetNames as $sheetIndex => $sheetName) {
            $sheetIndex;
            $result['sheet_lists']["sheet_".strval($sheetIndex)] = $sheetName;
        }

        foreach ($sheetNames as $sheetIndex => $sheetName) {
            // $time_start = microtime(true);
            $sheet = $spreadsheet->getSheet($sheetIndex);

            $highestRow = $sheet->getHighestRow();
            $highestColumn = $endColumn;//$sheet->getHighestColumn();
            $columnNames = $sheet->rangeToArray($startColumn.$columnNameRow.':' . $highestColumn . $columnNameRow)[0];
            // Replace line breaks or newline characters in column names
            $columnNames = array_map(function ($columnName) {
                return str_replace(["\n", "\r"], '', $columnName);
            }, $columnNames);
            $columnNames = array_map('trim', $columnNames); // Trim each column name
            // $columnNames = array_filter(array_map('trim', $columnNames)); // Remove null items and trim each column name
            // $targetIndex = max(array_keys($columnNames))+1; // The target column index (e.g., 10 for the 10th column)
            // $additionColumn = min(array_keys($columnNames));
            // $highestColumn = Coordinate::stringFromColumnIndex($targetIndex);

            // Log::info($columnNames);
            // Log::info($highestColumn);
            $rowData = [];
            for ($row = $startDataRow; $row <= $highestRow; $row++) {
                $rowValues = $sheet->rangeToArray($startColumn . $row . ':' . $highestColumn . $row)[0];
                $trimmedRowValues = (array_map('trim', $rowValues)); // Trim each row value
                // Log::info($trimmedRowValues);
                $rowData[] = array_combine($columnNames, $trimmedRowValues);
            }

            $result['sheet_data']["sheet_".strval($sheetIndex)] = [
                'sheet_name' => $sheetName,
                'column_names' => $columnNames,
                'row_data' => $rowData,
            ];
            // $time_end = microtime(true);

            //dividing with 60 will give the execution time in minutes otherwise seconds
            // $execution_time = ($time_end - $time_start);

            //execution time of the script
            // echo 'highestRow: '.$highestRow.'<b>Total Execution '.$sheetName.' Time:</b> '.$execution_time.' Secs'."</br>";
        }
        // Log::info(($result));
        return $result;
    }
    private function getSheetName($file, $sheetIndex)
    {
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file);
        $sheetNames = $spreadsheet->getSheetNames();
        return $sheetNames[$sheetIndex];
    }
    public function checkPhpIniSettings()
    {
        $uploadMaxFileSize = ini_get('upload_max_filesize');
        $postMaxSize = ini_get('post_max_size');

        return response()->json([
            'upload_max_filesize' => $uploadMaxFileSize,
            'post_max_size' => $postMaxSize,
            'max_execution_time' => ini_get('max_execution_time'),
            'max_input_time' => ini_get('max_input_time'),
            'memory_limit' => ini_get('memory_limit'),
        ]);
    }
    # check if delivery belongs to user's shop
    static function checkShopOwnerDelivery($userID, $deliveryID)
    {
        $delivery = Delivery::find($deliveryID);
        if ($delivery) {
            $order = Order::find($delivery->order_id);
            if ($order) {
                $user = User::where('id', $userID)->with('shop')->first();
                if ($user && isset($user->shop[0])) {
                    return $user->shop[0]->id == $order->shop_id;
                }
            }
        }
        return false;
    }

    # check if delivery belongs to user's shop
    static function checkShopAgentDelivery($userID, $deliveryID)
    {
        $delivery = Delivery::find($deliveryID);
        if ($delivery) {
            $order = Order::on('pgsqlReplica')->with('shops')->find($delivery->order_id);
            if ($order && $order->shops) {
                return $order->shops['agent_id'] == $userID;
            }
        }
        return false;
    }

    # check request Order is belongs to account or not.
    static function checkShopOwnerOrder($userID, $orderID){
        $user = User::where('id', $userID)->with('shop')->first();
        if($user){
            $order = Order::find($orderID);
            foreach ($user->shop as $key => $shop) {
                if($order && $shop->id ==  $order->shop_id){
                    return true;
                }
            }
        }
        return false;
    }

    # check request Shop is belongs to account or not.
    static function checkShopOwner($userID, $shopID){

        // $shops = Shop::where('user_id', $userID)->where('id', $shopID)->count();
        // if($shops > 0) return true;
        // return false;
        $shop = Shop::find($shopID);
        if($shop){
            if($shop->user_id == $userID)
                return true;
        }
        return false;
    }
    # check request Shop is relate to account or not.
    static function checkAgentShopRelate($agentID, $shopID)
    {
        $user = User::find($agentID);
        if ($user && $user->role_id == User::ROLE_ADMIN) {
            return true;
        }
        $field = preg_match('/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i', $shopID) ? 'id' : 'slug';
        $result = Shop::where($field, $shopID)->first();
        if (!$result) {
            return false;
        }
        return ($result && $result->agent_id == $agentID);
    }

    static function generateQRCodeAndPlaceInFrame($qrValue, $logo = '', $title = 'remagan.com')
    {
        // Generate the QR code
        // $tempQrCode = public_path('qr/qr_tmp.jpg');
        if (!is_dir( public_path('qr/'))) {
            mkdir( public_path('qr/'), 0777, true);
        }
        // echo "Generate QR for " . $qrValue;
        $unique = uniqid();
        $qrCode = $tempQrCode = '';
        if($logo !== ''){
            $logoPath = $logo;
            // Path to store the cropped logo temporarily
            $croppedLogoPath =  public_path('qr/').'cropped_logo_'.$unique.'.png';
            // $maskPath =  public_path('qr/').'mask.png';
            $maskPath =  "https://s3.remagan.com/pro.remagan.uploads/static/logo_mask.png";

            $image = Im::make($logoPath)->fit(75,75);
            $image->encode('png');


            // making mask: round & white, no background
            /*
            $width = 75;$height = 75;$mask = Im::canvas($width, $height);
            // draw a white circle
            $mask->circle($width, $width/2, $height/2, function ($draw) {
                $draw->background('#fff');
            });
            $mask->save($maskPath);
            */


            // echo "\n adding mask";

            $image->mask($maskPath, true);
            $image->save($croppedLogoPath);


            // Generate the QR code with logo
            $qrCode = QrCode::format('png')
                ->size(450)
                ->errorCorrection('H')
                ->merge($croppedLogoPath, 0.3, true)
                ->generate($qrValue);
            unlink($croppedLogoPath);
        }else{
            $qrCode = QrCode::size(450)->format('png')->generate($qrValue);
        }

        // // Load the shop frame
        // $framePath = public_path('qr/qr_shop_frame.jpg');
        $framePath = "https://s3.remagan.com/pro.remagan.uploads/static/qr_shop_frame.jpg";
        $frame = Im::make($framePath);
        // Create a temporary image from QR code data
        $tempQrCode = public_path('qr/').'qr_tmp_'.$unique.'.jpg';
        file_put_contents($tempQrCode, $qrCode);


        // Check if the QR code image is readable
        if ($tempQrCode && is_readable($tempQrCode))
        {
            // Load the QR code image
            $qrCodeImage = Im::make($tempQrCode);

            // Resize QR code to fit inside the frame
            $qrCodeImage->resize(450, 450);

            // Calculate position to place QR code in the frame
            $x = ($frame->width() - $qrCodeImage->width()) / 2;
            $y = ($frame->height() - $qrCodeImage->height()) / 2 + 80;

            // Insert QR code into the frame
            $frame->insert($qrCodeImage, 'top-left', $x, $y);
            // echo "\n Adding text";
            $frame->text($title, 450, 250, function($font) {
                $font->file(public_path('assets/fonts/Nunito/Nunito-Bold.ttf'));
                $font->size(44);
                $font->color('#000000');
                $font->align('center');
                $font->valign('top');
            });
            // Save the image
            $outputPath = public_path('qr/generated_qr_code_'.$unique.'.jpg');
            $frame->save($outputPath);

            // Clean up temporary QR code file
            unlink($tempQrCode);
            // die;
            $QRIMAGE = file_get_contents($outputPath);
            $uploaded = S3Utils::upload($QRIMAGE, 'static/qr_code_'.$unique.'.jpg');
            return $qrPathS3 = 'static/qr_code_'.$unique.'.jpg';
            return response()->json($qrPathS3, JsonResponse::HTTP_OK);
            return response()->download($outputPath)->deleteFileAfterSend(true);
        }
        else {
            // Handle the case where the QR code image is not readable
            return response()->json(['error' => 'Unable to read QR code image'], 500);
        }
    }

}
