<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class ImportGeocodeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    // protected $signature = 'command:importGeocode {file}';
    protected $signature = 'command:importGeocode';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import geocode data from a CSV file';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // $filePath = public_path(('Shipper_Map2025_full.csv'));
        // $filePath = public_path(('geocodes/ninh_thuan_streets_with_admin_units.csv'));
        // $filePath = public_path(('geocodes/binh_thuan_streets_with_admin_units.csv'));
        $filePath = public_path(('geocodes/khanh_hoa_streets_with_admin_units.csv'));
        // $filePath = public_path($this->argument('file'));
        $data = Excel::toArray([], $filePath)[0]; // Read the first sheet

        $geocodeArr = [];
        $countTotal = count($data) - 1; // Exclude header row
        $this->info("Total records to import: $countTotal");
        $header = $data[0]; // Get the header row
        foreach ($data as $index => $record) {
            if ($index === 0) {
                continue; // Skip header row
            }
            $geocodeArr[] = [
                'id' => Str::uuid(),
                'address' => $record[array_search('full_address', $header)],
                'latitude' => $record[array_search('latitude', $header)],
                'longitude' => $record[array_search('longitude', $header)],
                'district_id' => $record[array_search('district_id', $header)],
                'ward_id' => $record[array_search('ward_id', $header)],
                'province_id' => $record[array_search('province_id', $header)],
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Insert in batches to avoid memory issues
            if (count($geocodeArr) >= 1000 || $index === $countTotal) {
                echo $index."\n";
                DB::table('geocodes')->insert($geocodeArr);
                $geocodeArr = []; // Reset the array
            }
        }

        // Insert any remaining records
        if (!empty($geocodeArr)) {
            DB::table('geocodes')->insert($geocodeArr);
        }

        Log::info('Geocode data imported successfully.');
        return 'Data imported successfully.';
    }
}
