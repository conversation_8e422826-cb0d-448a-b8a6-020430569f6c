<?php

namespace App\Http\Requests\Address;

use App\Http\Requests\BaseRequest;

class ProvinceConvertVN2000ToWGS84 extends BaseRequest
{
    
    public function rules()
    {
        return [
            'arrVN2000'     => 'required|array',
            'arrVN2000.*.X' => 'required',
            'arrVN2000.*.Y' => 'required'
        ];
    }
    public function messages()
    {
        return [

            'arrVN2000.required' => 'Arr_VN_001_E_001',
            'arrVN2000.array' => 'Arr_VN_002_E_002',

            'arrVN2000.*.X.required' => 'Arr_VN_001_E_003',
            'arrVN2000.*.Y.required' => 'Arr_VN_001_E_004'
        ];
    }
}
