<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Routing\Exceptions\InvalidSignatureException;
use Illuminate\Support\Facades\Redis;
use App\Services\GeneralService;
use App\User;
class Email_verify
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = User::find($request->route('id'));
        if( $user && $user->hasVerifiedEmail())
        {
            $response = [
                'status' => JsonResponse::HTTP_FORBIDDEN,
                'body' => [
                    'C_E_010',
                    'email' => $user->email,
                    'name' => $user->name
                ],
            ];
            return response()->json($response,JsonResponse::HTTP_OK);
        }
        $redis = GeneralService::redisConnection();
        try {
            $dataRedis = $redis->get($request->route('id'));
            if ($dataRedis != null) {
                return $next($request);
            }
        } catch (\Throwable $th) {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_BAD_REQUEST,
            'body' => ['C_E_009'],
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }
}
