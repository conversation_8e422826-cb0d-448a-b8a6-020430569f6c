<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Reel extends Model
{
    protected $table = 'reels';
    protected $primaryKey = 'id';
    public $incrementing = false;
    public $timestamps = false;
    protected $fillable = ['id', 'object_id', 'object_type', 'description', 'sound', 'reel_type'];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
            $model->views = 0;
        });
    }

    function comments()
    {
        return $this->hasMany(Rating::class, 'object_id', 'id');
    }
}
