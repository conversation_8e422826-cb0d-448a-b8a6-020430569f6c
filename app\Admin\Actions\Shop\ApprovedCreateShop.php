<?php

namespace App\Admin\Actions\Shop;

use App\Mail\SendMailApprovedShop;
use App\Notifications\NotificationUser;
use App\Services\Notification\NotificationService;
use App\Services\Token\TokenService;
use App\User;
use Encore\Admin\Actions\RowAction;
use Illuminate\Support\Facades\Log;
use Notification;

class ApprovedCreateShop extends RowAction
{

    public function name()
    {
        return __('admin.shop.approved');
    }

    public function handle($model)
    {
        $currentStatus = $model->status;
        $status = request('status');
        $reason = request('reason') ?? "";

        $mess = [
            config('constants.shop.status.accepted') => [
                "body"  => "Cửa hàng '[" . $model->name . "]' của bạn đã được phê duyệt thành công.",
                "title" => "Cửa hàng '[" . $model->name . "]' đã được phê duyệt."
            ],
            config('constants.shop.status.rejected') => [
                "body"  => "Rất tiếc cửa hàng '[" . $model->name . "]' của bạn đã bị từ chối duyệt.",
                "title" => "Cửa hàng '[" . $model->name . "]' bị từ chối duyệt."
            ]
        ];

        $messToNoti = [
            'body'  => $mess[$status]["body"] ?? "",
            'title' => $mess[$status]["title"] ?? "",

            // 'image' => $image,
            'url'   => $model->id,
            'target_url' => '/my-shop',
            'type'  => 'shop_update',
        ];
        if($status != config('constants.shop.status.in_process') && $status != $currentStatus) {
            $user = User::find($model->user_id);
            if($user){
                $tokenService = new TokenService();
                $tokens = $tokenService->listByUser($user->id, 2);
                $notiService = new NotificationService;
                if(count($tokens) > 0){
                    $notiService->sendBatchNotification($tokens, $messToNoti);
                }
                if($user->email){

                    \Mail::to($user->email)->send(new SendMailApprovedShop($user->name, $model->name, $status, $reason));
                }

                $user->notify(new NotificationUser($model->user_id, $messToNoti));
            }
        }


        if($currentStatus != $status){
            switch ($status) {
                case 2:
                    $model->enable = true;
                    break;
                case 3:
                case 1:
                default:
                    $model->enable = false;
                    break;
            }
        }

        switch ($status) {
            case 1:
                $message = __('admin.shop.status.1');
                break;
            case 2:
                $message = __('admin.shop.status.2');
                break;
            case 3:
                $message = __('admin.shop.status.3');
                break;
            default:
                $message = __('Unknown');
                break;
        }

        $model->status = $status;
        $model->save();

        return $this->response()
            ->success($message)
            ->refresh();
    }



    public function form()
    {
        $type = [
            1 => __('admin.shop.status.1'),
            2 => __('admin.shop.status.2'),
            3 => __('admin.shop.status.3'),
        ];

        $currentStatus = $this->row->status;

        // Radio button
        $this->radio('status', __('admin.status'))
            ->options($type)
            ->attribute(['class'])
            ->default($currentStatus);


        $this->textarea('reason', __("admin.reason.title"));

    }


}
