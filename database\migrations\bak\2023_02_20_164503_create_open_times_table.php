<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOpenTimesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('open_times', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('object_id');
            $table->uuid('object_type')->comment("Loại đối tượng: 1 - Shop, 2 - Product");
            $table->boolean('enable')->default(1);
            $table->uuid('created_by')->nullable();
            $table->timestamp('from')->nullable()->comment('thời gian mở bán');
            $table->timestamp('to')->nullable()->comment('thời gian nghỉ bán');

            $table->index('created_by');
            $table->index('from');
            $table->index('to');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('open_times');
    }
}
