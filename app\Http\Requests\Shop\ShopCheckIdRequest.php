<?php

namespace App\Http\Requests\Shop;

use App\Http\Requests\BaseRequest;

class ShopCheckIdRequest extends BaseRequest
{
   
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:shops,id'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'Shop_001_E_012',
    //         'id.uuid' => 'Shop_002_E_013',
    //         'id.exists' => 'Shop_003_E_014',
    //     ];
    // }
}
