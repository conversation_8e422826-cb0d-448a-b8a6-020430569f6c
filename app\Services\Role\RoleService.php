<?php
namespace App\Services\Role;
use App\Role;
use Illuminate\Support\Facades\Auth;
use App\User;

class RoleService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }
    //-----process list role--------------
    public function list()
    {
        $role = Auth::user()->role_id;
        $data = Role::find($role);
        if($data->name == 'Root Admin')
        {
            $result = Role::orderBy('id','asc')->get();
            return $result;
        }
        elseif($data->name == 'Sub-Admin')
        {
            $result = Role::whereIn('name',['Seller','Messenger','Collector','Freelancer'])->orderBy('id','asc')->get();
            return $result;
        }

    }

    //-----process insert role--------------

    public function insert(array $role)
    {
        $result = Role::create($role);

        return $result;
    }

    //-----process update role-------------

    public function update(array $role)
    {
        $result = Role::find($role['id']);

        if(!$result)
        {
            return false;
        }

        $result->update($role);

        return $result;
    }

    //-----------process detail role------------

    public function detail($id)
    {
        $result = Role::find($id);

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //----------process delete role---------------

    public function delete(array $role)
    {
        $result = Role::find($role['id']);

        if(!$result)
        {
            return false;
        }

        $result->delete();

        return $result;
    }
    public function listAddFile()
    {
        $result = Role::get();
        return $result;
    }
}
