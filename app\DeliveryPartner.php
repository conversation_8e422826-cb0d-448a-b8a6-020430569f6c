<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use App\Shop;

class DeliveryPartner extends Model
{
    use  SoftDeletes;

    protected $table = 'delivery_partners';

    protected $dateFormat = 'Y-m-d H:i:s';
    protected $keyType = 'string';
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'information' => 'array'
    ];
    protected $fillable = [
        'name',
        'contact_number',
        'email',
        'address',
        'api_key',
        'api_secret',
        'is_active',
        'notes',
        'information'
    ];


    /**
     * Boot function to handle UUID generation.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = $model->id ?? Str::uuid();
        });
    }

    /**
     * Define the relationship with ShopDeliveryPartner.
     */
    public function shops()
    {
        return $this->belongsToMany(Shop::class, 'shop_delivery_partners')
            ->withPivot('is_enabled')
            ->withTimestamps();
    }

    public function shopDeliveryPartners()
    {
        return $this->hasMany(ShopDeliveryPartner::class, 'delivery_partner_id');
    }
}
