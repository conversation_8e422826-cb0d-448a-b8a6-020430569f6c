#!/bin/bash

# stops the execution of a script if a command or pipeline has an error - which is the opposite of the default shell behaviour, which is to ignore errors in scripts.
set -e

role=${CONTAINER_ROLE:-app}
env=${APP_ENV:-production}
queue_name=${QUEUE_NAME:-default}

if [ "$env" != "local" ]; then
    echo "Caching configuration... MUST --restart always"
    (cd /workspace/clomart-backend && php artisan optimize:clear)
fi

if [ "$role" = "app" ]; then

    exec apache2-foreground

elif [ "$role" = "queue" ]; then
    if [ "$queue_name" = "summary" ]; then
            echo "Running summary_queue the queue..."
            php /workspace/clomart-backend/artisan queue:work --queue=summary_queue --tries=3 --timeout=36000
    elif [ "$queue_name" = "nac" ]; then
            echo "Running nac_queue the queue..."
            php /workspace/clomart-backend/artisan queue:work --queue=nac_queue --tries=3 --timeout=36000
    else
            echo "Running the default queue..."
            php /workspace/clomart-backend/artisan queue:work --tries=3 --timeout=36000
    fi

elif [ "$role" = "scheduler" ]; then

    while [ true ]
    do
      php /workspace/clomart-backend/artisan schedule:run --verbose --no-interaction &
      sleep 60
    done

else
    echo "Could not match the container role \"$role\""
    exit 1
fi
