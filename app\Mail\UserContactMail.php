<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Services\HistoryMail\HistoryMailService;

class UserContactMail extends Mailable
{
    use Queueable, SerializesModels;


    private $_data;
    public function __construct($data)
    {
        $this->_data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {

        $historyMail = null;
        try {
            $historyMail = HistoryMailService::insert([
                'title' => 'Yêu cầu khách hàng về bất động sản',
                'content' => json_encode($this->_data),
                'status' => config('constants.status_history_mail.success')
            ]);
            return $this->subject('Yêu cầu khách hàng về bất động sản: '.$this->_data['name_property'])
            ->view('emails.contact_user_mail')->with([
                                                    'name'              => $this->_data['name'],
                                                    'phone'             => $this->_data['phone'],
                                                    'content'           => $this->_data['content'],
                                                    'id_property'       => $this->_data['id_property'],
                                                    'name_property'     => $this->_data['name_property'],
                                                    'url_property'      => $this->_data['url_property'],
                                                    'knb_id'            => $this->_data['knb_id'],
                                                    'local_url'             => $this->_data['local_url'],
                                                    'email'             => $this->_data['email']
                                                ]);

        } catch (\Exception $e) {
            Log::info("Mail Error: ".$e);

            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

    }
}
