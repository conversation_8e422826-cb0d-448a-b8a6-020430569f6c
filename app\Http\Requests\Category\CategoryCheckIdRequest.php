<?php

namespace App\Http\Requests\Category;

use App\Http\Requests\BaseRequest;

class CategoryCheckIdRequest extends BaseRequest
{
   
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:place_types,id'
        ];
    }

    public function messages()
    {
        return [
            // 'id.required' => 'Category_001_E_012',
            // 'id.uuid' => 'Category_002_E_013',
            // 'id.exists' => 'Category_003_E_014',
        ];
    }
}
