<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class UpdateInactiveDriverStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'drivers:update-inactive-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set user_status to offline for drivers inactive for 24 hours';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $twentyFourHoursAgo = Carbon::now()->subHours(24);
            
            // Find and update all inactive drivers
            $inactiveDrivers = User::where('role_id', User::ROLE_DRIVER)
                ->where('user_status', '!=', 0)
                ->where(function($query) use ($twentyFourHoursAgo) {
                    $query->whereNull('last_action_at')
                        ->orWhere('last_action_at', '<', $twentyFourHoursAgo);
                })
                ->get();
            
            $count = 0;
            foreach ($inactiveDrivers as $driver) {
                $driver->user_status = 0;
                $driver->save();
                $count++;
            }
            
            Log::info("Updated status for {$count} inactive drivers.");
            $this->info("Updated status for {$count} inactive drivers.");
            
            return 0;
        } catch (\Exception $e) {
            Log::error('Error updating inactive driver status: ' . $e->getMessage());
            $this->error('Error updating inactive driver status: ' . $e->getMessage());
            return 1;
        }
    }
}
