<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class RestoreUserRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:users,id',
        ];
    }
    // public function messages()
    // {
    //     return [
    //         'id.exists' => 'User_003_E_019',
    //         'id.uuid' => 'User_002_E_020',
    //         'id.required' => 'User_001_E_053',
    //     ];
    // }
}
