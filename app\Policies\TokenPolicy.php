<?php

namespace App\Policies;

use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use App\Policies\BasePolicy;

class TokenPolicy extends BasePolicy
{
    use HandlesAuthorization;

     // ------can view---------
     public function show(?User $user)
     {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting = $this->setting('token_view');

        return in_array($user->role_id, json_decode($setting['value'],true));
     }
}
