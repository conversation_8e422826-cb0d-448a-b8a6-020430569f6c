<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\BusinessType\BusinessTypeService;

class BusinessTypeController extends Controller
{
    // -------create businessType---------------
    public function create(Request $request)
    {
        $data = $request->only([
            'name',
            'description',
        ]);

        $service = new BusinessTypeService(Auth::user()->id);
        $result = $service->create($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list businessType-----------------
    public function list(Request $request, BusinessTypeService $service)
    {
        $arrays = $request->all();

        $result = $service->list();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
                'count' => $result->count()
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------detail businessType-------------
    public function detail($id = null, BusinessTypeService $service)
    {
        $result = $service->detail($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update businessType-----------
    public function update(Request $request, BusinessTypeService $service)
    {
        $data = $request->only([
            'id',
            'name',
            'description',
        ]);

        $result = $service->update($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------remove businessType----------------
    public function remove(BusinessTypeCheckIdRequest $request, BusinessTypeService $service)
    {
        $data = $request->only(['id']);

        $result = $service->remove($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------delete businessType--------------
    public function delete(Request $request, BusinessTypeService $service)
    {
       $data = $request->only(['id']);

       $result = $service->delete($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }

}
