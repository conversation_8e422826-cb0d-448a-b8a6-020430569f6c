<?php
namespace App\Services\Agent;

use App\Notifications\NotificationUser;
use App\Product;
use App\ProductCategory;
use App\Category;
use App\Shop;
use App\Image;
use App\User;
use App\PlaceType;
use App\Order;
use App\OrderItem;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use App\Services\GeneralService;
use App\Services\Token\TokenService;
use App\Services\Notification\NotificationService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\Helpers\Helper;
use App\Translation;
use App\StockTracking;

class AgentService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

     //-------process create shop--------
    public function create_shop(array $shop)
    {

        $shop['created_by'] = $this->_userId;
        $shop['agent_id'] = $this->_userId;
        $shop['order_type'] = 2;


        //--------create slug-----------------

        // crc32(Carbon::now());
        $shop['slug'] = Helper::makeSlug($shop['name'], true);


        $result = Shop::create($shop);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.create'),
        //                 $result->id,
        //                 config('constants.object_type.shop'),
        //                 $result,
        //                 json_encode($shop));

        return $result;
    }

    //-------process copy shop--------
    public function copy_shop($shopId)
    {
        $shop = array();
        $oldShop = Shop::with('all_products','categories')->findOrFail($shopId);
        // crc32(Carbon::now());
        if($oldShop){
            $shop['address'] = $oldShop->address;
            // $shop['user_id'] = $oldShop->user_id;
            $shop['agent_id'] = $oldShop->agent_id;
            $shop['slug'] = $oldShop->slug;
            $shop['business_type_id'] = $oldShop->business_type_id;
            $shop['latitude'] = $oldShop->latitude;
            $shop['longitude'] = $oldShop->longitude;
            $shop['province_id'] = $oldShop->province_id;
            $shop['district_id'] = $oldShop->district_id;
            $shop['ward_id'] = $oldShop->ward_id;
            $shop['banner_id'] = $oldShop->banner_id;
            $shop['logo_id'] = $oldShop->logo_id;
            $shop['currency'] = $oldShop->currency;
            $shop['language'] = $oldShop->language;
            $shop['description'] = $oldShop->description;
            $shop['open_hours'] = $oldShop->open_hours;
            $shop['order_type'] = 2;
            $shop['name']  = $oldShop->name. " - Bản sao";
            $shop['slug'] = Helper::makeSlug($shop['name'], true);

        }else{ return false;}
        $newShop = Shop::create($shop);
        $productArray = [];

        $categoryMap = [];
        foreach ($oldShop->categories as $key => $category) {
            $newCat = [];
            $newCat['name'] = $category->name;
            $newCat['slug'] = $category->slug;
            $newCat['profile_picture'] = $category->profile_picture;
            $newCat['index'] = $category->index;
            unset($newCat['id']);
            $newCat['shop_id'] = $newShop['id'];
            $newCategory = Category::create($newCat);
            $categoryMap[$category['id']] = $newCategory->id;
        }
        foreach ($oldShop->all_products as $key => $product) {
            $newProduct = array();
            $newProduct['name'] = $product['name'];
            $newProduct['is_main'] = $product['is_main'];
            $newProduct['type'] = $product['type'];
            $newProduct['brand_id'] = $product['brand_id'];
            $newProduct['profile_picture'] = $product['profile_picture'];
            $newProduct['latitude'] = $product['latitude'];
            $newProduct['longitude'] = $product['longitude'];
            $newProduct['notes'] = $product['notes'];

            $newProduct['shop_id'] = $newShop['id'];

            $newProduct['price'] = $product['price'];
            $newProduct['price_root'] = $product['price_root'];
            $newProduct['price_off'] = $product['price_off'];
            $newProduct['enable'] = $product['enable'];
            $newProduct['created_by'] = $product['created_by'];
            $newProduct['parent_id'] = $product['parent_id'];

            $newProductResult = Product::create($newProduct);
            $productArray[] = $newProduct;
            foreach ($product['categories'] as $key => $category) {

                $product_category = [
                    'product_id' => $newProductResult->id,
                    'category_id' => $categoryMap[$category['id']],
                ];

                ProductCategory::create($product_category);
            }
        }
        return $newShop;
    }
    //-------process clone shop products by shop ID--------
    public function clone_shop_product_by_shop_id($from_shop, $to_shop)
    {
        $oldShop = Shop::with('all_products', 'categories')->findOrFail($from_shop);
        $newShop = Shop::findOrFail($to_shop);

        $productArray = [];
        $categoryMap = [];

        foreach ($oldShop->all_products as $product) {
            $newProduct = array();
            $newProduct['name'] = $product['name'];
            $newProduct['is_main'] = $product['is_main'];
            $newProduct['type'] = $product['type'];
            $newProduct['brand_id'] = $product['brand_id'];
            $newProduct['profile_picture'] = $product['profile_picture'];
            $newProduct['latitude'] = $product['latitude'];
            $newProduct['longitude'] = $product['longitude'];
            $newProduct['notes'] = $product['notes'];

            $newProduct['shop_id'] = $newShop['id'];
            $newProduct['price'] = $product['price'];
            $newProduct['price_off'] = $product['price_off'];
            $newProduct['enable'] = $product['enable'];
            $newProduct['created_by'] = $product['created_by'];
            $newProduct['parent_id'] = $product['parent_id'];

            $newProductResult = Product::create($newProduct);
            $productArray[] = $newProduct;

            foreach ($product['categories'] as $category) {
                $product_category = [
                    'product_id' => $newProductResult->id,
                    'category_id' => $category['id'], // Assuming category IDs are the same
                ];

                ProductCategory::create($product_category);
            }
        }

        return $productArray; // Return the array of cloned products
    }

    //-------process listing Shop agent manage--------------------
    public function list_shop($agentID, $offset = 0, $limit = 100, $search = '')
    {
        // $general = new GeneralService($this->_userId);
        {
            $result = Shop::with('banner','logo','owner', 'business_types','agent')
            // ->where('enable', true)
                // ->with(['products' ])
                ->orderBy('created_at','desc')
                ->where('agent_id', $agentID);
                if($search != ''){
                    $result = $result->where('name','ilike',"%{$search}%");
                }
            // foreach ($result as $key => $value) {
            //     $result[$key]['products'] = Product::where('shop_id', $value->id)->get()->take(4);
            // }
        }

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        $result = [
            'count' => $result->count(),
            'result' => $result->offset($offset)->limit($limit)->get()
        ];

        return $result;
    }

    //------process detail Shop----------
    public function shop_detail($id)
    {

        $field = preg_match('/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i', $id) ? 'id' : 'slug';
        $result = Shop::where($field, $id)
            ->with('banner','logo', 'created_by','owner','provinces','districts','wards', 'business_types', 'products', 'agent')
            // ->where('enable', true)
            ->first();

        if(!$result)
        {
            return false;
        }

        return $result;
    }
    //------shop_orders all or by shop_id----------
    public function shop_orders($agent_id, $shop_id = 'all', $offset = 0, $limit = 10)
    {
        $shopListIds = Shop::where('agent_id', $agent_id)->pluck('id');
        if($shop_id == 'all'){
            $shop_id = $shopListIds;
        }else{
            if (!in_array($shop_id, $shopListIds->toArray())) {
                return 'this shop is not belong to this agent';
            }
            $shop_id = [$shop_id];
        }
        $result = Order::with('provinces','districts','wards','items', 'shops')
            ->orderBy('created_at','asc')
            ->whereIn('shop_id', $shop_id)
            ->offset($offset)->limit($limit)
            ->get();
        foreach ($result as $key => $order) {
            $result[$key]['price'] = $order['grand_total'];
        }


        if(!$result)
        {
            return false;
        }

        return $result;
    }

    /**
     * Check if a shop belongs to an agent.
     *
     * @param int $agentId The ID of the agent.
     * @param int $shopId The ID of the shop.
     * @return bool Returns true if the shop belongs to the agent, false otherwise.
     */
    static function checkAgentShopRelate($agentID, $shopID)
    {
        $user = User::find($agentID);
        if ($user && $user->role_id == User::ROLE_ADMIN) {
            return true;
        }
        
        $shop = Shop::find($shopID);
        return ($shop && $shop->agent_id == $agentID);
    }
    //------process detail Shop----------
     public function myShop()
     {
        $user = User::find($this->_userId);
        $result = Shop::where('user_id',$this->_userId)
            ->with('banner','logo', 'created_by','provinces','districts','wards', 'business_types', 'products')
            ->where('enable', true)
            ->first();

         if(!$result)
         {
             return false;
         }

         return $result;
     }
    //-------process update shop--------
    public function update_shop(array $shop)
    {
        $result = Shop::find($shop['id']);
        // $check_permission = $this->checkAgentShopRelate($this->_userId, $result->id);
        if(!$result)
        // if(!$result || !$check_permission)
        {
            return false;
        }

        if($result['latitude'] != $shop['latitude'] || $result['longitude'] != $shop['longitude']){
            Product::where('shop_id', $shop['id'])->update([
                'latitude' => $shop['latitude'],
                'longitude' => $shop['longitude']
            ]);
        }
        $result->update($shop);


        $result = $this->shop_detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.shop'),
        //                 $result,
        //                 json_encode($shop));

        return $result;

    }

    //-----process disable shop----------
    public function disableShop(array $shop)
    {
        $result = Shop::where([
            ['id', $shop['id']],
            ['enable', true]
        ])->first();

        if(!$result)
        {
            return false;
        }

        $shop['enable'] = false;
        $result->update($shop);

        return true;
    }

    //-----process delete shop----------
    public function delete_shop(array $shop)
    {
        $result = Shop::find($shop['id']);

        if(!$result)
        {
            return false;
        }

        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.shop'),
        //                 $result,
        //                 json_encode($shop));

        return true;
    }

    //------process listOrderByShopId----------
    public function listOrderByAgent(
            $agent_id,
            $shop_id = "all",
            $offset = 0,
            $limit = 10,
            $status = null,
            $search_text = "",
            $start_date = "",
            $end_date = ""
        )
    {
        $shopListIds = $shop_id == 'all' ? Shop::where('agent_id', $agent_id)->pluck('id') : [$shop_id];

        $result = Order::whereIn('shop_id', $shopListIds)
            ->with('provinces','districts','wards', 'items', 'shops', 'customer','delivery','deliveryPartner');

        if(isset($status) && !empty($status)){
            $result->where('status', $status);
        }
        if(isset($search_text) && !empty($search_text)){
            $result->where(function ($query) use($search_text){
                $query->where('customer_name', 'ILIKE', '%'.$search_text.'%')
                ->orWhere('customer_phone', 'ILIKE', '%'.$search_text.'%')
                ->orWhere('short_code', 'ILIKE', '%'.$search_text.'%')
                ->orWhere('address', 'ILIKE', '%'.$search_text.'%');
            });
        }
        if(isset($start_date) && !empty($start_date)){
            $result->whereDate('created_at', '>=', $start_date);
        }
        if(isset($end_date) && !empty($end_date)){
            $result->whereDate('created_at', '<=', $end_date);
        }
        // if(isset($short_code) && !empty($short_code)){
        //     $result->where('short_code', 'ILIKE', '%'.$short_code.'%');
        // }

        if(!$result)
        {
            return false;
        }

        $offset = isset($offset) && !empty($offset) ? $offset : 0;
        $limit = isset($limit) && !empty($limit) ? $limit : 20;

        $count = $result->count();

        $result = $result->orderBy('created_at', 'desc')->offset($offset)->limit($limit)->get();
        foreach ($result as $key => $order) {
            $result[$key]['price'] = $order['grand_total'];
        }
        return [
            'count' => $count,
            'result' => $result,
        ];
    }

    //------process detail order----------
    public function order_detail($id)
    {
        $result = Order::where('id', $id)
            ->with('items', 'shops', 'customer','images', 'delivery', 'items', 'deliveryPartner')
            ->first();

        // $check_permission = $this->checkAgentShopRelate($this->_userId, $result->shop_id);

        if(!$result)
        {
            return false;
        }
        $result['price'] = $result['grand_total'];
        return $result;
    }

    //-------process agent update order-------------
    public function order_update(array $order)
    {
        $result = Order::find($order['id']);

        if(!$result)
        {
            return false;
        }
        OrderItem::where('order_id', $order['id'])->delete();

        $result->update($order);
        foreach($order['items'] as $item){
            $order_item = [
                'order_id'      => $result->id,
                'product_id'    => $item['product_id'],
                'quantity'      => $item['quantity'],
                'price'         => $item['price'],
                'price_off'     => $item['price_off'],
                'price_total'   => $item['price_total'],
                'notes'         => isset($item['notes']) && !empty($item['notes']) ? $item['notes'] : "",
            ];

            OrderItem::create($order_item);
        };

        if(isset($order['cusomter_id']) && !empty($order['cusomter_id'])){

            $messText = '';
            switch ($order['status']) {
                case config('constants.order.status.new'):
                    $messText = "Vừa được cập nhật";
                    break;
                case config('constants.order.status.wait_delivery'):
                    $messText = "Vừa được xác nhận";
                    break;
                case config('constants.order.status.successful_delivery'):
                    $messText = "Đã giao thành công";
                    break;
                case config('constants.order.status.return'):
                    $messText = "Khách yêu cầu trả hàng";
                    break;
                case config('constants.order.status.cancel'):
                    $messText = "Bị hủy";
                    break;
                default:
                    $messText = "Vừa được cập nhật";
                    break;
            }

            $data = [
                'body'  => $messText,
                'title' => "Đơn hàng ".$order['short_code'],
                // 'image' => $image,
                'url'   => $result->short_code,
                'type'  => 'order',
            ];

            $tokenService = new TokenService();
            $tokens = $tokenService->listByUser($order['customer_id'], 2);

            $notiService = new NotificationService;
            $notiService->sendBatchNotification($tokens, $data);

            # nhận thông báo và lưu vào table notifications
            $customer_obj = User::find($order['customer_id']);
            if($customer_obj){
                $orderUrl = $order['short_code'] ?? $order['id'];
                $messToCustomer = array_merge($data, [
                    'target_url' => '/my-orders/'.$orderUrl
                ]);
                $customer_obj->notify(new NotificationUser($order['customer_id'], $messToCustomer));
            }
        }

        $result = $this->order_detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.order'),
        //                 $result,
        //                 json_encode($order));

        return $result;

    }

    //-------process create private product to a shop by agent--------------------
    public function createPrivateProduct(array $product)
    {
        $user = User::find($this->_userId);
        // if($user)
        // {
        //     $product['created_by'] = $user->id;
        // }
        $product['type'] = 3;
        $check_permission = $this->checkAgentShopRelate($this->_userId, $product['shop_id']);
        if(!$check_permission)
        {
            return false;
        }

        $result = Product::create($product);
        if($result && isset($product['translation'])){
            $this->excuteTranslation($product['translation'], config('constants.object_type.product'), $result->id);
        }
        foreach($product['category_ids'] as $key=>$value){
            $product_category = [
                'product_id' => $result->id,
                'category_id' => $value,
            ];

            ProductCategory::create($product_category);
        };
        $result = Product::where('id',$result->id)
            ->with('shop', 'categories', 'children_products', 'parent_product')
            // ->where('enable', true)
            ->first();
        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.create'),
        //                 $result->id,
        //                 config('constants.object_type.product'),
        //                 $result,
        //                 json_encode($product));

        return $result;
    }
    public function excuteTranslation(array $transArr, $objectType, $objectId){
        foreach ($transArr as $key => $langValue) {
            Translation::updateOrCreate(
                [
                    'object_type' => $objectType,
                    'object_id' => $objectId,
                    'language_code' => $langValue['language_code']
                ],
                [
                    'name' => $langValue['name'],
                    'description' => $langValue['description'] ?? ''
                ]
            );
        }
    }
    //-------process add system product to a shop by agent--------------------
    public function addProductFromSystem(array $data){
        $shop = Shop::with('products')->find($data['shop_id']);

        // var_dump($shop->products->toArray());
        if(!$this->checkAgentShopRelate($this->_userId, $shop->id)){
            return false;
        }
        $arr = [];
        foreach ($data['list_product'] as $product) {
            if (array_search($product['id'], array_column($shop->products->toArray(), 'parent_id')) === false){                $arr[] = [
                    "id" => Str::uuid(),
                    "name" => $product['name'],
                    "price" => $product['price'],
                    "profile_picture" => $product['profile_picture'],
                    "type" => config('constants.product.type.system'),
                    "shop_id" => $shop->id,
                    "latitude" => $shop->latitude,
                    "longitude" => $shop->longitude,
                    "extra_id" => $product['id'],
                    "is_feature" => $product['is_feature'] ?? false,
                    "unaccent_name" => $product['unaccent_name'] ?? Str::slug($product['name'],' '),
                    "lowercase_name" => $product['lowercase_name'] ?? Str::lower($product['name']),
                    "created_by" => $this->_userId,
                    "stock" => $product['stock'],
                    "commission_percent" => $product['commission_percent'] ?? 0,
                    "slug" => Str::slug($product['name'],'-').'-'.strtolower(Str::random(2)).str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                    "created_at" => now()->format('Y-m-d H:i:s'),
                    "updated_at" => now()->format('Y-m-d H:i:s'),
                ];
            }
        }
        return Product::insert($arr);

        // $result = Product::
        //     select('id','name', 'profile_picture', 'price')
        //     ->where('is_system', true)
        //     ->where('name', 'ILIKE', '%'.$search_word.'%')
        //     ->orderByRaw("CASE
        //     WHEN name ILIKE '".$search_word."' THEN 1
        //     WHEN name ILIKE '".$search_word."%' THEN 2
        //     WHEN name ILIKE '%".$search_word."%' THEN 3
        //     WHEN name ILIKE '%".$search_word."' THEN 4
        //     ELSE 5
        //     END");
        // $count = $result->count();
        return [
            // 'count' => $count,
            // 'result' => $result->limit(50)->get()
        ];
    }

    //-------process update product in shop by agent--------------------
    public function updateProduct(array $product)
    {
        $result = Product::find($product['id']);
        $check_permission = $this->checkAgentShopRelate($this->_userId, $result->shop_id);
        if(!$result || !$check_permission)
        {
            return false;
        }

        if(isset($product['stock']) && $product['stock'] > 0 && $product['stock'] != $result->stock){
            StockTracking::recordStockChange(
                $product['id'],
                $result->stock ?? 0,
                $product['stock'],
                'stock_update'
            );
        }
        $product['updated_at'] = now();
        $result->update($product);

        if($result && isset($product['translation'])){
            $this->excuteTranslation($product['translation'], config('constants.object_type.product'), $result->id);
        }

        $listCategoryIdCurrent = ProductCategory::where('product_id', $product['id'])->delete();

        foreach($product['category_ids'] as $key=>$value){
            $product_category = [
                'product_id' => $product['id'],
                'category_id' => $value,
            ];

            ProductCategory::create($product_category);
        };

        $result = Product::where('id',$result->id)
            ->with('shop', 'categories', 'children_products', 'parent_product')
            // ->where('enable', true)
            ->first();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.product'),
        //                 $result,
        //                 json_encode($product));

        return $result;

    }

    //-----process update category's products----------
    public function categoryUpdateListProduct(array $data)
    {
        $result = Category::find($data['category_id']);
        $check_permission = $this->checkAgentShopRelate($this->_userId, $result->shop_id);
        if(!$result || !$check_permission)
        {
            return false;
        }

        $listCategoryIdCurrent = ProductCategory::where('category_id', $data['category_id'])->delete();
        foreach($data['product_ids'] as $key=>$value){
            $product_category = [
                'category_id' => $data['category_id'],
                'product_id' => $value,
            ];

            ProductCategory::create($product_category);
        };

        return true;
    }
    //-----process update category's Index----------
    public function categoryUpdateIndex(array $data, $shopId)
    {
        $check_permission = $this->checkAgentShopRelate($this->_userId, $shopId);
        if(!$check_permission)
        {
            return false;
        }
        foreach($data as $key => $value){
            $category = Category::where('id', $value['id'])->where('shop_id', $shopId)->first();
            if($category)
            {
                $category['index'] = $value['index'];
                $category->save();
            }else{
                return false;
            }

        };

        return true;
    }
}
