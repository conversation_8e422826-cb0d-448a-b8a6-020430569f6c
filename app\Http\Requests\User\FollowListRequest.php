<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class FollowListRequest extends BaseRequest
{
    public function rules()
    {
        return [
            // 'sort' => 'nullable|in:asc,desc',
            'limit' =>'nullable|integer|min:0',
            'offset' => 'nullable|integer|min:0',
        ];

    }
    // public function messages()
    // {
    //     return [
    //         'sort.in' => 'User_002_E_104',

    //         'limit.integer' => 'User_002_E_105',
    //         'limit.min' => 'User_004_E_106',

    //         'offset.integer' => 'User_002_E_107',
    //         'offset.min' => 'User_004_E_108',

    //     ];
    // }
}
