<?php

namespace App\Http\Controllers\Api\v1\Client;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Illuminate\Auth\Events\Verified;
use App\User;
use Illuminate\Support\Facades\URL;
use Carbon\Carbon;
use App\Mail\SendMail;
use App\Mail\SendRegisterUserMail;
use Mail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Redirect;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Illuminate\Support\Facades\Redis;
use App\Services\Auth\AuthClientService;
use App\Services\GeneralService;
use App\Http\Requests\User\VerificationRequest;
class VerificationController extends Controller
{
    use VerifiesEmails;
    public function verify(Request $request, AuthClientService $service)
    {
        $check = User::find($request->route('id'));
        if(!$check)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'data' => 'User_003_E_001'
                ]
            ];
            return response()->json($response,JsonResponse::HTTP_OK);
        }
        $result = $service->verificationEmail($request->route('id'),$request->route('token'));
        if($result['result'] == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'data' => $result,
                ]
            ];
            return response()->json($response,JsonResponse::HTTP_OK);
        }

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function resend(Request $request , AuthClientService $service)
    {
        $result = $service->resend($request->route('email'));
        if ($result == false) {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'data' => $result,
                ]
            ];
            return response()->json($response,JsonResponse::HTTP_OK);
        }

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    // public function verificationUrl($notifiable)
    // {
    //     // \URL::forceScheme('https');
    //     $temporarySignedURL = URL::temporarySignedRoute(
    //         'verification.verify', Carbon::now()->addMinutes(5), ['id' => $notifiable->id]
    //     );
    //     // $temporarySignedURL = str_replace("http","https",$temporarySignedURL);
    //     // I use urlencode to pass a link to my frontend.
    //     return  urlencode($temporarySignedURL);
    // }

    // public function __construct()
    // {
    //     // $this->middleware('auth');
    //     $this->middleware('signed')->only('verify');
    //     $this->middleware('throttle:6,1')->only('verify', 'resend');
    // }
}
