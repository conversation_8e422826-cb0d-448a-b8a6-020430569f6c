<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ProductOption extends Model
{
    protected $fillable = [
        'product_id',
        'option_id',
        'min_select',
        'max_select',
        'enable',
        'created_by',
    ];
    protected $primaryKey = 'id';
    protected $table = 'product_options';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
    protected $hidden = ['updated_at', 'created_at'];

    public function created_by()
    {
        return $this->belongsTo(User::class,'created_by');
    }

    public function products()
    {
        return $this->belongsTo(Product::class,'product_id');
    }

    public function options()
    {
        return $this->belongsTo(Option::class,'option_id');
    }
}
