<?php

namespace App\Admin\Controllers;

use App\Order;
use App\OrderItem;
use App\User;
use App\Product;
use App\Shop;
use App\District;
use App\Province;
use App\Ward;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
Use Encore\Admin\Widgets\Table;
use App\Admin\Selectable\Products as ProductsSelectable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use App\Services\Token\TokenService;
use App\Services\Notification\NotificationService;

class OrderItemController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Chi tiết sản phẩm đơn hàng(Order Item)';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Order());
        $grid->column('id', 'id');

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($orderId, $productId)
    {
        $show = new Show(OrderItem::select('quantity','price','price_off','price_total','notes','order_id','product_id')
        ->where('order_id', $orderId)->where('product_id',$productId)->get());
        echo $orderId;
        echo $productId;
        echo json_encode($show);die;
        $show->field('quantity', __('admin.order.quantity'));
        $show->field('notes', __('admin.order.notes'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new OrderItem());
        return $form;
    }
}
