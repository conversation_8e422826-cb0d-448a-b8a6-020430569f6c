<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInventoryFieldsToStockTrackingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('stock_tracking', function (Blueprint $table) {
            $table->uuid('executor_id')->nullable()->after('order_id')->comment('User who executed the stock change');
            $table->string('unit')->nullable()->after('executor_id')->comment('Unit of measurement (kg, pieces, etc.)');
            $table->decimal('unit_price', 10, 2)->nullable()->after('unit')->comment('Price per unit');
            $table->text('note')->nullable()->after('unit_price')->comment('Additional notes for the stock change');
            
            // Add index for executor_id
            $table->index(['executor_id']);
            
            // Add foreign key constraint
            $table->foreign('executor_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('stock_tracking', function (Blueprint $table) {
            $table->dropForeign(['executor_id']);
            $table->dropIndex(['executor_id']);
            $table->dropColumn(['executor_id', 'unit', 'unit_price', 'note']);
        });
    }
}
