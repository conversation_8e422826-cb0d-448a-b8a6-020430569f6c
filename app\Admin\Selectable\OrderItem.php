<?php

namespace App\Admin\Selectable;

use App\Product;
use App\OrderItem;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Grid\Selectable;
use Illuminate\Support\Facades\Log;

class OrderItemSelectable extends Selectable
{
    public $model = OrderItem::class;

    public function make()
    {


        $this->filter(function (Filter $filter) {
            $filter->like('name');
        });
        $this->column('quantity')->display(function($quantity) {
            return $quantity; // Display the quantity directly
        })->label('Quantity'); // Optional: Add a label for clarity
    }
}