<?php 
namespace App\Admin\Actions;

use App\User;
use App\Token;
use App\Shop;
use App\Services\Token\TokenService;
use Encore\Admin\Actions\RowAction;
use App\Services\Notification\NotificationService;

class EnableDisable2 extends RowAction
{
    public function name()
    {
        return $this->row->enable ? 'Disable' : 'Enable';
    }

    public function handle($model)
    {
        // Toggle the enabled status
        $actionName = $model->enable ? __('admin.disable') : __('admin.enable');
        $action = $model->enable ? 'is_disable' : 'is_enable';
        $model->enable = !$model->enable;

        if ($model instanceof User) { // Check if the model is User
            if ($actionName == 'Disable') {
                $model->token = null;
                // Disable user's token in the Token table by user_id
                // Token::where('user_id', $model->id)->update(['token' => null]);
                TokenService::deleteList(['id' => $model->id]);
            }
        }elseif($model instanceof Shop){
            $messToOwner = [
                'title' => __('admin.shop.title').": ".$model->name." ".__('admin.'.$action),
                'body'  => '',
                'url'   => $model->id,
                'target_url' => '/shop/'. $model->id,
                'type'  => 'shop_status',
            ];

            $messToAgent = [
                'title' => __('admin.shop.title').": ".$model->name." ".__('admin.'.$action),
                'body'  => '',
                'url'   => $model->id,
                'target_url' => '/agent/shop/'. $model->id,
                'type'  => 'shop_status',
            ];
            $shop_owner = $model->user_id;
            $shop_agent = $model->user_id != $model->agent_id ? $model->agent_id : "";


            # tự nhận đơn
            $tokenService = new TokenService();
            $tokens = $tokenService->listByUser($shop_owner, 2);
            $agentTokens = $tokenService->listByUser($shop_agent, 2);
            $notiService = new NotificationService;
            if(count($tokens) > 0){
                $notiService->sendBatchNotification($tokens, $messToOwner);
            }
            if(count($agentTokens) > 0){
                $notiService->sendBatchNotification($agentTokens, $messToAgent);
            }
        }
        $model->save();

        return $this->response()->success($actionName . ' Success!')->refresh();
    }
    public function display($star)
    {
        // return $star ? "<i class=\"fa fa-star-o\"></i>" : "<i class=\"fa fa-star\"></i>";
        return $star ? "<i class=\"fa fa-check text-green\"></i>" : "<i class=\"fa fa-close text-red\"></i>";
    }
}