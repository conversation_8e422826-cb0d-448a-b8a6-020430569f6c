<?php

namespace App\Services;

use App\Voucher;
use App\Order;

class VoucherService
{
    public function applyVoucher(Voucher $voucher, Order $order)
    {
        if (!$this->isVoucherValid($voucher, $order)) {
            return false;
        }

        $discount = $this->calculateDiscount($voucher, $order);
        
        $order->discount = $discount;
        $order->save();
        $voucher->incrementUsage();

        return true;
    }

    private function isVoucherValid(Voucher $voucher, Order $order)
    {
        return $voucher->isValid() &&
               $voucher->shop_id === $order->shop_id &&
               $order->discount === 0; // Ensure no discount has been applied yet
    }

    private function calculateDiscount(Voucher $voucher, Order $order)
    {
        if ($voucher->type === 'percent') {
            return $order->subtotal * ($voucher->value / 100);
        } else { // fixed amount
            return min($voucher->value, $order->subtotal);
        }
    }
}
