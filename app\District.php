<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class District extends Model
{
    //
     //set time to false

    protected $fillable = [
    	'name','province_id','slug','longitude','latitude','bound','address',
        'streetmap','satellite', 'planning'
    ];

    protected $primaryKey = 'id';

    protected $table = 'dvhc2021_huyen';
    // protected $table = 'districts';
    protected $hidden = ['created_at', 'updated_at', 'pivot'];

    public function projectAreas()
    {
        return $this->hasMany(ProjectArea::class,'district_id');
    }
    public function wards()
    {
        return $this->hasMany(Ward::class,'district_id')->orderBy('slug','asc');
    }

    public function provinces()
    {
        return $this->belongsTo(Province::class, 'province_id');
    }
}
