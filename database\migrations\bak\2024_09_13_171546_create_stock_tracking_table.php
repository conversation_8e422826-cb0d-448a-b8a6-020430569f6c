<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStockTrackingTable extends Migration
{
    public function up()
    {
        Schema::create('stock_tracking', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('product_id');
            $table->integer('old_stock');
            $table->integer('new_stock');
            $table->integer('change');
            $table->string('reason')->comment('order', 'restock', 'addstock', 'return', 'loss', 'adjustment', 'transfer', 'import', 'export', 'waste');
            $table->uuid('order_id')->nullable();
            $table->uuid('executor_id')->nullable()->comment('User who executed the stock change');
            $table->string('unit')->nullable()->comment('Unit of measurement (kg, pieces, etc.)');
            $table->decimal('unit_price', 10, 2)->nullable()->comment('Price per unit');
            $table->text('note')->nullable()->comment('Additional notes for the stock change');
            $table->timestamps();
            $table->index(['product_id', 'order_id']);
            $table->index(['executor_id']);

            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('set null');
            $table->foreign('executor_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::dropIfExists('stock_tracking');
    }
}