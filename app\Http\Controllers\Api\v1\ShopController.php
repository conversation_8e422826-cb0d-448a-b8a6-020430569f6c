<?php

namespace App\Http\Controllers\Api\v1;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Shop\ShopSettingRequest;
use App\Http\Requests\Shop\ShopUpdateRequest;
use App\Services\GeneralService;
use App\Services\Shop\ShopService;
use App\Shop;
use App\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redis;

class ShopController extends Controller
{
    // -------create shop---------------
    public function create(Request $request)
    {
        $data = $request->only([
            'name',
            'address',
            'user_id',
            'slug',
            'business_type_id',
            'latitude',
            'longitude',
            'province_id',
            'district_id',
            'ward_id',
            'phone',
            'description',
            'banner_id',
            'logo_id',
            'language',
            'open_hours',
        ]);
        $service = new ShopService(Auth::user()->id);
        $result = $service->create($data);

        if($result){
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ]
            ];
        }else{
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'Failed to create shop'
                ]
            ];
        }

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list shop-----------------
    public function list(Request $request, ShopService $service)
    {
        $arrays = $request->all();

        $result = $service->list($arrays['offset'], $arrays['limit'], $arrays['all']);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------detail shop-------------
    public function detail($id = null, ShopService $service)
    {
        $result = $service->detail($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update shop-----------
    public function update(ShopUpdateRequest $request, ShopService $service)
    {
        $data = $request->only([
            'id',
            'name',
            'address',
            'user_id',
            'slug',
            'business_type_id',
            'latitude',
            'longitude',
            'province_id',
            'district_id',
            'ward_id',
            'description',
            'phone',
            'banner_id',
            'logo_id',
            'language',
            'currency',
            'open_hours',
            'branch_id',
            'enable'
        ]);

        $result = Shop::find($data['id']);
        if(!$result->enable){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => 'this Shop was disabled, please contact support for more.',
                'errors' => [
                    "error_message"=>"this Shop was disabled, please contact support for more.",
                    "field"=>"id",
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if(!GeneralService::checkShopOwner(Auth::user()->id, $data['id']))
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => 'this Shop is not exists or not belongs to your account.',
                'errors' => [
                    "error_message"=>"this Shop is not exists or not belongs to your account",
                    "field"=>"id",
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $result = $service->update($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------remove shop----------------
    public function remove(Request $request, ShopService $service)
    {
        $data = $request->only(['id']);

        $result = $service->remove($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------delete shop--------------
    public function delete(Request $request, ShopService $service)
    {
       $data = $request->only(['id']);

       $result = $service->delete($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }


    public function myShop()
    {
        if(Auth::user()->role_id == User::ROLE_ADMIN)
        {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => true
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $service = new ShopService(Auth::user()->id);
        $result = $service->myShop();

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => false
            ];

            return response()->json($response, JsonResponse::HTTP_BAD_REQUEST);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function getProductsByShopId(Request $request, ShopService $service)
    {
        $data = $request->only([
            'shop_id',
            'limit',
            'offset',
            'sortBy',
            'category_ids'
        ]);
        $result = $service->getProductsByShopId($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => false
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function searchProductsInShop(Request $request, ShopService $service)
    {
        $data = $request->only([
            'shop_id',
            'search',
            'category_ids',
            'limit',
            'offset',
            'sort_by',
            'product_type',
            'is_owner',
            'is_feature',
            'is_saleoff'
        ]);

        if(!GeneralService::checkShopOwner(Auth::user()->id, $data['shop_id']) && !GeneralService::checkAgentShopRelate(Auth::user()->id,$data['shop_id']))
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'You are not authorized to perform this action.'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $result = $service->searchProductsInShop($data, 'all');
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => false
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function superMenu($id = null, ShopService $service, Request $request)
    {
        $id = $service->slugToUUID($id);
        $cacheKey = env('APP_ENV').":"."super_menu:".$id;
        $tags = [
            env('APP_ENV').":".'api_responses',
            env('APP_ENV').":".'products',
            env('APP_ENV').":".'super_menu',
            env('APP_ENV').":".'shop:' . $id
        ];

        $result = null;
        $flushCache = $request->get('flush_cache') ?? 'false';
        if($flushCache == 'true'){
            Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $id);
        }
        if(!Redis::exists($cacheKey)){
            // echo 'no cache';
            $result = $service->superMenu($id);
            // $cacheRes = Redis::setex($cacheKey, 86400, json_encode($result));
            Helper::storeCacheWithTags($cacheKey, $result, $tags);
            // var_dump($cacheRes);
        }else{
            // echo 'has cache';
            $result = json_decode(Redis::get($cacheKey));

        }

        if($result == false || !$result)
        {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => false
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function productWithCategory($id = null, ShopService $service, Request $request)
    {
        $cacheKey = env('APP_ENV').":"."product_with_category_in_shop:".$id;
        $tags = [
            env('APP_ENV').":".'product_with_category_in_shop',
            env('APP_ENV').":".'shop:' . $id
        ];
        $result = null;
        $flushCache = $request->get('flush_cache') ?? 'false';
        if($flushCache == 'true'){
            // Redis::del($cacheKey);
            Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $id);
        }
        if(!Redis::exists($cacheKey)){
            // echo 'no cache';
            $result = $service->productListInCategory($id);
            // $cacheRes = Redis::setex($cacheKey, 86400, json_encode($result));
            Helper::storeCacheWithTags($cacheKey, $result, $tags);
            // var_dump($cacheRes);
        }else{
            // echo 'has cache';
            $result = json_decode(Redis::get($cacheKey));

        }

        if($result == false || !$result)
        {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => false
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function updateSetting(ShopSettingRequest $request)
    {
        $data = $request->only(['shop_id', 'setting']);
        $userId = Auth::user()->id;

        if(!GeneralService::checkAgentShopRelate($userId,$data['shop_id']) && !GeneralService::checkShopOwner($userId,$data['shop_id'])){
            return response()->json([
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body'   => [
                    'message' => 'You are not authorized to perform this action.'
                ]
            ],JsonResponse::HTTP_OK);
        }
        $service = new ShopService();
        $result  = $service->settingShop($data['shop_id'], $data['setting']);

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => [
                'shop' => $result
            ]
        ]);

    }
}
