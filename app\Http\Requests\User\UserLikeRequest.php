<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UserLikeRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:properties,id',
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.exists' => 'Property_003_E_019',
    //         'id.uuid' => 'Property_002_E_020',
    //         'id.required' => 'Property_001_E_053',
    //     ];
    // }
}
