<?php
namespace App\Services\Auth;
use App\Role;
use App\User;
use App\SellArea;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;
use Tymon\JWTAuth\Facades\JWTAuth;
use App\Services\Auth\AuthService;
use App\Services\SellArea\SellAreaService;
use App\Services\Auth\AuthClientService;
use Illuminate\Support\Facades\Auth;
use App\Services\Token\TokenService;
use Illuminate\Support\Str;

class RegisterService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId)
        {
            $this->_userId = $userId;
        }
        else
        {
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }
    public function loginDriver($request)
    {
        //----device user-----------
        $device = request()->header('User-Agent');
        $general = new GeneralService();
        $address_ip = $general->getUserIpAddr();
        $request['provider_name'] = Str::upper($request['provider_name']);
        $resgister = false;
        $user = false;
        switch ($request['provider_name']) {
            case 'FACEBOOK':
                $user = User::where([
                    ['provider_id', $request['provider_id']],
                    ['provider_name', $request['provider_name']],
                    ])->first();
                break;
            case 'ZALO':
                $user = User::where([
                    ['provider_id', $request['provider_id']],
                    ['provider_name', $request['provider_name']],
                    ])->first();
                break;
            case 'GOOGLE':
                $user = User::where([
                        ['email', $request['email']],
                    ])->first();
                break;
            case 'APPLE':
                $user = User::where([
                    ['provider_id', $request['provider_id']],
                    ['provider_name', $request['provider_name']],
                    ])->first();
                break;
            default:
                return false;
                break;
        }

        if(!$user)
        {
            # chưa tạo tk
            $resgister = $this->registerUser($request);
            if($resgister)
            {
                $resgister->markEmailAsVerified();
                $token = JWTAuth::fromUser($resgister);
                $data = [
                    'id' => $resgister->id,
                    'token' => $token,
                    'provider_id' => $request['provider_id'],
                    'provider_name' => $request['provider_name'],
                ];
                $result = $this->update($data);
                $result['is_register'] = true;
                TokenService::insert(['token' => $token, 'user_id' => $resgister->id, 'device' => $device, 'address_ip' => $address_ip, 'token_type' => 1]);
                return $result;
            }
        }else{
            # đã tạo tk
            if($user->enable == false)
            {
                return 'lock';
            }
            $token = JWTAuth::fromUser($user);
            $data = [
                'id' => $user->id,
                'token' => $token
            ];
            $result = $this->update($data);
            TokenService::insert(['token' => $token, 'user_id' => $user->id, 'device' => $device, 'address_ip' => $address_ip, 'token_type' => 1]);
            return $result;
        }
        return false;
    }


    public function registerUser(array $register)
    {
        $user = User::find($this->_userId);
        // var_dump($user);
        if($user)
        {
            $register['created_by'] = $user->id;
        }
        $register['role_id'] = Role::where('name','User')->first()->id;
        $register['profile_picture'] = $register['picture']['data']['url'] ?? null;
        // $register['gender'] =  isSet($register['gender']) && !empty($register['gender']) ? $register['gender'] : NULL;
        $result = User::create($register);
        // var_dump($result);die;
        $result->markEmailAsVerified();
        $general = new GeneralService($this->_userId);
        $general->addLog(
            Config::get('constants.log_action.create', 1),
            $result->id,
            Config::get('constants.object_type.user', 1),
            $result, 
            json_encode($register)
        );

        return $result;
    }

    public function update($data)
    {
        $user = User::find($data['id']);

        if(!$user)
        {
            return false;
        }

        $user->update($data);
        $service = new AuthService();
        $result = $service->detail($user->id);
        return $result;
    }

    public function loginZalo(array $zalo)
    {
        
        $data = http_build_query( array(
            "app_id" => '1735716509130650710',
            "code" => $zalo["code"],
            // "code_verifier" => $_SESSION["zalo_code_verifier"],
            "grant_type" => "authorization_code"
        ) );
        
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://oauth.zaloapp.com/v4/access_token',
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_HTTPHEADER => array(
                    "Content-Type: application/x-www-form-urlencoded",
                    "secret_key: " . env('SECRET_KEY_ZALO')
                ),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_FAILONERROR => true,
        ) );
        $response = json_decode(curl_exec($curl));
        curl_close($curl);

        if(isset($response->access_token))
        {
            $urlZalo = 'https://graph.zalo.me/v2.0/me?access_token='.$response->access_token.'&fields=id,name,picture';
            $curlZalo = curl_init($urlZalo);
            curl_setopt($curlZalo, CURLOPT_RETURNTRANSFER, true);
            $dataZalo = json_decode(curl_exec($curlZalo));
            curl_close($curlZalo);

            if(isset($dataZalo->name))
            {
                $register = [
                    'name' => $dataZalo->name,
                    'provider_name' => 'ZALO',
                    'provider_id' => $dataZalo->id,
                    // 'file' => $dataZalo->picture->data->url
                ];
                $path = isset($dataZalo->picture->data->url) ? $dataZalo->picture->data->url : null;
                $resultUser = $this->loginDriver($register);

                if($resultUser == 'lock')
                {
                    return 'lock';
                }

                $result = [
                    'path' => $path,
                    'result' => $resultUser
                ];
                return $result;
            }
        }

        return false;
    }

    //-------update image--------------
    public function updateImage(array $request)
    {
        $user = User::find($this->_userId);

        if(!$user)
        {
            return false;
        }
        
        $data = [
            'id' => $user['id'],
            'file' => $request['file'],
            'title' => $request['title']
        ];

        $service = new AuthClientService($this->_userId);
        $service->updateImageDrive($data);

        // $token = JWTAuth::fromUser($user);
        // $data = [
        //     'id' => $user->id,
        //     'token' => $token
        // ];
        // $result = $this->update($data);
        //----device user-----------
        return true;
    }
}
