<?php

namespace App\Http\Controllers\Api\v2;

use App\DeliveryPartner;
use App\Http\Controllers\Controller;
use App\Http\Requests\Delivery\DeliveryRequest;
use App\Http\Requests\DeliveryPartner\CheckPriceDeliveryRequest;
use App\Http\Requests\DeliveryPartner\CreateDeliveryRequest;
use App\Http\Requests\DeliveryPartner\DeliveryPartnerRequest;
use App\Order;
use App\Services\DeliveryPartner\DeliveryPartnerService;
use App\Services\DeliveryPartner\DeliveryErrorService;
use App\Services\GeneralService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeliveryPartnerController extends Controller
{
    function checkPrice(CheckPriceDeliveryRequest $request)
    {
        $data = $request->all();
        $service = new DeliveryPartnerService();
        $service = $service->setDeliveryPartner($data['partner'], $data['shop_id']);

        $paths = $data['path'];
        foreach ($paths as &$path) {
            $path['lat'] = (float)$path['lat'];
            $path['lng'] = (float)$path['lng'];
            if(isset($path['cod'])){
                $path['cod'] = (int)$path['cod'];
            }
        }
        $data['path'] = $paths;


        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => [
                'data' => $service->checkPrice($data)
            ],
        ], JsonResponse::HTTP_OK);
    }

    // Tạo vận đơn
    function createDelivery(CreateDeliveryRequest $request){
        $errorService = new DeliveryErrorService();

        try {
            $data = $request->all();

            // Log the delivery creation attempt
            $errorService->logApiCall($data['partner'], $data['order_id'], 'v2/delivery-partner/create', $data);

            $service = new DeliveryPartnerService();
            $service = $service->setDeliveryPartner($data['partner'], $data['shop_id']);

            $paths = $data['path'];
            foreach ($paths as &$path) {
                $path['lat'] = (float)$path['lat'];
                $path['lng'] = (float)$path['lng'];
                $path['mobile'] = $path['phone'] ?? "";
                if(isset($path['cod'])){
                    $path['cod'] = (int)$path['cod'];
                }
            }
            $data['path'] = $paths;

            $result = $service->createDelivery($data, $data['order_id']);

            if(isset($result['message'])){
                // Log the service failure
                $errorService->logAndNotify(
                    'DELIVERY_PARTNER_SERVICE_FAILED',
                    'DeliveryPartnerService returned error message: ' . $result['message'],
                    $data['partner'],
                    $data['order_id'],
                    [
                        'request_data' => $data,
                        'service_response' => $result,
                        'api_endpoint' => 'v2/delivery-partner/create'
                    ]
                );

                return response()->json([
                    'status' => JsonResponse::HTTP_BAD_REQUEST,
                    'body'   => $result
                ], JsonResponse::HTTP_OK);
            }

            // Log successful creation
            $errorService->logSuccess($data['partner'], $data['order_id'], $result['short_code'] ?? 'unknown', [
                'delivery_data' => $result,
                'shop_id' => $data['shop_id']
            ]);

            return response()->json([
                 'status' => JsonResponse::HTTP_OK,
                 'body'   => [
                    'data' => $result
                 ],
            ], JsonResponse::HTTP_OK);

        } catch (\Exception $e) {
            // Log the exception
            $errorService->logAndNotify(
                'DELIVERY_PARTNER_CONTROLLER_EXCEPTION',
                'Exception occurred in DeliveryPartnerController createDelivery method: ' . $e->getMessage(),
                $data['partner'] ?? 'unknown',
                $data['order_id'] ?? 'unknown',
                [
                    'request_data' => $data ?? [],
                    'api_endpoint' => 'v2/delivery-partner/create'
                ],
                $e
            );

            return response()->json([
                'status' => JsonResponse::HTTP_INTERNAL_SERVER_ERROR,
                'body'   => [
                    'message' => 'Internal server error occurred while creating delivery'
                ]
            ], JsonResponse::HTTP_OK);
        }
    }

    // Chi tiết vận đơn
    function detailDelivery(DeliveryPartnerRequest $request)
    {
        $data = $request->all();
        $service = new DeliveryPartnerService();
        $service = $service->setDeliveryPartner($data['partner'], $data['shop_id']);
        $result = $service->detailDelivery($data['order_id']);
        if(isset($result['message']))
            $response = ['status' => JsonResponse::HTTP_BAD_REQUEST,];
        else
            $response = ['status' => JsonResponse::HTTP_OK,];
        $response['body'] = ['data' => $result];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // Huỷ vận đơn
    function cancelDelivery(DeliveryPartnerRequest $request)
    {
        // Get request data
        $data = $request->all();
        $userId = Auth::user()->id;

        if(!GeneralService::checkShopOwner($userId, $data['shop_id']) && !GeneralService::checkAgentShopRelate($userId, $data['shop_id'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body'   => [
                    'message' => 'You are not allowed to cancel order delivery.'
                ]
            ], JsonResponse::HTTP_OK);
        }

        // Create delivery service instance
        $service = new DeliveryPartnerService();

        // Set delivery partner
        $service = $service->setDeliveryPartner($data['partner'], $data['shop_id']);

        // Call cancel delivery API
        $result = $service->cancelDelivery($data ,$data['order_id']);

        // Check result and prepare response
        if(isset($result['message']))
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'   => $result];
        else
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body'   => [
                    'message' => "Cancel delivery success"
                ]];

        // Return JSON response
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    function listDeliveryPartner()
    {
        $partner = DeliveryPartner::on('pgsqlReplica')->select('name', 'id', 'is_active')->get();
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => [
                'data' => $partner
            ]
        ], JsonResponse::HTTP_OK);
    }

    function updateStatus(Request $request, $partner)
    {
        Log::info("RUN CALLBACK UPDATE PARTNER STATUS");
        $data =$request->all();

        Log::info("Data: ", $data);

        $deliveryId = $data['_id'] ?? null;

        if(!$deliveryId) return response()->json([
            'status' => JsonResponse::HTTP_BAD_REQUEST,
            'message' => "Delivery partner id is required."
        ], JsonResponse::HTTP_OK);

        $order = Order::on('pgsqlReplica')->where('delivery_id', $deliveryId)->first();
        if(!$order) return response()->json([
            'status' => JsonResponse::HTTP_BAD_REQUEST,
            'message' => "Order not found."
        ], JsonResponse::HTTP_OK);

        $service = new DeliveryPartnerService();
        $service = $service->setDeliveryPartner($partner, $order->shop_id);

        // Format to notify
        $service->notification($data, $order->id);

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'message' => "Delivery partner status updated successfully."
        ], JsonResponse::HTTP_OK);
    }
}
