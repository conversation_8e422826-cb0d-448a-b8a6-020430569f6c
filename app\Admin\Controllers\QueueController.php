<?php
namespace App\Admin\Controllers;

use App\Queue;
use App\Request;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class QueueController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Queue';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */

    protected function grid(){
        $grid = new Grid(new Queue());
        $grid->column('id', __('Id'));
        $grid->column('queue', __('Queue'));
        $grid->column('payload', __('Payload'))->display(function($payload){
            return '<pre style="max-width: 100rem">'.$payload.'</pre>';
        });
        $grid->column('attempts', __('Attempts'));
        $grid->column('reserved_at', __('Reserved_at'));
        $grid->column('available_at', __('Available_at'));
        $grid->column('created_at', __('Created_at'));

//        $grid->disableActions();
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id){
        $show = new Show(Queue::findOrFail($id));
        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */

    protected function form()
    {
        $form = new Form(new Queue());
        $form->text('id', __('Id'));
        $form->text('queue', __('Queue'));
        $form->textarea('payload', __('Payload'));
        $form->text('attempts', __('Attempts'));
        $form->text('reserved_at', __('Reserved_at'));
        $form->text('available_at', __('Available_at'));
        return $form;
    }
}
