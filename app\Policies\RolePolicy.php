<?php

namespace App\Policies;
use App\User;
use App\Role;
use Illuminate\Auth\Access\HandlesAuthorization;
use App\Policies\BasePolicy;

class RolePolicy extends BasePolicy
{
    use HandlesAuthorization;

    public function show(User $user)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting =  $this->setting('role_view');

        return in_array($user->role_id, json_decode($setting['value'],true));
    }
}
