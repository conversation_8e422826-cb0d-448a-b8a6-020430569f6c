<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('logs', function (Blueprint $table) {
            $table->uuid('id');
            $table->integer('action');
            $table->integer('object_type');
            $table->string('object_id');
            $table->string('created_by');
            $table->text('data_response');
            $table->text('data_request')->nullable();
            $table->index('object_type');
            $table->index('object_id');
            $table->index('action');
            $table->index('created_by');
            $table->index('created_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('logs');
    }
}
