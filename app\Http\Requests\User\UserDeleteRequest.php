<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UserDeleteRequest extends BaseRequest
{
    
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:users,id'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'User_001_E_009',
    //         'id.uuid' => 'User_002_E_010',
    //         'id.exists' => 'User_003_E_011',
    //     ];
    // }
}
