<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class OTPRequest extends BaseRequest
{

    public function rules()
    {
        return [
            // 'session_id' => 'required|uuid',
            'phone'   => 'required_without:email|digits_between:10,11',
            'email'   => 'required_without:phone|email:rfc,dns'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'User_001_E_001',
    //         'id.uuid' => 'User_002_E_002',
    //         'id.exists' => 'User_003_E_003',
    //     ];
    // }
}
