<?php

namespace App\Http\Requests\Shop;

use App\Http\Requests\BaseRequest;

class ShopSettingRequest extends BaseRequest
{
    function rules()
    {
        return [
          'shop_id' => 'required|uuid|exists:shops,id',

          'setting' => 'required|array',

          'setting.general' => 'nullable|array',
          'setting.general.*' => 'nullable|array',
          'setting.genaral.*.enabled' => 'required_with:setting.genaral.*|boolean',

          'setting.order' => 'nullable|array',
          'setting.order.delivery' => 'nullable|array',
          'setting.order.delivery.*' => 'nullable|array',
          'setting.order.delivery.discount.*.relationship_condition' => 'required_with:setting.order.delivery.discount.*|in:AND,OR',
          'setting.order.delivery.discount.*.name' => 'required_with:setting.order.delivery.discount.*|string',
          'setting.order.delivery.discount.*.value' => 'required_with:setting.order.delivery.discount.*|numeric',
          'setting.order.delivery.discount.*.enabled' => 'required_with:setting.order.delivery.discount.*|boolean',
          'setting.order.delivery.discount.*.type' => 'required_with:setting.order.delivery.discount.*|string|in:percent,money',
          'setting.order.delivery.discount.*.conditions' => 'required_with:setting.order.delivery.discount.*|array',
          'setting.order.delivery.discount.*.conditions.*.param' => 'required_with:setting.order.delivery.discount.*.conditions.*|string',
          'setting.order.delivery.discount.*.conditions.*.operator' => 'required_with:setting.order.delivery.discount.*.conditions.*|string',
          'setting.order.delivery.discount.*.conditions.*.value' => 'required_with:setting.order.delivery.discount.*.conditions.*|string',

          'setting.order.delivery.discount.*.max_amount' => 'nullable|numeric|min:0',
        ];
    }
}
