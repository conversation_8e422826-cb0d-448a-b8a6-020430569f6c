<?php

namespace App\Http\Requests;

use App\Http\Requests\BaseRequest;

class PropertyFacilityRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'property_id' => 'integer|exists:properties,id',
            'data.*.facility_id' => 'integer|exists:facilities,id',
            'data.*.mark' => 'numeric|min:0|max:10'
        ];
    }
    
    public function messages()
    {

        return [
            'data.*.facility_id.integer' => 'PropertyFacility_002_E_001',
            'data.*.facility_id.exists' => 'PropertyFacility_003_E_002',
            'property_id.integer' => 'PropertyFacility_002_E_003',
            'property_id.exists' => 'PropertyFacility_003_E_004',
            'data.*.mark.min' => 'PropertyFacility_004_E_005',

            'data.*.mark.max' => 'PropertyFacility_004_E_006',
            'data.*.mark.numeric' => 'PropertyFacility_002_E_007',


        ];
    }
}
