<?php

namespace App\Admin\Controllers;

use App\BusinessType;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Illuminate\Support\Str;

class BusinessTypeController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'BusinessType';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new BusinessType());

        $grid->column('id', __('Id'));
        $grid->column('name', __('Name'))->sortable()->link(function () {
            return "business_type/".$this->id;
        });
        $grid->column('description', __('Description'));
        $grid->column('thumbnail', __('Thumbnail'))->image();
        $grid->column('enable', __('Enable'))->icon([
            0 => 'toggle-off',
            1 => 'toggle-on',
        ], $default = '');
        $grid->column('updated_at', __('Updated at'));

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(BusinessType::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('name', __('Name'));
        $show->field('description', __('Description'));
        $show->field('enable', __('Enable'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new BusinessType());

        $form->text('name', __('Name'));
        $form->switch('enable', __('Enable'))->default(1);
        $form->text('description', __('Description'));
        $form->image('thumbnail', __('Thumbnail'))->removable()->downloadable()->move("business_type")
            ->rules('mimes:jpeg,png,jpg,gif,svg,webp');

        return $form;
    }
}
