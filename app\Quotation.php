<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
class Quotation extends Model
{
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $table = 'quotations';

    protected $fillable = [
        'supplier_id', 'name', 'from', 'to', 'notes', 'file', 'shop_id', 'status', 'link'
        // Add other fillable columns
    ];
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
        
        static::deleting(function ($model) {
            $model->materialQuotations()->detach();
        });
    }

    // Define any relationships or additional methods here
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }
    
    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    public function materialQuotations()
    {
        return $this->hasMany(MaterialQuotation::class)->with('material');
    }
}
