# Referral System Tests

This directory contains test files for the referral system functionality.

## Test Files

### 1. `test_referral_system.php`
**Purpose**: Comprehensive test of all referral system functionality
**Usage**: 
```bash
cd d:\workspace\xampp\htdocs\clomart-backend
php tests/referral/test_referral_system.php
```

**What it tests**:
- Database schema (referral_code and ref_id fields)
- Referral code generation
- findByReferralCode method
- Model relationships (referredOrders, referrer)
- Referral statistics

### 2. `test_referral_case_sensitivity.php`
**Purpose**: Test case-insensitive handling of referral codes
**Usage**:
```bash
cd d:\workspace\xampp\htdocs\clomart-backend
php tests/referral/test_referral_case_sensitivity.php
```

**What it tests**:
- Uppercase referral codes
- Lowercase referral codes
- Mixed case referral codes
- Referral codes with whitespace
- Invalid referral codes

### 3. `tinker_referral_test.php`
**Purpose**: Quick test script for referral code case sensitivity
**Usage**: Copy and paste into Laravel Tinker
```bash
php artisan tinker
# Then copy/paste the contents of this file
```

### 4. `tinker_test.php`
**Purpose**: Basic referral system test for use in Laravel Tinker
**Usage**: Copy and paste into Laravel Tinker
```bash
php artisan tinker
# Then copy/paste the contents of this file
```

## Prerequisites

Before running these tests, ensure:

1. **Database is set up**: Make sure your database is configured and migrations have been run
2. **Laravel environment**: Tests need access to Laravel's bootstrap
3. **Test data**: Some tests may require existing users in the database

## Running Tests

### Option 1: Direct PHP execution
```bash
cd d:\workspace\xampp\htdocs\clomart-backend
php tests/referral/test_referral_system.php
php tests/referral/test_referral_case_sensitivity.php
```

### Option 2: Laravel Tinker (for tinker_*.php files)
```bash
cd d:\workspace\xampp\htdocs\clomart-backend
php artisan tinker
```
Then copy and paste the content of the tinker test files.

## Expected Results

### Successful Test Output
- ✓ All database fields exist
- ✓ Referral code generation works
- ✓ Case-insensitive lookups work
- ✓ Model relationships function correctly
- ✓ Statistics are calculated properly

### Common Issues
- **Class not found errors**: Ensure you're running from the project root
- **Database connection errors**: Check your `.env` file configuration
- **Migration errors**: Run `php artisan migrate` first

## Notes

- Tests are designed to be non-destructive where possible
- Some tests create temporary users that are cleaned up automatically
- If tests fail unexpectedly, check the Laravel logs in `storage/logs/`
