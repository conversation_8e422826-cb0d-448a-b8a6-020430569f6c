<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('/notify', function () {
//     return view('e  mails.notification');
// });

// // Auth::routes();

// Route::get('/home', 'HomeController@index')->name('home');

// //Admin
// Route::get('admin', 'AdminController@index')->name('admin');

Route::group(['prefix' => 'admin_get', 'as' => 'admin_get.', 'namespace' => '\App\Admin\Controllers'], function () {
    //user
    Route::get('/district', 'AdminController@districtByProvinceId')->name('district');
    Route::get('/ward', 'AdminController@wardByDistrictId')->name('ward');
    Route::get('/shops', 'AdminController@shops')->name('shops');
    Route::get('/shop_by_id', 'AdminController@shop_by_id')->name('shop_by_id');
    Route::get('/users', 'AdminController@users')->name('users');
    Route::get('/agents', 'AdminController@agents')->name('agents');
});

// //language
// Route::get('lang/{lang}', 'LangController@changeLang')->name('lang');

// //Logout Admin
// Route::get('/logout', 'Admin\LogoutController@logout')->name('admin.logout');

//Login Admin
// Route::get('admin/login', 'Admin\LoginController@index');
// Route::post('admin/login', 'Admin\LoginController@login')->name('admin.login');
