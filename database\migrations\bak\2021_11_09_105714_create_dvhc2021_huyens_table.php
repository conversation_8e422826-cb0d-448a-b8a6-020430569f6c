<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDvhc2021HuyensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('dvhc2021_huyen', function (Blueprint $table) {
            $table->integer('id');
            $table->string('name');
            $table->integer('province_id');
            $table->text('slug')->nullable();
            $table->float('longitude')->nullable();
            $table->float('latitude')->nullable();
            $table->string('profile_picture')->nullable();
            $table->string('bound')->nullable();
            $table->string('address')->nullable();
            $table->integer('administrative_unit');
            $table->string('streetmap')->nullable();
            $table->string('satellite')->nullable();
            $table->boolean('planning')->nullable()->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('dvhc2021_huyen');
    }
}
