<?php

namespace App\Http\Middleware;

use Closure;

class XSS
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        // $url = str_replace($request->url(), "", $request->fullUrl());
        $input = $request->all();

        array_walk_recursive($input, function (&$input) {

            if($input !== null && $input !== false && $input !== true)
            {
                $input = strip_tags($input);
                // $input = htmlspecialchars($input);
            }

        });

        // if (preg_match('/[\'^£$%&*()}{@#~><>|_+¬-]/', $url))
        //     return redirect($request->url() . "/" . preg_replace('/[\'^£$%&*()}{@#~><>|_+¬-]/',"",strip_tags($url)));
        $request->merge($input);
        return $next($request);
    }
}
