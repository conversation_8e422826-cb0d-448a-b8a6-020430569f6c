<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Services\Stock\StockService;
use App\Services\GeneralService;
use App\Http\Requests\Stock\StockImportRequest;
use App\Http\Requests\Stock\StockExportRequest;
use App\Http\Requests\Stock\StockWasteRequest;
use App\Http\Requests\Stock\StockDailySummaryRequest;
use App\Product;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class StockController extends Controller
{
    private $stockService;

    public function __construct()
    {
        $this->stockService = new StockService();
    }

    /**
     * Import stock (Purchase)
     *
     * @param StockImportRequest $request
     * @return JsonResponse
     */
    public function import(StockImportRequest $request)
    {
        $data = $request->validated();
        $userId = Auth::user()->id;

        // Get product to check shop ownership
        $product = Product::find($data['product_id']);
        if (!$product) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'Product not found'
                ]
            ], JsonResponse::HTTP_OK);
        }

        // Check permission using GeneralService pattern
        if (!GeneralService::checkShopOwner($userId, $product->shop_id) &&
            !GeneralService::checkAgentShopRelate($userId, $product->shop_id)) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'You are not authorized to manage this shop\'s inventory.'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $result = $this->stockService->importStock($data, $userId);

        if (isset($result['error'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => $result['error']
                ]
            ], JsonResponse::HTTP_OK);
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'message' => $result['message'],
                'data' => $result['data']
            ]
        ], JsonResponse::HTTP_OK);
    }

    /**
     * Export stock (Sale)
     *
     * @param StockExportRequest $request
     * @return JsonResponse
     */
    public function export(StockExportRequest $request)
    {
        $data = $request->validated();
        $userId = Auth::user()->id;

        // Get product to check shop ownership
        $product = Product::find($data['product_id']);
        if (!$product) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'Product not found'
                ]
            ], JsonResponse::HTTP_OK);
        }

        // Check permission using GeneralService pattern
        if (!GeneralService::checkShopOwner($userId, $product->shop_id) &&
            !GeneralService::checkAgentShopRelate($userId, $product->shop_id)) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'You are not authorized to manage this shop\'s inventory.'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $result = $this->stockService->exportStock($data, $userId);

        if (isset($result['error'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => $result['error']
                ]
            ], JsonResponse::HTTP_OK);
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'message' => $result['message'],
                'data' => $result['data']
            ]
        ], JsonResponse::HTTP_OK);
    }

    /**
     * Waste/Discard stock
     *
     * @param StockWasteRequest $request
     * @return JsonResponse
     */
    public function waste(StockWasteRequest $request)
    {
        $data = $request->validated();
        $userId = Auth::user()->id;

        // Get product to check shop ownership
        $product = Product::find($data['product_id']);
        if (!$product) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'Product not found'
                ]
            ], JsonResponse::HTTP_OK);
        }

        // Check permission using GeneralService pattern
        if (!GeneralService::checkShopOwner($userId, $product->shop_id) &&
            !GeneralService::checkAgentShopRelate($userId, $product->shop_id)) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'You are not authorized to manage this shop\'s inventory.'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $result = $this->stockService->wasteStock($data, $userId);

        if (isset($result['error'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => $result['error']
                ]
            ], JsonResponse::HTTP_OK);
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'message' => $result['message'],
                'data' => $result['data']
            ]
        ], JsonResponse::HTTP_OK);
    }

    /**
     * Get stock history for a product
     *
     * @param string $productId
     * @return JsonResponse
     */
    public function history($productId)
    {
        $userId = Auth::user()->id;

        // Get product to check shop ownership
        $product = Product::find($productId);
        if (!$product) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'Product not found'
                ]
            ], JsonResponse::HTTP_OK);
        }

        // Check permission using GeneralService pattern
        if (!GeneralService::checkShopOwner($userId, $product->shop_id) &&
            !GeneralService::checkAgentShopRelate($userId, $product->shop_id)) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'You are not authorized to view this shop\'s inventory.'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $result = $this->stockService->getStockHistory($productId);

        if (isset($result['error'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => $result['error']
                ]
            ], JsonResponse::HTTP_OK);
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result['data']
            ]
        ], JsonResponse::HTTP_OK);
    }

    /**
     * Get daily stock summary for a shop
     *
     * @param StockDailySummaryRequest $request
     * @return JsonResponse
     */
    public function dailySummary(StockDailySummaryRequest $request)
    {
        $data = $request->validated();
        $userId = Auth::user()->id;

        // Check permission using GeneralService pattern
        if (!GeneralService::checkShopOwner($userId, $data['shop_id']) &&
            !GeneralService::checkAgentShopRelate($userId, $data['shop_id'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'You are not authorized to view this shop\'s inventory.'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $result = $this->stockService->getDailyStockSummary(
            $data['shop_id'],
            $data['date'] ?? null
        );

        if (isset($result['error'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => $result['error']
                ]
            ], JsonResponse::HTTP_OK);
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result['data']
            ]
        ], JsonResponse::HTTP_OK);
    }

    /**
     * Get stock summary for current user's shops
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function myShopsSummary(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body' => [
                    'message' => 'Unauthorized'
                ]
            ], JsonResponse::HTTP_OK);
        }

        // Get user's shops based on role
        $shops = [];
        if ($user->role_id == User::ROLE_SHOP_OWNER) {
            $shops = \App\Shop::where('user_id', $user->id)->get();
        } elseif ($user->role_id == User::ROLE_AGENT) {
            $shops = \App\Shop::where('agent_id', $user->id)->get();
        } elseif ($user->role_id == User::ROLE_ADMIN) {
            $shops = \App\Shop::all();
        }

        $summaries = [];
        $date = $request->input('date');

        foreach ($shops as $shop) {
            $result = $this->stockService->getDailyStockSummary($shop->id, $date);
            if (isset($result['data'])) {
                $summaries[] = [
                    'shop' => $shop,
                    'summary' => $result['data']
                ];
            }
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $summaries
            ]
        ], JsonResponse::HTTP_OK);
    }
}
