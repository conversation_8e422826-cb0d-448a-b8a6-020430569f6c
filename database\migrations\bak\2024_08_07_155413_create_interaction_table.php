<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInteractionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('interactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->integer('object_type_a');
            $table->uuid('object_id_a')->comment('user => 1,product=> 2,shop=> 3,category=> 4, order=> 9');
            $table->integer('object_type_b');
            $table->uuid('object_id_b')->comment('user => 1,product=> 2,shop=> 3,category=> 4, order=> 9');
            $table->string('interaction_type', 50);
            $table->index(['object_id_a','object_id_b']);
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('interactions');
    }
}
