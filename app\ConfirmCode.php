<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ConfirmCode extends Model
{
    protected $fillable = [
        'code',
        'user_id',
        'action_type',
        'expire_time'
    ];

    protected $table = 'confirm_codes';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
}
