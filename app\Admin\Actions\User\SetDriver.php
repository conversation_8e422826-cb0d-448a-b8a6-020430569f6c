<?php

namespace App\Admin\Actions\User;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class SetDriver extends RowAction
{
    public function name()
    {
        return __('admin.driver.set');
    }

    public function handle (Model $model)
    {
        $model->role_id = 5;
        $model->save();

        return $this->response()->success('SetDriver')->refresh();
    }

    public function dialog()
    {
        $this->confirm(__('admin.driver.set').'?');
    }
}