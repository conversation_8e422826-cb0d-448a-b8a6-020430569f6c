<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class SinglePage extends Model
{
    protected $fillable = [
        'title',
        'content',
        'slug',
        'keywords',
        'description',
        'profile_picture',
        'image',
        'enable'
    ];
    protected $primaryKey = 'id';
    protected $table = 'single_pages';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
    protected $hidden = ['updated_at', 'created_at'];

    public function avatars()
    {
        return $this->belongsTo(Image::class,'image')->select(['id','path','title','path_thumbnail'])->where('enable', true);
    }

    public function images()
    {
        return $this->hasMany(Image::class,'parent_id')->orderBy('index')->select(['id','path','title','index','parent_id','panorama'])->where('enable', true);
    }
}
