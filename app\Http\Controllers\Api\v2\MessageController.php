<?php

namespace App\Http\Controllers\Api\v2;


use App\ChannelConnect;
use App\Http\Requests\Message\MessageGetRequest;
use App\Http\Requests\Message\MessageRequest;
use App\Http\Requests\Message\RemoveMessageRequest;
use App\Http\Requests\Message\UpdateMessageRequest;
use App\Jobs\NotifyNewMessage;
use App\Jobs\TranslateContent;
use App\Message;
use App\Notifications\NotificationUser;
use App\Services\Chat\ChannelConnectionService;
use App\Services\Chat\ChannelService;
use App\Services\Chat\MessageService;
use App\Services\GeneralService;
use App\Services\Mqtt\MqttChatService;
use App\Services\Notification\NotificationService;
use App\Services\Token\TokenService;
use App\Shop;
use App\user;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\Translate\TranslateService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class MessageController extends Controller
{
    public function sendMessage(Request $request)
    {
        $data = $request->only(['channel_id','receiver','sender_id', 'sender_type', 'content', 'language']);
        $sender = null;
        switch ($data['sender_type']) {
            case 'user': {
                if($data['sender_id'] != Auth::user()->id) {
                    $response = [
                        'status' => JsonResponse::HTTP_UNAUTHORIZED,
                        'body' => [
                            'message' => 'Unauthorized.'
                        ]
                    ];
                    return response()->json($response, Response::HTTP_OK);
                }
                $sender  = User::find($data['sender_id']);
                $sender->avatar = ($sender->profile_picture ?? '');
                break;
            }
            case 'shop': {
                if(!GeneralService::checkShopOwner(Auth::user()->id, $data['sender_id']))
                {
                    $response = [
                        'status' => JsonResponse::HTTP_BAD_REQUEST,
                        'body' => [
                            'message' => 'This shop doesn\'t belong to you.'
                        ]
                    ];

                    return response()->json($response, JsonResponse::HTTP_OK);
                }
                $sender  = Shop::find($data['sender_id']);
                $sender->avatar = env('S3_HOST_PATH').($sender->logo->path ?? '');
                break;
            }
            default:
                $response = [
                    'status' => JsonResponse::HTTP_BAD_REQUEST,
                    'body' => [
                        'message' => 'Send message failed.'
                    ]
                ];
                return response()->json($response, JsonResponse::HTTP_OK);
        }
        $channelId = '';
        $ChannelService = new ChannelService($data['sender_id'], $data['sender_type']);
        if (!$request->channel_id) {
            $channel = $ChannelService->create([$data['receiver']]);
            $channelId = $channel->id;
        } else {
            $channelId =  $data['channel_id'] ?? '';
        }

        if(!$channelId){
            $response = [
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body' => [
                    'message' => 'hanndle Channel Failed.'
                ]
            ];
            return response()->json($response, Response::HTTP_OK);
        }

        $result = Message::create([
            'channel_id' => $channelId,
            'member_id' => $data['sender_id'],
            'member_type' => $data['sender_type'],
            'content' => ($data['content'])]);

        //gửi notify đến cho các thành viên trong channel
        $channelMembers = $ChannelService->getMemberInfoChannel($channelId);
        if($channelMembers && count($channelMembers) > 0){
            $tokenService = new TokenService();

            foreach ($channelMembers as $key => $member) {
                if($member['member_type'] == 'shop'){
                    $shop = Shop::find($member['member']['id']);
                    $messToOwner = [
                        'title' => "Tin nhắn mới",
                        'body'  => "Tin nhắn mới",
                        'icon' => $sender->avatar,
                        'url'   => $member['channel_id'],
                        'type'  => 'mesage_new',
                    ];

                    $messToAgent = [
                        'title' => "Tin nhắn mới",
                        'body'  => "Tin nhắn mới",
                        'icon' => $sender->avatar,
                        'url'   => $member['channel_id'],
                        'type'  => 'mesage_new',
                    ];

                    $shop_owner = $shop->user_id;
                    $shop_agent = $shop->user_id != $shop->agent_id ? $shop->agent_id : "";

                    # tự nhận đơn
                    $tokenService = new TokenService();
                    $tokens = $tokenService->listByUser($shop_owner, 2);
                    $agentTokens = $tokenService->listByUser($shop_agent, 2);
                    $notiService = new NotificationService;
                    if(count($tokens) > 0){
                        $notiService->sendBatchNotification($tokens, $messToOwner);
                    }
                    if(count($agentTokens) > 0){
                        $notiService->sendBatchNotification($agentTokens, $messToAgent);
                    }

                    // die(json_encode($channelMembers));
                }else{
                    //member is user
                    $messToUser = [
                        'title' => "Tin nhắn mới",
                        'body'  => $sender->name . " gửi tin nhắn cho bạn.",
                        'icon' => $sender->avatar,
                        'url'   => $member['channel_id'],
                        'type'  => 'mesage_new',
                    ];
                    $tokenService = new TokenService();
                    $tokens = $tokenService->listByUser($member['member']['id'], 2);
                    $notiService = new NotificationService;
                    if(count($tokens) > 0){
                        $notiService->sendBatchNotification($tokens, $messToUser);
                    }
                }
                //
                # code...
            }

        }
        $result['content'] = $data['content'];
        return response()->json([
            'status' => JsonResponse::HTTP_CREATED,
            'body' => $result,
        ], Response::HTTP_OK);
    }


    public function createMessage(MessageRequest $request)
    {
        $data = $request->only(['channel_id','receiver','sender_id', 'sender_type', 'content', 'language']);
        $sender = null;
        $receiver = $data["receiver"] ?? null;

        // Kiểm tra ID ngưởi gửi có quyền tạo tin nhắn hay không?
        if(!ChannelConnectionService::checkAuthor($data['sender_id'], $data['sender_type'])){
            $response = [
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body' => [
                    'message' => 'Unauthorized.'
                ]
            ];
            return response()->json($response, Response::HTTP_OK);
        }

        switch ($data['sender_type']) {
            case 'user': {
                $sender  = User::find($data['sender_id'])->toArray();
                $sender['avatar'] = $sender['profile_picture'] ?? '';
                if (!str_contains($sender['avatar'], 'http')) {
                    $sender['avatar'] = env('S3_HOST_PATH') . $sender['avatar'];
                }

                break;
            }
            case 'shop': {
                $sender  = Shop::find($data['sender_id'])->toArray();
                $sender['avatar'] = env('S3_HOST_PATH').($sender['logo']['path'] ?? '');
                break;
            }
        }
        $channelService = new ChannelService($data['sender_id'], $data['sender_type']);
        $messageService = new MessageService($data['sender_id'], $data['sender_type']);


        //      Tìm ID của channel khi có người nhận nhưng không có channel
            if(!isset($data['channel_id'])){
                $channelId = $channelService->getChannelWithReceiver($receiver['id']);
            }else {
                $channelId = $data['channel_id'];
            }

            if (!$channelId) {
                $channel = $channelService->create([$data['receiver']]);
                $channelId = $channel->id;
            }

        //      Get receiver information through the channel if there is no receiver information in the request
        $channelMembers = $channelService->getMemberInfoChannel($channelId);
        if(!$receiver){
            $receiver = $channelMembers[0]['member'];
            $receiver['type'] = $channelMembers[0]['member_type'];
        }


        $content = MessageService::convertFormatContent($data['language'], $data['content']['text'], $data['content']);

        $result = Message::create([
            'channel_id' => $channelId,
            'member_id' => $data['sender_id'],
            'member_type' => $data['sender_type'],
            'content' => ($content)]);

        if(!$messageService->checkLanguageBetweenTwoMember($data['language'], $receiver)){
            $languages = ChannelConnectionService::getLanguageOfAMember($receiver['id'], $receiver['type']);
            TranslateContent::dispatch($languages, $content, $result->id)->chain([
                new NotifyNewMessage($channelMembers, $sender, $channelId)
            ]);
        }else{
            //gửi notify đến cho các thành viên trong channel
            NotifyNewMessage::dispatch($channelMembers,$sender, $channelId);
        }


        return response()->json([
            'status' => JsonResponse::HTTP_CREATED,
            'body' => $result,
        ], Response::HTTP_OK);
    }

    public function retractMessage(RemoveMessageRequest $request){
        $data = $request->only(['message_id', 'member_id', 'status']);
        $message = Message::find($data['message_id']);

        // Kiểm tra quyền xoá của người dùng với tin nhắn
        if($message->member_id != $data['member_id']){
            return response() ->json(
                [
                    'status' => JsonResponse::HTTP_FORBIDDEN,
                    'errors' => [
                        'message' =>  'Do not have permission to delete this message',
                        'field' =>  'member_id',
                    ]
                ], Response::HTTP_OK
            );
        }

        // Kiểm tra thời gian có thể thu hồi tin nhắn đã gửi
        if ($message->created_at < now()->subDay()) {
            return response()->json([
                'status' => JsonResponse::HTTP_FORBIDDEN,
                'body' => [
                    'message' => 'Cannot delete messages older than 24 hours.',
                ]
            ], Response::HTTP_OK);
        }

        $message->update(['status' => $data['status']]);
        MessageService::notifyMessageUpdate($message->channel_id);

        return response() ->json(
            [
                'status' => JsonResponse::HTTP_OK,
                'message' => 'Retract message success'
            ], Response::HTTP_OK);
    }

    public function updateMessage(UpdateMessageRequest $request, MqttChatService $mqtt)
    {
        $data = $request->only(['message_id', 'member_id', 'content', 'language']);
        $message = Message::where('id', $data['message_id'])->first();
        // Kiểm tra quyền xoá của người dùng với tin nhắn
        if($message->member_id != $data['member_id']){
            return response() ->json(
                [
                    'status' => JsonResponse::HTTP_FORBIDDEN,
                    'errors' => [
                        'message' =>  'Do not have permission to delete this message',
                        'field' =>  'member_id',
                    ]
                ], Response::HTTP_OK
            );
        }

        // Kiểm tra trạng thái thu hồi
        if($message->status != 1){
            return response() ->json(
                [
                    'status' => JsonResponse::HTTP_FORBIDDEN,
                    'errors' => [
                        'message' => 'This message had been retracted',
                    ]
                ], Response::HTTP_OK
            );
        }

        $contentOld = $message->content;

        $connections = ChannelConnect::on('pgsqlReplica')
            ->where('channel_id', $message->channel_id)
            ->where('member_id', "!=", $message->member_id)->get();

        $content = $data['content'];

        $message->update(['content' => ($content)]);

        foreach ($connections as $connection) {
            if(!empty($data['content']['text']) && $data['content']['text'] != $contentOld['text']){
                $languages = ChannelConnectionService::getLanguageOfAMember($connection->member_id, $connection->member_type);
                TranslateContent::dispatch($languages, $content, $data['message_id']);
            }
        }


        MessageService::notifyMessageUpdate($message->channel_id);

        return response() ->json(
            [
                'status' => JsonResponse::HTTP_OK,
                'body' => $message
            ]
        );
    }



    // Lấy tin nhắn trong kênh chat
    public function getMessages(MessageGetRequest $request){
        $data = $request->only(['channel_id', 'limit', 'offset', 'language', 'member_id', 'member_type']);
        $limit = $data['limit'] ?? 10;
        $offset = $data['offset'] ?? 0;
        $language = $data['language'] ?? "";
        $userId = Auth::user()->id;

        $memberId = $data['member_type'] == 'shop' ? $data['member_id'] : $userId;

        if($data['member_type'] == 'shop' && !GeneralService::checkShopOwner($userId, $memberId) && !GeneralService::checkAgentShopRelate($userId, $memberId)){
            return response() ->json([
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body'   => [
                    "message" => 'Not authorized',
                ]
            ], JsonResponse::HTTP_OK);
        }

        $connections = ChannelConnect::on('pgsqlReplica')->where([
            'channel_id' => $data['channel_id'],
            'member_id' => $memberId,
        ])->exists();

        if(!$connections){
            return response() ->json([
                'status' => JsonResponse::HTTP_FORBIDDEN,
                'body'   => [
                    "message" => 'Not authorized',
                ]
            ]);
        }

        $messages = Message::where('channel_id', $data['channel_id']);
        //->where('member_id', $data['owner_id'])
        // ->whereIn('id',['0773b528-db57-4bd4-b0f2-319d75074de2'])
        $count = $messages->count();
        $messagesResponse = $messages->orderBy('created_at', 'desc')->take($limit)->skip($offset)->get()
            ->map(function ($message) use ($memberId) {
                if($message->status == 3){
                    $message->content = null;
                }
                if($message->member_id == $memberId && $message->status == 2){
                    $message->content = null;
                }
                return $message;
        });
//        if($language){
//            foreach ($messages as &$message) {
//                if($message->content){
//                    $content = $message->content;
//                    // $content = json_decode($message->content, true);
//                    // var_dump($content);die;
//                    if (!isset($content['translate']) || !isset($content['translate'][$language]) ) {
//                        $translateService = new TranslateService();
//                        $translate = $translateService->translate($content['text'],$language);
//                        if($translate['status'] == 'success'){
//                            // echo ($translate);die;
//                            $content['translate'][$language] = $translate['content'];
//                            $message->update(['content' => ($content)]);
//                        }else{
//                        }
//                    }
//                    // $message->content = ($message->content);
//                }
//            }
//        }

        $result = [
            'count' => $count,
            'result' => $messagesResponse
        ];
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ], Response::HTTP_OK);


    }

//    public function getMessagesWithReceiver(MessageGetRequest $request){
//        $data = $request->only(['owner_id', 'owner_type', 'opponent_id' , 'limit', 'offset', 'language']);
//        $service= new MessageService($data['owner_id'], $data['owner_type']);
//
//        if(!ChannelConnectionService::checkAuthor($data['owner_id'], $data['owner_type'])){
//            $response = [
//                'status' => JsonResponse::HTTP_UNAUTHORIZED,
//                'body' => [
//                    'message' => 'Not Authorized.',
//                ]
//            ];
//            return response() ->json($response, Response::HTTP_OK);
//        }
//
//        $limit = $data['limit'] ?? 10;
//        $offset = $data['offset'] ?? 0;
//        $messages = $service->getMessagesWithOtherMember($data['opponent_id'], $limit, $offset);
//        return response() -> json([
//            'status' => JsonResponse::HTTP_OK,
//            'body' => [
//                'count' => count($messages),
//                'data' => $messages,
//            ]
//        ], Response::HTTP_OK);
//    }
}
