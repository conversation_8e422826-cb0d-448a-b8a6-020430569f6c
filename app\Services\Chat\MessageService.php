<?php

namespace App\Services\Chat;

use App\Message;
use App\Services\GeneralService;
use App\Services\Mqtt\MqttChatService;
use Illuminate\Support\Facades\Auth;
use phpDocumentor\Reflection\Type;

class MessageService
{
    private $_memberId;
    private $_memberType;


    public function __construct($_memberId, $_memberType = null)
    {
        $this->_memberId = $_memberId;
        $this->_memberType = $_memberType;
    }

    function getMessagesWithOtherMember($opponentId, $limit, $offset)
    {
        $modelMessage = new Message();
        $modelMessage->setConnection('pgsqlReplica');
        $service = new ChannelService($this->_memberId, $this->_memberType);
        $channelId = $service->getChannelWithReceiver($opponentId);
        $result = $modelMessage
            ->where('channel_id',$channelId)
            ->orderBy('created_at', 'desc')
            ->take($limit)
            ->skip($offset)
            ->get();
        return $result;
    }

    /**
     * Check if the language between the receiver and the sender is the same.
     *
     * @param  mixed  $senderLanguage
     * @param  mixed  $receiver
     * @return bool
     */
    function checkLanguageBetweenTwoMember( $senderLanguage, $receiver)
    {
        $receiverLanguage = ChannelConnectionService::getLanguageOfAMember($receiver['id'], $receiver['type']);
        if(in_array($senderLanguage, $receiverLanguage)) {
            return true;
        }else{
            return false;
        }
    }

    /**
     * Convert the content to the correct system format.
     * @param mixed $language The language code (e.g., 'en', 'fr', 'vi') to specify the formatting language.
     * @param mixed $contentTranslated The translated content that needs to be formatted.
     * @param mixed $content The original content to be formatted.
     * @return mixed
     */
    static function convertFormatContent($language, $contentTranslated, $content){
        $content['translate'][$language] = $contentTranslated;
        return $content;
    }

    static function notifyMessageUpdate($channelId)
    {
        $mess = [
            'title' => "Cập nhật tin nhắn",
            'body'  => "Có tin nhắn được cập nhật",
            'url'   => $channelId,
            'type'  => "message_update",
        ];
        $mqtt = new MqttChatService();
        $mqtt -> publish(['topic' => "remagan_chat/".$channelId, 'message' => $mess]);
    }
}
