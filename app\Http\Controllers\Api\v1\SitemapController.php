<?php

namespace App\Http\Controllers\Api\v1;
use App\Services\Sitemap\SitemapService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use ZipArchive;
use App\Helpers\S3Utils;
use Storage;


class SitemapController extends Controller
{
    public function getSlug(Request $request,SitemapService $service)
    {
       $data = $request->only([
            'type',
            'limit',
            'offset'
        ]);
        $result = $service->getSlug($data);

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => $result
        ];
        return $response;
    }
    public function notificationInterval(Request $request, SitemapService $service)
    {
        $data = $request->only([
            'name'
        ]);
        $result = $service->notificationInterval($data);

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => $result
        ];
        return $response;
    }
}
