<?php

return [
    'client' => [
        'hosts' => [
            env('SCOUT_ELASTIC_HOST', 'localhost:9200'),
            // This is effectively equal to: "https://username:password!#$?*<EMAIL>:9200/elastic"
            // [
            //     'host' => 'foo.com',
            //     'port' => '9200',
            //     'scheme' => 'https',
            //     'path' => '/elastic',
            //     'user' => 'username',
            //     'pass' => 'password!#$?*abc'
            // ],
            // $hosts = [
            //     [
                // elastic:MyPwd123@localhost:9200
                //     'host' => env('HOST_ELASTIC', 'localhost'),
                //     'port' => env('PORT_ELASTIC', '9200'),
                //     'user' => env('USER_ELASTIC', 'elastic'),
                //     'pass' => env('PASSWORD_ELASTIC', 'MyPwd123')
            //     ]
            // ];
        ],
    ],
    'update_mapping' => env('SCOUT_ELASTIC_UPDATE_MAPPING', true),
    'indexer' => env('SCOUT_ELASTIC_INDEXER', 'single'),
    'document_refresh' => env('SCOUT_ELASTIC_DOCUMENT_REFRESH'),
];
