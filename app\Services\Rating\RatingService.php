<?php
namespace App\Services\Rating;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Product;
use App\Rating;
use App\Shop;
use App\Image;
use App\PlaceType;
use App\Order;
use App\Brand;
use App\Category;

class RatingService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }
    //-------process list --------------------
    public function list()
    {
        $result = Rating::orderBy('updated_at','desc')
                ->with('users', 'images')->get()->toArray();

        $total_rate = 0;
        foreach($result as $key => $item){
            // $item['obj'] = $this->ratingObj($item['object_id'], $item['object_type']);
            $total_rate += $item['rating'];
        }
        if(!$result)
        {
            return [
                'count' => 0,
                'average' => 0,
                'result' => []
            ];
        }

        $count = count($result);
        return [
            'count' => $count,
            'average' => $total_rate/$count,
            'result' => array_slice($result, $offset, $limit)
        ];
    }
    //-------process list rating by object id--------------------
    public function listByObjectId($object_id, $limit, $offset)
    {
        $result = Rating::select('*')->where('object_id', $object_id)
                ->orderBy('updated_at','desc')
                ->with('users', 'images')->get();

        $total_rate = 0;
        if(!$result)
        {
            return false;
        }

        $count = count($result);
        return [
            'count' => $count,
            'result' => array_slice($result->toArray(), $offset, $limit)
        ];
    }
    //-------process detail rating--------
    public function detail($id)
    {
        $result = Rating::where('id',$id)->with('users', 'images')->first();

        if(!$result)
        {
            return false;
        }
        $result->object = $this->ratingObj($result['object_id'], $result['object_type']);
        return $result;
    }

    public function detailByUserAndObject($objectId)
    {
        $result = Rating::on('pgsqlReplica')->where([
            'object_id' => $objectId,
            'user_id' => $this->_userId])
            ->with('users', 'images')->first();
        if(!$result){
            return ["error" => "You have not rated this object yet."];
        }
        return $result;
    }


    //-------process create rating--------
    public function create(array $obj)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $obj['user_id'] = $user->id;
        }

        $result = Rating::create($obj);

        return $result;
    }

    //-------process update rating--------
    public function update(array $obj)
    {
        $user = User::find($this->_userId);
        $result = Rating::find($obj['id']);
        if($user && $result && $user->id == $result->user_id)
        {
            if(isset($obj['image_delete'])){
                foreach($obj['image_delete'] as $key=>$value){
                    if(isset($value['id'])){
                        $image = Image::find($value['id']);
                        if($image){
                            $image->delete();
                        }
                    }
                }
            }
            if(isset($obj['images'])){
                foreach($obj['images'] as $key=>$value){
                    if(isset($value['id'])){
                        $image = Image::find($value['id']);
                        if($image){
                            $image->update([
                                'title' => $value['title'] ?? $image->title,
                                'description' => $value['description'] ?? $image->description,
                                'parent_id' => $obj['id'],
                                'style' => $value['style'] ?? $image->style,
                                'index' => $value['index'] ?? $image->index,
                            ]);
                        }
                    }
                }
            }

            $result->update($obj);
        }

        return $this->detail($obj['id']);
    }

    //-----process remove rating----------
    public function delete(array $obj)
    {
        $result = Rating::find($obj['id']);

        if(!$result)
        {
            return false;
        }

        //----------- delete action ---------------
        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.product'),
        //                 $result,
        //                 json_encode($product));

        return true;
    }

    //-------process get rating object--------
    public function ratingObj($object_id, $object_type)
    {
        if($object_type == config('constants.object_type.user')){
            return User::find($object_id);
        }
        if($object_type == config('constants.object_type.product')){
            return Product::find($object_id);
        }
        if($object_type == config('constants.object_type.shop')){
            return Shop::find($object_id);
        }
        if($object_type == config('constants.object_type.category')){
            return Category::find($object_id);
        }
        if($object_type == config('constants.object_type.image')){
            return Image::find($object_id);
        }
        if($object_type == config('constants.object_type.brand')){
            return Brand::find($object_id);
        }
        if($object_type == config('constants.object_type.order')){
            return Order::find($object_id);
        }
        return null;
    }

    //-------process calculating average rating of object--------
    public function calcAverageRating($object_id)
    {
        $result = Rating::where('object_id', $object_id)->get();

        $count = count($result);
        if($count == 0) return null;
        $total_rate = 0;
        foreach($result as $item){
            $total_rate += $item['rating'];
        }
        return $total_rate/count($result);
    }

    public function hasRatingByUser($object_id, $object_type) : bool
    {
        return Rating::on('pgsqlReplica')->where([
            'object_id' => $object_id,
            'object_type' => $object_type,
            'user_id' => $this->_userId
        ])->exists();
    }
}
