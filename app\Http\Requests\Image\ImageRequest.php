<?php

namespace App\Http\Requests\Image;

use App\Http\Requests\BaseRequest;

class ImageRequest extends BaseRequest
{

    public function rules()
    {
        switch ($this->object_type) {
            case '1':
                $parent_id = 'required|uuid|exists:users,id';
                break;
            case '2':
                $parent_id = 'required|uuid|exists:products,id';
                break;
            case '3':
                $parent_id = 'required|uuid|exists:shops,id';
                break;
            case '4':
                $parent_id = 'required|uuid|exists:categories,id';
                break;
            case '6':
                $parent_id = 'required|uuid|exists:brands,id';
                break;
            case '9':
                $parent_id = 'required|uuid|exists:orders,id';
                break;
            case '10':
                $parent_id = 'required|uuid|exists:ratings,id';
                break;
            case '11':
                $parent_id = 'nullable|uuid|exists:reels,id';
                break;
           case '12':
                $parent_id = 'required|uuid|exists:files,id';
                break;
           case '13':
                $parent_id = 'required|uuid|exists:channels,id';
                break;
            case '14':
                $parent_id = 'nullable|uuid|exists:addresses,id';
                break;
            default:
                $parent_id = 'required|uuid|exists:products,id';
                break;
        }
        return [
            'path'                  => 'bail|required|base64image|base64size',
            'object_type'           => 'required|in:1,2,3,4,5,6,7,8,9,10,11,12,13,14',
            'parent_id'             => $parent_id,
            'orientation'           => 'bail|nullable|in:0,1,2,3,4,5,6,7,8',
            // 'is_profile_picture'    => 'nullable|boolean',
            // 'index'                 => 'nullable|integer|min:0',
            // 'panorama'              => 'nullable|boolean',
            // 'description'           => 'nullable|max:5000'
        ];
    }
    // public function messages()
    // {
    //     return [
    //         'path.required' => 'Image_001_E_001',
    //         'path.base64size' => 'Image_004_E_002',
    //         'path.panoramasize' => 'Image_004_E_002',
    //         'path.base64image' => 'Image_002_E_003',

    //         'object_type.required' => 'Image_001_E_004',
    //         'object_type.in' => 'Image_002_E_005',
    //         // 'object_type.min' => 'Image_004_E_006',
    //         // 'object_type.max' => 'Image_004_E_007',

    //         'parent_id.required' => 'Image_001_E_008',
    //         'parent_id.uuid' => 'Image_002_E_009',
    //         'parent_id.exists' => 'Image_003_E_010',
    //         // 'orientation.integer' => 'Image_003_E_011',
    //         'orientation.in' => 'Image_002_E_011',

    //         'is_profile_picture.boolean' => 'Image_002_E_017',

    //         'index.integer'             => 'Image_002_E_018',
    //         'index.min'                 => 'Image_004_E_019',

    //         'panorama.boolean'          => 'Image_002_E_020',

    //         'description.max'          => 'Image_004_E_021'


    //     ];
    // }
}
