<?php

namespace App;

use ScoutElastic\IndexConfigurator;
use ScoutElastic\Migratable;

class MyIndexConfigurator extends IndexConfigurator
{
    use Migratable;

     // It's not obligatory to determine name. By default it'll be a snaked class name without `IndexConfigurator` part.
     protected $name = 'my_index';

     /**
     * You can specify any settings you want, for example, analyzers.
      * @var array
      */
    /**
     * @var array
     */
    protected $settings = [
        'analysis' => [
            'analyzer' => [
                'custom_analyzer' => [
                    'type' => 'standard',
                    'tokenizer' => 'keyword',
                    'filter' => [
                        'lowercase'
                    ],
                ],
            ],
        ],
    ];
}
