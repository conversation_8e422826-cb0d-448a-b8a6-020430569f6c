<?php
namespace App\Services\Translate;

use App\Jobs\SendNotification;
use Illuminate\Support\Facades\Auth;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Response;
use App\Setting;
use Illuminate\Support\Facades\Log;
use Google\Client as GoogleClient;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use App\Token;

class TranslateService {

    private $url   = null;
    private $token = null;
    private $project_id = null;

    public function __construct()
    {
        $setting = Setting::where('key', 'openai_api')->first();
        $options = json_decode($setting->value ?? '{}');
        $this->url = $options->url ?? 'https://api.openai.com/v1/chat/completions';
        $this->token = $options->token ?? null;
        $this->project_id = $options->project_id ?? 'proj_e71Fl29p5vvqaEpLhe9ZouAk';
    }

    public function translate($text, $language) 
    {
        if(!$text || strlen($text) < 1)
        return [
            "status" => "failed",
            "content" => "No text provided for translation."
        ];
        $languageMap = [
            'en' => 'English',
            'es' => 'Spanish',
            'fr' => 'French',
            'de' => 'German',
            'it' => 'Italian',
            'pt' => 'Portuguese',
            'nl' => 'Dutch',
            'ru' => 'Russian',
            'zh' => 'Chinese',
            'ja' => 'Japanese',
            'ko' => 'Korean',
            'ar' => 'Arabic',
            'he' => 'Hebrew',
            'hi' => 'Hindi',
            'vi' => 'Vietnamese',
            'ru' => 'Russian',
        ];

        $languageName = $languageMap[$language] ?? 'English'; // Default to English if not found

        $headers = [
            'Content-Type: application/json',
            'OpenAI-Project: '.$this->project_id,
            'Authorization: Bearer ' . $this->token,
        ];
        
        $data = [
            'model' => 'gpt-3.5-turbo',
            'messages' => [
                [
                    'role' => 'user',
                    // 'content' => "Translate to {$languageName} if not already in {$languageName}, do nothing if can not detect language: '{$text}'"
                    'content' => "Translate the following text to {$languageName}. If the text is already in {$languageName} or if you cannot detect the language, return the original text unchanged. Respond with only the translated text, nothing else: {$text}"
                    // 'content' => "Translate '{$text}' to {$languageName}"
                ]
            ],
            "temperature" => 0
        ];

        $ch = curl_init($this->url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        $response = curl_exec($ch);
        Log::channel('translate')->info($data);
        Log::channel('translate')->info($response);
        $response = json_decode($response, true);
        curl_close($ch);
        if($response['choices'][0]['message']['content']){
            return [
                "status" => "success",
                "content" => $response['choices'][0]['message']['content']
            ];
        }
        return [
            "status" => "failed",
            "content" => $response
        ];
    }
    
}