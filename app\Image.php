<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\User;

class Image extends Model
{
    //
    protected $fillable = [
    	'path','title','description','parent_id','object_type','created_by'
        ,'created_at', 'updated_at', 'enable', 'style', 'index'
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model){
            $model->id = Str::uuid();
        });
        static::deleted(function ($model) {
            \App\Helpers\S3Utils::deleteFileFromS3($model->path);
        });
    }

    public $incrementing = false;
    protected $primaryKey = 'id';

    protected $table = 'images';
    // protected $hidden = ['created_at', 'updated_at', 'pivot'];
    // public function user()
    // {
    //     return $this->hasMany(User::class);
    // }

}
