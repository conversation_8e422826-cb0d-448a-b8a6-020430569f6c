<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Services\GeneralService;
use App\Helpers\Helper;

class CustomController extends Controller
{
    public function custom_1() {
        $service = new GeneralService;
        return $service->checkPhpIniSettings();
    }
    public function custom_2() {
        Helper::publishMessage();
        return response()->json(['status' => 'Message published']);
    }
}