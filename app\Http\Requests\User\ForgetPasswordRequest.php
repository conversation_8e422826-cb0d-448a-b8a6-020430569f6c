<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class ForgetPasswordRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'email' => 'required_without:phone|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u',
            'phone' => 'required_without:email|digits_between:10,11',
        ];
    }
    // public function messages()
    // {
    //     return [
    //     'email.required' => 'User_001_E_013',
    //     'email.exists'  => 'User_003_E_014',
    //     'email.regex'   => 'User_002_E_015',
    //     'email.max'     => 'User_004_E_016',
    //     ];
    // }
}
