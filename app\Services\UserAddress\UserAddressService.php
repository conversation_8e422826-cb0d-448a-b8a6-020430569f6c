<?php
namespace App\Services\UserAddress;

use App\Product;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use App\Services\GeneralService;
use App\Services\Token\TokenService;
use App\Services\Notification\NotificationService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Shop;
use App\Image;
use App\UserAddress;
use App\KnbSupport;
use App\Services\Delivery\DeliveryService;

class UserAddressService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

     // ------------process my list addresses-----------------
    public function myAddresses()
    {
        $result = UserAddress::where('user_id',$this->_userId)
            ->with('users','provinces','districts','wards', 'images')->orderBy('created_at','desc')
            ->get();
 
         if(!$result)
         {
             return false;
         }
 
         return $result;
    }

    //------process detail Shop----------
    public function detail($id="aaa")
    {
        $result = UserAddress::where('id',$id)
            ->with('users','provinces','districts','wards', 'images')
            ->first();

        if(!$result)
        {
            return false;
        }
        return $result;
    }

    //-------process create user address--------
    public function create(array $data)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $data['user_id'] = $user->id;
        }

        $result = UserAddress::create($data);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.create'),
        //                 $result->id,
        //                 config('constants.object_type.data'),
        //                 $result,
        //                 json_encode($data));

        return $result;
    }

    //-------process update data--------
    public function update(array $data)
    {
        $result = UserAddress::find($data['id']);

        if(!$result || ($result->user_id != $this->_userId))
        {
            return false;
        }
        if(isset($data['image_delete'])){
            foreach($data['image_delete'] as $key=>$value){
                if(isset($value['id'])){
                    $image = Image::find($value['id']);
                    if($image){
                        $image->delete();
                    }
                }
            }
        }
        if(isset($data['images'])){
            foreach($data['images'] as $key=>$value){
                if(isset($value['id'])){
                    $image = Image::find($value['id']);
                    if($image){
                        $image->update([
                            'title' => $value['title'] ?? $image->title,
                            'description' => $value['description'] ?? $image->description,
                            'parent_id' => $data['id'],
                            'style' => $value['style'] ?? $image->style,
                            'index' => $value['index'] ?? $image->index,
                        ]);
                    }
                }
            }
        }
        $result->update($data);

        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.data'),
        //                 $result,
        //                 json_encode($data));

        return $result;

    }

    //-----process delete user address ----------
    public function delete(array $data)
    {
        $result = UserAddress::find($data['id']);

        if(!$result || ($result->user_id != $this->_userId))
        {
            return false;
        }

        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.data'),
        //                 $result,
        //                 json_encode($data));

        return true;
    }

    //-----process set default user address ----------
    public function setDefault(array $data)
    {
        $result = UserAddress::find($data['id']);

        if(!$result || ($result->user_id != $this->_userId))
        {
            return false;
        }
        $currentDefault = UserAddress::where('user_id', $this->_userId)
            ->where('is_default', true)->first();
        if($currentDefault){
            if($currentDefault['id'] == $data['id']){
                return true;
            }
            $currentDefault->update(['is_default' => false]);
        }
        
        $result->update(['is_default' => true]);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.data'),
        //                 $result,
        //                 json_encode($data));

        return true;
    }
}
