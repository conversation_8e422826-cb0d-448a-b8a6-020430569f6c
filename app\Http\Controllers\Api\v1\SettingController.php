<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Setting\SettingRequest;
use App\Http\Requests\Setting\SettingUpdateRequest;
use App\Http\Requests\Setting\SettingCheckIdRequest;
use App\Services\Setting\SettingService;

class SettingController extends Controller
{ 
    //------------ insert setting----------------
    public function insert(SettingRequest $request, SettingService $service)
    {
        $data = (array)$request->only([
            'key',
            'value',
            'description',
            'reference_value',
            'object_type',
            'show_on_site'
        ]);

        $result = $service->add($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'settingId' => $result->id,
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-----------list setting------------------
    public function list(SettingService $service)
    {
        $result = $service->list();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }


    //-------detail setting--------------------
    public function detail($id = null, SettingService $service)
    {
        $result = $service->detail($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Setting_003_E_006'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-----------update setting----------------
    public function update(SettingUpdateRequest $request, SettingService $service)
    {
        $data = (array)$request->only([
            'id',
            'key',
            'value',
            'description',
            'reference_value',
            'object_type',
            'show_on_site'
        ]);

        $result = $service->update($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Setting_003_E_006'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-----------------delete setting----------------
    public function delete(SettingCheckIdRequest $request, SettingService $service)
    {
        $data = (array)$request->only([
            'id'
        ]);

        $result = $service->delete($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Setting_003_E_006'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
