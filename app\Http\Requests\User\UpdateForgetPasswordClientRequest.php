<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UpdateForgetPasswordClientRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'phone'   => 'required_without:email|digits_between:10,11',
            'email'   => 'required_without:phone|email:rfc,dns',
            'otp'   => 'required|min:6',
            'password' => 'required|min:6|max:255',//|regex:/^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};:\\|,.<>\/?]).{6,20}$/',
            'otp' => 'required|min:6',
            'agent' => 'required|in:agent,phone,zalo,email',
            // 'key' => 'bail|required'
        ];
    }
    public function messages()
    {

        return [

    //         'id.required' => 'User_001_E_030',
    //         'id.uuid' => 'User_002_E_031',
    //         'id.exists' => 'User_003_E_032',

    //         'password.required' => 'User_001_E_006',
    //         'password.confirmed' => 'User_010_E_007',
    //         'password.min' => 'User_004_E_008',

    //         'key.required' => 'User_001_E_035',

    //         'password.max'      => 'User_004_E_065',
    //         'password.regex'      => 'User_002_E_066'
        ];
    }
}
