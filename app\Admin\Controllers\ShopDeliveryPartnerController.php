<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\DeliveryPartnerDisable;
use App\Admin\Actions\ShopDeliveryPartnerDisable;
use App\DeliveryPartner;
use App\Shop;
use App\ShopDeliveryPartner;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use Encore\Admin\Show;
use Encore\Admin\Widgets\Table;

class ShopDeliveryPartnerController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Shop Delivery Partner';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */

    protected function grid(){
        // Create a new grid instance for ShopDeliveryPartner model
        $grid = new Grid(new Shop());

        $grid->model()->whereHas('deliveryPartner');

        // Add filters
        $grid->filter(function($filter){
            $filter->disableIdFilter();
            $filter->like('name', __('admin.shop.name'));
        });
        // Add expandable column to show delivery partners for each shop
        $grid->column('name', __('admin.shop.name'))->expand(function($shop) {
            $grid = new Grid(new ShopDeliveryPartner());

            $grid->model()->whereIn('id', $shop->deliveryPartner->pluck('pivot.id'));

            $grid->column('id', __('Id'));
            $grid->column('deliveryPartner.name', __('admin.delivery_partner.name'));
            $grid->column('is_enabled', __('admin.enable'))
                ->action(new ShopDeliveryPartnerDisable);

            $grid->disableCreateButton()
                 ->disableExport()
                 ->disableFilter()
                 ->disableRowSelector()
                 ->disablePagination()
                 ->disableActions();


            $grid->actions(function($actions) {
                $actions->disableEdit();
                $actions->disableView();
            });

            return $grid->render();
        });
        $grid->column('phone', __('phone'));
        $grid->column('address', __('admin.shop.address'))->editable();
        $grid->column('owner', __('admin.user.title'))->display(function ($owner) {return $owner['name'] ?? "";});
        $grid->column('agent', __('admin.agent.title'))->display(function ($agent) {return $agent['name'] ?? "";});
        $grid->column('slug', __('Slug'))->link(function () {
            return "shops/".$this->id;
        });


        $grid->disableActions();

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed   $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(DeliveryPartner::findOrFail($id));

        return $show;
    }

    protected function form()
    {
        $form = new Form(new DeliveryPartner());
        return $form;
    }



}
