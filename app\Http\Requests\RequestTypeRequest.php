<?php

namespace App\Http\Requests;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class RequestTypeRequest extends BaseRequest
{

    public function rules()
    {
        return [

            'name'=>'required|unique:request_types,name',
            'title' =>'required',

        ];

    }
    public function messages()
    {

        return [
            'name.required' => 'Request_Type_001_E_001',
            'name.unique' => 'Request_Type_005_E_003',

            'title.required' => 'Request_Type_001_E_002',





        ];
    }
}
