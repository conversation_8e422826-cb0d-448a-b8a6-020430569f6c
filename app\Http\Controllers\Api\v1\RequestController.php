<?php

namespace App\Http\Controllers\Api\v1;

use App\Services\RequestService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class RequestController extends Controller
{
    private $requestService;

    public function __construct(RequestService $requestService)
    {
        $this->requestService = $requestService;
    }

    public function reportProduct(Request $request)
    {
        $userId = auth()->user()->id;
        $productId = $request->input('product_id');
        $reason = $request->input('reason');

        $result = $this->requestService->createRequest($userId, 'report_product', $productId, $reason);

        if (!$result) {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'Failed to submit report'
                ]
            ];
        } else {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'message' => 'Report submitted successfully'
                ]
            ];
        }

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function requestAccountDeletion()
    {
        $userId = auth()->user()->id;
        $result = $this->requestService->createRequest($userId, 'delete_account');

        if (!$result) {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'Failed to schedule account deletion'
                ]
            ];
        } else {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'message' => 'Account deletion scheduled in 30 days'
                ]
            ];
        }

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // Thêm các phương thức khác cho report shop, report user, etc.
}