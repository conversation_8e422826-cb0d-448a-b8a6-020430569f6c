<?php

namespace App;

use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Support\Str;
use App\Category;
use App\Product;
use App\Order;

class OrderItem extends Pivot
{
    protected $fillable = [
        'quantity',
        'price',
        'price_off',
        'price_total',
        'notes',
        'order_id',
        'product_id',
        'commission_percent',
    ];
    protected $table = 'order_items';
    
    // No need for primaryKey with Pivot models
    // protected $primaryKey = ['order_id', 'product_id'];
    
    public $timestamps = false;
    public $incrementing = false;
    
    protected $casts = [
        'price' => 'float',
        'price_off' => 'float',
        'price_total' => 'float',
        'quantity' => 'float',
        'commission_percent' => 'float',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }    
    
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
