<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateChannelConnectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Bảng liên kết kênh với thành viên
        Schema::create('channel_connects', function (Blueprint $table) {
            $table->uuid('channel_id');
            $table->uuid('member_id');
            $table->tinyInteger('member_type')->comment('1 = User, 2 = Shop');
            $table->timestamps();

            $table->primary(['channel_id', 'member_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('channel_connects');
    }
}
