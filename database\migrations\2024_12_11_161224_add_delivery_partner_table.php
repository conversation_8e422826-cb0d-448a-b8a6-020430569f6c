<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeliveryPartnerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('delivery_partners', function (Blueprint $table) {
            $table->uuid('id')->primary(); // UUID làm khóa chính
            $table->string('name'); // Tên đối tác giao hàng
            $table->string('contact_number')->nullable(); // Số liên lạc
            $table->string('email')->unique()->nullable(); // Email liên hệ
            $table->string('address')->nullable(); // Địa chỉ
            $table->string('api_key')->nullable(); // API key để tích hợp
            $table->string('api_secret')->nullable(); // API secret (mã bí mật)
            $table->boolean('is_active')->default(true); // Trạng thái hoạt động
            $table->text('notes')->nullable(); // <PERSON>hi chú bổ sung
            $table->timestamps(); // created_at và updated_at
            $table->softDeletes(); // deleted_at cho xóa mềm
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('delivery_partners');
    }
}
