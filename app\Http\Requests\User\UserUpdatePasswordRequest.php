<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UserUpdatePasswordRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'password' => [
                'required',
                'string',
                'regex:/^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};:\\|,.<>\/?\-]).{6,20}$/'
            ],
        ];
    }


    // public function messages()
    // {

    //     return [

    //         'current_password.required' => 'User_001_E_015',
    //         'current_password.current_password' => 'User_002_E_016',

    //         'password.required' => 'User_001_E_005',
    //         'password.min' => 'User_004_E_007',
    //         'password.max'      => 'User_004_E_065',
    //         'password.regex'      => 'User_002_E_066'

    //     ];
    // }
}
