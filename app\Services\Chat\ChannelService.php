<?php

namespace App\Services\Chat;

use App\Channel;
use App\ChannelConnect;
use App\Interaction;
use App\Shop;
use App\User;
use App\Message;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ChannelService
{
    private $_memberId;
    private $_memberType;

    private $connection;


    public function __construct($_memberId, $_memberType = null)
    {
        $this->_memberId = $_memberId;
        $this->_memberType = $_memberType;
        $this->connection = new ChannelConnectionService($_memberId, $_memberType);
    }

    public function create(array $members, string $name = null, $type = 1)
    {
        $channel = Channel::create([
            'name' => $name,
            'type' => $type ?? 1
        ]);
        if($channel){
            $channelID = $channel->id->toString();
            $this->connection->create($channelID);

            foreach ($members as $member) {
                $this->connection->setMember($member['id'], $member['type'] ?? 'user');
                $this->connection->create($channelID);
            }
        }

        return $channel;
    }

    public function list($limit = 2, $offset = 0)
    {
        $memberId = $this->_memberId;
        // Lấy danh sách các channel mà member đã tham gia
        $modelChannels = new Channel();
        $modelChannels->setConnection('pgsqlReplica');
        $results = $modelChannels->whereHas('members', function ($query) use ($memberId) {
            $query->where('member_id', $memberId); // Chỉ lấy các kênh mà member đã tham gia
        })
            ->with([
                'members' => function ($query) use ($memberId) {
                    $query->where('member_id', '!=', $memberId) // Lấy các thành viên khác trong kênh
                    ->with('member'); // Giả sử có quan hệ với bảng `member` qua `morphTo`
                },
                'latest_message'
            ])
            ->get()
            ->sortByDesc(function ($channel) {
                return $channel->latest_message->created_at ?? null;
            })
            ->slice($offset, $limit)
            ->map(function ($channel) use ($memberId) {
                $channel->viewed = !Interaction::on("pgsqlReplica")
                    ->where('object_id_a', $channel->id)
                    ->where('object_id_b', $memberId)
                    ->where('interaction_type', config('constants.interaction.unread'))
                    ->exists();
                return $channel;
            })
            ->values()->all();

        return $results;
    }


    function getListChannels() {
        $memberId = $this->_memberId;
        // Lấy danh sách các channel mà member đã tham gia
        $modelChannels = new Channel();
        $modelChannels->setConnection('pgsqlReplica');
        $results = $modelChannels->whereHas('members', function ($query) use ($memberId) {
            $query->where('member_id', $memberId);
        })
        ->with([
            'members' => function ($query) use ($memberId) {
                $query->where('member_id', '!=', $memberId) // Lấy tất cả các thành viên khác trong channel
                ->with('member');// Giả sử đã có quan hệ với bảng member qua morphTo'
            },
            'latest_message'
        ])
        ->get()
        ->sortByDesc(function ($channel) {
            return $channel->latest_message->created_at ?? null;
        })->toArray();

        foreach ($results as $key => &$channel) {
            foreach ($channel['members'] as &$member) {
                if (isset($member['member']) && $member['member']) {
                    $member = array_merge($member, $member['member']);
                    unset($member['member']);
                }
            }
        }
        return $results;
    }


    // Lấy thông tin các thành viên trong channel
    function getMemberInfoChannel($channelId) {
        $modelChannels = new Channel();
        $modelChannels->setConnection('pgsqlReplica');
        $memberId = $this->_memberId;

        // Fetch channel information along with members
        $results = $modelChannels->where('id', $channelId) // Get channel by ID
            ->with([
                'members' => function ($query) use ($memberId) {
                    $query->where('member_id', '!=', $memberId)
                    ->with('member');
                },
            ])
            ->first(); // Use first() to get a single channel with its members

        return $results->members->toArray() ?? [];
    }

    function getSenderInfo()
    {
        $modelChannels = new Channel();
        $modelChannels->setConnection('pgsqlReplica');
        $memberId = $this->_memberId;

        $results = $modelChannels->whereHas('channelConnects', function ($query) use ($memberId)  {
            $query->where('member_id', $memberId);
        })
            ->with([
                'channelConnects' => function ($query) use ($memberId) {
                    $query->where('member_id', '=', $memberId)
                        ->with('member');
                },

            ])
            ->first();
        return $results;
    }

    function getChannelWithReceiver($receiverId)
    {
        $channelIds = ChannelConnect::where('member_id', $this->_memberId)
            ->pluck('channel_id');

        $channel = ChannelConnect::where('member_id', $receiverId)
            ->whereIn('channel_id', $channelIds)
            ->first()->channel_id ?? null;
        if($channel){
            return Channel::select('id','name','type')->where('id', $channel)
            ->with([
                'members' => function ($query) {
                    $query->with('member');
                },
            ])
            ->first();
        }
        return $channel;
    }

    static function countChannelUnread($userId)
    {
        $interaction = Interaction::on('pgsqlReplica')->where([
            'object_type_a' => config('constants.object_type.channel'),
            'interaction_type' => config('constants.interaction.unread')
        ]);

        // Số lượng channel chưa đọc tin của user
        $userCount = (clone $interaction)->where('object_id_b', $userId)->count();

        $shopModel = Shop::on('pgsqlReplica');

        $userShop = (clone $shopModel)->where('user_id', $userId)->first();
        $userShopId = $userShop ? $userShop->id : null;

        $agentShopIds = (clone $shopModel)->where('agent_id', $userId)->get()->pluck('id')->toArray();

        // Số lượng channel chưa đọc tin của shop
        $shopCount = (clone $interaction)->where('object_id_b', $userShopId)->count();

        // Số lượng channel chưa đọc tin của agent
        $agentCount = (clone $interaction)->whereIn('object_id_b',$agentShopIds)->count();

        return [
            'user' => $userCount,
            'shop' => $shopCount,
            'agent'=> $agentCount
        ];
    }
}
