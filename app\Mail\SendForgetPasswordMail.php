<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Services\HistoryMail\HistoryMailService;

class SendForgetPasswordMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $name;
    protected $key;
    public function __construct($name,$key)
    {
        $this->name = $name;
        $this->key = $key;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $historyMail = null;
        try {
            $historyMail = HistoryMailService::insert([
                'title' => 'Thông báo phục hồi mật khẩu',
                'content' => json_encode(['name'=> $this->name,'key'=> $this->key]),
                'status' => config('constants.status_history_mail.success')
            ]);
            return $this->subject('Thông báo phục hồi mật khẩu')
            ->view('emails.forget_password_mail')
            ->with(['name'=> $this->name,'key'=> $this->key]);


        } catch (\Exception $e) {
            Log::info("Mail Error: ".$e);

            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

    }
}
