<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\Mqtt\MqttChatService;
use Illuminate\Support\Facades\Auth;

class MqttChatController extends Controller
{
    public function publish(Request $request, MqttChatService $service)
    {
        $data = $request->only([
            'topic',
            'message'
        ]);
        $userId = Auth::user()->id;
        $result = $service->publish($data, $userId);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
