<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Log;

class NotificationActionMark extends Notification implements ShouldQueue
{
    use Queueable;

    private $_user;
    private $_action_mark;
    public function __construct($user, $action_mark)
    {
        $this->_action_mark = $action_mark;
        $this->_user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $name = $this->_action_mark->name;
        $content = "";
        $title = "";
        if($this->_action_mark->mark > 0){
            $content = "Uy tín: +".$this->_action_mark->mark;
            $title = "Chúc mừng bạn";
        }
        else {
            $content = "Uy tín: ".$this->_action_mark->mark;
            $title = "Thông báo";
        }

        return [
            'title' => $title,
            'content' => $content,
            'image' => $path,
            'type' => 'user',
            'link' => null,
        ];
    }
}
