<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UserResetRequest extends BaseRequest
{
    
    public function rules()
    {
        return [
            'email' => 'required|exists:users,email'
        ];
    }
    
    // public function messages()
    // {
    //     return [
    //         'email.required' => 'User_001_E_002',
    //         'email.exists' => 'User_003_E_017'
    //     ];
    // }
}
