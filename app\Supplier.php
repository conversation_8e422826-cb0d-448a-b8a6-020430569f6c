<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Supplier extends Model
{
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $table = 'suppliers';
    protected $fillable = [
        'name', 'phone', 'address', 'email', 'shop_id'
        // Add other fillable columns
    ];
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });

    }
    protected $hidden = ['updated_at', 'created_at'];
    // Define any relationships or additional methods here
    public function quotations()
    {
        return $this->hasMany(Quotation::class);
    }
    public function shop()
    {
        return $this->belongsTo(Shop::class,'shop_id');
    
    }
}