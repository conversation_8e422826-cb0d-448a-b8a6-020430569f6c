<?php

namespace App;

use App\Services\Shop\ShopService;
use Elasticsearch\Endpoints\Ccr\Follow;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Str;
use Staudenmeir\EloquentEagerLimit\HasEagerLimit;
use App\Helpers\Helper;

class Shop extends Model
{
    use HasEagerLimit;    
    protected $fillable = [
        'name',
        'address',
        'slug',
        'user_id',
        'description',
        'business_type_id',
        'enable',
        'latitude',
        'longitude',
        'province_id',
        'district_id',
        'ward_id',
        'created_by',
        'order_type',
        'agent_id',
        'phone',
        'currency',
        'language',
        'banner_id',
        'logo_id',
        'qr_code',
        'views',
        'likes',
        'follows',
        'ratings',
        'branch_id',
        'settings',
        'status',
        'open_hours'
    ];
    protected $primaryKey = 'id';
    protected $table = 'shops';
    public $incrementing = false;    
    protected $casts = [
        'ratings' => 'float',
        'settings' => 'array'
    ];
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
            
            // Always generate a unique slug with 4 random characters
            $baseSlug = Str::slug($model->name);
            $randomSuffix = strtolower(Str::random(4)); // Generate 4 random characters
            $customSlug = $baseSlug . '-' . $randomSuffix;
            
            // Ensure the slug is unique by checking and regenerating if needed
            while (self::where('slug', $customSlug)->exists()) {
                $randomSuffix = strtolower(Str::random(4));
                $customSlug = $baseSlug . '-' . $randomSuffix;
            }
            
            $model->slug = $customSlug;
        });
        static::saving(function ($model){
            if($model->id){
                Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $model->id);
                Helper::flushCacheByTag(env('APP_ENV').":".'dashboard');
            }
        });
        static::deleting(function ($shop) {
            Helper::flushCacheByTag(env('APP_ENV').":".'dashboard');
            Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $shop->id);
            $shop->products()->delete();
        });
        static::addGlobalScope('logo', function (Builder $builder) {
            $builder->with('logo','banner','business_types');
        });
        //retrieved chậm hơn addGlobalScope. // Nạp quan hệ logo sau khi bản ghi được truy vấn
        // static::retrieved(function ($shop) {
        //     $shop->load('logo'); // Nạp quan hệ logo sau khi bản ghi được truy vấn
        // });
    }
    protected $hidden = ['updated_at', 'created_at','created_by'];

    public function banner()
    {
        return $this->belongsTo(Image::class,'banner_id')->select(['id','path','title','style','description'])->where('enable', true);
    }

    public function logo()
    {
        return $this->belongsTo(Image::class,'logo_id')->select(['id','path','title','style','description'])->where('enable', true);
    }

    public function created_by()
    {
        return $this->belongsTo(User::class,'created_by')->select(['id','name']);
    }

    public function owner()
    {
        return $this->belongsTo(User::class,'user_id')->select(['id','name','phone','email','profile_picture','address']);
    }
    public function agent()
    {
        return $this->belongsTo(User::class,'agent_id')->select(['id','name','phone','email','profile_picture','address']);
    }

    public function business_types()
    {
        return $this->belongsTo(BusinessType::class,'business_type_id')->select('id','name','description');
    }

    public function provinces()
    {
        return $this->belongsTo(Province::class,'province_id')->select('name','slug','longitude','latitude','profile_picture','bound','address');
    }


    public function districts()
    {
        return $this->belongsTo(District::class,'district_id')->select('name','slug','longitude','latitude','province_id','bound','address');
    }

    public function wards()
    {
        return $this->belongsTo(Ward::class,'ward_id')->select('name','slug','longitude','latitude','district_id','bound','address');
    }

    public function products(){
        return $this->hasMany(Product::class, 'shop_id')->with('categories')->limit(5)->offset(0);//->orderBy('name','asc');//;
    }
    public function all_products(){
        return $this->hasMany(Product::class, 'shop_id');//->orderBy('name','asc');//;
    }

    public function products_for_filter(){
        return $this->hasMany(Product::class, 'shop_id')->orderBy('price','desc')->take(5);
        return $this->hasMany(Product::class, 'shop_id')->orderBy('price','asc')->take(5);
    }

    public function categories(){
        return $this->hasMany(Category::class, 'shop_id')->with('translation');//->limit(500)->offset(0);
    }

    public function messages()
    {
        return $this->morphMany(Message::class, 'member');
    }

    public function channel()
    {
        return $this->morphMany(ChannelConnect::class, 'member');
    }

    public function deliveryPartner()
    {
        return $this->belongsToMany(DeliveryPartner::class, 'shop_delivery_partners')
            ->withPivot(['id', 'is_enabled']);
    }
}
