<?php

namespace App\Http\Requests\Category;

use App\Http\Requests\BaseRequest;

class CategoryUpdateRequest extends BaseRequest
{
    public function rules()
    {
        // if(isset($this->id))
        // {
        //     $id = 'required|uuid|exists:categories,id';
        //     $title = 'required|unique:categories,title,'.$this->id;
        // }
        // else
        // {
        //     $id = 'required|uuid|exists:categories,id';
        //     $title = 'required|unique:categories,title';
        // }
        // return [
        //     'id' => $id,
        //     'title' => $title,
        //     'category_id' => 'nullable|uuid|exists:categories,id',
        //     'featured_image' => 'nullable|base64size|base64image',

        // ];
        if(isset($this->id))
        {
            $id = 'required|uuid|exists:place_types,id';
            $name = 'required|max:255|unique:place_types,name,'.$this->id;
        }
        else{
            $id = 'required|uuid|exists:place_types,id';
            $name = 'required|max:255|unique:place_types,name';
        }
        return [
            'id' => $id,
            'title' => $name,
            'parent_id' => 'nullable|uuid|exists:categories,id'
        ];
    }

    public function messages()
    {
        return [
            // // 'title.required' => 'Category_001_E_001',
            // // // 'title.regex' => 'Category_002_E_002',
            // // 'title.unique' => 'Category_005_E_003',

            // // 'category_id.uuid' => 'Category_002_E_004',
            // // 'category_id.exists' => 'Category_003_E_005',

            // // 'featured_image.base64size' => 'Category_004_E_006',
            // // 'featured_image.base64image' => 'Category_002_E_007',

            // // 'id.required' => 'Category_001_E_012',
            // // 'id.uuid' => 'Category_002_E_013',
            // // 'id.exists' => 'Category_003_E_014',
            // 'title.required' => 'Category_001_E_001',
            // 'title.unique' => 'Category_005_E_002',

            // 'name.required' => 'Category_001_E_003',

            // 'id.uuid' => 'Category_002_E_004',
            // 'id.exists' => 'Category_003_E_005',
            // 'id.required' => 'Category_001_E_006',

            // 'mark.integer' => 'Category_002_E_007',
            // 'mark.min' => 'Category_004_E_008',
            // 'mark.max' => 'Category_004_E_009',

            // 'parent_id.uuid' => 'Category_002_E_010',
            // 'parent_id.exists' => 'Category_003_E_011',

            // 'title.max' => 'Category_004_E_012',
            // 'name.max'  => 'Category_004_E_013'
        ];
    }
}
