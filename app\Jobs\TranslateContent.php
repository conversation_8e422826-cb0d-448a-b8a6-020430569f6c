<?php

namespace App\Jobs;

use App\Message;
use App\Services\Translate\TranslateService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class TranslateContent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $languages;
    protected $content;
    protected $messageId;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($languages, $content, $messageId)
    {
        $this->languages = $languages;
        $this->content = $content;
        $this->messageId = $messageId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach ($this->languages as $language) {
            $translateService = new TranslateService();
            $translate = $translateService->translate($this->content['text'],$language);
            if($translate['status'] == 'success'){
                // echo ($translate);die;
                $this->content['translate'][$language] = $translate['content'];
            }
        }
        Message::where('id', $this->messageId)->update(['content' => json_encode($this->content)]);
    }
}
