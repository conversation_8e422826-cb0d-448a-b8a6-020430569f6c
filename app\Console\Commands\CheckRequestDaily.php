<?php

namespace App\Console\Commands;

use App\Jobs\DeleteAccount;
use App\Request;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckRequestDaily extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:checkRequestDaily';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check all request';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel("cronjob")->info("RUN CHECK REQUEST DAILY");
        $requests = Request::where('status', 'pending')
            ->where('scheduled_at', "<=", now())
            ->get();

        foreach ($requests as $request) {
            DeleteAccount::dispatch($request);
        }
    }
}
