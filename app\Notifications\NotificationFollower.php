<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use App\Services\Mqtt\MqttChatService;
use App\User;
use Illuminate\Support\Facades\Log;

class NotificationFollower extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    private $_user;
    private $_property;
    private $_path;
    private $_name;
    public function __construct($user, $property, $path, $name)
    {
        $this->_user = $user;
        $this->_property = $property;
        $this->_path = $path;
        $this->_name = $name;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    // public function toMail($notifiable)
    // {
    //     return (new MailMessage)
    //                 ->line('The introduction to the notification.')
    //                 ->action('Notification Action', url('/'))
    //                 ->line('Thank you for using our application!');
    // }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $mqtt = new MqttChatService();
        $result = User::find($this->_user->id);
        $_notify = $result->notifications();
        $countUnread = $_notify->whereNull('read_at')->count();
        $path = $this->_path;
        $content = "Vừa đăng bất động sản: ".$this->_property->name;
        $mqtt->publish(['topic' => $this->_user->id, 'message' => $countUnread]);
        return [
            'content' => $content,
            'image' => $path,
            'title'  => $this->_name,
            'link'  => $this->_property->slug,
            'type'  => 'property'
        ];
    }
}
