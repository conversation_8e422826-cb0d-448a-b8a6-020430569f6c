# Stage 1: Install dependencies
FROM php:7.4.2-apache-buster AS builder

WORKDIR /workspace/clomart-backend/

# Copy only necessary files for dependency installation
COPY composer.* /workspace/clomart-backend/
# Install Composer globally
RUN cd /tmp \
    && curl -sS https://getcomposer.org/installer | php \
    && mv composer.phar /usr/local/bin/composer

RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        # Dependencies for PHP extensions
        libfreetype6-dev \
        libjpeg62-turbo-dev \
        libmcrypt-dev \
        libpng-dev \
        libwebp-dev \
        libtiff5-dev \
        curl \
        libcurl4 \
        libcurl4-openssl-dev \
        zlib1g-dev \
        libicu-dev \
        libmagickwand-dev \
        unzip \
        libzip-dev \
        zip \
        nano \
        libpq-dev \
        ffmpeg \
    && docker-php-ext-configure gd --enable-gd --with-freetype --with-jpeg --with-webp \
    && docker-php-ext-configure intl \
    && docker-php-ext-configure zip \
    && docker-php-ext-install -j "$(nproc)" \
        gd \
        intl \
        mysqli \
        opcache \
        pdo_mysql \
        zip \
        exif \
    && pecl install imagick \
    && docker-php-ext-enable imagick \
    && rm -rf /var/lib/apt/lists/* \
    && chown -R www-data:www-data /workspace/clomart-backend

# Install ca-certificates for SSL support
RUN apt-get update \
    && apt-get install -y ca-certificates \
    && apt-get clean
RUN apt-get install -y libpq-dev && docker-php-ext-install pdo pdo_pgsql


# Move PHP configuration file
RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

# Copy additional PHP configuration
COPY ./php.ini $PHP_INI_DIR/conf.d/

RUN mkdir -p public/videos
RUN chmod 777 public/videos

ADD . /workspace/clomart-backend/
# Install project dependencies using Composer
# ...
RUN cd /workspace/clomart-backend/ && composer --version && ls /usr/local/bin
RUN composer clear-cache && composer install --no-scripts --no-interaction --prefer-dist --optimize-autoloader --verbose
# ...
# Set PHP configurations
RUN echo "upload_max_filesize = 100M" >> /usr/local/etc/php/conf.d/zzz-custom.ini
RUN echo "post_max_size = 100M" >> /usr/local/etc/php/conf.d/zzz-custom.ini


# Copy the rest of the application code
# RUN pwd && ls
# ADD vendor /workspace/clomart-backend/


# Stage 2: Build the application
FROM builder AS final

WORKDIR /workspace/clomart-backend/

# Copy the remaining source code
COPY --from=builder /workspace/clomart-backend/ /workspace/clomart-backend/
RUN mkdir -p /home/<USER>
COPY  --from=builder /workspace/clomart-backend/docker/vhost.conf /etc/apache2/sites-available/000-default.conf
COPY  --from=builder /workspace/clomart-backend/docker/start.sh /usr/local/bin/start

RUN a2enmod rewrite \
    && a2enmod headers \
    && service apache2 restart \
    && mkdir -m 775 -p /workspace/clomart-backend/storage/logs \
    && mkdir -m 775 -p /workspace/clomart-backend/storage/public \
    && chmod u+x /usr/local/bin/start

STOPSIGNAL SIGWINCH

RUN chown -R www-data:www-data /workspace/clomart-backend/storage/ && a2enmod rewrite && a2enmod headers && service apache2 restart

RUN chmod u+x /usr/local/bin/start

RUN mkdir -m 775 -p /workspace/clomart-backend/storage/logs && \
    mkdir -m 775 -p /workspace/clomart-backend/storage/public
#RUN chmod -R 775 /workspace/clomart-backend/storage/framework/cache/data

CMD ["/usr/local/bin/start"]
