<?php
namespace App\Services\Order;

use App\DeliveryPartner;
use App\Notifications\NotificationUser;
use App\POSConnection;
use App\Services\DeliveryPartner\DeliveryPartnerService;
use App\Services\Mqtt\MqttChatService;
use App\Services\POS\POSService;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use App\Services\GeneralService;
use App\Services\Token\TokenService;
use App\Services\Notification\NotificationService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Shop;
use App\Delivery;
use App\KnbSupport;
use App\Product;
use App\Order;
use App\Image;
use App\OrderItem;
use App\StockTracking;
use App\Services\Delivery\DeliveryService;
use Illuminate\Support\Facades\Log;
use DateTime;
use Illuminate\Support\Facades\DB;

class OrderService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

     //-------process create order--------
    public function create(array $order)
    {
        // Start transaction
        DB::beginTransaction();
        try {
            $user = User::find($this->_userId);
            if($user && !$order['customer_id'])
            {
                $order['customer_id'] = $user->id;
            }

            $totalAmount = $discountAmount = $grandTotal = 0;
            $orderID = Str::uuid();

            // $orderTmp = $order;
            $orderTmp['id'] = $orderID;
            $orderTmp['customer_id']        = $order['customer_id'] ?? null;
            $orderTmp['address']            = $order['address'] ?? null;
            $orderTmp['customer_name']      = $order['customer_name'] ?? null;
            $orderTmp['customer_phone']     = $order['customer_phone'] ?? null;
            $orderTmp['customer_latitude']  = $order['customer_latitude'] ?? null;
            $orderTmp['customer_longitude'] = $order['customer_longitude'] ?? null;
            $orderTmp['province_id']        = $order['province_id'] ?? null;
            $orderTmp['district_id']        = $order['district_id'] ?? null;
            $orderTmp['ward_id']            = $order['ward_id'] ?? null;
            $orderTmp['delivery_price']     = $order['delivery_price'] ?? null;
            $orderTmp['delivery_price_estimate']     = $order['delivery_price_estimate'] ?? null;
            $orderTmp['delivery_discount']  = isset($order['delivery_discount'])? abs($order['delivery_discount']) * -1 : 0;
            $orderTmp['delivery_type']      = $order['delivery_type'];
            $orderTmp['delivery_time']      = $order['delivery_time'] == "Invalid date" ? null : $order['delivery_time'];
            $orderTmp['delivery_partner']   = $order['delivery_partner'] ?? '';
            $orderTmp['delivery_distance']  = $order['delivery_distance'] ?? 0;
            $orderTmp['payment_method']     = $order['payment_method'];
            $orderTmp['notes']              = $order['notes'] ?? null;
            $orderTmp['total_amount']       = $totalAmount;
            $orderTmp['discount_amount']    = $discountAmount; ////always <= 0
            $orderTmp['grand_total']        = $totalAmount;            
            $orderTmp['delivery_partner_id']= $order['delivery_partner_id'] ?? null;
            $orderTmp['extra_data']         = isset($order['extra_data']) ? json_encode($order['extra_data']) : null;

            // Handle referral code
            $orderTmp['referral_code']      = $order['referral_code'] ?? null;
            if (isset($order['referral_code']) && !empty($order['referral_code'])) {
                $referrer = User::findByReferralCode($order['referral_code']);
                if ($referrer) {
                    $orderTmp['ref_id'] = $referrer->id;
                }
            }

            // Tìm shop dựa trên id của sản phẩm đầu tiên của mảng items (do các items cùng 1 shop)
            $shop = Shop::find(Product::find($order['items'][0]['product_id'])->shop_id);

            switch ($orderTmp['delivery_partner']) {
                case 'remagan':
                    $price = DeliveryService::checkPrice(['distance' => $order['delivery_distance']]);
                    $orderTmp['delivery_price'] = $price ?? $orderTmp['delivery_price'];
                    break;
            }

            // Create the order record first
            $orderTmp = Order::create($orderTmp);

            if(!$shop || !$shop->enable){
                return ['error'=> "Shop not found or Shop not enable"];
            }

            foreach($order['items'] as $item){
                $product = Product::find($item['product_id']);
                if(!$product || $item['quantity'] < 0){
                    return ['error' => 'Product not found or Error with quantity'];
                }

                if(!$product->enable){
                    return ['error' => 'Product not enable'];
                }

                if ($product->stock !== null) {
                    if ($product->stock < $item['quantity']) {
                        return ['error' => "Not enough stock for product: {$product->name}"];
                    }
                    $oldStock = $product->stock;
                    $product->stock -= $item['quantity'];
                    $product->save();

                    // Record stock change
                    StockTracking::recordStockChange(
                        $product->id,
                        $oldStock,
                        $product->stock,
                        'order_updated_new',
                        $orderID
                    );
                }

                $itemAmount =  $item['quantity'] * ($product->price);
                $totalAmount += $itemAmount;
                $discountAmount += $item['quantity'] * (($product->price_off > 0 && $product->price_off != NULL && $product->price_off < $product->price) ? ($product->price_off - $product->price) : 0);

                $usingPrice = ($product->price_off > 0 && $product->price_off < $product->price && $product->price_off != null) ? $product->price_off : $product->price;

                $itemTotal =  $item['quantity'] * $usingPrice;                
                $order_item = [
                    'order_id'           => $orderID,
                    'product_id'         => $item['product_id'],
                    'quantity'           => $item['quantity'],
                    'price'              => $product->price,
                    'price_off'          => $product->price_off,
                    'price_total'        => $itemTotal,
                    'notes'              => $item['notes'] ?? "",
                    'commission_percent' => $product->commission_percent ?? 0,
                ];

                OrderItem::create($order_item);
            };

            //final price
            $orderTmp['total_amount']     = $totalAmount;
            $orderTmp['discount_amount']  = $discountAmount;
            $orderTmp['grand_total']      = $totalAmount + $discountAmount + $orderTmp['delivery_price'] + $orderTmp['delivery_discount'];


            // $shop_product_id = Product::find($order['shop_id']);
            $shopField = preg_match('/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i', $order['shop_id']) ? 'id' : 'slug';
            $shop = Shop::where($shopField, $order['shop_id'])->first();
            if($shop){
                $orderTmp['shop_id'] = $shop->id;

            }else{
                return false;
            }

            //
            $orderTmp['delivery_discount'] = DeliveryService::checkDiscountBySettingShop($orderTmp['delivery_price'], $orderTmp, $shop);
            $orderTmp->update();

            $messToOwner = [
                'title' => "Bạn có đơn hàng mới",
                'body'  => $order['customer_name']." vừa đặt đơn hàng trị giá ".$orderTmp['grand_total']. ' đồng',
                // 'image' => $image,
                'url'   => $orderID,
                'target_url' => '/my-shop/orders/'. $orderID,
                'type'  => 'order_new',
            ];

            $messToAgent = [
                'title' => $shop->name." có đơn hàng mới",
                'body'  => $order['customer_name']." vừa đặt đơn hàng trị giá ".$orderTmp['grand_total']. ' đồng',
                // 'image' => $image,
                'url'   => $orderID,
                'target_url' => '/agent/shop/'. $shop->slug .'/orders/'. $orderID,
                'type'  => 'agent_order_new',            ];
            $shop_owner = $shop->user_id;
            $shop_agent = $shop->user_id != $shop->agent_id ? $shop->agent_id : null;


            # tự nhận đơn
            $tokenService = new TokenService();
            $tokens = $tokenService->listByUser($shop_owner, 2);
            $agentTokens = $shop_agent ? $tokenService->listByUser($shop_agent, 2) : [];
            $notiService = new NotificationService;
            if(count($tokens) > 0){
                $notiService->sendBatchNotification($tokens, $messToOwner);
            }            if(count($agentTokens) > 0){
                $notiService->sendBatchNotification($agentTokens, $messToAgent);
            }

            # nhận thông báo và lưu vào table notifications
            $shop_owner_obj = User::find($shop_owner);
            if($shop_owner_obj){
                $shop_owner_obj->notify(new NotificationUser($shop_owner, $messToOwner));
            }
            
            if($shop_agent) {
                $shop_agent_obj = User::find($shop_agent);
                if($shop_agent_obj){
                    $shop_agent_obj->notify(new NotificationUser($shop_agent, $messToAgent));
                }
            }            
            // Send notification to observation operators
            $settings = DB::table('settings')->where('key', 'observation_operator')->first();
            if ($settings && $settings->value && !empty($settings->value)) {
                $operatorId = $settings->value;
                {
                    $operatorTokens = $tokenService->listByUser($operatorId, 2);
                    if (count($operatorTokens) > 0) {
                        $notiService->sendBatchNotification($operatorTokens, $messToAgent);
                    }
                }
            }


            # send 1 bản backup sang hệ thống KNB
            if(env('APP_ENV') == 'production' && $orderTmp['customer_name'] != 'TESTER01')
            {
                $shop = Shop::find($orderTmp['shop_id']);
                $agent = User::find($shop['agent_id']);
                $agentinF = '';
                if($agent) $agentinF = $agent->name. " - ".$agent->phone;
                $time = date("d/m/Y H:i");
                $time2 = date("Y-m-d");
                $CaseKnbArr = [
                    'name' => $orderTmp['short_code'],
                    'number' => $orderTmp['short_code'],
                    'date_ordered' => $time2,
                    'amount' => $orderTmp['grand_total'],
                    'description' => $time." \n".$orderTmp['notes']
                    . " \n Customer name: ".$orderTmp['customer_name']
                    . " \n Customer phone: ".$orderTmp['customer_phone']
                    . " \n Address: ".$orderTmp['address']
                    . " \n Price: ".$orderTmp['grand_total']
                    . " \n Shop: ".$shop->name
                    . " \n Shop phone: ".$shop->phone
                    . " \n Shop address: ".$shop->address
                    . " \n Agent: ".$agentinF
                    . " \n URL: https://api.remagan.com/admin/orders/". $orderID
                ];
                $CaseKnb = KnbSupport::create($CaseKnbArr);
            }

            // Commit transaction
            DB::commit();
            $result = $this->detail($orderID);

            $posConnects = POSConnection::on('pgsqlReplica')->where([
                "shop_id" => $shop->id,
                "enabled" => true])->get();
            $pos = new POSService();
            $posResult = [];
            $order = Order::find($orderID);
            foreach ($posConnects as $posConnect) {
                $pos = $pos->setPOS($posConnect->pos_type, $shop->id);
                $data = [
                    'order_id' => $orderID,
                    'voucher'  => false
                ];
                $resultPOS = $pos->createOrder($data, $this);
                switch ($posConnect->pos_type){
                    case "kiot_viet": $type = "Kiot Việt"; break;
                    default: $type = "bên thứ 3";
                }
                if(!$resultPOS){
                    $posResult[] = "Tạo đơn hàng thất bại với hệ thống " . $type;
                }
                else {
                    $posResult[] = "Tạo đơn hàng thành công với hệ thống " . $type;
                }
            }
            $order->update(['pos_note' => $posResult]);

            $general = new GeneralService($this->_userId);
            $general->addLog(
                config('constants.log_action.create'),
                $result->id,
                config('constants.object_type.order'),
                $result,
                json_encode($order)
            );

            return $result;
        } catch (\Exception $e) {
            // Rollback transaction on error
            DB::rollBack();
            return ['error' => 'Transaction failed: ' . $e->getMessage()];
        }
    }

    //-------process listing Order--------------------
    public function list($offset, $limit)
    {
        $result = Order::with('provinces','districts','wards','items', 'shops','delivery','images', 'deliveryPartner')
                ->orderBy('created_at','asc')
                ->offset($offset)->limit($limit)
                ->get();

        if(!$result)
        {
            return false;
        }
        foreach ($result as $key => $order) {
            $result[$key]['price'] = $order['grand_total'];
        }
        return $result;
    }

    //------process detail Order----------
    public function detail($id)
    {
        $result = Order::where('id',$id)
            ->with('items', 'shops', 'customer','delivery','items','images', 'deliveryPartner')
            ->first();

        if(!$result)
        {
            return false;
        }
        $result['price'] = $result['grand_total'];
        $result['extra_data'] = $result['extra_data'] ? json_decode($result['extra_data']) : null;
        return $result;
    }
    //------process detail Order----------
     public function myOrder()
     {
        $user = User::find($this->_userId);
        $result = Order::where('user_id',$this->_userId)
            ->with('provinces','districts','wards','items', 'shops', 'customer')
            ->first();

        if(!$result)
        {
            return false;
        }
        $result['price'] = $result['grand_total'];
        $result['extra_data'] = $result['extra_data'] ? json_decode($result['extra_data']) : null;
         return $result;
     }
    //-------process update order--------
    public function update(array $order)
    {       
            # Origin order data
            $checkOrder = Order::where('id', $order['id'])->with('delivery', 'items','deliveryPartner', 'shops')->first();

            if(!$checkOrder)
            {
                return ['error' => 'Order not found'];
            }

            if ($checkOrder->status <= config('constants.order.status.ready_to_deliver')) {

                // Restore old stock and record changes: trả lại tồn kho cho sp trước khi thay đổi đơn.
                $productIds = collect($checkOrder->items)->pluck('product_id')->toArray();

                Product::findMany($productIds)->each(function ($product) use ($checkOrder, $order) {

                    $oldItem = collect($checkOrder->items)->firstWhere('product_id', $product->id);

                    if ($product->stock !== null) {
                        $oldStock = $product->stock;
                        $product->stock += $oldItem['quantity'];
                        $product->save();

                        StockTracking::create([
                            'product_id' => $product->id,
                            'old_stock' => $oldStock,
                            'new_stock' => $product->stock,
                            'change' => $oldItem['quantity'],
                            'order_id' => $order['id'],
                            'reason' => 'order_updated_restore'
                        ]);
                    }

                });

                OrderItem::where('order_id', $order['id'])->delete();
                $totalAmount = $discountAmount = $grandTotal = 0;

                foreach ($order['items'] as $item) {

                    $product = Product::find($item['product_id']);
                    if (!$product || $item['quantity'] < 0) {
                        return ['error' => 'Product not found or Error with quantity'];
                    }

                    if ($product->stock !== null && $order['status'] <= config('constants.order.status.in_process')) {
                        if ($product->stock < $item['quantity']) {
                            return ['error' => "Not enough stock for product: {$product->name}"];
                        }
                        $oldStock = $product->stock;
                        $product->stock -= $item['quantity'];
                        $product->save();

                        // Record stock change
                        StockTracking::recordStockChange(
                            $product->id,
                            $oldStock,
                            $product->stock,
                            'order_updated_new',
                            $checkOrder->id,
                        );
                    }

                    # if shop change the selling price, use this price instead of current price or price off.
                    $order_item = [];
                    if (isset($item['price_temp']) && $item['price_temp'] != null && (floatval($item['price_temp']) != $product->price || floatval($item['price_temp']) != $product->price_off)) {
                        $itemAmount = $item['quantity'] * ($product->price);
                        $discountAmount += $item['quantity'] * (($item['price_temp'] > 0 && $item['price_temp'] != NULL && $item['price_temp'] < $product->price) ? ($item['price_temp'] - $product->price) : 0);
                        $itemTotal = $item['quantity'] * $item['price_temp'];
                        $order_item = [
                            'order_id'           => $checkOrder->id,
                            'product_id'         => $item['product_id'],
                            'quantity'           => $item['quantity'],
                            'price'              => floatval($item['price_temp']),
                            'price_off'          => floatval($item['price_temp']),
                            'price_total'        => $itemTotal,
                            'notes'              => $item['notes'] ?? "",
                            'commission_percent' => $product->commission_percent ?? 0,
                        ];
                    } else {
                        $itemAmount = $item['quantity'] * ($product->price);
                        $discountAmount += $item['quantity'] * (($product->price_off > 0 && $product->price_off != NULL && $product->price_off < $product->price) ? ($product->price_off - $product->price) : 0);
                        $usingPrice = ($product->price_off > 0 && $product->price_off < $product->price && $product->price_off != null) ? $product->price_off : $product->price;
                        $itemTotal = $item['quantity'] * $usingPrice;                        
                        $order_item = [
                            'order_id'           => $checkOrder->id,
                            'product_id'         => $item['product_id'],
                            'quantity'           => $item['quantity'],
                            'price'              => $product->price,
                            'price_off'          => $product->price_off,
                            'price_total'        => $itemTotal,
                            'notes'              => $item['notes'] ?? "",
                            'commission_percent' => $product->commission_percent ?? 0,
                        ];
                    }
                    $totalAmount += $itemAmount;
                    OrderItem::create($order_item);
                };

                //final price
                $checkOrder->total_amount = $totalAmount;
                
                // chủ shop có thể thay đổi giảm giá cho đơn hàng.
                if(isset($order['discount_amount']) && $order['discount_amount'] != null && $order['discount_amount'] < 0 && ($order['discount_amount'] + $totalAmount >= 0)){
                    $discountAmount = $order['discount_amount'];
                }
                $checkOrder->discount_amount = $discountAmount;
                $checkOrder->address = $order['address'] ?? $checkOrder->address;
                $checkOrder->notes = $order['notes'] ?? $checkOrder->notes;
                $checkOrder->delivery_price = $order['delivery_price'] ?? $checkOrder->delivery_price;
                $checkOrder->delivery_discount = DeliveryService::checkDiscountBySettingShop($checkOrder->delivery_price, $checkOrder, $checkOrder['shops']);
                
                $checkOrder->grand_total = $totalAmount + $discountAmount + $checkOrder->delivery_price + $checkOrder->delivery_discount;
                // Update order in POS Partner
                $posConnects = POSConnection::on('pgsqlReplica')->where([
                    "shop_id" => $checkOrder->shop_id,
                    "enabled" => true])->get();

                /* POS CONNECT KIOT-VIET
                $pos = new POSService();
                $posResult = [];
                foreach ($posConnects as $posConnect) {
                    $pos = $pos->setPOS($posConnect->pos_type, $checkOrder->shop_id);
                    $data = [
                        'order_id' => $checkOrder->id,
                        'voucher' => false
                    ];
                    $resultPOS = $pos->update($data, $this);
                    switch ($posConnect->pos_type) {
                        case "kiot_viet":
                            $type = "Kiot Việt";
                            break;
                        default:
                            $type = "bên thứ 3";
                    }
                    if (!$resultPOS) {
                        $posResult[] = "Cập nhật đơn hàng thất bại với hệ thống " . $type;
                    } else {
                        $posResult[] = "Cập nhật đơn hàng thành công với hệ thống " . $type;
                    }
                }
                $checkOrder->pos_note = $posResult;
                */
            }

            $checkOrder->update();
            //Check delivery before updating the order status.
            if(isset($order['status']) && $checkOrder->status != $order['status']){
                // Verify the target order status before updating.
                if($order['status'] == config('constants.order.status.cancel') && $checkOrder->delivery_id && $checkOrder->delivery_partner_id) {
                    $deliveryPartner = DeliveryPartner::find($checkOrder->delivery_partner_id);
                    $serviceDelivery = (new DeliveryPartnerService())->setDeliveryPartner(strtolower($deliveryPartner->name), $checkOrder->shop_id);
                    $checkDelivery = $serviceDelivery->validateDeliveryStatus($order);
                    // Get detail delivery
                    if($checkDelivery) $checkOrder->status = $order['status'];
                    else{
                        // Set order status to null to prevent sending notifications
            //                    $order['status'] = null;
                        return ['error' => 'Cannot cancel the order status because the delivery has already been processed.'];
                    }
                }
                else $checkOrder->status = $order['status'];
            }else{
                if ($checkOrder->status >= config('constants.order.status.done')) {
                    return ['error' => 'Cannot update the order because it has already been done or close.'];
                }
            }

            $checkOrder->update();
            // if($checkOrder->status != config('constants.order.status.new')){

            // }

        //        foreach ($checkOrder->items as $oldItem) {
        //            $product = Product::find($oldItem->product_id);
        //            if ($product && $product->stock !== null) {
        //                $oldStock = $product->stock;
        //                $product->stock += $oldItem->quantity;
        //                $product->save();
        //
        //                StockTracking::create([
        //                    'product_id' => $product->id,
        //                    'old_stock' => $oldStock,
        //                    'new_stock' => $product->stock,
        //                    'change' => $oldItem->quantity,
        //                    'order_id' => $order['id'],
        //                    'type' => 'order_updated_restore'
        //                ]);
        //            }
        //        }

        // NOTIFY to shop owner, agent
         $originalStatus = $checkOrder->status;
        // Check if the order status is going to the next status
        $processFlag = false;
        if (isset($order['status']) && $originalStatus !== $order['status']) {
            // Only process notifications if status has changed
            $processFlag = true;
        }
        if(isset($order['shop_id'])){
            $shop = Shop::where('id', $order['shop_id'])->first();
            // Get the original status from the checkOrder object
           
            if($shop && $processFlag){//check shop exists
                $messText = '';
                switch (intval($order['status'])) {
                    case config('constants.order.status.new'):
                        $messText = " đã được tạo";
                        break;
                    case config('constants.order.status.in_process'):
                        $messText = " đã được xác nhận";
                        break;
                    case config('constants.order.status.ready_to_deliver'):
                        $messText = " đang được giao";
                        break;
                    case config('constants.order.status.done'):
                        $messText = " đã được giao thành công";
                        foreach ($order['items'] as $item) {
                            $product = Product::find($item['product_id']);
                            if ($product) {
                                // Increment the sold_count for the product
                                $product->sold_count += $item['quantity'];
                                $product->save();
                            }
                        }
                        break;
                    case config('constants.order.status.return'):
                        $messText = " bị yêu cầu trả hàng";
                        break;
                    case config('constants.order.status.cancel'):
                        $messText = " đã bị hủy";
                        break;
                    default:
                        $messText = " đã được cập nhật";
                        break;

                }
                // NOTIFY to owner | agent
                if(intval($order['status']) > config('constants.order.status.in_process')){
                    $messToOwner = [
                        'title' => "Đơn hàng ".$messText,
                        'body'  => "Đơn hàng ".$order['short_code'].$messText,
                        // 'image' => $image,
                        'url'   => $checkOrder->id,
                        'target_url' => '/my-shop/orders/'. $checkOrder->id,
                        'type'  => 'order_new',
                    ];

                    $messToAgent = [
                        'title' => "Đơn hàng ".$messText,
                        'body'  => "Đơn hàng ".$order['short_code']." của shop: ". $shop->name.$messText,
                        // 'image' => $image,
                        'url'   => $checkOrder->id,
                        'target_url' => '/agent/shop/'. $shop->slug .'/orders/'. $checkOrder->id,
                        'type'  => 'agent_order_new',
                    ];
                    $shop_owner = $shop->user_id;
                    $shop_agent = $shop->user_id != $shop->agent_id ? $shop->agent_id : "";

                    # tự nhận đơn
                    $tokenService = new TokenService();
                    $tokens = $tokenService->listByUser($shop_owner, 2);
                    $agentTokens = $tokenService->listByUser($shop_agent, 2);
                    $notiService = new NotificationService;
                    if(count($tokens) > 0){
                        $notiService->sendBatchNotification($tokens, $messToOwner);
                    }
                    if(count($agentTokens) > 0){
                        $notiService->sendBatchNotification($agentTokens, $messToAgent);
                    }

                    # nhận thông báo và lưu vào table notifications
                    $shop_owner_obj = User::find($shop_owner);
                    if($shop_owner_obj){
                        $shop_owner_obj->notify(new NotificationUser($shop_owner, $messToOwner));
                    }
                    
                    $shop_agent_obj = User::find($shop_agent);
                    if($shop_agent_obj){
                        $shop_agent_obj->notify(new NotificationUser($shop_agent, $messToAgent));
                    }

                }

            }//end check shop exists
        }

        $result = $this->detail($checkOrder->id);
        $mqtt = new MqttChatService();
        // NOTIFY to customer
        if(isset($order['customer_id']) && ($order['customer_id'])  && $processFlag)
        {
            $NotiOrderType = 'order_update';
            $NotifyArr = [];
            $NotifyArr[] = config('constants.order.status.in_process');
            $NotifyArr[] = config('constants.order.status.ready_to_deliver');
            $NotifyArr[] = config('constants.order.status.done');
            $NotifyArr[] = config('constants.order.status.cancel');
            if(in_array(intval($order['status']), $NotifyArr)){
                switch (intval($order['status'])) {
                    case config('constants.order.status.in_process'):
                        $NotiOrderType = $NotiOrderType . '_in_process';break;
                    case config('constants.order.status.ready_to_deliver'):
                        $NotiOrderType = $NotiOrderType . '_ready_to_deliver';break;
                    case config('constants.order.status.done'):
                        $NotiOrderType = $NotiOrderType . '_done';break;
                    case config('constants.order.status.cancel'):
                        $NotiOrderType = $NotiOrderType . '_cancel';break;
                    default:
                        break;
                }
                $data = [
                    'title' => "Đơn hàng tại ".$result->shops->name,
                    'body'  => 'Đơn hàng '. $order['short_code'] .$messText,
                    // 'image' => $image,
                    'url'   => $result->short_code,
                    'target_url' => '/my-orders/'. $result->short_code,
                    'type'  => $NotiOrderType,
                ];
                $tokenService = new TokenService();
                $tokens = $tokenService->listByUser($order['customer_id'], 2);
                $notiService = new NotificationService;
                if(count($tokens) > 0){
                    $notiService->sendBatchNotification($tokens, $data);
                }
                $mqtt->publish(['topic' => 'remagan_order/'. $checkOrder->id, 'message' => $data]);

                # nhận thông báo và lưu vào table notifications
                $customer_obj = User::find($order['customer_id']);
                if($customer_obj){
                    $customer_obj->notify(new NotificationUser($order['customer_id'], $data));
                }
            }

        }

        // excute images
        if(isset($order['image_delete'])){
            foreach($order['image_delete'] as $key=>$value){
                if(isset($value['id'])){
                    $image = Image::find($value['id']);
                    if($image){
                        $image->delete();
                    }
                }
            }
        }

        $general = new GeneralService($this->_userId);
        $general->addLog(
            config('constants.log_action.update'),
            $result->id,
            config('constants.object_type.order'),
            $result,
            json_encode($order)
        );

        return $result;

    }

    //-----process remove order----------
    public function remove(array $order)
    {
        $result = Order::where([
            ['id', $order['id']],
        ])->first();

        if(!$result)
        {
            return false;
        }

        $order['status'] = config('constant.order.status.cancel');
        $this->update($order);

        return true;
    }

    //-----process delete order----------
    public function delete(array $order)
    {
        $result = Order::find($order['id']);

        if(!$result)
        {
            return false;
        }

        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.order'),
        //                 $result,
        //                 json_encode($order));

        return true;
    }


    //------process getProductsByOrderId----------
    public function getProductsByOrderId($data)
    {
        $limit = $data['limit'] ? $data['limit'] : 20;
        $offset = $data['offset'] ? $data['offset'] : 0;
        $result = Product::with('categories')
            ->where('order_id', $data['order_id'])
            ->where('enable', true);

        if(isset($data['sortBy']) && !empty(isset($data['sortBy']))){
            switch(intval($data['sortBy'])){
                case 1:
                    $result->orderBy('price', 'asc');
                    break;
                case 2:
                    $result->orderBy('price', 'desc');
                    break;
                case 3:
                    $result->orderBy('created_at', 'desc');
                    break;
                case 4:
                    $result->orderBy('name', 'asc');
                    break;
                case 5:
                    $result->orderBy('name', 'desc');
                    break;
                default:
                    $result->orderBy('price', 'asc');
                    break;
            }
        }
        else {
            $result->orderBy('price', 'asc');
        }

        $count = $result->count();

        if(!$result)
        {
            return false;
        }

        return [
            'count'  => $count,
            'result' => $result->offset($offset)->limit($limit)->get()->toArray(),
        ];
    }

    //------process listOrderByShopId----------
    public function listOrderByShopId($data)
    {
        $result = Order::where('shop_id', $data['shop_id'])
            ->with('provinces','districts','wards', 'items', 'shops', 'customer','delivery','images', 'deliveryPartner');
        if(isset($data['status']) && !empty($data['status'])){
            $result->where('status', $data['status']);
        }
        if(isset($data['search_text']) && !empty($data['search_text'])){
            $result->where(function ($query) use($data){
                $query->where('customer_name', 'ILIKE', '%'.$data['search_text'].'%')
                ->orWhere('customer_phone', 'ILIKE', '%'.$data['search_text'].'%')
                ->orWhere('short_code', 'ILIKE', '%'.$data['search_text'].'%')
                ->orWhere('address', 'ILIKE', '%'.$data['search_text'].'%');
            });
        }
        if(isset($data['start_date']) && !empty($data['start_date'])){
            $result->whereDate('created_at', '>=', $data['start_date']);
        }
        if(isset($data['end_date']) && !empty($data['end_date'])){
            $result->whereDate('created_at', '<=', $data['end_date']);
        }
        // if(isset($data['short_code']) && !empty($data['short_code'])){
        //     $result->where('short_code', 'ILIKE', '%'.$data['short_code'].'%');
        // }

        if(!$result)
        {
            return false;
        }

        $offset = isset($data['offset']) && !empty($data['offset']) ? $data['offset'] : 0;
        $limit = isset($data['limit']) && !empty($data['limit']) ? $data['limit'] : 20;

        $count = $result->count();
        $result = $result->orderBy('created_at', 'desc')->offset($offset)->limit($limit)->get();
        foreach ($result as $key => $order) {
            $result[$key]['price'] = $order['grand_total'];
        }
        return [
            'count' => $count,
            'result' =>$result,
        ];
    }
    //------process listOrderByCustomerId----------
    public function listOrder($data)
    {
        $result = new Order;

        if(isset($data['customer_id']) && !empty($data['customer_id'])){
            $result = $result->where('customer_id', $data['customer_id']);
        };

        if(isset($data['shop_id']) && !empty($data['shop_id'])){
            $result = $result->where('shop_id', $data['shop_id']);
        }

        $result = $result->with('provinces','districts','wards', 'items', 'shops', 'customer','delivery','images', 'deliveryPartner');

        if(isset($data['status']) && !empty($data['status'])){
            $result = $result->where('status', $data['status']);
        }
        if(isset($data['search_text']) && !empty($data['search_text'])){
            $result = $result->where(function ($query) use($data){
                $query->where('customer_name', 'ILIKE', '%'.$data['search_text'].'%')
                ->orWhere('customer_phone', 'ILIKE', '%'.$data['search_text'].'%')
                ->orWhere('short_code', 'ILIKE', '%'.$data['search_text'].'%')
                ->orWhere('address', 'ILIKE', '%'.$data['search_text'].'%');
            });
        }
        if(isset($data['start_date']) && !empty($data['start_date'])){
            $result = $result->whereDate('created_at', '>=', $data['start_date']);
        }
        if(isset($data['end_date']) && !empty($data['end_date'])){
            $result = $result->whereDate('created_at', '<=', $data['end_date']);
        }
        // if(isset($data['short_code']) && !empty($data['short_code'])){
        //     $result->where('short_code', 'ILIKE', '%'.$data['short_code'].'%');
        // }

        if(!$result)
        {
            return false;
        }

        $offset = isset($data['offset']) && !empty($data['offset']) ? $data['offset'] : 0;
        $limit = isset($data['limit']) && !empty($data['limit']) ? $data['limit'] : 20;

        $count = $result->count();
        $result = $result->orderBy('created_at', 'desc')->offset($offset)->limit($limit)->get();
        foreach ($result as $key => $order) {
            $result[$key]['price'] = $order['grand_total'];
        }
        return [
            'count' => $count,
            'result' =>$result,
        ];
    }

    public function countOrderByStatus($shop_id)
    {
        $result = collect();
        $count = Order::where('shop_id', $shop_id)->count();
        $result->push([
            'status' => null,
            'name'  => "all",
            'count' => $count
        ]);
        foreach(config('constants.order.status') as $key => $value){

            $count = Order::where('shop_id', $shop_id)->where('status', $value)->count();
            $result->push([
                'status' => $value,
                'name'  => $key,
                'count' => $count
            ]);
        }
        return $result;
    }
}
