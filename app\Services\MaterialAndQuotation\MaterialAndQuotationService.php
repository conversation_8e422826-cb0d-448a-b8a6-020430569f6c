<?php
namespace App\Services\MaterialAndQuotation;

use App\Services\Log\LogService;
use App\Quotation;
use App\Material;
use App\MaterialImportHistory;
use App\MaterialQuotation;
use App\Shop;
use App\Supplier;
use App\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MaterialAndQuotationService 
{
    private $_userId;
    public function __construct($userId = null)
    {
        if ($userId) {
            $this->_userId = $userId;
        } else {
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    public function list($shopId = '', $limit = 10, $offset = 0, $from = '', $to = ''){
        if($shopId == ''){
            $user = User::where('id',$this->_userId)->with('shop')->first();
            if($user){
                if(isset($user->shop[0])){
                    $shopId = $user->shop[0]->id;
                }else return [];
            }else return [];
        }
        // $quotationList = [];
        $query = Quotation::query();
        $query = $query->select('*')->where('shop_id', $shopId)->orderBy('created_at','desc')->limit($limit)->offset($offset);
        if($from != '' && $to != '') $query = $query->whereBetween('created_at',[$from,$to]);
        $quotationList = $query->get();


        return $quotationList;
    }
    public function detail($quotationId){
        // $shopId = '';
        // if($shopId == ''){
        //     $user = User::where('id',$this->_userId)->with('shop')->first();
        //     if($user){
        //         if(isset($user->shop[0])){
        //             $shopId = $user->shop[0]->id;
        //         }else return [];
        //     }else return [];
        // }
        // $quotationList = [];
        $query = Quotation::query();
        $field = preg_match('/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i', $quotationId) ? 'id' : 'link';
        $query = $query->select('*')
        // ->where('shop_id', $shopId)
        ->where($field, $quotationId)->with('shop','supplier','materialQuotations');
        $quotationDetail = $query->get()->first();
        // if($quotationDetail && isset($quotationDetail['material_quotations'])){
        //     foreach ($quotationDetail['material_quotations'] as $key => $value) {
        //         echo $value['name'];
        //     }
        // }

        return $quotationDetail;
    }

    public function addQuotation($data){
        $materialModel = new Material;
        $quotationModel = new Quotation;
        $user = User::where('id',$this->_userId)->with('shop')->first();
        DB::beginTransaction();
        if($user){
            if(isset($user->shop[0])){
                try {
                    $shopId = $user->shop[0]->id; 
                    $quotation = [];
                    $quotation['supplier_id'] = $data['supplier_id'];
                    $quotation['from'] = $data['from'];
                    $quotation['to'] = $data['to'];
                    $quotation['name'] = $data['name'] ?? "";
                    $quotation['notes'] = $data['notes'] ?? "";
                    $quotation['file'] = $data['file'] ?? "";
                    $quotation['shop_id'] = $shopId;
            
                    $quotationResult = $quotationModel->create($quotation);
            
                    $materialQuotations = $data['material_quotations'];
                    foreach ($materialQuotations as $keyItem => $valueItem) {
                        $materialID = $valueItem['material_id'];
                        if(isset($valueItem['name']) && isset($valueItem['price'])){
                            //case new material
                            if(!$valueItem['material_id']){
                                $checkMaterial = Material::where('name', $valueItem['name'])->where('shop_id', $shopId)->get();
                                if(count($checkMaterial) < 1){
                                    $newMaterial = [];
                                    $newMaterial['name'] = $valueItem['name'];
                                    $newMaterial['name_en'] = isset($valueItem['name_en']) ? $valueItem['name_en'] : '';
                                    $newMaterial['packing'] = isset($valueItem['packing']) ? $valueItem['packing'] : '';
                                    $newMaterial['brand']   = isset($valueItem['brand']) ? $valueItem['brand'] : '';
                                    $newMaterial['origin']  = isset($valueItem['origin']) ? $valueItem['origin'] : '';
                                    $newMaterial['unit']    = isset($valueItem['unit']) ? $valueItem['unit'] : '';
                                    $newMaterial['barcode'] = isset($valueItem['barcode']) ? $valueItem['barcode'] : '';
                                    $newMaterial['shop_id'] = $shopId;
                                    $materialID = $materialModel->create($newMaterial)->id;
                                }else{
                                    $materialID = $checkMaterial[0]->id;
                                }
                            }
                            if(isset($valueItem['price']) && $valueItem['price'] != ''){
                                $materialQuotationModel = new MaterialQuotation;
                                $materialQuotation_ = [];
                                $materialQuotation_['material_id'] = $materialID;
                                $materialQuotation_['quotation_id'] = $quotationResult->id;
                                $materialQuotation_['price'] = intval(str_replace(",","",$valueItem['price']));
                                $materialQuotation_['price_vat'] = intval(str_replace(",","",$valueItem['price_vat']));
                                $materialQuotation_['notes'] = $valueItem['notes'];
                                $materialQuotationModel->create($materialQuotation_);
                            }
                        }
                    }
                    DB::commit();
                    return $quotationResult;
                } catch (\Throwable $e) {
                    DB::rollback();
                    throw $e;
                }
            }
            return "Not found shop";

        }
        return "Not found user";
    }

    public function updateQuotation($data){
        
        $result = Quotation::find($data['id']);
        $result->update($data);
        
        $materialQuotations = $data['material_quotations'];
        
        foreach ($materialQuotations as $keyItem => $valueItem) {
            
            $materialID = $valueItem['material_id'];
            if(isset($valueItem['id']) && isset($valueItem['price'])){
                if(isset($valueItem['price']) && $valueItem['price'] !== ''){
                    $materialQuotation = MaterialQuotation::find($valueItem['id']);
                    
                    $materialQuotation->update($valueItem);
                }
            }
        }

        return $result;
    }

    public function requestQuotation($data){
        
        $user = User::where('id',$this->_userId)->with('shop')->first();
        DB::beginTransaction();
        if($user){
            if(isset($user->shop[0])){
                $result = [];
                try {
                    foreach($data["supplier_ids"] as $supplier_id){
                        $materialModel = new Material;
                        $quotationModel = new Quotation;

                        $shopId = $user->shop[0]->id; 
                        $quotation = [];
                        $quotation['supplier_id'] = $supplier_id;
                        $quotation['from'] = $data['from'];
                        $quotation['to'] = $data['to'];
                        $quotation['name'] = $data['name'] ?? "";
                        $quotation['notes'] = $data['notes'] ?? "";
                        $quotation['file'] = $data['file'] ?? "";
                        $quotation['shop_id'] = $shopId;
                        $quotation['status'] = 1;
                
                        $quotationResult = $quotationModel->create($quotation);
                
                        $materialQuotations = $data['material_quotations'];

                        
                        foreach ($materialQuotations as $keyItem => $valueItem) {
                            $materialID = $valueItem['material_id'];
                            if(isset($valueItem['name'])){
                                //case new material
                                if(!$valueItem['material_id']){
                                    $checkMaterial = Material::where('name', $valueItem['name'])->where('shop_id', $shopId)->get();
                                    if(count($checkMaterial) < 1){
                                        $newMaterial = [];
                                        $newMaterial['name'] = $valueItem['name'];
                                        $newMaterial['name_en'] = isset($valueItem['name_en']) ? $valueItem['name_en'] : '';
                                        $newMaterial['packing'] = isset($valueItem['packing']) ? $valueItem['packing'] : '';
                                        $newMaterial['brand']   = isset($valueItem['brand']) ? $valueItem['brand'] : '';
                                        $newMaterial['origin']  = isset($valueItem['origin']) ? $valueItem['origin'] : '';
                                        $newMaterial['unit']    = isset($valueItem['unit']) ? $valueItem['unit'] : '';
                                        $newMaterial['barcode'] = isset($valueItem['barcode']) ? $valueItem['barcode'] : '';
                                        $newMaterial['shop_id'] = $shopId;
                                        $materialID = $materialModel->create($newMaterial)->id;
                                    }else{
                                        $materialID = $checkMaterial[0]->id;
                                    }
                                }
                                $materialQuotationModel = new MaterialQuotation;
                                $materialQuotation_ = [];
                                $materialQuotation_['material_id'] = $materialID;
                                $materialQuotation_['quotation_id'] = $quotationResult->id;
                                $materialQuotation_['price'] = 0;
                                $materialQuotation_['price_vat'] = 0;
                                $materialQuotation_['notes'] = $valueItem['notes'];
                                $materialQuotationModel->create($materialQuotation_);
                            }
                        }
                        array_push($result, $quotationResult);
                        
                    }
                    DB::commit();
                } catch (\Throwable $e) {
                    DB::rollback();
                    throw $e;
                }
                return $result;
            }
            return "Not found shop";

        }
        return "Not found user";
    }

    public function searchMaterial($string, $ShopMaterials){
        foreach ($ShopMaterials as $key => $value) {
            // Remove whitespace and convert to lowercase
            $normalizedString1 = strtolower(preg_replace('/\s+/', '', $string));
            $normalizedString2 = strtolower(preg_replace('/\s+/', '', $value['name']));
            
            // Compare the normalized strings
            if ($normalizedString1 === $normalizedString2) {
                    // Log::info($normalizedString1 ."===". $normalizedString2);
                    return $value;
                }
            }

        // $ShopMaterialsCollection = collect($ShopMaterials);
        // $filteredMaterials = $ShopMaterialsCollection->filter(function ($material) use ($string) {
        //     return stripos(strtolower(preg_replace('/\s+/', '', $string)), strtolower(preg_replace('/\s+/', '', $material['name']))) !== false;
        // });
        // // Pluck the ID for the filtered materials
        // return $filteredMaterials->pluck('id')->all();

        return [];
    }

    public function searchMaterialByName($string, $shopId){
        $result = Material::where('name', 'ilike', "%{$string}%")->where('shop_id', $shopId)->get();
        return $result;
    }
    
    public function filter($M_IDs, $shopId ,$order = 'price', $sort = 'asc', $from = '', $to = ''){
        $query = new Material;
        $query = $query->whereIn('id', $M_IDs);
        $orderMethod = 'materialQuotations';
        switch ($order) {
            case 'price':
                $orderMethod = ($sort == 'asc' ? 'materialQuotations' : 'materialQuotationsDESC');
                break;
            case 'date':
                $orderMethod = ($sort == 'asc' ? 'materialQuotationsDate' : 'materialQuotationsDateDESC');
                break;
        }
        $query = $query->with($orderMethod);
        $query = $query->whereHas($orderMethod, function($q) use($from, $to, $shopId){
            if($from != '' && $to != ''){
                if($from == $to){
                    //filter for single date
                    $q->whereHas('quotation', function($qq) use($from, $to, $shopId){
                        $qq->where('from','<=', $from)->where('to','>=', $to)->where('shop_id', $shopId);
                    });
                }else{
                    $q->whereHas('quotation', function($qq) use($from, $to, $shopId){
                        $qq->where('from','>=', $from)->where('to','<=', $to)->where('shop_id', $shopId);
                    });
                }
            }
        });
        $result = $query->get();
        if($orderMethod != 'materialQuotations'){
            foreach ($result as $key => $value) {
                $result[$key]['materialQuotations'] = $result[$key][$orderMethod];
                unset($result[$key][$orderMethod]);
            }
        }

        return $result;
    }

    public function materialImportHistory($MQIds, $shopId){
        DB::beginTransaction();
        $importResult = 0;
        $message = '';
            try {
                foreach ($MQIds as $key => $value) {
                    if(!isset($value['notes']) || !isset($value['quantity'])){
                        $message .= 'Missing quantity or notes';
                        return ["status" =>false,"message" => $message];
                    }
                    $materialQuotationModel = new MaterialImportHistory;
                    $materialQuotationModel->material_id = $value['material_id'];
                    $materialQuotationModel->material_quotation_id = $value['id'];
                    $materialQuotationModel->quantity = $value['quantity'];
                    $materialQuotationModel->notes = $value['notes'];
                    $materialQuotationModel->shop_id = $shopId;
                    $materialQuotationModel->save();
                    $importResult++;
                }
                DB::commit();
                // return $importResult;
            } catch (\Throwable $e) {
                return ["status" =>false,"message" => $message];
                DB::rollback();
                throw $e;
            }
        return [
            "status"  => true,
            "message" => "Success import ".$importResult." materials."
        ];
    }
}