<?php

namespace App\Http\Requests\Delivery;

use App\Http\Requests\BaseRequest;

class DriverActionRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'action'       => 'required|string|in:update_delivery_status,set_online,set_offline,moving',
            'delivery_id'  => 'required_if:action,update_delivery_status|uuid|exists:deliveries,id',
            'latitude'          => 'required',
            'longitude'         => 'required',
        ];
    }
}
