<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CheckOrigin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {

        // $requestHost  = request()->headers->get('origin') ?? null;
        // echo
        $ipAddress = $request->ip();
        $requestHost  = parse_url(request()->headers->get('origin'),  PHP_URL_HOST);
        $acceptedHosts = [
            'remagan.com',
            'remagan.vn',
            'staging.remagan.com',
            'dev.remagan.com',
            '*************',
            '**************',
            '**************',
            '**********',
            '172.19.0.*',
            '172.18.0.*',
            'clomart.xampps'
        ];
        if(env('APP_ENV') != 'production'){
            $acceptedHosts[] = '127.0.0.1';
            $acceptedHosts[] = '************';
            $acceptedHosts[] = '***********';
            $acceptedHosts[] = '***********';
            $acceptedHosts[] = 'localhost:3000';
            $acceptedHosts[] = 'localhost';
            $acceptedHosts[] = '*************';
        }
        $requestHost = $requestHost ?? $ipAddress;



        $accept = false;
        // Check direct matches first
        if (in_array($requestHost, $acceptedHosts)) {
            $accept = true;
        }else{
            $patterns = [
                '/^172\.19\.0\.\d{1,3}$/', // Matches '172.19.0.*' pattern
            ];
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $requestHost)) {
                    $accept = true;
                }
            }
        }


        if ($accept) {
            return $next($request);
        }else{
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'error_code' => 'remagan_001_E_001',
                    'error_message' => $requestHost
                    ]
            ];
            Log::channel('check-origin')->info($requestHost);
            return response()->json($response,JsonResponse::HTTP_OK);
        }

    }
}
