<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class DeliveryErrorMail extends Mailable
{
    use Queueable, SerializesModels;

    protected $errorType;
    protected $errorMessage;
    protected $context;
    protected $deliveryPartner;
    protected $orderId;

    /**
     * Create a new message instance.
     *
     * @param string $errorType
     * @param string $errorMessage
     * @param string $deliveryPartner
     * @param string $orderId
     * @param array $context
     * @return void
     */
    public function __construct($errorType, $errorMessage, $deliveryPartner, $orderId, $context = [])
    {
        $this->errorType = $errorType;
        $this->errorMessage = $errorMessage;
        $this->deliveryPartner = $deliveryPartner;
        $this->orderId = $orderId;
        $this->context = $context;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        try {
            return $this->subject('Delivery Error Report - ' . $this->deliveryPartner . ' - ' . $this->errorType)
                ->view('emails.delivery_error_report')
                ->with([
                    'errorType' => $this->errorType,
                    'errorMessage' => $this->errorMessage,
                    'deliveryPartner' => $this->deliveryPartner,
                    'orderId' => $this->orderId,
                    'context' => $this->context,
                    'timestamp' => now(),
                ]);
        } catch (\Exception $e) {
            Log::channel('delivery')->error("Delivery Error Mail Error: " . $e->getMessage());
        }
    }
}
