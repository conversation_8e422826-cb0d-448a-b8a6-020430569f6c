<?php

namespace App\Console\Commands;

use App\Interaction;
use App\Shop;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateShopTotalFollowAndLike extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shops:update-total-follow-and-like';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update total follow and like count for all shops';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $follows = Interaction::where([
            'object_type_b' => config("constants.object_type.shop"),
            'interaction_type' => config("constants.interaction.follow")])
            ->select('object_id_b', DB::raw('COUNT(*) as count'))
            ->groupBy('object_id_b', 'object_type_b', 'interaction_type')
            ->get();

        $likes = Interaction::where([
            'object_type_b' => config("constants.object_type.shop"),
            'interaction_type' =>  config("constants.interaction.like")])
            ->select('object_id_b', DB::raw('COUNT(*) as count'))
            ->groupBy('object_id_b', 'object_type_b', 'interaction_type')
            ->get();

        foreach ($follows as $follow) {
            Shop::where("id", $follow->object_id_b)->update(['follows' => $follow->count]);
        }
        foreach ($likes as $like) {
            Shop::where("id", $like->object_id_b)->update(['likes' => $like->count]);
        }
    }
}
