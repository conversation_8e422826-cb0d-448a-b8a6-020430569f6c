<?php
namespace App\Services\Auth;

use App\Property;
use App\Http\Requests\PropertyRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\User;
use App\Role;
use App\Setting;
use App\Image;
use App\Token;
use App\SellArea;
use App\Province;
use App\Notification;
use App\Mail\SendMail;
use App\Mail\SendRegisterUserMail;
use App\Mail\SendMailUpdateMail;
use App\Mail\ForgetPassword;
use App\Mail\SendOTPEmail;
use App\Mail\ErrorReportMail;
use Illuminate\Support\Facades\Mail;
use Hash;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use Illuminate\Support\Facades\URL;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Illuminate\Auth\Events\Verified;
use App\Http\Controllers\Api\v1\Client\VerificationController;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Redirect;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use App\Jobs\ActionMark;
use Illuminate\Support\Facades\Log;
use App\Services\Log\LogSendMailService;
use App\Services\Token\TokenService;
use App\Services\View\ViewService;
use App\Services\Request\RequestService;
use App\FollowLike;
use App\HistoryMark;
use DB;
use phpDocumentor\Reflection\Types\Void_;

class AuthClientService
{
    use VerifiesEmails;
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId)
        {
            $this->_userId = $userId;
        }
        else
        {
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    public function getUserInfor()
    {
        $result = User::with('roles','shop')->select([
            'id','name','email','phone','user_name','password',
            'gender','date_of_birth','identity_card','address','profile_picture','is_new','user_status',
            'description','custom_path','role_id','latitude','longitude','provider_name', 'language','referral_code'])->find($this->_userId);
        $result["language"] = json_decode($result["language"], true)[0];
        // $result['property_like'] = json_decode($result->property_like,true);

        // $general = new GeneralService($this->_userId);
        // $result['image'] = $general->avatar($result->profile_picture, config('constants.image_type.thumbnail'));
        // $result['background'] = $general->avatar($result->background_picture, config('constants.image_type.medium'));

        if($result){
            $result['has_password'] = false;
            if(!empty($result['password'])){
                $result['has_password'] = true;
                unset($result['password']);
            }
        }
        return $result;

    }

    //------general update-------------
    public function generalUpdate(array $user)
    {
        $result = User::find($user['id']);

        $result->update($user);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(Config::get('constants.log_action.update', 2),$result->id,Config::get('constants.object_type.user', 5),$result, json_encode($user));

        return $result;

    }

    //----process update user----
    public function update(array $user)
    {

        $result = User::find($user['id']);

        if(!$result)
        {
            return false;
        }

        $con = new Controller;
        $con->authorize('updateClient', $result);

        $this->generalUpdate($user);

        $result = $this->preview($result->id);
        return $result;
    }

    //---process preview user-----------

    public function preview($id)
    {
        $result = User::with(['roles'
        // 'image:id,path,path_thumbnail','background:id,path,path_thumbnail',
        // 'properties.requests:id,property_id,price,type_id',
        // 'properties.images:id,path,parent_id',
        // 'properties.propertyTypes:id,name,slug'
        ])->where('id', $id)->first(['id','address','background_picture','created_by','date_of_birth','description','email','gender','identity_card','is_new','name','phone','profile_picture','user_name','custom_path','role_id','referral_code',]);
        if(!$result)
        {
            return false;
        }
        // $general = new GeneralService($this->_userId);
        // $result['image'] = $general->avatar($result->profile_picture, config('constants.image_type.thumbnail'));
        // $result['background'] = $general->avatar($result->background_picture, config('constants.image_type.medium'));

        return $result;
    }

    //---process detail user seller-----------

    public function detail($id)
    {
        $user = User::find($this->_userId);

        $result = User::with('roles')
        ->select(['users.id', 'users.name','users.profile_picture','users.email',
        'users.phone','users.gender',
        'users.address',
        'users.description',
        'users.created_by',
        'users.custom_path',
        'users.latitude','users.longitude',
        'users.referral_code',
        ])
        ->where(function($query) use($id){
            $query->where('id',$id)->orWhere('custom_path', $id);
        })
        ->where('users.enable', true)
        ->first();


        if(!$result)
        {
            return false;
        }

        $follower = isset($user->id) ? $user->id : null;
        // if(isset($user->id) && $user->id != $result->id)
        // {
        //     $view = new ViewService();
        //     $view->increase([
        //         'object_id'     => $result->id,
        //         'object_type'   => config('constants.object_type_view.user'),
        //         'increase_view' => config('constants.increase_view.view')
        //     ], true);
        // }


        // $follow = FollowLike::where([
        //     ['follow_likes.object_type', config('constants.object_type_view.user')],
        //     ['follow_likes.type', config('constants.type_follow_like.follow')],
        //     ['follow_likes.user_id',$follower],
        //     ['follow_likes.object_id',$result->id]
        // ])->first();

        // if($follow)
        // {
        //     $result['followed'] = true;
        // }
        // else {
        //     $result['followed'] = false;
        // }

        // $general = new GeneralService($this->_userId);
        //     $result['image'] = $general->avatar($result->profile_picture, config('constants.image_type.thumbnail'));
        //     $result['background'] = $general->avatar($result->background_picture, config('constants.image_type.medium'));

        return $result;
    }

    //----process update password-----
    public function updatePassword(array $user)
    {
        $check = User::find($this->_userId);
        $result = $check;

        $user['password'] = bcrypt($user['password']);
        $user['id'] = $result->id;

        $this->generalUpdate($user);

        return $result;
    }

    //--------reset password------------
    public function reset_password(array $user)
    {

        $result = User::where('email',$user['email'])->first();

        if(!$result)
        {
            return false;
        }

        $con = new Controller;
        $con->authorize('update',$result);

        //  $password = substr(str_shuffle("0123456789"), 0, 8);
        $password = rand((pow(10, 7)), (pow(10, 8) - 1));

        $user['id'] = $result->id;
        $user['password'] = bcrypt($password);

        $this->generalUpdate($user);


        $log = new LogSendMailService();
        try {
            Mail::to($user['email'])->send(new SendMail($result->name,$password));
            $mess ='Sent Successfully';
            $log->logSendMail($result, $mess);
        } catch (\Exception $e) {
            $log->logSendMail($result,$e);
        }

        return $result;
    }



    //---------forget password----------------
    public function forget(array $user)
    {
        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        $result = User::whereRaw('LOWER(email) = (?)', [strtolower($user['email'])])->first();
        if(!$result){
            return false;
        }

        // if($result->enable == false)
        // {
        //     return 'lock';
        // }

        // if($result->email_verified_at == null)
        // {
        //     return 'verify';
        // }

        $key = "Forget Password";
        $code = str_pad(mt_rand(1, 999999),6,0,STR_PAD_LEFT);
        // check $key exisit in Cache

        if(Cache::has($user['email']))
        {
            Cache::forget($user['email']);
            $expiresAt = Carbon::now()->addSeconds(60 * 60);
            Cache::put($user['email'], $key, $expiresAt);
        }
        else{

            $expiresAt = Carbon::now()->addSeconds(60 * 60);
            Cache::put($user['email'], $key, $expiresAt);
        }

        $log = new LogSendMailService();
        try {
            Mail::to($user['email'])->send(new ForgetPassword($result,$this->changeTextEmail($result->email), $code));
            $mess ='Sent Successfully';
            $log->logSendMail($result, $mess);
        } catch (\Exception $e) {
            $log->logSendMail($result,$e);
        }




        return true;
    }
    //------------------------
    public function confirmCode(array $user)
    {
        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        $result = User::find($user['id']);

        if(!$result){
            return false;
        }

        if($result->enable == false)
        {
            return 'lock';
        }

        if($result->email_verified_at == null)
        {
            return 'verify';
        }

        if(Cache::has($result->email))
        {
            return true;
        }

        return false;
    }
    //----process update forget password-----
    public function updateForgetPassword(array $user)
    {
        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }
        //----device user-----------
        $device = request()->header('User-Agent');

        // $result = User::find($user['id']);
        // $result = User::with(['roles:id,name','districts','provinces','wards'])->find($user['id']);
        // $result['property_like'] = json_decode($result->property_like,true);
        if(!$user)
        {
            return false;
        }

        // if($result->enable == false)
        // {
        //     return 'lock';
        // }

        // if($result->email_verified_at == null)
        // {
        //     return 'verify';
        // }

        $general = new GeneralService($this->_userId);
        // $result['image'] = $general->avatar($result->profile_picture, config('constants.image_type.thumbnail'));
        // $result['background'] = $general->avatar($result->background_picture, config('constants.image_type.medium'));

        $user['password'] = bcrypt($user['password']);

        // if(Cache::has($result->email))
        // {
        //     Cache::forget($result->email);

        // }
        // $user['token'] = JWTAuth::fromUser($result);
        $this->generalUpdate($user);
        // $address_ip = $general->getUserIpAddr();
        // TokenService::deleteList(['id' => $result->id]);
        // TokenService::insert(['token' => $user['token'], 'user_id' => $result->id, 'device' => $device, 'address_ip' => $address_ip, 'token_type' => 1]);
        $data = [
            'result'    => $user,
            // 'token'     => $user['token']
        ];
        return $data;

        // return false;


    }

    public function register(array $register)
    {
        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        $register['role_id'] = Role::where('name','User')->first()->id;

        $result = User::create($register);
        // $url = $this->verificationUrl($result);
        $token = str_random(32);
        // $redis = GeneralService::redisConnection();
        $redis->set($result->id, $token,'EX', 2592000);

        // $log = new LogSendMailService();
        // try {
        //     Mail::to($register['email'])->send(new SendRegisterUserMail($token,$result->id,$result->name,$this->changeTextEmail($result->email)));
        //     $mess ='Sent Successfully';
        //     $log->logSendMail($result, $mess);
        // } catch (\Exception $e) {
        //     $log->logSendMail($result,$e);
        // }


        // store log
        // $log = new LogSendMailService();
        // if(count(Mail::failures()) > 0 ) {
        //     $mess = 'Sent Failures';
        //     $log->logSendMail($result,$mess);
        // }
        // else
        // {
        //     $mess ='Sent Successfully';
        //     $log->logSendMail($result, $mess);
        // }

        // $general = new GeneralService();
        // $general->addLog(
        //     Config::get('constants.log_action.create', 1),
        //     $result->id,
        //     Config::get('constants.object_type.user', 5),
        //     $result,
        //     json_encode($register)
        // );

        return $result;
    }    public function verificationUrl($notifiable)
    {
        $data = User::find($notifiable->id);
        $token = str_random(32);
        $temporarySignedURL = URL::temporarySignedRoute(
            'verification.verify', Carbon::now()->addMinutes(60), ['id' => $data->id, 'token' => $token]
        );
        $temporarySignedURL = str_replace("http","https",$temporarySignedURL);
        return urlencode($temporarySignedURL);
    }
    public function registerCode(array $register)
    {

        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        $key = rand( pow(10,5) , (pow(10,6) - 1));
        if(Cache::has($register['email']))
        {
            Cache::forget($register['email']);
            $expiresAt = Carbon::now()->addSeconds(300);
            Cache::put($register['email'], $key, $expiresAt);
        }
        else{

            $expiresAt = Carbon::now()->addSeconds(300);
            Cache::put($register['email'], $key, $expiresAt);
        }

        $log = new LogSendMailService();
        try {
            Mail::to($register['email'])->send(new SendMailUpdateMail($key));
            $mess ='Sent Successfully';
            $log->logSendMail($key, $mess);
        } catch (\Exception $e) {
            $log->logSendMail($key,$e);
        }

        //store log
        // $log = new LogSendMailService();
        // if(count(Mail::failures()) > 0 ) {
        //     $mess = 'Sent Failures';
        //     $log->logSendMail($register,$mess);
        // }
        // else
        // {
        //     $mess ='Sent Successfully';
        //     $log->logSendMail($register, $mess);
        // }
        return true;
    }
    public function confirmRegisterCode(array $register)
    {
        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        if(!Cache::has($register['email']))
        {

            return false;
        }
        if(Cache::get($register['email']) != $register['key'])
        {

            return false;
        }

        return true;
    }
    public function checkExist(array $check)
    {
        $search = isset($check['key']) ? strtolower($check['key']): "";
        switch ($check['type']) {
            case 1;
                $user = User::where('user_name','ILIKE',$search)->first();
                if($user)
                return true;
                // return false;

                break;

            case 2;
                $user = User::where('email','ILIKE',$search)->first();
                if($user)
                return true;
                // return false;
                break;

            case 3;
                $user = User::where('phone',$search)->first();
                if($user)
                return true;
                // return false;
                break;
            case 4;
                $user = User::where('referral_code', 'ILIKE',$search)->first();
                if($user)
                return true;
                // return false;
                break;

            default:
                return false;
                break;
        }
        return false;
    }

    //--------check custom_path----------------
    public function checkExistPath(array $check)
    {
        $search = isset($check['key']) ? strtolower($check['key']): "";

        $dataUser = User::where('custom_path', $search)->first();

        if(!$dataUser)
        {
            return true;
        }

        return false;

    }


    //---------handle image user --------------
    public function handleImage(array $user, $image, $image_format = 1, $orientation = null, $id = null)
    {
        $service = new ImageService($this->_userId);

        if($id)
        {
            $data = [
                'id' => $id
            ];
            $service->delete($data);
        }

        $data = [
            'path' => $image,
            'parent_id' => $user['id'],
            'title' => $user['title'],
            'object_type' => config('constants.image_object_type.user'),
            'orientation' => $orientation
        ];
        if($image_format == 2)
        {
            $image = $service->addBase64NoWaterMark($data);
            if($image)
            {
                $result = $image['images'];
                return $result['path'];
            }
            else
            {
                return null;
            }
        }
        else
        {
            $image = $service->insertAvatar($data);
            if($image)
            {
                $result = $image['images'];
                return $result['path'];
            }
            else
            {
                return null;
            }
        }



    }

    public function updateImageDrive(array $user)
    {
        $result = User::find($user['id']);

        if(!$result)
        {
            return false;
        }

        //------profile_picture-------
        if(isset($user['file']))
        {
            $image_format = config('constants.image.avatar', 1);
            if($result->profile_picture)
            {
                $user['profile_picture'] = $this->handleImage($user, $user['file'], $image_format, $result->profile_picture);
            }
            else
            {
                $user['profile_picture'] = $this->handleImage($user, $user['file'], $image_format);

                // $actionMark = new ActionMark(config('constants.action_mark_object_type.user'),$result->id,13,$result->id);
                // Log::info("Run Queue Action Mark");
                // dispatch($actionMark);
            }

        }

        //-------background--------
        // if(isset($user['file_background']))
        // {
        //     $image_format = config('constants.image.normal');
        //     if($result->background_picture)
        //     {
        //         $user['background_picture'] = $this->handleImage($user, $user['file_background'], $image_format,$result->background_picture);
        //     }
        //     else
        //     {
        //         $user['background_picture'] = $this->handleImage($user, $user['file_background'], $image_format);
        //     }

        // }

        $this->generalUpdate($user);

        $result = $this->preview($result->id);
        return $result;
    }

    //---------------update image user------------
    public function updateImage(array $user)
    {
        $result = User::find($user['id']);

        if(!$result)
        {
            return false;
        }

        $con = new Controller;
        $con->authorize('updateClient', $result);

        //------profile_picture-------
        if(isset($user['file']))
        {
            $image_format = config('constants.image.avatar');
            $countAv = HistoryMark::where([
                ['object_type', config('constants.action_mark_object_type.user')],
                ['active_mark_id', 13],
                ['user_id', $result->id]
            ])->count();

            if($countAv == 1)
            {
                $user['profile_picture'] = $this->handleImage($user, $user['file'], $image_format, isset($user["orientation_avatar"])?$user["orientation_avatar"]:null, $result->profile_picture);
            }
            else
            {
                $user['profile_picture'] = $this->handleImage($user, $user['file'], $image_format, isset($user["orientation_avatar"])?$user["orientation_avatar"]:null);

                $actionMark = new ActionMark(config('constants.action_mark_object_type.user'),$result->id,13,$result->id);
                Log::info("Run Queue Action Mark");
                dispatch($actionMark);
            }

        }

        //-------background--------
        if(isset($user['file_background']))
        {
            $image_format = config('constants.image.normal');

            $countBa = HistoryMark::where([
                ['object_type', config('constants.action_mark_object_type.user')],
                ['active_mark_id', 14],
                ['user_id', $result->id]
            ])->count();

            if($countBa == 1)
            {
                $user['background_picture'] = $this->handleImage($user, $user['file_background'], $image_format, isset($user["orientation_background"])?$user["orientation_background"]:null, $result->background_picture);
            }
            else
            {
                $user['background_picture'] = $this->handleImage($user, $user['file_background'], $image_format, isset($user["orientation_background"])?$user["orientation_background"]:null);

                $actionMark = new ActionMark(config('constants.action_mark_object_type.user'),$result->id,14,$result->id);
                Log::info("Run Queue Action Mark");
                dispatch($actionMark);
            }

        }

        $this->generalUpdate($user);

        $result = $this->preview($result->id);
        return $result;
    }

    //----------notifi----------------------
    public function notify(array $data)
    {
        $result = User::find($this->_userId);

        if(!$result)
        {
            return false;
        }

        $offset = isset($data['offset']) ? $data['offset'] : 0;
        $limit  = isset($data['limit']) ? $data['limit'] : 20;

        $_notify = $result->notifications();
        $count = $_notify->count();
        $unread = $_notify->whereNull('read_at')->count();
        $notify = [

            'count' => $count,
            'data' => Notification::where('notifiable_id',$result->id)->orderBy('created_at','desc')->offset($offset)->limit($limit)->get(),
            'unread' => $unread,
        ];

        return $notify;
    }

    //----------detail notify------------------
    public function detailNotify($id, $data)
    {

        $request_type = isset($data->request_type) ? $data->request_type : 'off';
        if($request_type == 'on')
        {

            $user = User::find($this->_userId);

            if($user)
            {
                $_notify = $user->notifications();
                $user->unreadNotifications->markAsRead();
                $notify = [
                    'unread' => $_notify->whereNull('read_at')->count()
                ];
                return $notify;
            }
            return [];
        }
        else
        {
            $result = Notification::find($id);

            if($result)
            {
                if(!$result->read_at)
                {
                    $result->update(['read_at' => Carbon::now()->toDateTimeString()]);
                }

                $user = User::find($result->notifiable_id);
                if($user)
                {
                    $_notify = $user->notifications();
                    $result['unread'] = $_notify->where('read_at')->count();
                }

                $result['data'] = $result['data'] ? json_decode($result['data']): null;
                return $result;
            }

            return "No found";
        }

    }
    public function verificationEmail($id,$token)
    {
        $redis = GeneralService::redisConnection();

        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        //----device user-----------
        $device = request()->header('User-Agent');
        $general = new GeneralService();
        $address_ip = $general->getUserIpAddr();

        $user = User::find($id);
        $name = $user->name;
        // $redis = GeneralService::redisConnection();
        $dataRedis = $redis->get($id);
        if (!$user->hasVerifiedEmail() && $dataRedis == $token) {
            $user->markEmailAsVerified();
            $token = JWTAuth::fromUser($user);
            $user->token = $token;
            $redis->del($id);
            $user->save();
            TokenService::insert(['token' => $token, 'user_id' => $user->id, 'device' => $device, 'address_ip' => $address_ip, 'token_type' => 1]);
            $data = [
                'result' => true,
                'token' => $token,
                'name' => $name
            ];
            return $data;
        }
        $data = [
            'result' => false,
            'email' => $user->email,
            'name' => $name
        ];
        return $data;
    }
    public function resend($email)
    {
        $redis = GeneralService::redisConnection();

        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        $user = User::where('email',$email)->first();
        // die($user);
        // var_dump($user->hasVerifiedEmail());
        // exit;
        if(!$user)
        {
            return false;
        }
        if (!$user->hasVerifiedEmail()) {
            $token = str_random(32);
            // $redis = GeneralService::redisConnection();
            $redis->set($user->id, $token,'EX', 2592000);

            $log = new LogSendMailService();
            try {
                Mail::to($user['email'])->send(new SendRegisterUserMail($token,$user->id,$user->name,$this->changeTextEmail($user->email)));
                $mess ='Sent Successfully';
                $log->logSendMail($user, $mess);
            } catch (\Exception $e) {
                $log->logSendMail($user,$e);
            }
            return true;
        }
        return false;
    }



    //-----------list device ---------------
    public function listDevice()
    {
        $user = User::find($this->_userId);

        if(!$user)
        {
            return false;
        }

        $result = Token::where('user_id', $user->id)->orderBy('created_at','desc')->get(['id','device','created_at']);

        return $result;
    }

    public function logoutDevice(array $data)
    {
        $user = User::find($this->_userId);
        $token = Token::where([
            ['id', $data['id']],
            ['user_id', $user->id]
        ])->first();

        if(!$user || !$token)
        {
            return false;
        }
        Redis::setex(env('APP_ENV').":".'token_blacklist:'.$token->token,86400*30, true);
        TokenService::delete(['user_id' => $user->id, 'token' => $token->token]);

        return true;
    }    

    public function get_otp($emailOrPhone, $otpType = 'default', $otpAgent, $retry = 0) {
        $result = [];
        $otpAccessToken = '';
        $OTPLifeTime = 3600;//seconds
        $redis_syntax = Str::slug(env('APP_NAME'),'_')."_".$otpAgent."_";
        $originalOtpAgent = $otpAgent; // Keep track of original agent for fallback

     # STEP 1: check access token for Phone number case.
        if($otpAgent == 'zalo' || $otpAgent == 'phone'){
            if($otpAgent == 'zalo'){
                if(Redis::exists($redis_syntax . "otp_access_token"))
                {
                    // echo "1\n";
                    $otpAccessToken = Redis::get($redis_syntax . "otp_access_token");
                    Log::channel('otp')->info("get ZALO OTP access token from cache");
                }else{
                    // refesh zalo token after 25h expired of access_token
                    if(Redis::exists($redis_syntax . "otp_refresh_token")){
                        $refreshToken = Redis::get($redis_syntax . "otp_refresh_token");
                        $otpAccessToken = $this->zalo_otp_refresh_access_token($refreshToken);
                        if(isset($otpAccessToken->access_token)){
                            Redis::setex($redis_syntax . "otp_access_token", 90000, $otpAccessToken->access_token);
                            Redis::set($redis_syntax . "otp_refresh_token", $otpAccessToken->refresh_token);
                            $otpAccessToken = $otpAccessToken->access_token;
                        }else{
                            Log::channel('otp')->info("REFRESH ZALO access token failed!");
                            Log::channel('otp')->info(json_encode($otpAccessToken));
                            
                            // Try SMS fallback for ZALO failure
                            if($originalOtpAgent == 'zalo' && $retry == 0) {
                                Log::channel('otp')->info("ZALO refresh failed, trying SMS fallback");
                                $this->sendErrorReport('ZALO Token Refresh Failed', 'Failed to refresh ZALO access token', [
                                    'phone' => $emailOrPhone,
                                    'error' => $otpAccessToken->error_description ?? 'Unknown error'
                                ]);
                                return $this->get_otp($emailOrPhone, $otpType, 'phone', 1);
                            }
                            
                            $response = [
                                'status' => false,
                                'message' => 'REFRESH Zalo OTP access token failed! ' . ($otpAccessToken->error_description ?? '')
                            ];
                            return $response;
                        }
                    }
                    // get first new token
                    else{
                        Log::channel('otp')->info("get new ZALO access token for OTP");
                        // echo "2\n";

                        // $expiresAt = Carbon::now()->addSeconds(86400);
                        $otpAccessToken = $this->zalo_otp_get_access_token();
                        // $newToken =  Str::uuid();
                        if(isset($otpAccessToken->access_token)){
                            Redis::setex($redis_syntax . "otp_access_token", 90000, $otpAccessToken->access_token);
                            Redis::set($redis_syntax . "otp_refresh_token", $otpAccessToken->refresh_token);
                            $otpAccessToken = $otpAccessToken->access_token;
                        }else{
                            Log::channel('otp')->info("get ZALO OTP access token failed!");
                            Log::channel('otp')->info(json_encode($otpAccessToken));
                            
                            // Try SMS fallback for ZALO failure
                            if($originalOtpAgent == 'zalo' && $retry == 0) {
                                Log::channel('otp')->info("ZALO token generation failed, trying SMS fallback");
                                $this->sendErrorReport('ZALO Token Generation Failed', 'Failed to get new ZALO access token', [
                                    'phone' => $emailOrPhone,
                                    'error' => $otpAccessToken->error_description ?? 'Unknown error'
                                ]);
                                return $this->get_otp($emailOrPhone, $otpType, 'phone', 1);
                            }
                            
                            $response = [
                                'status' => false,
                                'message' => 'get Zalo OTP access token failed! ' . ($otpAccessToken->error_description ?? '')
                            ];
                            return $response;
                        }

                    } // end: get first new token



                }
            }else{
                // not ZALO -> phone sms by FTI
                try {
                    if(Redis::exists($redis_syntax . "otp_access_token"))
                    {
                        // echo "1\n";
                        $otpAccessToken = Redis::get($redis_syntax . "otp_access_token");
                        Log::channel('otp')->info("get OTP access token from cache");
                    }else{
                        Log::channel('otp')->info("get new access token for OTP");
                        // echo "2\n";

                        // $expiresAt = Carbon::now()->addSeconds(86400);
                        $otpAccessToken = $this->otp_get_access_token();
                        // $newToken =  Str::uuid();
                        if(isset($otpAccessToken->access_token)){
                            Redis::setex($redis_syntax . "otp_access_token", 86400, $otpAccessToken->access_token);
                            $otpAccessToken = $otpAccessToken->access_token;
                        }else{
                            Log::channel('otp')->info("get OTP access token failed!");
                            Log::channel('otp')->info($otpAccessToken);
                            $this->sendErrorReport('SMS Token Generation Failed', 'Failed to get SMS OTP access token', [
                                'phone' => $emailOrPhone,
                                'error' => $otpAccessToken
                            ]);
                            $response = [
                                'status' => false,
                                'message' => 'get OTP access token failed!'
                            ];
                            return $response;
                        }

                    }
                }catch (\Throwable $th) {
                    //throw $th;
                    Log::channel('otp')->info("try again to get new access token for OTP");
                    // $expiresAt = Carbon::now()->addSeconds(86400);
                    $otpAccessToken = $this->otp_get_access_token();
                    // $newToken =  Str::uuid();
                    if(isset($otpAccessToken->access_token)){
                        Redis::setex($redis_syntax . "otp_access_token", 86400, $otpAccessToken->access_token);
                        $otpAccessToken = $otpAccessToken->access_token;
                    }else{
                        Log::channel('otp')->info("get OTP access token failed!");
                        $this->sendErrorReport('SMS Token Generation Exception', 'Exception while getting SMS OTP access token', [
                            'phone' => $emailOrPhone,
                            'exception' => $th->getMessage(),
                            'error' => $otpAccessToken
                        ]);
                        $response = [
                            'status' => false,
                            'message' => 'get OTP access token failed!'
                        ];
                        return $response;
                    }
                }
            }
        }else{
            // Log::info("send OTP for mail.");
        }     # STEP 2: check cooldown OTP before send

        $sendOTPStatus = true;
        $sessionID = Str::uuid();
        $OTP_COOLDOWN = false;

        // Update redis_syntax if agent changed due to fallback
        $redis_syntax = Str::slug(env('APP_NAME'),'_')."_".$otpAgent."_";

        // check cool down otp request
        if(Redis::exists($redis_syntax . "otp_cooldown_".$emailOrPhone)){
            Log::info("otp is cooling down");
            $timeRemaining = 0;
            if(Redis::exists($redis_syntax . "otp_cooldown_".$emailOrPhone)){
                $timeRemaining = Redis::ttl($redis_syntax . "otp_cooldown_".$emailOrPhone);

            }
            if(Redis::exists($redis_syntax . "otp_cooldown_count_".$emailOrPhone)){
                $currentCount = Redis::get($redis_syntax . "otp_cooldown_count_".$emailOrPhone);
            //     $timeRemaining = $currentCount * 15;
            }
            return $response = [
                'status' => false,
                'message' => "otp is cooling down. Time remaining: ". $timeRemaining,
                'retry_count_today' => $currentCount,
                'otp_cooldown' => $timeRemaining,
            ];

        }

     # STEP 3: ready to SEND OTP

        // $OTP = random_int(111111,999999);
        $OTP = sprintf('%06d', random_int(0, 999999));

        // $expiresAt = Carbon::now()->addSeconds(360);
        Redis::setex($redis_syntax . "otp_".$emailOrPhone, 360, $OTP);

        if($otpAgent == 'zalo' || $otpAgent == 'phone'){
            if($otpAgent == 'zalo'){
             #zalo
                $emailOrPhoneForZALO = preg_replace('/^0/', '84', $emailOrPhone);
                Log::channel('otp')->info("SEND ZALO OTP :: ".$emailOrPhoneForZALO . " :: " . $OTP);
                // Log::info($emailOrPhone . " :: " . $message);
                $result = $this->zalo_otp_send_request($otpAccessToken, $emailOrPhoneForZALO, $OTP, '344173');
                Log::channel('otp')->info("otp_send_request response:: ");
                Log::channel('otp')->info(json_encode($result));
            }else{
             #phone
                // $message = ($OTP . " la ma xac thuc tu Remagan, co hieu luc trong 10 phut.");
                $message = "Remagan ".$OTP." la ma xac thuc app Remagan Vua Re Vua Gan cua ban. Ma het han sau 5 phut. Vui long khong chia se ma nay voi bat ki ai. CSKH Remagan: 0879555799";
                Log::channel('otp')->info($emailOrPhone . " :: " . $message);
                // Log::info($emailOrPhone . " :: " . $message);
                $result = $this->otp_send_request($otpAccessToken, $sessionID, $emailOrPhone, base64_encode($message),"REMAGAN OTP". date("Y-mm-dd"));
                Log::channel('otp')->info("otp_send_request response:: ");
                Log::channel('otp')->info(json_encode($result));
            }        }else{
            //using email
            $log = new LogSendMailService();
            try {
                Mail::to($emailOrPhone)->send(new SendOTPEmail($emailOrPhone, $OTP, $OTPLifeTime, $otpType));
                $mess ='Sent Successfully';
                $log->logSendMail($OTP, $mess);
            } catch (\Exception $e) {
                $log->logSendMail($result,$e);
                $this->sendErrorReport('Email OTP Send Failed', 'Failed to send OTP email', [
                    'email' => $emailOrPhone,
                    'error' => $e->getMessage(),
                    'otp_type' => $otpType
                ]);
            }
        }

        //set cool down var
        $currentCount = 1;
        $timeRemaining = 0;
        $coolDownStep = env('APP_ENV') != "production" ? 1 : 5;

        if(Redis::exists($redis_syntax . "otp_cooldown_count_".$emailOrPhone)){
            $currentCount = Redis::get($redis_syntax . "otp_cooldown_count_".$emailOrPhone) + 1;
            $timeRemaining = $currentCount * $coolDownStep;
            Redis::set($redis_syntax . "otp_cooldown_count_".$emailOrPhone, $currentCount);
            Redis::setex($redis_syntax . "otp_cooldown_".$emailOrPhone, $timeRemaining, 'cooling down');
        }else{
            $timeRemaining = $currentCount * $coolDownStep;
            Redis::setex($redis_syntax . "otp_cooldown_count_".$emailOrPhone, 86400, $currentCount);
            Redis::setex($redis_syntax . "otp_cooldown_".$emailOrPhone, $timeRemaining, 'cooling down');
        }

        switch ($otpAgent) {            case 'phone':
                if(isset($result->MessageId)){
                    //set OTP to cache
                    Redis::setex($redis_syntax . "otp_code_".$emailOrPhone, $OTPLifeTime, $OTP);
                    Log::channel('otp')->info($redis_syntax . "otp_code_".$emailOrPhone);
                    return $response = [
                        'status' => true,
                        'message' => 'OTP sent successfully',
                        'otp' => env('APP_ENV') != "production" ? $OTP : '',
                        'otp_cooldown' => $timeRemaining,
                    ];
                }elseif(isset($result->error) && $result->error == '1011' && $retry == 0){
                    // REMOVE CACHE ACCESSTOKEN AND REQUEST NEW ONE
                    Redis::del($redis_syntax . 'otp_access_token');
                    return $this->get_otp($emailOrPhone , $otpType , $otpAgent, 1);
                }
                if(isset($result->error) && $result->error == '1014'){
                    Redis::del($redis_syntax . 'otp_access_token');
                    $this->sendErrorReport('SMS Invalid Phone Number', 'Invalid phone number format for SMS OTP', [
                        'phone' => $emailOrPhone,
                        'error_code' => $result->error
                    ]);
                    return $response = [
                        'status' => false,
                        'message' => 'Invalid phone number.',
                        'otp_cooldown' => $timeRemaining,
                    ];
                }
                
                // General SMS failure
                $this->sendErrorReport('SMS OTP Send Failed', 'SMS OTP send failed with unknown error', [
                    'phone' => $emailOrPhone,
                    'response' => json_encode($result)
                ]);
                
                return $response = [
                    'status' => false,
                    'message' => 'lỗi chưa xác định',
                    'otp_cooldown' => $timeRemaining,
                ];

                break;case 'zalo':
                #success
                    // zalo success response{
                    //     "error": 0,
                    //     "message": "Success",
                    //     "data": {
                if(isset($result->error) && $result->error == 0){
                    //set OTP to cache
                    Redis::setex($redis_syntax . "otp_code_".$emailOrPhone, $OTPLifeTime, $OTP);
                    Log::channel('otp')->info($redis_syntax . "otp_code_".$emailOrPhone);
                    return $response = [
                        'status' => true,
                        'message' => 'OTP sent successfully',
                        'otp' => env('APP_ENV') != "production" ? $OTP : '',
                        'otp_cooldown' => $timeRemaining,
                    ];
                }else{
                    // ZALO failed, try SMS fallback if this is original ZALO request
                    if($originalOtpAgent == 'zalo' && $retry == 0) {
                        Log::channel('otp')->info("ZALO OTP send failed, trying SMS fallback");
                        $errorMsg = isset($result->error_description) ? $result->error_description : 
                                   (isset($result->message) ? $result->message : 'Unknown ZALO error');
                        
                        $this->sendErrorReport('ZALO OTP Send Failed', $errorMsg, [
                            'phone' => $emailOrPhone,
                            'error_code' => $result->error ?? 'unknown',
                            'response' => json_encode($result)
                        ]);
                        
                        // Use phone SMS as fallback
                        return $this->get_otp($emailOrPhone, $otpType, 'phone', 1);
                    }
                    
                    Log::channel('otp')->info("ZALO OTP FAILED! " . json_encode($result));
                    return $response = [
                        'status' => false,
                        'message' => isset($result->error_description) ? $result->error_description : 
                                    (isset($result->message) ? $result->message : 'ZALO OTP failed'),
                        'otp' => env('APP_ENV') != "production" ? $OTP : '',
                        'otp_cooldown' => $timeRemaining,
                    ];
                }

                break;            case 'email':
                Redis::setex($redis_syntax . "otp_code_".$emailOrPhone, $OTPLifeTime, $OTP);
                Log::channel('otp')->info($redis_syntax . "otp_code_".$emailOrPhone);
                return $response = [
                    'status' => true,
                    'message' => 'OTP sent successfully',
                    'otp' => env('APP_ENV') != "production" ? $OTP : '',
                    'otp_cooldown' => $timeRemaining,
                ];
                break;
            default:
                {
                    Log::channel('otp')->info(json_encode($result));
                    return $response = [
                        'status' => false,
                        'message' => ''
                    ];
                }
                break;
        }
    }

    public function confirmOTP($phone, $OTP, $otpAgent) {
        $redis_syntax  = Str::slug(env('APP_NAME'),'_')."_".$otpAgent."_";
        if(Redis::exists($redis_syntax . "otp_code_".$phone)){
            if(Redis::get($redis_syntax . "otp_code_".$phone) === $OTP){
                return true;
            }
        }
        return false;
    }

    // [FTI MES]
    public function otp_get_access_token() {

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('OTP_HOST').'oauth2/token',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{

            "client_id": "' .env('OTP_CLIENT_ID') .'",
            "client_secret": "' .env('OTP_CLIENT_SECRET') .'",



            "scope": "send_brandname_otp send_brandname",
            "session_id": "' . Str::uuid() .'",
            "grant_type": "client_credentials"
        }
        ',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        ));

        // Log::info($curl);
        $response = curl_exec($curl);
        // Log::info($response);
        // Check for cURL errors
        if ($errno = curl_errno($curl)) {
            $error_message = curl_strerror($errno);
            Log::channel('otp')->info("cURL error ({$errno}):\n {$error_message}");
        }

        curl_close($curl);
        return json_decode($response);

    }
    // [FTI MES]
    public function otp_send_request($accessToken,$sessionID,$phone,$message,$requestID = "REMAGAN OTP"){
        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('OTP_HOST').'api/push-brandname-otp',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
            "access_token": "'.$accessToken.'",
            "session_id": "'.$sessionID.'",
            "BrandName": "Remagan",
            "Phone": "'.$phone.'",
            "Message": "'.$message.'",
            "RequestId":"'.$requestID.'"
        }',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        ));

        $response = curl_exec($curl);
        if ($errno = curl_errno($curl)) {
            $error_message = curl_strerror($errno);
            Log::channel('otp')->info("cURL error ({$errno}):\n {$error_message}");
        }
        curl_close($curl);
        return json_decode($response);

    }




    function generateCodeChallenge($code_verifier) {
        return rtrim(strtr(base64_encode(hash('sha256', $code_verifier, true)), '+/', '-_'), '=');
    }


    // [ZNS - ZALO NOTIFY SERVICE]
    // https://developers.zalo.me/docs/official-account/bat-dau/xac-thuc-va-uy-quyen-cho-ung-dung-new
    public function zalo_otp_get_access_token() {

        $curl = curl_init();
        $codeChallenge = $this->generateCodeChallenge(env('ZALO_CODE_VERIFER'));
        Log::channel('otp')->info("ZALO OTP code_verifier : ". $codeChallenge);
        $zaloAuthCode = Redis::get('zalo_authcode') ?? '';
        Log::channel('otp')->info("ZALO zaloAuthCode: ". $zaloAuthCode);
        // Log::channel('otp')->info("ZALO_APP_ID: ". env('ZALO_APP_ID'));

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('ZALO_OTP_HOST').'v4/oa/access_token',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>'code='.$zaloAuthCode.'&app_id='.env('ZALO_APP_ID').'&grant_type=authorization_code&code_verifier=oBUjuxslHKdpMrPPTa05mIN2chqwiudkjKKBWELSDFJ',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded',
                'secret_key: 2sW0cVMmY884p12M0DeG'
            ),
        ));

        // Log::info(json_encode($curl));
        $response = curl_exec($curl);
        // Log::info(json_decode($response));
        // Check for cURL errors
        if ($errno = curl_errno($curl)) {
            $error_message = curl_strerror($errno);
            Log::channel('otp')->info("cURL error ({$errno}):\n {$error_message}");
        }

        curl_close($curl);
        return json_decode($response);

    }
    // [ZNS - ZALO NOTIFY SERVICE]
    public function zalo_otp_refresh_access_token($refreshToken) {

        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => env('ZALO_OTP_HOST').'v4/oa/access_token',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'app_id='.env('ZALO_APP_ID') .'&grant_type=refresh_token&refresh_token='.$refreshToken,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded',
            'secret_key: 2sW0cVMmY884p12M0DeG'
        ),
        ));
        $response = curl_exec($curl);
        if ($errno = curl_errno($curl)) {
            $error_message = curl_strerror($errno);
            Log::channel('otp')->info("cURL error ({$errno}):\n {$error_message}");
        }
        curl_close($curl);
        return json_decode($response);
    }

    // [ZNS - ZALO NOTIFY SERVICE]
    public function zalo_otp_send_request($accessToken, $phone, $otp, $templateId = '344173'){
        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://business.openapi.zalo.me/message/template',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{'.
            // '"mode": "development",'.
            '"phone": "' .$phone .'",
            "template_id": "' .$templateId .'",
            "template_data": {
                "otp": "' .$otp .'",
            },
            "tracking_id":"tracking_id"
        }',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'access_token: ' . $accessToken
        ),
        ));

        $response = curl_exec($curl);
        if ($errno = curl_errno($curl)) {
            $error_message = curl_strerror($errno);
            Log::channel('otp')->info("zalo cURL error ({$errno}):\n {$error_message}");
        }
        curl_close($curl);
        return json_decode($response);

    }

    public function setZaloAuthCode($code){
        Log::channel('otp')->info("zalo_authcode:: ".$code);
        Redis::setex("zalo_authcode", 86760, $code);
        return true;
    }    public function delete_account(){
        $user = User::find($this->_userId);
        if ($user) {
            $user->enable = false;
            $user->save();
            RequestService::createRequest($this->_userId, 'delete_account', $this->_userId);
        }
        return true;
    }    /**
     * Send error report <NAME_EMAIL>
     *
     * @param string $errorType
     * @param string $errorMessage
     * @param array $context
     * @return void
     */
    private function sendErrorReport($errorType, $errorMessage, $context = [])
    {
        try {
            Mail::to('<EMAIL>')->send(new ErrorReportMail($errorType, $errorMessage, $context));
            Log::channel('otp')->info("Error report sent successfully for: $errorType");
        } catch (\Exception $e) {
            Log::channel('otp')->error("Failed to send error report email: " . $e->getMessage());
        }
    }

    /**
     * Change email text for display purposes
     *
     * @param string $email
     * @return string
     */
    private function changeTextEmail($email)
    {
        if (strpos($email, '@') !== false) {
            $parts = explode('@', $email);
            $username = $parts[0];
            $domain = $parts[1];
            
            // Hide part of username
            $usernameLength = strlen($username);
            if ($usernameLength > 3) {
                $visibleChars = 2;
                $hiddenPart = str_repeat('*', $usernameLength - $visibleChars);
                $username = substr($username, 0, $visibleChars) . $hiddenPart;
            }
            
            return $username . '@' . $domain;
        }
        
        return $email;
    }

}
