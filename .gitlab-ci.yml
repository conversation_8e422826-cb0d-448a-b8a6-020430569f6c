image: docker:24.0.5

services:
  - name: docker:24.0.5-dind
    command: ["--experimental", "--registry-mirror", "https://registry-mirror.example.com"]

stages:
  # - build_dev
  - build_prod
  - deploy_dev
  - deploy_prod

variables:
  #IMAGE_TAG_DEV: $CI_REGISTRY_IMAGE:dev-$(date +%Y%m%d_%H-%M)
  #IMAGE_TAG_PROD: $CI_REGISTRY_IMAGE:pro-$(date +%Y%m%d_%H-%M)

# build_dev:
#   stage: build_dev
#   only:
#     - develop
#   before_script:
#     - export IMAGE_TAG_DEV=$CI_REGISTRY_IMAGE:dev-$(date +%Y%m%d_%H-%M)
#     - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN registry.gitlab.com
#   script:
#     - mv -f .env.dev .env
#     - echo $IMAGE_TAG_DEV
#     - docker build -t $IMAGE_TAG_DEV -t $CI_REGISTRY_IMAGE:dev-latest .
#     - docker push $IMAGE_TAG_DEV
#     - docker push $CI_REGISTRY_IMAGE:dev-latest

build_prod:
  stage: build_prod
  only:
    - production
  before_script:
    - export IMAGE_TAG_PROD=$CI_REGISTRY_IMAGE:pro-$(date +%Y%m%d_%H-%M)
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN registry.gitlab.com
  script:
    - mv -f .env.pro .env
    - echo $IMAGE_TAG_PROD
    - docker build -t $IMAGE_TAG_PROD -t $CI_REGISTRY_IMAGE:pro-latest .
    - docker push $IMAGE_TAG_PROD
    - docker push $CI_REGISTRY_IMAGE:pro-latest

deploy_dev:
  stage: deploy_dev
  only:
    - develop
  variables:
    GIT_STRATEGY: none
  before_script:
    - apk update && apk add openssh-client bash
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY};
  script:
    # Deployment script for development environment
    # chạy ssh-agent tương ứng với Gitlab Runner hiện tại
    - eval $(ssh-agent -s)
    # thêm nội dung của biến SSH_PRIVATE_KEY vào agent store
    - bash -c 'ssh-add <(echo "$SSH_PRIVATE_KEY_PRO_1")'
    #- echo $SSH_PRIVATE_KEY_PRO_2
    # tạo folder ~/.ssh
    - mkdir -p ~/.ssh
    
    # Scan lấy SSH Host key cho địa chỉ IP server
    # Được kết quả bao nhiêu thì thêm vào file known_hosts
    - ssh-keyscan -H $SSH_SERVER_IP_PRO_1 >> ~/.ssh/known_hosts
    
    # Sửa lại quyền của file known_hosts
    - chmod 644 ~/.ssh/known_hosts
    
    # Thực hiện SSH vào server, login vào Registry, chuyển tới folder project
    # Down project, pull image về, up project và xoá đi image cũ
    - >
      ssh $SSH_USER@$SSH_SERVER_IP_PRO_1
      "cd ${PATH_TO_PROJECT_DEV_SOURCE};
      git pull https://oauth2:${GITLAB_ACCESS_TOKEN}@gitlab.com/nomnom-team/clomart-backend --no-edit;"
    # - >
    #   ssh $SSH_USER@$SSH_SERVER_IP_PRO_1
    #   "docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}; cd ${PATH_TO_PROJECT_DEV};
    #   docker pull ${CI_REGISTRY_IMAGE}:dev-latest; docker-compose down; docker-compose up -d; docker image prune -f;"

deploy_prod:
  stage: deploy_prod
  only:
    - production
  variables:
    GIT_STRATEGY: none
  before_script:
    - apk update && apk add openssh-client bash
  script:
    # Deployment script for production environment
    # Customize this script based on your production deployment needs
    - eval $(ssh-agent -s)
    - bash -c 'ssh-add <(echo "$SSH_PRIVATE_KEY_PRO_3")'
    - mkdir -p ~/.ssh
    - ssh-keyscan -H $SSH_SERVER_IP_PRO_3 >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - > 
      ssh $SSH_USER@$SSH_SERVER_IP_PRO_3 
      "docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}; cd ${PATH_TO_PROJECT_PRO}; 
      docker pull ${CI_REGISTRY_IMAGE}:pro-latest; docker-compose down; docker-compose up -d; docker image prune -f;"
