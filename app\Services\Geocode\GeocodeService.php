<?php

namespace App\Services\Geocode;

use App\Jobs\GeocodeUpdateDataPlace;
use App\Jobs\GeocodeUpdateDataProperty;
use App\Jobs\GeocodeUpdateDataAddress;
use App\Jobs\ParseAddressGeocode;
use App\Jobs\GeocodeUpdateData;
use App\Jobs\UpdatePopularAddress;
use App\Jobs\ReduceGeocode;
use App\Geocode;
use Illuminate\Support\Facades\Auth;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use App\Services\Search\SearchService;
use Illuminate\Support\Facades\Log;

class GeocodeService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }


    //----search--------------
    public function search_by_latlong(array $data)
    {
        $radius = isset($data['radius']) ? $data['radius'] : 5;
        // 500m = 0.0045, 1km = 0.009
        $distance =0.009 * $radius;
        $geocodeModel = new Geocode();
        $geocodeModel->setConnection('pgsqlGeocode');
        $result = $geocodeModel->select('address','latitude','longitude','province_id','district_id','ward_id','unaccent_address','lowercase_address')
        ->whereBetween('latitude', [$data['latitude'] - $distance,$data['latitude'] + $distance])
        ->whereBetween('longitude', [$data['longitude'] - $distance,$data['longitude'] + $distance])
        // ->orderBy('latitude')
        // ->orderBy('longitude')
        ->selectRaw(
            "(1000 * 6371 * acos(
                LEAST(1, GREATEST(-1, 
                    cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + 
                    sin(radians(?)) * sin(radians(latitude))
                ))
            )) AS distance", [
                $data['latitude'], 
                $data['longitude'], 
                $data['latitude']
            ]
        )        
        ->orderBy('distance', 'asc');
        // ->get(['id','address', 'province_id', 'district_id', 'ward_id', 'longitude', 'latitude']);
        // foreach ($result as $key => $value) {
        //     $value['distance'] = $this->getDistance($data['latitude'],$data['longitude'],$value['latitude'],$value['longitude']);
        // }

        return $result->get()->take(3);
    }

    public function search_by_address($data)
    {
        $geocodeModel = new Geocode();
        $geocodeModel->setConnection('pgsqlGeocode');
        $searchText = Str::lower($data['search']);
        
        # Search level 1
        $result = $geocodeModel->select(
            'address',
            'latitude',
            'longitude',
            'province_id',
            'district_id',
            'ward_id',
            'unaccent_address',
            'lowercase_address')
            ->where('lowercase_address','like', "%".$searchText."%")
            // ->orderByRaw("CASE
            //     WHEN lowercase_address LIKE '".$searchText."' THEN 1
            //     WHEN lowercase_address LIKE '".$searchText."%' THEN 2
            //     WHEN lowercase_address LIKE '%".$searchText."%' THEN 3
            //     WHEN lowercase_address LIKE '%".$searchText."' THEN 4
            //     ELSE 5
            // END")
            // ->groupBy('lowercase_address')
            ->limit(5)
            ->offset(0)
            ->get();

        # Search level 2
        if($result->count() < 1){
            
            $searchText = Str::slug($searchText," ");    
            $result = $geocodeModel->select(
            'address',
            'latitude',
            'longitude',
            'province_id',
            'district_id',
            'ward_id',
            'unaccent_address',
            'lowercase_address')
            ->where('unaccent_address','like', "%".$searchText."%")
            // ->where('type','!=', 1)
            // ->where('enable', true)
            ->orderByRaw("CASE
                WHEN unaccent_address LIKE '".$searchText."' THEN 1
                WHEN unaccent_address LIKE '".$searchText."%' THEN 2
                WHEN unaccent_address LIKE '%".$searchText."%' THEN 3
                WHEN unaccent_address LIKE '%".$searchText."' THEN 4
                ELSE 5
            END")
            // ->groupBy('lowercase_address','unaccent_address')
            ->limit(5)
            ->offset(0)
            ->get();
        }


        # Search level 3
        if($result->count() < 1){
            $result = $geocodeModel->select(
                'address',
                'latitude',
                'longitude',
                'province_id',
                'district_id',
                'ward_id',
                'unaccent_address',
                'lowercase_address')
            ->selectRaw("similarity(unaccent_address, ?) AS similarity_score", [$searchText])
            ->whereRaw("similarity(unaccent_address, ?) > 0.25", [$searchText])
            // ->where([
                //     ['longitude', '>=', $data['longitude_s']],
                //     ['longitude', '<=', $data['longitude_b']],
                //     ['latitude', '>=', $data['latitude_s']],
                //     ['latitude', '<=', $data['latitude_b']]
                // ])
                // ->groupBy('lowercase_address','unaccent_address')
                ->orderByDesc('similarity_score')
                ->limit(5)
                ->offset(0)
                ->get();
            }
                

            


        return $result;
    }


    public function test()
    {
        $vertices_x = array(13.125, 13.134, 13.125, 13.116);
        $vertices_y = array(109.271, 109.280,109.289,109.280);
        $points_polygon = count($vertices_x);
        // $longitude_x = $_GET["longitude"];
        // $latitude_y = $_GET["latitude"];
        $longitude_x = 13.126943833563178;
        $latitude_y = 109.27672988370993;
        // 13.126943833563178, 109.27672988370993

        if ($this->is_in_polygon($points_polygon, $vertices_x, $vertices_y, $longitude_x, $latitude_y)){
            var_dump( "Is in polygon!");
        }
        else var_dump("Is not in polygon");
        die('123');
    }

    public function is_in_polygon($points_polygon, $vertices_x, $vertices_y, $longitude_x, $latitude_y)
    {
        $i = $j = $c = 0;
        for ($i = 0, $j = $points_polygon-1 ; $i < $points_polygon; $j = $i++) {

            var_dump(2);
            if ( (($vertices_y[$i] > $latitude_y != ($vertices_y[$j] > $latitude_y)) &&
            ($longitude_x < ($vertices_x[$j] - $vertices_x[$i]) * ($latitude_y - $vertices_y[$i]) / ($vertices_y[$j] - $vertices_y[$i]) + $vertices_x[$i]) ) )
            {
                $c = !$c;
                break;
            }
        }
        return $c;
    }

    //----------distance--------------
    public function getDistance($latitude, $longitude, $latitudeT, $longitudeT)
    {
        // $theta = $longitude - $longitude1;
        // $distance = (sin(deg2rad($longitude)) * sin(deg2rad($longitude1))) + (cos(deg2rad($longitude)) * cos(deg2rad($longitude1)) * cos(deg2rad($theta)));
        // $distance = acos($distance);
        // $distance = rad2deg($distance);
        // $distance = $distance * 60 * 1.1515* 1.609344;

        //km
        return round((((acos(
            sin($this->radians($latitude))
                * sin($this->radians($latitudeT))
                + cos($this->radians($latitude))
                * cos($this->radians($latitudeT))
                * cos($this->radians($longitude - $longitudeT))))
                * 180/3.1415926535897931)
                * 60 * 1.1515 * 1.609344),2);
    }

    //--------------------------
    public function radians($degrees)
    {
        return 0.017453292519943 * $degrees;
    }


    //-----------popular adress-------
    public function updatePopularAddress()
    {
        $result = new UpdatePopularAddress();
        dispatch($result);

        return $result;
    }

    //-------delete---------------
    public function delete(array $geocode)
    {
        $result = Geocode::find($geocode['id']);

        if(!$result)
        {
            return false;
        }

        try {
            $result->delete();
            $result->unsearchable();
        } catch (\Throwable $th) {
            return false;
            Log::info("server elasticsearch error");
        }


        $general = new GeneralService($this->_userId);
        $general->addLog(Config::get('constants.log_action.delete', 3),$geocode['id'],Config::get('constants.object_type.geocode', 2),$result,json_encode($geocode));

        return $result;
    }

    public function deleteObject(array $geocode)
    {
        $result = Geocode::where([
            ['object_id',$geocode['object_id']],
            ['object_type',$geocode['object_type']]
        ])->first();

        if(!$result)
        {
            return false;
        }

        try {
            $result->delete();
            $result->unsearchable();
        } catch (\Throwable $th) {
            return false;
            Log::info("server elasticsearch error");
        }

        $general = new GeneralService($this->_userId);
        $general->addLog(Config::get('constants.log_action.delete', 3),$result['id'],Config::get('constants.object_type.geocode', 2),$result,json_encode($geocode));

        return true;
    }

    //---------reduce geocode----------------
    public function reduce()
    {
        $result = new ReduceGeocode();
        dispatch($result);

        return [];
    }

    //-------insert----------------------
    public function insert(array $data)
    {
        $result = null;
        try {
            $result = Geocode::create($data);
            $result->searchable();
        } catch (\Throwable $th) {
            Log::info("server elasticsearch error");
        }

        return $result;
    }

    //---------update------------------
    public function update(array $data)
    {
        $result = Geocode::where([
            ['object_id',$data['object_id']],
            ['object_type',$data['object_type']]
        ])->first();

        if(!$result)
        {
            $this->insert($data);
        }
        else {
            try {
                $result->update($data);
                $result->searchable();
            } catch (\Throwable $th) {
                Log::info("server elasticsearch error");
            }
            // $result->searchable();
        }

        // Geocode::where([
        //     ['object_id',$data['object_id']],
        //     ['object_type',$data['object_type']]
        // ])->update($data);

        return true;
    }
}
