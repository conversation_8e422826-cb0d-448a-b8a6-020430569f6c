<?php

namespace App\Admin\Selectable;

use App\Image;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Grid\Selectable;
use Encore\Admin\Grid\Tools\QuickCreate;
class Logo extends Selectable
{
    public $model = Image::class;

    public function make()
    {
        // $this->column('id');
        $this->column('title');
        $this->column('path','path')->image();
        $this->model()->orderBy('created_at', 'desc');
        // $this->column('object_type','object_type');


        $this->filter(function (Filter $filter) {
            $filter->like('title');
            $filter->in('object_type', 'object_type')->select(config('constants.image_object_type_admin'));
        });
    }
}