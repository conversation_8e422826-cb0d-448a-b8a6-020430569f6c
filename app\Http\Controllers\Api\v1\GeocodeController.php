<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\Geocode\GeocodeService;
use App\Http\Requests\Geocode\GeocodeRequest;
use App\Http\Requests\Geocode\GeocodeDeleteRequest;
use JWTAuth;

class GeocodeController extends Controller
{
    //---------update data adress-------------
    public function updateAddress(GeocodeService $service)
    {
        $result = $service->updateAddress();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [

            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //---------update data place-------------
    public function updatePlace(GeocodeService $service)
    {
        $result = $service->updatePlace();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [

            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //---------update data property-------------
    public function updateProperty(GeocodeService $service)
    {
        $result = $service->updateProperty();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [

            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //---------parse adress-------------
    public function parseAddress(GeocodeService $service)
    {
        $result = $service->parseAddress();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [

            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------------update data geocode----------
    public function updateData(GeocodeService $service)
    {
        $result = $service->updateData();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [

            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //----------search----------------------
    public function search(GeocodeRequest $request, GeocodeService $service)
    {
        $data = $request->only([
            'longitude',
            'latitude',
            'radius'
        ]);

        $result = $service->search($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function test(GeocodeService $service)
    {
        $result = $service->test();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function updatePopularAddress(GeocodeService $service)
    {
        $result = $service->updatePopularAddress();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [
                'data' => []
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }


    //---------search-----------------------
    public function searchAddress(Request $request)
    {
        $data = $request->only([
            'search'
        ]);

        $user = JWTAuth::getToken() && JWTAuth::check() ? JWTAuth::toUser(JWTAuth::getToken()) : null;
        $service = $user ? new GeocodeService($user->id) : new GeocodeService();

        $result = $service->searchAddress($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //---------delete--------------
    public function delete(GeocodeDeleteRequest $request, GeocodeService $service)
    {
        $data = $request->only([
            'id'
        ]);

        $result = $service->delete($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'  => [
                    'Geocode_003_E_009'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------reduce geocode--------------
    public function reduce(GeocodeService $service)
    {

        $result = $service->reduce();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body'  => [
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
