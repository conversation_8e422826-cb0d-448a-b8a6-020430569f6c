<?php

namespace App\Http\Controllers\Api\v2;

use App\Http\Requests\POS\POSConnectRequest;
use App\Http\Requests\POS\POSListRequest;
use App\POSConnection;
use App\Services\GeneralService;
use App\Services\Order\OrderService;
use App\Services\POS\POSService;
use App\Shop;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\JsonResponse;

class POSController extends Controller
{
    function connect (POSConnectRequest $request, POSService $service)
    {
        $user_id = Auth::user()->id;
        $data = $request->all();

        if(!GeneralService::checkShopOwner($user_id, $data['shop_id'])){
            return response()->json([
                'status' => JsonResponse::HTTP_FORBIDDEN,
                'body'   => [
                    'message'=> 'Forbidden'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $pos = $service->setPOS($data['pos_type'], $data['shop_id']);

        $data_connect = $data['data'];
        $data_connect['shop_id'] = $data['shop_id'];

        $connect = $pos->connect($data_connect);
        if(!$connect){
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'   => [
                    'message' => 'Connection failed',
                ],
            ], JsonResponse::HTTP_OK);
        }
        return response()->json([
            'status' => JsonResponse::HTTP_CREATED,
            'body'   => [
                'message' => 'Connection succeeded',
            ],
        ], JsonResponse::HTTP_OK);
    }

    function listBranch(POSListRequest $request, POSService $service)
    {
        $pos = $service->setPOS($request['pos_type'], $request['shop_id']);
        $branches =  $pos->listBranch();
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => [
                'data'   => $branches
            ],
        ], JsonResponse::HTTP_OK);
    }

    function listProduct(POSListRequest $request, POSService $service)
    {
        $pos = $service->setPOS($request['pos_type'], $request['shop_id']);
        $products = $pos->listProducts();
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => [
                'data'   => $products
            ]
        ], JsonResponse::HTTP_OK);
    }

    function createOrder(Request $request, OrderService $service, POSService $posService)
    {
        $data = [
            'order_id' => $request['order_id'],
            'voucher'  => false
        ];
        $pos = $posService->setPOS($request['pos_type'], $request['shop_id']);
        $order = $pos->createOrder($data, $service);
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => [
                'data'   => $order
            ]
        ], JsonResponse::HTTP_OK);
    }

    function updateOrder(Request $request, OrderService $service, POSService $posService)
    {
        $data = [
            'order_id' => $request['order_id'],
            'voucher'  => false
        ];
        $pos = $posService->setPOS($request['pos_type'], $request['shop_id']);
        $order = $pos->update($data, $service);
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => [
                'data'   => $order
            ],
        ], JsonResponse::HTTP_OK);
    }

    function setBranch(Request $request)
    {
        $data = $request->only(['shop_id', 'branch_id', 'pos_type']);
        $userId = Auth::user()->id;
        $connection = POSConnection::where([
            'shop_id' => $data['shop_id'],
            'pos_type' => $data['pos_type'],
        ])->first();
        if(!$connection){
            return response()->json([
                'status' => JsonResponse::HTTP_NOT_FOUND,
                'body'   => [
                    'message' => 'Connection not found'
                ]
            ], JsonResponse::HTTP_OK);
        }

        if(!GeneralService::checkShopOwner($userId, $data['shop_id'])){
            return response()->json([
                'status' => JsonResponse::HTTP_FORBIDDEN,
                'body'   => [
                    'message'=> 'Forbidden'
                ],
            ], JsonResponse::HTTP_OK);
        }

        $connection->update(['branch_id' => $data['branch_id']]);
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body'   => [
                'data'   => $connection
            ]
        ], JsonResponse::HTTP_OK);
    }
}
