<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class NotifyListRequest extends BaseRequest
{

    public function rules()
    {
        return [
            // 'id' => 'required|uuid|exists:users,id',
            'offset' => 'nullable|integer|min:0',
            'limit' => 'nullable|integer|min:0'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'User_001_E_009',
    //         'id.uuid' => 'User_002_E_010',
    //         'id.exists' => 'User_003_E_011',

    //         'limit.integer' => 'User_002_E_060',
    //         'limit.min'     => 'User_004_E_061',

    //         'offset.integer' => 'User_002_E_062',
    //         'offset.min'    => 'User_004_E_063'
    //     ];
    // }
}
