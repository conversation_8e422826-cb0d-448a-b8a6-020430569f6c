<?php

namespace App\Http\Requests\Stock;

use App\Http\Requests\BaseRequest;

class StockImportRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'product_id' => 'required|uuid|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'unit' => 'nullable|string|max:50',
            'unit_price' => 'nullable|numeric|min:0',
            'note' => 'nullable|string|max:1000'
        ];
    }
}
