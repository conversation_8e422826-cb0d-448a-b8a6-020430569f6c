<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class OTPConfirmRequest extends BaseRequest
{

    public function rules()
    {
        return [
            // 'session_id' => 'required|uuid',
            'phone'     => 'required_without:email|digits_between:10,11',
            'email'     => 'required_without:phone|email:rfc,dns',
            'otp'       => 'required|digits:6'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'phone.required_without' => 'OTP_001_E_001',
    //         'email.required_without' => 'OTP_001_E_002',
    //         'otp.required'           => 'OTP_001_E_003',
    //     ];
    // }
}
