<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UserSupportRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'name'      => 'required|max:255',
            'email'     => 'nullable|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u',
            'phone'     => 'nullable|max:255',
            'request'   => 'required',
            'content'   => 'nullable|max:5000'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'name.required'     => 'User_001_E_001',

    //         'email.required'    => 'User_001_E_003',
    //         'email.regex'       => 'User_002_E_004',

    //         'phone.required'    => 'User_001_E_024',
    //         'phone.digits_between' => 'User_004_E_025',

    //         'request.required'  => 'User_001_E_069',

    //         'name.max'          => 'User_004_E_070',

    //         'email.max'         => 'User_004_E_071',

    //         'phone.max'         => 'User_004_E_072',

    //         'content.max'       => 'User_004_E_073',
    //     ];
    // }
}
