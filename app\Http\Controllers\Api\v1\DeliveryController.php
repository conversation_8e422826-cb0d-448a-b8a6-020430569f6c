<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\Delivery\DeliveryService;
use App\Services\DeliveryPartner\DeliveryErrorService;
use App\Services\GeneralService;
use App\User;
use Illuminate\Support\Facades\Log;
use App\Order;
use App\Delivery;
use App\Http\Requests\Delivery\DeliveryRequest;
use App\Http\Requests\Delivery\CreateDeliveryRequest;
use App\Http\Requests\Delivery\UpdateDeliveryRequest;
use App\Http\Requests\Delivery\UpdateDriverIdRequest;
use App\Http\Requests\Delivery\FindDriverRequest;
use App\Http\Requests\Delivery\DriverActionRequest;
use Elasticsearch\Endpoints\Indices\Exists;

class DeliveryController extends Controller
{
    //-------create delivery-------------
    public function create(CreateDeliveryRequest $request, DeliveryService $service)
    {
        $errorService = new DeliveryErrorService();

        try {
            $data = $request->validated();

            // Log the delivery creation attempt
            $errorService->logApiCall('internal', $data['order_id'] ?? 'unknown', 'v1/delivery/create', $data);

            $result = $service->create($data);

            if($result === false) {
                // Log the failure
                $errorService->logAndNotify(
                    'DELIVERY_SERVICE_FAILED',
                    'DeliveryService create method returned false',
                    'internal',
                    $data['order_id'] ?? 'unknown',
                    [
                        'request_data' => $data,
                        'api_endpoint' => 'v1/delivery/create'
                    ]
                );

                return response()->json([
                    'status' => JsonResponse::HTTP_BAD_REQUEST,
                    'body' => [
                        'message' => 'Failed to create delivery'
                    ]
                ], JsonResponse::HTTP_OK);
            }

            // Log successful creation
            $errorService->logSuccess('internal', $data['order_id'] ?? 'unknown', $result['id'] ?? 'unknown', [
                'delivery_data' => $result
            ]);

            return response()->json([
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ]
            ], JsonResponse::HTTP_OK);

        } catch (\Exception $e) {
            // Log the exception
            $errorService->logAndNotify(
                'DELIVERY_CONTROLLER_EXCEPTION',
                'Exception occurred in DeliveryController create method: ' . $e->getMessage(),
                'internal',
                $data['order_id'] ?? 'unknown',
                [
                    'request_data' => $data ?? [],
                    'api_endpoint' => 'v1/delivery/create'
                ],
                $e
            );

            return response()->json([
                'status' => JsonResponse::HTTP_INTERNAL_SERVER_ERROR,
                'body' => [
                    'message' => 'Internal server error occurred while creating delivery'
                ]
            ], JsonResponse::HTTP_OK);
        }
    }

    //-------update delivery-------------
    public function update(UpdateDeliveryRequest $request, DeliveryService $service)
    {
        $data = $request->validated();

        $delivery = Delivery::find($data['id']);
        if (!$delivery) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'This delivery does not exist.'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $userId = Auth::user()->id;
        if ($userId !== $delivery->driver_id && !GeneralService::checkShopOwnerDelivery($userId, $data['id']) && !GeneralService::checkShopAgentDelivery($userId, $data['id'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'You are not authorized to update this delivery.'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $result = $service->update($data);

        if($result === false) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'Failed to update delivery'
                ]
            ], JsonResponse::HTTP_OK);
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ], JsonResponse::HTTP_OK);
    }
    //-------detail delivery-------------
    public function detail($id = null, DeliveryService $service)
    {
        $result = $service->detail($id);
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //-------detail delivery by order id-------------
    public function detail_by_order_id($order_id = null, DeliveryService $service)
    {
        $result = $service->detail_by_order_id($order_id);
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //-------check price base on distance-----------
    public function check_price(DeliveryRequest $request, DeliveryService $service)
    {
        $data = $request->only([
            'distance',
            'duration',
            'address',
            'user_id',
            'latitude',
            'longitude',
            'province_id',
            'district_id',
            'ward_id',
            'language',
            'currency',
        ]);

        $result = $service->checkPrice($data);
        if($result === false)
        {
            $response = [
                'status'  => JsonResponse::HTTP_BAD_REQUEST,
                'message' => 'error when calculate delivery fee.',
                'errors'  => [
                    "error_message" => "error when calculate delivery fee",
                    "field"         => "delivery_price",
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }



        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
            'message' => $result === null ? 'Need to contact the shop for delivery price.' : 'Success'
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //-------find driver-----------
    public function find_driver(FindDriverRequest $request, DeliveryService $service)
    {
        $data = $request->only([
            'radius',
            'address',
            'user_id',
            'shop_id',
            'latitude',
            'longitude',
            'latitude_s',
            'longitude_s',
            'latitude_b',
            'longitude_b',
            'province_id',
            'district_id',
            'ward_id',
        ]);

        $result = $service->findDriver($data);
        if(!$result)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => 'error when finding driver.',
                'errors' => [
                    "error_message"=>"error when finding driver",
                    "field"=>"id",
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }



        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update delivery-----------
    public function update_driver_id(UpdateDriverIdRequest $request, DeliveryService $service)
    {
        $data = $request->only([
            'id',
            'driver_id'
        ]);

        $result = $service->updateDriverId($data);
        if(!$result)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => 'error when update driver_id.',
                'errors' => [
                    "error_message"=>"error when update driver_id",
                    "field"=>"id",
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }



        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //-------update delivery status-----------
    public function driverAction(DriverActionRequest $request, DeliveryService $service)
    {
        $driver = User::find(Auth::user()->id);
        if (!$driver || $driver->role_id !== 5) {
            return response()->json([
                'status' => JsonResponse::HTTP_FORBIDDEN,
                'message' => 'Unauthorized access. Only drivers can do action.',
            ], JsonResponse::HTTP_FORBIDDEN);
        }

        $data = $request->only('delivery_id','action','status','notes', 'latitude', 'longitude');

        $result = true;

        switch ($data['action']) {
            case 'update_delivery_status':
                $delivery = Delivery::find($data['delivery_id']);
                // if (!$delivery) {
                //     return response()->json([
                //         'status' => JsonResponse::HTTP_NOT_FOUND,
                //         'message' => 'Delivery not found.',
                //     ], JsonResponse::HTTP_NOT_FOUND);
                // }

                if ($delivery->driver_id !== $driver->id) {
                    return response()->json([
                        'status' => JsonResponse::HTTP_FORBIDDEN,
                        'message' => 'You are not authorized to update this delivery.',
                    ], JsonResponse::HTTP_FORBIDDEN);
                }
                $result = $service->updateStatus($data);
                if($data['status'] >=2 && $data['status'] <=5){
                    //1 = pending; 2 = confirmed; 3 = prepared; 4 = picked_up; 5 = in_transit; 6 = delivered; 7 = cancelled; 8 = failed;
                    $driver->user_status = 2; //0 offline, 1 online; 2 busy on delivery
                    $driver->last_action_at = now();
                    $result = $driver->save();
                }
                $service->updateDriverLocation($driver, $data['longitude'], $data['latitude']);
                break;
            case 'set_online':
                if($driver->user_status != 1){
                    $driver->user_status = 1;
                    $driver->last_action_at = now();
                    $result = $driver->save();
                }
                $service->updateDriverLocation($driver, $data['longitude'], $data['latitude']);
                break;
            case 'set_offline':
                if($driver->user_status != 0){
                    $driver->user_status = 0;
                    $driver->last_action_at = now();
                    $result = $driver->save();
                }
                break;
            case 'moving':
                if($driver->user_status != 0){
                    $service->updateDriverLocation($driver, $data['longitude'], $data['latitude']);
                }
                break;

            default:
                return response()->json([
                    'status' => JsonResponse::HTTP_BAD_REQUEST,
                    'message' => 'Invalid action.',
                ], JsonResponse::HTTP_BAD_REQUEST);
        }


        if (!$result) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => 'Error when updating delivery status.',
                'errors' => [
                    "error_message" => "Error when updating delivery status",
                    "field" => "status",
                ]
            ], JsonResponse::HTTP_BAD_REQUEST);
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ], JsonResponse::HTTP_OK);
    }

    //-------get deliveries by driver-----------
    public function getDeliveriesByDriver(Request $request, DeliveryService $service)
    {
        $driver = Auth::user();
        if (!$driver || $driver->role_id !== 5) {
            $response = [
                'status' => JsonResponse::HTTP_FORBIDDEN,
                'message' => 'Unauthorized access. Only drivers can access this endpoint.',
            ];
            return response()->json($response, JsonResponse::HTTP_FORBIDDEN);
        }

        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);
        $start_date = $request->input('start_date', null);
        $end_date = $request->input('end_date', null);
        $status = $request->input('status', null);
        $data = [
            'limit' => $limit,
            'offset' => $offset,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'status' => $status,
        ];
        $result = $service->getDeliveriesByDriverId($driver->id, $data);


        if (!$result) {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => 'Error when fetching deliveries.',
                'errors' => [
                    "error_message" => "Error when fetching deliveries",
                    "field" => "driver_id",
                ]
            ];
            return response()->json($response, JsonResponse::HTTP_BAD_REQUEST);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result['result'],
                'total' => $result['count'],
                'limit' => $limit,
                'offset' => $offset
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
