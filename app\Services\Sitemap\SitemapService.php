<?php
namespace App\Services\Sitemap;

use App\Report;
use App\Property;
use App\PropertyType;
use App\Province;
use App\Facility;
use App\Direction;
use App\Place;
use App\User;
use App\Image;
use Illuminate\Support\Facades\Auth;
use App\Services\GeneralService;
use App\Services\Image\ImageService;
use App\Services\Mqtt\MqttChatService;
use App\Jobs\ActionMark;
use Illuminate\Support\Facades\Log;
use SimpleXMLElement;
use Response;
use DateTimeZone;
use DateTime;
use Carbon\Carbon;
use App\Helpers\S3Utils;
use Illuminate\Support\Facades\Redis;
use App\Jobs\Sitemap;
use Illuminate\Support\Facades\Config;
class SitemapService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    public function getSlug(array $sitemap)
    {
        S3Utils::deleteIfExist(env('URL_IMAGE').env('ZIP_SITEMAP_PATH'));
        $offset = isset($sitemap['offset']) ? $sitemap['offset'] : 0;
        $limit = isset($sitemap['limit']) ? $sitemap['limit'] : 50000;
        $user = User::find($this->_userId);
        if($user)
        {
            $importFile = new Sitemap($user->id);
        }
        else
        {
            $importFile = new Sitemap();
        }
        $redis = GeneralService::redisConnection();

        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        Log::info("Run Queue Create Sitemap");
        $queueStatus = $redis->get("create_sitemap_queue_status");
        if(!$queueStatus || $queueStatus == "DONE"){
            Log::info("queueStatus::Run::");
            dispatch($importFile);
        }else{
            Log::info("queueStatus::Already::");
        }
        return [];
    }
    public function saveXML($name,$data,$stt)
    {
        $colection = collect($data);
        $data = explode(',',$colection->implode('slug',','));
 
        $listSitemap_index = [];
 

                $name = str_replace(["/","_"],"-",$name);
                $fileName = $name.'-sitemap-'.$stt.'.xml.gz';
                header('content-type: application/gzip');
                header('Content-Encoding: gzip');
                header('Accept-Encoding: gzip, deflate');
                header('Content-Transfer-Encoding', 'binary');
                header('Content-Disposition: attachment; filename='.$fileName);
                $xml = '<?xml version="1.0" encoding="UTF-8"?>';
                $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
               
                foreach ($data as $key => $value) {
                    $xml .= '<url>';
                    $xml .= '<loc>'.env('CLIENT_DEV_URL').env('VIEW_PROPERTY').$value.'</loc>';
                    $xml .= '<lastmod>'.Carbon::now()->toIso8601ZuluString().'</lastmod>';
                    $xml .= '</url>';
                }
                $xml .= '</urlset>';
                
                $url = 'uploads/sitemap/'.$fileName;
                Log::info( $url);
                S3Utils::upload(gzencode($xml,9),$url);

                array_push($listSitemap_index,$url);
            // }
        // }
        return $listSitemap_index;
    }
    public function saveXML2($name,$data)
    {
        $sitemap = \App::make('sitemap');

        foreach ($data as $p) {
            
            $sitemap->add($p->slug, Carbon::now()->toIso8601ZuluString(),'0.8','daily');
            
        }
        $sitemap->store('xml', 'mysitemap');
        exit;
    }
    public function saveZip(array $sitemap)
    {
        $zipname = 'file.zip';
        $zip = new \ZipArchive;
        $zip->open($zipname, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

        foreach ($sitemap as $key => $value) {
            $zip->addFromString(basename(env('S3_HOST_PATH').$value),file_get_contents( env('S3_HOST_PATH').$value));
        }
        $zip->close();

        header('Content-Type: application/zip');
        header('Content-disposition: attachment; filename='.$zipname);
        header('Content-Length: ' . filesize($zipname));
        // readfile($zipname);

        S3Utils::upload(file_get_contents($zipname),'uploads/sitemap/sitemap.zip');
        Log::info("upload sitemap zip");
        return true;
    }
    public function saveSitemapIndex($RType,$PType,array $sitemap)
    {

        $listSitemap_index = [];
        Log::info(count($sitemap));
        // create xml file
        $name = $RType."-".$PType;
        $fileName = str_replace("_","-",$name).'_index.xml.gz';
        header('content-type: application/x-gzip');
        header('Content-Encoding: gzip');
        header('Accept-Encoding: gzip, deflate');
        header('Content-Transfer-Encoding', 'binary');
        header('Content-Disposition: attachment; filename='.$fileName);
       
        $xml = '<?xml version="1.0" encoding="utf-8"?><sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
        foreach ($sitemap as $key => $value) {
            // $url = $xml->addChild('sitemap');
            // $value = str_replace("uploads/sitemap/","",$value);
            // $url->addChild('loc',env('CLIENT_DEV_URL').env('SITEMAP_FOLDER').$value);
            // $url->addChild('lastmod',Carbon::now()->toIso8601ZuluString());
            $xml .= '<sitemap>';
            $value = str_replace("uploads/sitemap/","",$value);
            $xml .= '<loc>'.env('CLIENT_DEV_URL').env('SITEMAP_FOLDER').$value.'</loc>';
            $xml .= '<lastmod>'.Carbon::now()->toIso8601ZuluString().'</lastmod>';
            $xml .= '</sitemap>';
        }
        $xml .= '</sitemapindex>';
        
        // $xml->saveXML($fileName);

        // header('Cache-Control', 'public');
        // header('Content-Description', 'File Transfer');
        // header('Content-Disposition', 'attachment; filename='.$fileName);
        // header('Content-Transfer-Encoding', 'binary');
        // header('Content-Type', 'text/xml');

        $url = 'uploads/sitemap/'.$fileName;
        Log::info( $url);
        S3Utils::upload(gzencode($xml,9),$url);

        array_push($listSitemap_index,$url);
        // unlink(public_path($fileName));

        return $listSitemap_index;
    }
    public function saveIndex(array $sitemap, $requestType)
    {
        $listSitemap_index = [];

        // create xml file
        $fileName = $requestType.'.xml.gz';
        header('content-type: application/x-gzip');
        header('Content-Encoding: gzip');
        header('Accept-Encoding: gzip, deflate');
        header('Content-Transfer-Encoding', 'binary');
        header('Content-Disposition: attachment; filename='.$fileName);
        $xml = '<?xml version="1.0" encoding="utf-8"?><sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
        foreach ($sitemap as $key => $value) {
            // $url = $xml->addChild('sitemap');
            // $value = str_replace("uploads/sitemap/","",$value);
            // $url->addChild('loc',env('CLIENT_DEV_URL').env('SITEMAP_FOLDER').$value);
            // $url->addChild('lastmod',Carbon::now()->toIso8601ZuluString());
            
            $xml .= '<sitemap>';
            $value = str_replace("uploads/sitemap/","",$value);
            $xml .= '<loc>'.env('CLIENT_DEV_URL').env('SITEMAP_FOLDER').$value.'</loc>';
            $xml .= '<lastmod>'.Carbon::now()->toIso8601ZuluString().'</lastmod>';
            $xml .= '</sitemap>';
        }
        $xml .= '</sitemapindex>';

        
        // $xml->saveXML($fileName);

        // header('Cache-Control', 'public');
        // header('Content-Description', 'File Transfer');
        // header('Content-Disposition', 'attachment; filename='.$fileName);
        // header('Content-Transfer-Encoding', 'binary');
        // header('Content-Type', 'text/xml');

        $url = 'uploads/sitemap/'.$fileName;
        Log::info( $url);
        S3Utils::upload(gzencode($xml,9),$url);

        array_push($listSitemap_index,$url);
        // unlink(public_path($fileName));

        return $listSitemap_index;
    }
    
    public function saveSitemapLocation1(array $sitemap,$slug)
    {
        Log::info($slug."-".count($sitemap));
        $amount_sitemap_each_file = Config::get('constants.amount_sitemap_each_file', 45000);
        Log::info("amount".$amount_sitemap_each_file);
        $loop = floor(count($sitemap) / $amount_sitemap_each_file);
        $soDu = count($sitemap) % $amount_sitemap_each_file;
        $listSitemap_index = [];
        $amount = 0;
        $array = [];
        if($soDu > 0)
        {
            $loop += 1;
        }
        if($loop > 0){
            
            for ($i=1; $i <= $loop ; $i++) 
            {
                array_push($array,array_splice($sitemap, $amount, $amount_sitemap_each_file));
            }
            for ($i=1; $i <= $loop ; $i++) 
            {
                Log::info(count($array[$i-1]));
                $fileName = $slug."-".$i.'.xml.gz';
                header('content-type: application/x-gzip');
                header('Content-Encoding: gzip');
                header('Accept-Encoding: gzip, deflate');
                header('Content-Transfer-Encoding', 'binary');
                header('Content-Disposition: attachment; filename='.$fileName);
               
                $xml = '<?xml version="1.0" encoding="UTF-8"?>';
                $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
                foreach ($array[$i-1] as $key => $value) {
                    // $url = $xml->addChild('url');
                    // $url->addChild('loc',env('CLIENT_DEV_URL').env('VIEW_LOCATION').$value);
                    // $url->addChild('lastmod', Carbon::now()->toIso8601ZuluString());
                    
                    $xml .= '<url>';
                    $xml .= '<loc>'.env('CLIENT_DEV_URL').env('VIEW_LOCATION').$value.'</loc>';
                    $xml .= '<lastmod>'.Carbon::now()->toIso8601ZuluString().'</lastmod>';
                    $xml .= '</url>';
                }
                $xml .= '</urlset>';
                
                // $xml->saveXML($fileName);

                // header('Cache-Control', 'public');
                // header('Content-Description', 'File Transfer');
                // header('Content-Disposition', 'attachment; filename='.$fileName);
                // header('Content-Transfer-Encoding', 'binary');
                // header('Content-Type', 'text/xml');

                $url = 'uploads/sitemap/'.$fileName;
                Log::info( $url);
                S3Utils::upload(gzencode($xml,9),$url);
                // S3Utils::upload($xml->asXML(),$url);

                array_push($listSitemap_index,$url);
            }
        }
        return $listSitemap_index;
    }
    public function saveSitemapLocation(array $sitemap,$slug)
    {
        Log::info($slug."-".count($sitemap));
        $amount_sitemap_each_file = Config::get('constants.amount_sitemap_each_file', 45000);
        Log::info("amount".$amount_sitemap_each_file);
        $loop = floor(count($sitemap) / $amount_sitemap_each_file);
        $soDu = count($sitemap) % $amount_sitemap_each_file;
        $listSitemap_index = [];
        $amount = 0;
        $array = [];
        if($soDu > 0)
        {
            $loop += 1;
        }
        if($loop > 0){
            
            for ($i=1; $i <= $loop ; $i++) 
            {
                array_push($array,array_splice($sitemap, $amount, $amount_sitemap_each_file));
            }
            for ($i=1; $i <= $loop ; $i++) 
            {
                Log::info(count($array[$i-1]));
                $fileName = $slug."-".$i.'.xml.gz';
                header('content-type: application/x-gzip');
                header('Content-Encoding: gzip');
                header('Accept-Encoding: gzip, deflate');
                header('Content-Transfer-Encoding', 'binary');
                header('Content-Disposition: attachment; filename='.$fileName);
               
                $xml = '<?xml version="1.0" encoding="UTF-8"?>';
                $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
                foreach ($array[$i-1] as $key => $value) {
                
                    $xml .= '<url>';
                    $xml .= '<loc>'.env('CLIENT_DEV_URL').env('VIEW_LOCATION').$value.'</loc>';
                    $xml .= '<lastmod>'.Carbon::now()->toIso8601ZuluString().'</lastmod>';
                    $xml .= '</url>';
                }
                $xml .= '</urlset>';
                
                $url = 'uploads/sitemap/'.$fileName;
                Log::info( $url);
                S3Utils::upload(gzencode($xml,9),$url);
                // S3Utils::upload($xml->asXML(),$url);

                array_push($listSitemap_index,$url);
            }
        }
        return $listSitemap_index;
    }
    public function saveZipLocation(array $sitemap,$name)
    {
        $zipname = 'file_location.zip';
        $zip = new \ZipArchive;
        $zip->open($zipname, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

        foreach ($sitemap as $key => $value) {
            $zip->addFromString(basename(env('S3_HOST_PATH').$value),file_get_contents( env('S3_HOST_PATH').$value));
        }
        $zip->close();

        header('Content-Type: application/zip');
        header('Content-disposition: attachment; filename='.$zipname);
        header('Content-Length: ' . filesize($zipname));
        // readfile($zipname);

        S3Utils::upload(file_get_contents($zipname),'uploads/sitemap/'.$name.'.zip');
        Log::info("upload sitemap location zip");
        return true;
    }
    public function saveSitemapIndexLocation(array $sitemap,$name)
    {

        $listSitemap_index = [];

        // create xml file
        $fileName = $name.'.xml.gz';
        header('content-type: application/x-gzip');
        header('Content-Encoding: gzip');
        header('Accept-Encoding: gzip, deflate');
        header('Content-Transfer-Encoding', 'binary');
        header('Content-Disposition: attachment; filename='.$fileName);

        $xml = '<?xml version="1.0" encoding="utf-8"?><sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
        foreach ($sitemap as $key => $value) {
            // $url = $xml->addChild('sitemap');
            // $value = str_replace("uploads/sitemap/","",$value);
            // $url->addChild('loc',env('CLIENT_DEV_URL')."sitemap/catalog/".$value);
            // $url->addChild('lastmod',Carbon::now()->toIso8601ZuluString());
            $xml .= '<sitemap>';
            $value = str_replace("uploads/sitemap/","",$value);
            $xml .= '<loc>'.env('CLIENT_DEV_URL')."sitemap/catalog/".$value.'</loc>';
            $xml .= '<lastmod>'.Carbon::now()->toIso8601ZuluString().'</lastmod>';
            $xml .= '</sitemap>';
        }
        $xml .= '</sitemapindex>';
        // $name = $RType."-".$PType;
        
        // $xml->saveXML($fileName);

        // header('Cache-Control', 'public');
        // header('Content-Description', 'File Transfer');
        // header('Content-Disposition', 'attachment; filename='.$fileName);
        // header('Content-Transfer-Encoding', 'binary');
        // header('Content-Type', 'text/xml');

        $url = 'uploads/sitemap/'.$fileName;
        Log::info( $url);
        // S3Utils::upload($xml->asXML(),$url);
        S3Utils::upload(gzencode($xml,9),$url);

        array_push($listSitemap_index,$url);
        // unlink(public_path($fileName));

        return $listSitemap_index;
    }
    public function saveSitemapCatalog2(array $sitemap,$name)
    {

        $listSitemap_index = [];

        // create xml file
        $fileName = $name.'.xml.gz';
        header('content-type: application/x-gzip');
        header('Content-Encoding: gzip');
        header('Accept-Encoding: gzip, deflate');
        header('Content-Transfer-Encoding', 'binary');
        header('Content-Disposition: attachment; filename='.$fileName);

        $xml = '<?xml version="1.0" encoding="utf-8"?><sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
        foreach ($sitemap as $key => $value) {
            // $url = $xml->addChild('sitemap');
            // $value = str_replace("uploads/sitemap/","",$value);
            // $url->addChild('loc',env('CLIENT_DEV_URL')."sitemap/catalog/".$value);
            // $url->addChild('lastmod',Carbon::now()->toIso8601ZuluString());
            $xml .= '<sitemap>';
            $value = str_replace("uploads/sitemap/","",$value);
            $xml .= '<loc>'.env('CLIENT_DEV_URL')."sitemap/catalog/".$value.'</loc>';
            $xml .= '<lastmod>'.Carbon::now()->toIso8601ZuluString().'</lastmod>';
            $xml .= '</sitemap>';
        }
        $xml .= '</sitemapindex>';
        // $name = $RType."-".$PType;
        
        // $xml->saveXML($fileName);

        // header('Cache-Control', 'public');
        // header('Content-Description', 'File Transfer');
        // header('Content-Disposition', 'attachment; filename='.$fileName);
        // header('Content-Transfer-Encoding', 'binary');
        // header('Content-Type', 'text/xml');

        $url = 'uploads/sitemap/'.$fileName;
        Log::info( $url);
        // S3Utils::upload($xml->asXML(),$url);
        S3Utils::upload(gzencode($xml,9),$url);

        array_push($listSitemap_index,$url);
        // unlink(public_path($fileName));

        return $listSitemap_index;
    }
    public function notificationInterval(array $data)
    {
        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        switch ($data['name']) {
            case 'sitemap':
                $queueStatus = $redis->get("create_sitemap_queue_status");
                $exists = S3Utils::fileExist("uploads/sitemap/sitemap.zip");

                if($queueStatus == 'DONE' && $exists)
                {
                    $result = true;
                }
                else {
                    $result = false;
                }
                break;
            case 'propertyImport':
                $queueStatus = $redis->get("import_property_excel_queue_status");
                if($queueStatus == 'DONE')
                {
                    $result = true;
                }
                else {
                    $result = false;
                }
                break;
            case 'postImport':
                $queueStatus = $redis->get("import_post_excel_queue_status");
                if($queueStatus == 'DONE')
                {
                    $result = true;
                }
                else {
                    $result = false;
                }
                break;
            case 'placeImport':
                $queueStatus = $redis->get("import_place_excel_queue_status");
                if($queueStatus == 'DONE')
                {
                    $result = true;
                }
                else {
                    $result = false;
                }
                break;

            default:
                $result = [];
                break;
        }
        return $result;
    }
    public function objectToArray($data)
    {   
        $dataReturn = [];
        foreach ($data as $key => $value) {
            array_push($dataReturn,$value) ;
        }
        return $dataReturn;
    }
}
