<?php

namespace App\Services\Log;

use Illuminate\Filesystem\Filesystem;

class LogSendMailService
{
    private $_pathLog;
    private $_logMessage;
    public function __construct()
    {
            $this->_pathLog      = base_path('storage/logs/sendmail-'.date('Y-m-d').'.log');
            $this->_logMessage   = new Filesystem();
    }

    public function logSendMail($data, $mess){
    $this->_logMessage->append($this->_pathLog, "======================================== SEND MAIL log ==================================\n");
            $this->_logMessage->append($this->_pathLog,
                                        date('Y-m-d H:i:s') ."\n".
                                        json_encode($data) ."\n" . $mess ."\n\n\n\n");
    }
}
