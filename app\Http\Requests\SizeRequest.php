<?php

namespace App\Http\Requests;

use App\Http\Requests\BaseRequest;

class SizeRequest extends BaseRequest
{
    
    public function rules()
    {

        return [
            'id' =>'integer|exists:sizes,id',
            'site_area' => 'required|numeric|min:0',
            'width' => 'required|numeric|min:0',
            'length' => 'required|numeric|min:0',
            'back_hatch' => 'boolean',
            'front_hatch' => 'boolean',
            'direction_1' => 'nullable|integer|exists:directions,id',
            'direction_2' => 'nullable|integer|exists:directions,id',
            'direction_3' => 'nullable|integer|exists:directions,id',
            'direction_4' => 'nullable|integer|exists:directions,id',
            'building_density' => 'nullable|integer|min:0|max:100',


        ];
    }



    public function messages()
    {

        return [
            'site_area.required' => 'Size_001_E_001',
            'site_area.numeric' => 'Size_002_E_002',
            'site_area.min' => 'Size_004_E_003',

            'width.required' => 'Size_001_E_004',
            'width.numeric' => 'Size_002_E_005',
            'width.min' => 'Size_004_E_006',

            'length.required' => 'Size_001_E_007',
            'length.numeric' => 'Size_002_E_008',
            'length.min' => 'Size_004_E_009',

            'back_hatch.boolean' => 'Size_002_E_010',
            'front_hatch.boolean' => 'Size_002_E_011',

            'direction_1.integer' => 'Size_002_E_012',
            'direction_1.exists' => 'Size_003_E_013',

            'direction_2.integer' => 'Size_002_E_014',
            'direction_2.exists' => 'Size_003_E_015',

            'direction_3.integer' => 'Size_002_E_016',
            'direction_3.exists' => 'Size_003_E_017',

            'direction_4.integer' => 'Size_002_E_018',
            'direction_4.exists' => 'Size_003_E_019',

            'building_density.integer' => 'Size_002_E_020',
            'building_density.min' => 'Size_004_E_021',
            'building_density.max' => 'Size_004_E_022',

            'id.integer' => 'Size_002_E_023',
            'id.exists' => 'Size_003_E_024',


        ];
    }
}
