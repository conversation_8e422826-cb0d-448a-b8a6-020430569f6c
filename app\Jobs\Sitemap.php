<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Sitemap\SitemapService;
use Illuminate\Support\Facades\Log;
use App\Services\GeneralService;
use App\User;
use App\Property;
use App\PropertyType;
use App\Province;
use App\Facility;
use App\Direction;
use App\District;
use App\Ward;
use App\Setting;
use Illuminate\Support\Facades\Config;
use ZipArchive;
use App\Helpers\S3Utils;
use App\Services\Mqtt\MqttChatService;
use Carbon\Carbon;
class Sitemap implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userID;
    public function __construct($userID=null)
    {
        $this->userID = $userID;
    }


    public function handle()
    {
        Log::info("RUN HANDLE CREATE SITEMAP");
        $sitemapService = new SitemapService();
        $redis = GeneralService::redisConnection();
        $user = User::find($this->userID);
        $direction_unknow = config('constants.direction_unknow');
        $facility_location = config('constants.facility_location');
        $provinceSetting = Setting::where('key',config('constants.province_sitemap'))->first();
        // if($this->typeSitemap == 1)
        // {
            Log::info("RUN function");
            $propertyType = PropertyType::get(['id','slug']);
            $province     = Province::get(['id','slug']);
            $provinceIn     = Province::whereIn('id',json_decode($provinceSetting->value))->get(['id','slug']);
            $provinceNotIn     = Province::whereNotIn('id',json_decode($provinceSetting->value))->get(['id','slug']);
            $direction    = Direction::where('id','<>',$direction_unknow)->get(['id','slug']);
            $facility     = Facility::where('facility_type_id',$facility_location)->get(['id','slug']);
            // $district     = District::where()
            $typeRequest  = [['name'=>"ban",'request'=>config('constants.request_type.sell')],['name'=>"cho-thue",'request'=>config('constants.request_type.for_rent')]];
            $typeRequest1 = [['name' => "ban",'request'=>config('constants.request_type.sell')],['name' => "cho-thue",'request'=>config('constants.request_type.for_rent')],['name' => "for-sale",'request'=>config('constants.request_type.sell')],['name' => "for-rent",'request'=>config('constants.request_type.for_rent')]];
            $i = 0;
            $dataSitemapIndex = [];
            $sitemapIndex    = [];
            $sitemapIndexLocation = [];
            $sitemapIndexCatalog2 = [];
            $dataAddIndex    = [];
            $dataAddrobots   = [];
            $dataAddLocation = [];
            $dataCatelog1 = [];
            $dataAddSitemap = [];
            $sttFile = 0;
            Log::info("RUN function 1");
            //sitemap Property
            $dataSitemap = [];

            $number_of_days = Setting::where(
                'key', 
                config('constants.days_of_sitemap_creatable_key')
            )->first();

            $date_to_create = Carbon::now()->subDays($number_of_days['value']);

            
            foreach ($typeRequest as $key1 => $valueTypeReq) {
                
                    foreach ($provinceIn as $key3 => $valueProvince) {
                        Log::info($i++);
                        $slug1 = $valueTypeReq['name']."/".$valueProvince->slug;
                        $abc1 = Property::where('enable',true)
                            ->where('confirmed',config('constants.confirm_status.confirmed'))
                            ->where('province_id',$valueProvince->id)
                            ->where('updated_at', 
                                '>=', 
                                $date_to_create
                            )
                            ->limit(20)
                            ->whereHas('requests',function($query) use($valueTypeReq){
                                $query->where('type_id',$valueTypeReq['request']);
                            })->select('slug')
                            ->chunk(1000, function($ele) use(&$dataSitemap){
                                foreach ($ele as $key => $value) {
                                    array_push($dataSitemap,$value);
                                }
                            });
                        
                        if(count($dataSitemap) > 0)
                        {
                            $dataSitemapIndex = array_merge($dataSitemapIndex,$dataSitemap);
                            $dataSitemap = [];
                            if(count($dataSitemapIndex) > Config::get('constants.amount_sitemap_each_file', 45000))
                            {
                                $loop = floor( count($dataSitemapIndex) / Config::get('constants.amount_sitemap_each_file', 45000));
                                $array = [];
                                if($loop > 0)
                                {
                                    for ($i=1; $i <= $loop ; $i++) 
                                    {
                                        array_push($array,array_splice($dataSitemapIndex, 0, Config::get('constants.amount_sitemap_each_file', 45000)));
                                    }
                                    for ($i=1; $i <= $loop ; $i++) { 
                                        $sttFile++;
                                        $dataAddSitemap = array_merge($dataAddSitemap,$sitemapService->saveXML($valueTypeReq['name'],$array[$i-1],$sttFile));
                                    }
                                    
                                }
                            }
                        }
                    }
                    // foreach ($provinceNotIn as $key3 => $valueProvince) {
                    //     Log::info($i++);
                    //     $slug1 = $valueTypeReq['name']."/".$valueProvince->slug;
                    //     $abc3 = Property::where('enable',true)
                    //         ->where('confirmed',config('constants.confirm_status.confirmed'))
                    //         ->where('province_id',$valueProvince->id)
                    //         ->where('updated_at', 
                    //             '>=', 
                    //             $date_to_create
                    //         )
                    //         ->whereNotNull('profile_picture')
                    //         ->whereHas('requests',function($query) use($valueTypeReq){
                    //             $query->where('type_id',$valueTypeReq['request']);
                    //         })->select('slug')
                    //         ->chunk(1000, function($ele) use(&$dataSitemap){
                    //             foreach ($ele as $key => $value) {
                    //                 array_push($dataSitemap,$value);
                    //             }
                    //         });
                           
                    //     if(count($dataSitemap) > 0)
                    //     {
                    //         $dataSitemapIndex = array_merge($dataSitemapIndex,$dataSitemap);
                    //         $dataSitemap = [];
                    //         if(count($dataSitemapIndex) > Config::get('constants.amount_sitemap_each_file', 45000))
                    //         {
                    //             $loop = floor( count($dataSitemapIndex) / Config::get('constants.amount_sitemap_each_file', 45000));
                    //             $array = [];
                    //             if($loop > 0)
                    //             {
                    //                 for ($i=1; $i <= $loop ; $i++) 
                    //                 {
                    //                     array_push($array,array_splice($dataSitemapIndex, 0, Config::get('constants.amount_sitemap_each_file', 45000)));
                    //                 }
                    //                 for ($i=1; $i <= $loop ; $i++) { 
                    //                     $sttFile++;
                    //                     $dataAddSitemap = array_merge($dataAddSitemap,$sitemapService->saveXML($valueTypeReq['name'],$array[$i-1],$sttFile));
                    //                 }
                                    
                    //             }
                    //         }
                    //     }

                         
                    // }
                    
                if(count($dataSitemapIndex) > 0)
                {
                    $loop = ceil( count($dataSitemapIndex) / Config::get('constants.amount_sitemap_each_file', 45000));
                    $array = [];
                    if($loop > 0)
                    {
                        for ($i=1; $i <= $loop ; $i++) 
                        {
                            array_push($array,array_splice($dataSitemapIndex, 0, Config::get('constants.amount_sitemap_each_file', 45000)));
                        }
                        for ($i=1; $i <= $loop ; $i++) { 
                            $sttFile++;
                            $dataAddSitemap = array_merge($dataAddSitemap,$sitemapService->saveXML($valueTypeReq['name'],$array[$i-1],$sttFile));
                        }
                        
                    }

                    $dataSitemapIndex = [];
                    $sttFile = 0;
                    $sitemapIndex = array_merge($sitemapIndex,$dataAddSitemap);
                }
                if(count($dataAddSitemap) > 0)
                {
                    $dataAddCatelog1 = $sitemapService->saveIndex($dataAddSitemap,$valueTypeReq['name']);
                    $dataAddSitemap = [];
                    $sitemapIndex = array_merge($sitemapIndex,$dataAddCatelog1);
                }
            }
            //zip file
            if(count($sitemapIndex)>0){
                $sitemapService->saveZip($sitemapIndex);
            }
            
            // End zip file
            // end sitemap property

            //--------------------------------catalog 1 -------------------------------------------------------
            $dataAddLocation = [];
            
            foreach ($provinceIn as $key => $valueProvince) {
                
                array_push($dataAddLocation,$valueProvince->slug);
                
                $district = District::where('province_id',$valueProvince->id)->get(['id','slug']);

                foreach ($district as $key => $valueDistrict) {
                    $slugLocation1 = $valueProvince->slug."_".$valueDistrict->slug;
                    array_push($dataAddLocation,$slugLocation1);
                    
                    $ward = Ward::where('district_id',$valueDistrict->id)->get(['id','slug']);
                    foreach ($ward as $key => $valueWard) {
                        $slugLocation2 = $valueProvince->slug."_".$valueDistrict->slug."_".$valueWard->slug;
                        array_push($dataAddLocation,$slugLocation2);
                    }  
                }
                $sitemapIndexLocation =  array_merge($sitemapIndexLocation,$sitemapService->saveSitemapLocation($dataAddLocation,"catalog-1-".$valueProvince->slug));
                $dataAddLocation = [];
            }
            // add sitemap location
            $sitemapIndexLocation =  array_merge($sitemapIndexLocation,$sitemapService->saveSitemapIndexLocation($sitemapIndexLocation,"catalog-1"));
            // end add sitemap location
            // zip file
            $sitemapService->saveZipLocation($sitemapIndexLocation,"catalog-1");
            // End zip file
            //--------------------------------end catalog 1 -------------------------------------------------------
            //--------------------------------catalog 2 -------------------------------------------------------
            $dataAddCatalog2 = [];

                foreach ($provinceIn as $key => $valueProvince) {
                    
                    foreach ($typeRequest as $key => $valueTypeReq){
                        foreach ($propertyType as $key => $valuePropertyType) {
                            $slugCatalog = $valueProvince->slug."/".$valueTypeReq['name']."_".$valuePropertyType->slug;
                            array_push($dataAddCatalog2,$slugCatalog);
                        }
                    }
                    $district = District::where('province_id',$valueProvince->id)->get(['id','slug']);

                    foreach ($district as $key => $valueDistrict) {
                        foreach ($typeRequest as $key => $valueTypeReq){
                            foreach ($propertyType as $key => $valuePropertyType) {
                                $slugCatalog2 = $valueProvince->slug."_".$valueDistrict->slug."/".$valueTypeReq['name']."_".$valuePropertyType->slug;
                                array_push($dataAddCatalog2,$slugCatalog2);
                            }
                        }
                        $ward = Ward::where('district_id',$valueDistrict->id)->get(['id','slug']);
                        foreach ($ward as $key => $valueWard) {
                            foreach ($typeRequest as $key => $valueTypeReq){
                                foreach ($propertyType as $key => $valuePropertyType) {
                                    $slugCatalog3 = $valueProvince->slug."_".$valueDistrict->slug."_".$valueWard->slug."/".$valueTypeReq['name']."_".$valuePropertyType->slug;
                                    array_push($dataAddCatalog2,$slugCatalog3);
                                }
                            }
                        }  
                    }
                    $sitemapIndexCatalog2 =  array_merge($sitemapIndexCatalog2,$sitemapService->saveSitemapLocation($dataAddCatalog2,"catalog-2-".$valueProvince->slug));
                    $dataAddCatalog2 = [];
                }
                
            
             // add sitemap catalog 2
            $sitemapIndexCatalog2 =  array_merge($sitemapIndexCatalog2,$sitemapService->saveSitemapIndexLocation($sitemapIndexCatalog2,"catalog-2"));
            // end add sitemap catalog 2
            //zip file
            $sitemapService->saveZipLocation($sitemapIndexCatalog2,"catalog-2");
            // End zip file
            //--------------------------------end catalog 2 -------------------------------------------------------



            // end sitemap location
        // }
        


        $findSitemap = file_get_contents(env('S3_HOST_PATH').env('ZIP_SITEMAP_PATH'));
        
        $redis->set("create_sitemap_queue_status", "DONE",'EX', 600);
        Log::info("END queue create sitemap");
        return 'DONE';
    }
}
