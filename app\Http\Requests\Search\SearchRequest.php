<?php

namespace App\Http\Requests\Search;

use App\Http\Requests\BaseRequest;

class SearchRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'id' => 'nullable|max:255',
            'address' => 'nullable|max:1000'
        ];
    }
    public function messages()
    {
        return [
            'id.required'   => 'Search_001_E_001',
            'id.uuid'       => 'Search_002_E_002',
            'id.exists'     => 'Search_003_E_003',

            'address.max'   => 'Search_004_E_004',
        ];
    }
}
