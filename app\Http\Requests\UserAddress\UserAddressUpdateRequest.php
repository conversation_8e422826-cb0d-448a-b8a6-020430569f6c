<?php

namespace App\Http\Requests\UserAddress;

use App\Http\Requests\BaseRequest;

class UserAddressUpdateRequest extends BaseRequest
{
   
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:addresses,id',
            'address'           => 'required|max:5000',
            'province_id'       => 'required|exists:dvhc2021_tinh,id',
            'district_id'       => 'required|exists:dvhc2021_huyen,id',
            'ward_id'           => 'required|exists:dvhc2021_xa,id',
            'name'     => 'required|max:255',
            'phone'    => 'required|digits_between:10,11',
            'latitude'          => 'required',
            'longitude'         => 'required',
            'title'             => 'nullable|max:255',
            'note'              => 'nullable|max:5000',
            'images'            => 'nullable',
            'image_delete'      => 'nullable'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'Product_001_E_012',
    //         'id.uuid' => 'Product_002_E_013',
    //         'id.exists' => 'Product_003_E_014',
    //     ];
    // }
}
