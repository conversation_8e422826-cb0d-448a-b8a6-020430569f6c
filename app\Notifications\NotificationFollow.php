<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use App\Services\Mqtt\MqttChatService;
use App\User;
use Illuminate\Support\Facades\Log;

class NotificationFollow extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    private $_user;
    private $_follow;
    private $_property;
    public function __construct($user, $follow, $property = null)
    {
        $this->_follow = $follow;
        $this->_user = $user;
        $this->_property = $property;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }


    public function toArray($notifiable)
    {
        $mqtt = new MqttChatService();
        $other =  User::find($this->_follow->user_id);
        $result = User::find($this->_user->id);
        $_notify = $result->notifications();
        $countUnread = $_notify->whereNull('read_at')->count();
        $type = 'user';
        switch ($this->_follow->object_type) {
            case config('constants.object_type_follow_like.user'):
                $title = $other->name;
                $content = "Đã đăng ký theo dõi bạn";
                $image = $other->AssignAvatar()->first(['path_thumbnail']);
                $link = $other->custom_path ? $other->custom_path : $other->id;

                break;
            case config('constants.object_type_follow_like.property'):
                $title = $other->name;
                $name = isset($this->_property->name) ? $this->_property->name : null;
                $content = "Đã theo dõi bài đăng ".$name;
                $image = $other->AssignAvatar()->first(['path_thumbnail']);
                $link = $other->custom_path ? $other->custom_path : $other->id;
                break;

            default:
                $content = null;
                $image = null;
                $link = null;
                break;
        }

        $mqtt->publish(['topic' => $this->_user->id, 'message' => $countUnread]);

        $data = [
            'body'  => $content,
            'title' => $title,
            'image' => $image,
            'url'   => $link,
            'type'  => $type
        ];
        $tokenService = new TokenService();
        $tokens = $tokenService->listByUser($this->_user->id, 2);

        $notiService = new NotificationService;
        $notiService->sendBatchNotification($tokens, $data);
        return [
            'title' => $title,
            'content' => $content,
            'image' => $image,
            'link' => $link,
            'type' => $type
        ];
    }
}
