<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Stock\StockService;
use App\Services\Token\TokenService;
use App\Services\Notification\NotificationService;
use App\Notifications\NotificationUser;
use App\Shop;
use App\User;
use App\StockTracking;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DailyStockReportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:daily-report {--date= : Date for the report (Y-m-d format)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send daily stock summary notification to shop owners';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = $this->option('date') ?? Carbon::yesterday()->format('Y-m-d');
        
        $this->info("Generating daily stock report for date: {$date}");

        try {
            $shops = Shop::where('enable', true)->with(['owner', 'agent'])->get();
            $reportsSent = 0;

            foreach ($shops as $shop) {
                $this->processShopReport($shop, $date);
                $reportsSent++;
            }

            $this->info("Daily stock reports sent successfully to {$reportsSent} shops.");
            Log::info("Daily stock reports sent successfully to {$reportsSent} shops for date: {$date}");

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to send daily stock reports: " . $e->getMessage());
            Log::error("Failed to send daily stock reports: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Process stock report for a single shop
     *
     * @param Shop $shop
     * @param string $date
     */
    private function processShopReport(Shop $shop, string $date)
    {
        try {
            $stockService = new StockService();
            $summaryResult = $stockService->getDailyStockSummary($shop->id, $date);

            if (isset($summaryResult['error'])) {
                $this->warn("Skipping shop {$shop->name}: " . $summaryResult['error']);
                return;
            }

            $summary = $summaryResult['data']['summary'];
            
            // Skip if no stock movements for the day
            if ($summary->isEmpty()) {
                $this->line("No stock movements for shop: {$shop->name}");
                return;
            }

            // Generate report message
            $reportMessage = $this->generateReportMessage($shop, $summary, $date);

            // Send notifications to shop owner and agent
            $this->sendNotifications($shop, $reportMessage);

            $this->info("Report sent for shop: {$shop->name}");

        } catch (\Exception $e) {
            $this->error("Failed to process report for shop {$shop->name}: " . $e->getMessage());
            Log::error("Failed to process stock report for shop {$shop->name}: " . $e->getMessage());
        }
    }

    /**
     * Generate report message
     *
     * @param Shop $shop
     * @param \Illuminate\Support\Collection $summary
     * @param string $date
     * @return array
     */
    private function generateReportMessage(Shop $shop, $summary, string $date): array
    {
        $formattedDate = Carbon::parse($date)->format('d/m/Y');
        $title = "Báo cáo tồn kho ngày {$formattedDate} - {$shop->name}";
        
        $bodyLines = ["Báo cáo tồn kho ngày {$formattedDate}:"];
        $totalValue = 0;

        foreach ($summary as $item) {
            $reason = $this->translateReason($item->reason);
            $bodyLines[] = "• {$reason}: {$item->transaction_count} giao dịch, {$item->total_quantity} sản phẩm";
            
            if ($item->total_value > 0) {
                $bodyLines[] = "  Giá trị: " . number_format($item->total_value, 0, ',', '.') . " VNĐ";
                $totalValue += $item->total_value;
            }
        }

        if ($totalValue > 0) {
            $bodyLines[] = "Tổng giá trị: " . number_format($totalValue, 0, ',', '.') . " VNĐ";
        }

        return [
            'title' => $title,
            'body' => implode("\n", $bodyLines),
            'type' => 'stock_report',
            'url' => '',
            'target_url' => '/stock-report',
            'extra' => [
                'shop_id' => $shop->id,
                'date' => $date,
                'summary' => $summary
            ]
        ];
    }

    /**
     * Translate stock reason to Vietnamese
     *
     * @param string $reason
     * @return string
     */
    private function translateReason(string $reason): string
    {
        $translations = [
            'import' => 'Nhập kho',
            'export' => 'Xuất kho',
            'waste' => 'Hao hụt/Hủy',
            'order' => 'Đơn hàng',
            'order_updated_new' => 'Cập nhật đơn hàng',
            'order_updated_restore' => 'Hoàn trả đơn hàng',
            'stock_update' => 'Cập nhật tồn kho',
            'initial_stock' => 'Tồn kho ban đầu'
        ];

        return $translations[$reason] ?? ucfirst($reason);
    }

    /**
     * Send notifications to shop owner and agent
     *
     * @param Shop $shop
     * @param array $message
     */
    private function sendNotifications(Shop $shop, array $message)
    {
        $tokenService = new TokenService();
        $notificationService = new NotificationService();

        // Send to shop owner
        if ($shop->user_id) {
            $ownerTokens = $tokenService->listByUser($shop->user_id, 2);
            if (count($ownerTokens) > 0) {
                $notificationService->sendBatchNotification($ownerTokens, $message);
            }

            // Save notification to database
            $owner = User::find($shop->user_id);
            if ($owner) {
                $owner->notify(new NotificationUser($shop->user_id, $message));
            }
        }

        // Send to agent if different from owner
        if ($shop->agent_id && $shop->agent_id !== $shop->user_id) {
            $agentTokens = $tokenService->listByUser($shop->agent_id, 2);
            if (count($agentTokens) > 0) {
                $notificationService->sendBatchNotification($agentTokens, $message);
            }

            // Save notification to database
            $agent = User::find($shop->agent_id);
            if ($agent) {
                $agent->notify(new NotificationUser($shop->agent_id, $message));
            }
        }
    }
}
