<?php

namespace App\Http\Requests\Reel;

use App\File;
use App\Http\Requests\BaseRequest;

class ReelRequest extends BaseRequest
{
    public function rules(){
        return [
            'description' => 'required|string',
            'object_id' => 'required_if:object_type,shop|uuid',
            'object_type' => 'required|string|in:user,shop',
            'sound' => 'nullable|uuid',
            'video_id' => [
                'required_without:image_ids',
                'uuid',
                function ($attribute, $value, $fail) {
                    $result = File::on('pgsqlReplica')->where('id', $value)->where("file_type", "video")->exists();
                    if (!$result){
                        $fail("The selected video_id must be a valid video file");
                    }
                    if (request()->has('image_ids')) {
                        $fail('Cannot set video_id if image_ids is set.');
                    }
                },],
            'image_ids' => ['required_without:video_id',
                'array',
                'exists:images,id',
                function ($attribute, $value, $fail) {
                    if (request()->has('video_id')) {
                        $fail('Cannot set image_ids if video_id is set.');
                    }
                },
            ],
        ];
    }
}
