<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\ConfirmCode\ConfirmCodeRequest;
use App\Http\Requests\ConfirmCode\ConfirmCodeUpdateRequest;
use App\Http\Requests\ConfirmCode\ConfirmCodeCheckIdRequest;
use App\Services\ConfirmCode\ConfirmCodeService;
use Illuminate\Support\Facades\Auth;

class ConfirmCodeController extends Controller
{
    // -------create confirm_code---------------
    public function create(Request $request)
    {
        $data = $request->only([
            'code',
            'action_type',
            'user_id'
        ]);

        $service = new ConfirmCodeService();
        $result = $service->insert($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------check confirm_code---------------
    public function checkCode(Request $request)
    {
        $data = $request->only([
            'code',
            'action_type',
            'user_id'
        ]);

        $service = new ConfirmCodeService();
        $result = $service->checkCode($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
