<?php

namespace App\Services\POS;

use App\Services\Order\OrderService;
use Illuminate\Support\Facades\Log;

class CukCuk implements POS
{

    public function __construct($shopId)
    {
    }

    function createOrder($data, OrderService $service)
    {
        // TODO: Implement createOrder() method.
    }

    function listBranch()
    {
        // TODO: Implement listBranch() method.
    }

    function connect($data)
    {
        // TODO: Implement connect() method.
    }

    function listProducts($offset, $limit)
    {
        // TODO: Implement listProducts() method.
    }

    function getToken($data)
    {
        $request = [
            'scopes' => 'PublicApi.Access',
            'grant_type' => 'client_credentials',
            'client_id' => $data['client_id'],
            'client_secret' => $data['client_secret'],
        ];
        $url = "https://graphapi.cukcuk.vn/api/account/login";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);

        // Thực thi yêu cầu và nhận phản hồi
        $response = curl_exec($ch);

        // Kiểm tra lỗi
        if (curl_errno($ch)) {
//            Log::error( 'Lỗi cURL: ' . curl_error($ch));
            curl_close($ch);
            return false;
        }
        curl_close($ch);

        return json_decode($response, true);
    }

}
