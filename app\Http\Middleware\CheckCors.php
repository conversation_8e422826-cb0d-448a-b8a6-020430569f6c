<?php

namespace App\Http\Middleware;

use Closure;

class CheckCors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Methods', 'POST, GET, OPTIONS, PUT, DELETE');
        $response->headers->set('Access-Control-Max-Age', '86400');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-Withs');
        return $response;
                // return $next($request)
            //     ->header('Access-Control-Allow-Origin', '*')
            //     ->header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS, PUT, DELETE')
            //     ->header('Access-Control-Max-Age', '86400')
            //     ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-Withs');


                // header('Access-Control-Allow-Origin: *');
                // header('Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS');
                // header('Access-Control-Allow-Headers: Origin, Content-Type, X-Auth-Token');
    /*$headers = [
            'Access-Control-Allow-Origin'      => '*',
            'Access-Control-Allow-Methods'     => 'POST, GET, OPTIONS, PUT, DELETE',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age'           => '86400',
            'Access-Control-Allow-Headers'     => 'Content-Type, Authorization, X-Requested-With'
        ];

        if ($request->isMethod('OPTIONS'))
        {
            return response()->json('{"method":"OPTIONS"}', 200, $headers);
        }

        $response = $next($request);
        foreach($headers as $key => $value)
        {
            $response->header($key, $value);
        }

        return $response;*/

    }
}
