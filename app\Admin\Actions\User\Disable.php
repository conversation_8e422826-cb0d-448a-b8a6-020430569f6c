<?php

namespace App\Admin\Actions\User;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class Disable extends RowAction
{
    public $name = 'Disable';

    public function handle (Model $model)
    {
        $model->enable = false;
        $model->save();

        return $this->response()->success('Disable')->refresh();
    }

    public function dialog()
    {
        $this->confirm('Are you sure you want to Disable this user?');
    }
}