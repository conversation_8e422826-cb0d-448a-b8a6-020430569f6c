<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class MaterialImportHistory extends Model
{
    protected $primaryKey = 'id';
    public $incrementing = false;
    public $table = "material_import_histories";

    protected $fillable = [
        'material_id', 'material_quotation_id', 'quantity', 'notes', 'shop_id'
        // Add other fillable columns
    ];
    
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
        
        static::deleting(function ($model) {
            $model->materialQuotations()->detach();
        });
    }
    // Define any relationships or additional methods here
    public function materialQuotation()
    {
        return $this->belongsTo(MaterialQuotation::class, 'material_quotation_id', 'id');
    }

    public function material()
    {
        return $this->belongsTo(Material::class);
    }
}
