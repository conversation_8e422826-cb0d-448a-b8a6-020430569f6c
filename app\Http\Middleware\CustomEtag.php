<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use \Matthewbdaly\ETagMiddleware\ETag;


class CustomEtag extends Etag
{
    /**
     * Implement Etag support.
     *
     * @param Request $request The HTTP request.
     * @param Closure $next    Closure for the response.
     * @psalm-param Closure(Request): Response $next    Closure for the response.
     *
     * @return \Illuminate\Http\Response
     */
    public function handle(Request $request, Closure $next)
    {
        // If this was not a get or head request, just return
        if (!$request->isMethod('get') && !$request->isMethod('head')) {
            return $next($request);
        }

        // Get the initial method sent by client
        $initialMethod = $request->method();

        // Force to get in order to receive content
        $request->setMethod('get');

        // Get response
        /** @var Response $response */
        $response = $next($request);

        // Generate Etag
        $etag = md5(json_encode($response->headers->get('origin')) . (string)$response->getContent());

        // Load the Etag sent by client
        $requestEtag = str_replace('"', '', $this->getETags($request));

        // Check to see if Etag has changed
        if ($requestEtag && $requestEtag[0] == $etag) {
            $response->setNotModified();
        }

        // Set Etag
        $response->setEtag($etag);

        // Set back to original method
        $request->setMethod($initialMethod); // set back to original method

        // Send response
        return $response;
    }
    
    protected function getETags($request)
    {
        $etagHeader = $request->headers->get('If-None-Match', '');
        $etags = preg_split('/\s*,\s*/', $etagHeader, -1, \PREG_SPLIT_NO_EMPTY);
        $etags = array_map(function($etag) {
            return preg_replace('/^W\//', '', $etag);
        }, $etags);
        return $etags;
    }
}