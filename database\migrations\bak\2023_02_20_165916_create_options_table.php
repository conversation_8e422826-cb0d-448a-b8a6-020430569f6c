<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // bảng <PERSON><PERSON><PERSON> chọn cho <PERSON>ản phẩm
        Schema::create('options', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->comment("Tên option");
            $table->boolean('enable')->default(1);
            $table->uuid('created_by')->nullable();

            $table->index('name');
            $table->index('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('options');
    }
}
