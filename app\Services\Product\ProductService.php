<?php
namespace App\Services\Product;

use App\Product;
use App\ProductCategory;
use App\Shop;
use App\Image;
use App\PlaceType;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use App\Services\GeneralService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\User;
use App\Helpers\Helper;
use App\Services\ClientPublic\ClientPublicService;
use App\Translation;
use App\StockTracking;
use Illuminate\Database\Eloquent\Builder;

class ProductService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }
    public function checkProductOwnership($userId, $productId)
    {
        $product = Product::find($productId);
        if (!$product) {
            return false;
        }

        $shop = Shop::where('user_id', $userId)->where('id', $product->shop_id)->first();
        return $shop !== null;
    }

     //-------process create product--------
    public function create(array $product)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $product['created_by'] = $user->id;
        }

        $result = Product::create($product);

        if($result && isset($product['translation'])){
            $this->excuteTranslation($product['translation'], config('constants.object_type.product'), $result->id);
        }

        // ... rest of the existing code ...

        return $result;
    }

    public function excuteTranslation(array $transArr, $objectType, $objectId){
        foreach ($transArr as $key => $langValue) {
            Translation::updateOrCreate(
                [
                    'object_type' => $objectType,
                    'object_id' => $objectId,
                    'language_code' => $langValue['language_code'] ?? "vi"
                ],
                [
                    'name' => $langValue['name'] ?? "",
                    'description' => $langValue['description'] ?? ""
                ]
            );
        }
    }

     //-------process create private product--------
    public function createPrivateProduct(array $product)
    {
        $user = User::find($this->_userId);
        // if($user)
        // {
        //     $product['created_by'] = $user->id;
        // }
        $product['type'] = 3;
        $result = Product::create($product);

        foreach($product['category_ids'] as $key=>$value){
            $product_category = [
                'product_id' => $result->id,
                'category_id' => $value,
            ];

            ProductCategory::create($product_category);
        };
        if($result && isset($product['translation'])){
            $this->excuteTranslation($product['translation'], config('constants.object_type.product'), $result->id);
        }
        $result = $this->detail($result->id,false);
        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.create'),
        //                 $result->id,
        //                 config('constants.object_type.product'),
        //                 $result,
        //                 json_encode($product));

        return $result;
    }

    //-------process listing Product--------------------
    public function list($all)
    {
        // $general = new GeneralService($this->_userId);
        if($all == true){
            $result = Product::orderBy('created_at','asc')
                ->with('shop')
                ->get();
        }
        else {
            $result = Product::with('shop')
                ->where('enable', true)
                ->orderBy('created_at','asc')->get();
        }

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        // Filter price_root based on user permissions for each product
        foreach ($result as $product) {
            if (!$product->canViewPriceRoot($this->_userId)) {
                $product->makeHidden(['price_root']);
            }
        }

        return $result;
    }

    //------process detail Product----------
    public function detail($id, $checkEnable = false)
    {
        $field = preg_match('/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i', $id) ? 'id' : 'slug';
        $result = Product::where($field,$id)->where('shop_id','<>','9fbe5e90-b443-45d7-934a-b9daf3727a98')
            ->with('images', 'shop', 'categories', 'children_products', 'parent_product', 'translation');
            if($checkEnable){
                $result = $result->where('enable', true);
                $result->whereHas('shop', function (Builder $result) {
                    $result->where('enable', true);
                });
            }
            $result = $result->first();

        if(!$result)
        {
            return false;
        }

        // Filter price_root based on user permissions
        if (!$result->canViewPriceRoot($this->_userId)) {
            $result->makeHidden(['price_root']);
        }

        return $result;
    }

    //------process relate Product----------
    public function relate($id)
    {
        $field = preg_match('/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i', $id) ? 'id' : 'slug';
        $product = Product::where($field,$id)
            ->with('shop', 'categories', 'children_products', 'parent_product')
            // ->where('enable', true)
            ->first();
        if(!$product)
        {
            return false;
        }
        $filterArr = [];
        $filterArr['category_ids'] = [];
        $filterArr['category_ids'] = $product['categories']->pluck('id');
        if(empty($filterArr['category_ids'])){
            $filterArr['search'] = $product['name'];
        }
        $filterArr['latitude_user'] = $product['latitude'];
        $filterArr['longitude_user'] = $product['longitude'];
        $filterArr['map_data'] = true;
        $filterArr['except'] = [$id];
        $ClientPublicService = new ClientPublicService();
        $relateProduct = $ClientPublicService->filterProduct($filterArr);
        // dd($relateProduct);
        return $relateProduct;
    }

    //-------process update product--------
    public function update(array $product)
    {
        $result = Product::find($product['id']);

        if(!$result)
        {
            return false;
        }

        // Check permission for price_root field
        if (isset($product['price_root']) && !$result->canEditPriceRoot($this->_userId)) {
            unset($product['price_root']); // Remove price_root if user doesn't have permission
        }
        // Record stock change
        if(isset($product['stock']) && $product['stock'] > 0 && $product['stock'] != $result->stock){
            StockTracking::recordStockChange(
                $product['id'],
                $result->stock ?? 0,
                $product['stock'],
                'stock_update'
            );
        }

        if(isset($product['image_delete'])){
            foreach($product['image_delete'] as $key=>$value){
                if(isset($value['id'])){
                    $image = Image::find($value['id']);
                    if($image){
                        $image->delete();
                    }
                }
            }
        }
        if(isset($product['images'])){
            foreach($product['images'] as $key=>$value){
                if(isset($value['id'])){
                    $image = Image::find($value['id']);
                    if($image){
                        if($value['is_profile_picture']){
                            $result['profile_picture'] = $image['path'];
                        }
                        $image->update([
                            'title' => $value['title'] ?? $image->title,
                            'description' => $value['description'] ?? $image->description,
                            'parent_id' => $product['id'],
                            'style' => $value['style'] ?? $image->style,
                            'index' => $value['index'] ?? $image->index,
                        ]);
                    }
                }
            }
        }

        // Ensure only fillable fields are updated
        $product['updated_at'] = now();
        $result->fill($product); // Use fill to prevent mass assignment issues

        $result->save(); // Save the model

        if($result && isset($product['translation'])){
            $this->excuteTranslation($product['translation'], config('constants.object_type.product'), $result->id);
        }

        $listCategoryIdCurrent = ProductCategory::where('product_id', $product['id'])->delete();

        foreach($product['category_ids'] as $key=>$value){
            $product_category = [
                'product_id' => $result->id,
                'category_id' => $value,
            ];

            ProductCategory::create($product_category);
        };

        $result = $this->detail($result->id, false);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.product'),
        //                 $result,
        //                 json_encode($product));

        return $result;

    }

    //-----process remove product----------
    public function remove(array $product)
    {
        $result = Product::where([
            ['id', $product['id']],
            ['enable', true]
        ])->first();

        if(!$result)
        {
            return false;
        }

        $product['enable'] = false;
        $this->update($product);

        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.product'),
        //                 $result,
        //                 json_encode($product));

        return $result;
    }

    //-----process delete product----------
    public function delete(array $product)
    {
        $result = Product::find($product['id']);

        if(!$result)
        {
            return false;
        }

        //----------- set 'parent_id = NULL' to sub-categories--------------

        $sub_parent = Product::where('parent_id', $product['id'])->get();

        foreach ($sub_parent as $key => $value) {
            $data = [
                'id' => $value->id,
                'parent_id' => null,
            ];
            $this->update($data);
        }

        //----------- delete action ---------------
        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.product'),
        //                 $result,
        //                 json_encode($product));

        return true;
    }

    //-----process clone product from system shop to specify shop----------
    public function clone(array $products, $shop)
    {
        $arr = [];
        foreach ($products as $product) {
            $arr[] = [
                "id" => Str::uuid(),
                "name" => $product['name'],
                "price" => $product['price'],
                "profile_picture" => $product['profile_picture'],
                "type" => 2,
                "shop_id" => $shop->id,
                "latitude" => $shop->latitude,
                "longitude" => $shop->longitude,
                "parent_id" => $product['id'],
            ];

        }
        return Product::insert($arr);
    }


    // select with  pg_trgm extention
    public function searchBase($searchText, $limit, $offset)
    {
        if(!$searchText) return [];
        $count = 0;
        $results;

        $searchText = Str::lower($searchText);

        $productModel = new Product();
        $productModel->setConnection('pgsqlReplica');

        # Search level 1
        $selectAttributes = 'id,name,is_main,type,lowercase_name,enable,unaccent_name,price,profile_picture,stock';
        $results = $productModel->select(explode(',', $selectAttributes))
            ->where('lowercase_name','like', "%".$searchText."%")
            ->where('type','=', 1)
            ->where('enable', true)
            ->orderByRaw("CASE
                WHEN lowercase_name LIKE '".$searchText."' THEN 1
                WHEN lowercase_name LIKE '".$searchText."%' THEN 2
                WHEN lowercase_name LIKE '%".$searchText."%' THEN 3
                WHEN lowercase_name LIKE '%".$searchText."' THEN 4
                ELSE 5
            END");
        $count = $results->count();
        # Search level 2
        if($count == 0){
            $searchText = Str::slug($searchText," ");
            $results = $productModel->select(explode(',', $selectAttributes))
            ->where('unaccent_name','like', "%".$searchText."%")
            ->where('type','=', 1)
            ->where('enable', true)
            ->orderByRaw("CASE
                WHEN unaccent_name LIKE '".$searchText."' THEN 1
                WHEN unaccent_name LIKE '".$searchText."%' THEN 2
                WHEN unaccent_name LIKE '%".$searchText."%' THEN 3
                WHEN unaccent_name LIKE '%".$searchText."' THEN 4
                ELSE 5
            END");
            $count = $results->count();
        }

        # Search level 3
        // search more with similarity if above empty
        if($count == 0){
            // $searchText = strtolower(Helper::unaccentVietnamese($searchText));
            $results = $productModel->select(explode(',', $selectAttributes))
            ->selectRaw("similarity(unaccent_name, ?) AS similarity_score", [$searchText])
            ->whereRaw("similarity(unaccent_name, ?) > 0.16", [$searchText])
            ->where('type', 1)
            ->where('enable', true)
            ->orderByDesc('similarity_score');
            $count = $results->count();
        }

        return [
            'count' => $count,
            'result' => $results->limit($limit)
            ->offset($offset)
            ->get()
        ];
    }


    public function addProductFromSystem(array $data){
        $shop = Shop::with('products')->find($data['shop_id']);
        if($shop->user_id != $this->_userId){
            return false;
        }
        $arr = [];
        foreach ($data['list_product'] as $product) {
            // if (array_search($product['id'], array_column($shop->products->toArray(), 'parent_id')) === false)
            {
                $arr[] = [
                    "id" => Str::uuid(),
                    "name" => $product['name'],
                    "price" => $product['price'] ?? null,
                    "profile_picture" => $product['profile_picture'],
                    "type" => config('constants.product.type.system'),
                    "shop_id" => $shop->id,
                    "latitude" => $shop->latitude,
                    "longitude" => $shop->longitude,
                    "extra_id" => $product['id'],
                    "is_feature" => $product['is_feature'] ?? false,
                    "unaccent_name" => $product['unaccent_name']?? Str::slug($product['name'],' '),
                    "lowercase_name" => $product['lowercase_name']?? Str::lower($product['name']),
                    "created_by" => $this->_userId,
                    "stock" => $product['stock'] ?? null,
                    "slug" => Str::slug($product['name'],'-').'-'.strtolower(Str::random(2)).str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                    "created_at" => now()->format('Y-m-d H:i:s'),
                    "updated_at" => now()->format('Y-m-d H:i:s'),

                ];
            }
        }
        return Product::insert($arr);

        // $result = Product::
        //     select('id','name', 'profile_picture', 'price')
        //     ->where('is_system', true)
        //     ->where('name', 'ILIKE', '%'.$search_word.'%')
        //     ->orderByRaw("CASE
        //     WHEN name ILIKE '".$search_word."' THEN 1
        //     WHEN name ILIKE '".$search_word."%' THEN 2
        //     WHEN name ILIKE '%".$search_word."%' THEN 3
        //     WHEN name ILIKE '%".$search_word."' THEN 4
        //     ELSE 5
        //     END");
        // $count = $result->count();
        return [
            // 'count' => $count,
            // 'result' => $result->limit(50)->get()
        ];
    }
}
