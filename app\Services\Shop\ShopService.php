<?php
namespace App\Services\Shop;

use App\Category;
use App\Interaction;
use App\Product;
use App\Shop;
use App\Image;
use App\PlaceType;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use App\Services\GeneralService;
use App\Services\Rating\RatingService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Helpers\Helper;
use App\ProductCategory;
use Illuminate\Support\Facades\Log;
use Overtrue\ChineseCalendar\Calendar;

class ShopService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

     //-------process create shop--------
    public function create(array $shop)
    {
        if($this->hasShopByUser()){
            return false;
        }

        $user = User::find($this->_userId);
        if($user)
        {
            $shop['created_by'] = $user->id;
        }

        //--------create slug-----------------
        $shop['slug'] = Str::slug($shop['name']);

        $checkSlug = Shop::where('slug',$shop['slug'])->first();
        if($checkSlug)
        {
            $shop['slug'] = Helper::makeSlug($shop['name'], true);
        }
        $shop['user_id'] = $this->_userId;
        $shop['status'] = config("constants.shop.status.in_process");

        $result = Shop::create($shop);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.create'),
        //                 $result->id,
        //                 config('constants.object_type.shop'),
        //                 $result,
        //                 json_encode($shop));

        return $result;
    }

    //-------process listing Shop--------------------
    public function list($offset, $limit)
    {
        // $general = new GeneralService($this->_userId);
        {
            $result = Shop::where('enable', true)
                ->with('banner','logo', 'created_by','owner', 'business_types')
                // ->with(['products' ])
                ->orderBy('created_at','asc')
                ->offset($offset)->limit($limit)
                ->get();
            foreach ($result as $key => $value) {
                $result[$key]['products'] = Product::where('shop_id', $value->id)->get()->take(4);
            }
        }

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    // Check shop already exists
    public function hasShopByUser()
    {
        return Shop::where('user_id', $this->_userId)->exists();
    }

    //------process detail Shop----------
    public function detail($id, $simple = false)
    {
        $field = preg_match('/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i', $id) ? 'id' : 'slug';
        $result = null;

        $modelShop = new Shop();
        $modelShop->setConnection('pgsqlReplica');

        if($simple){
            $result = $modelShop->select('id','name','address','slug','phone','currency','language','banner_id','logo_id','qr_code')
            ->with('banner', 'logo');
        }else{
            $result = $modelShop->select('*')
            ->with('banner', 'logo', 'created_by','owner','provinces','districts','wards', 'business_types', 'products');
        }

        $result = $result->where($field, $id)->where('enable', true)
            ->first();

        if(!$result)
        {
            return false;
        }
        $result['open_hours_text'] = $this->getFormattedOpeningHours($result['id']);

        return $result;
    }
    public function slugToUUID($input){
        $field = preg_match('/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i', $input) ? 'id' : 'slug';
        if($field == 'id') return $input;
        $result = null;

        $modelShop = new Shop();
        $modelShop->setConnection('pgsqlReplica');
        $shop = $modelShop->select('id')->where($field, $input)->where('enable', true)->first();
        return $shop ? $shop->id : false;
    }
   //------process detail Shop----------
     public function myShop()
     {
        $user = User::find($this->_userId);
        $result = Shop::where('user_id',$this->_userId)
            ->with('banner', 'logo', 'created_by','provinces','districts','wards', 'business_types', 'products')
            // ->where('enable', true)
            ->first();

         if(!$result)
         {
             return false;
         }

         return $result;
     }
    //-------process update shop--------
    public function update(array $shop)
    {
        $result = Shop::find($shop['id']);

        if(!$result)
        {
            return false;
        }


        $result->update($shop);

        if ($result->latitude != $shop['latitude'] || $result->longitude != $shop['longitude']) {
            Product::where('shop_id', $shop['id'])->update([
                'latitude' => $shop['latitude'],
                'longitude' => $shop['longitude']
            ]);
        }


        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.shop'),
        //                 $result,
        //                 json_encode($shop));

        return $result;

    }

    //-----process remove shop----------
    public function remove(array $shop)
    {
        $result = Shop::where([
            ['id', $shop['id']],
            ['enable', true]
        ])->first();

        if(!$result)
        {
            return false;
        }

        $shop['enable'] = false;
        $this->update($shop);

        return true;
    }

    //-----process delete shop----------
    public function delete(array $shop)
    {
        $result = Shop::find($shop['id']);

        if(!$result)
        {
            return false;
        }

        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.shop'),
        //                 $result,
        //                 json_encode($shop));

        return true;
    }


    //-------process filter Shop--------------------
    public function filter($all)
    {
        // $general = new GeneralService($this->_userId);
        if($all == true){
            $result = Shop::orderBy('created_at','asc')
                ->with('banner', 'logo', 'created_by','owner','provinces','districts','wards', 'business_types')
                ->get();
        }
        else {
            $result = Shop::where('enable', true)
                ->with('banner', 'logo', 'created_by','owner','provinces','districts','wards', 'business_types')
                ->orderBy('created_at','asc')
                ->get();
        }

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //------process getProductsByShopId----------
    public function getProductsByShopId($data)
    {
        $limit = $data['limit'] ? $data['limit'] : 20;
        $offset = $data['offset'] ? $data['offset'] : 0;
        $result = Product::with('categories')
            ->where('shop_id', $data['shop_id'])
            ->where('is_main', true)
            ->whereNull('parent_id')
            ->where('enable', true);


        if (isset($data['category_ids']) && !empty($data['category_ids'])) {
            $result->whereHas('categories', function ($query) use ($data) {
                $query->whereIn('category_id', $data['category_ids']);
            });
        }

        if(isset($data['sortBy']) && !empty(isset($data['sortBy']))){
            switch(intval($data['sortBy'])){
                case 1:
                    $result->orderBy('price', 'asc');
                    break;
                case 2:
                    $result->orderBy('price', 'desc');
                    break;
                case 3:
                    $result->orderBy('created_at', 'desc');
                    break;
                case 4:
                    $result->orderBy('name', 'asc');
                    break;
                case 5:
                    $result->orderBy('name', 'desc');
                    break;
                default:
                    $result->orderBy('price', 'asc');
                    break;
            }
        }
        else {
            $result->orderBy('price', 'asc');
        }

        $count = $result->count();

        if(!$result)
        {
            return false;
        }

        return [
            'count'  => $count,
            'result' => $result->offset($offset)->limit($limit)->get()->toArray(),
        ];
    }

    //------process searchProductsInShop owner manage----------
    public function searchProductsInShop($data, $type = 'default')
    {
        $search_word = $data['search'];
        $result = Product::with('categories:id,name', 'shop:id,name,user_id')
            ->where('shop_id', $data['shop_id'])
            ->where('is_main', true)
            ->whereNull('parent_id');


        //owner,agent view products
        if ($type == 'default') {
            $result->where('enable', true);
        };
        if (isset($data['is_saleoff']) && !empty($data['is_saleoff']) && $data['is_saleoff'] == true) {
            $result->whereNotNull('price_off')
                ->where('price_off', '>', 0)
                ->whereColumn('price_off', '<', 'price');
        };
        if (isset($data['is_feature']) && !empty($data['is_feature']) && $data['is_feature'] == true) {
            $result->where('is_feature', true);
        };
        if (isset($data['product_type']) && !empty($data['product_type'])) {
            $result->where('type', $data['product_type']);
        };
        if (isset($data['category_ids']) && !empty($data['category_ids'])) {
            $result->whereHas('categories', function ($query) use ($data) {
                $query->whereIn('category_id', $data['category_ids']);
            });
        }

        if(isset($data['search']) && !empty($data['search'])){
            $result->where('name', 'ILIKE', '%'.$search_word.'%')
            ->orderByRaw("CASE
            WHEN name ILIKE '".$search_word."' THEN 1
            WHEN name ILIKE '".$search_word."%' THEN 2
            WHEN name ILIKE '%".$search_word."%' THEN 3
            WHEN name ILIKE '%".$search_word."' THEN 4
            ELSE 5
            END");
        }


        if(isset($data['sort_by']) && !empty(isset($data['sort_by']))){
            switch($data['sort_by']){
                case 1:
                    $result->orderBy('price', 'asc');
                    break;
                case 2:
                    $result->orderBy('price', 'desc');
                    break;
                case 3:
                    $result->orderBy('created_at', 'desc');
                    break;
                case 4:
                    $result->orderBy('name', 'asc');
                    break;
                case 5:
                    $result->orderBy('name', 'desc');
                    break;
                case 6:
                    $result->orderByRaw("is_feature DESC, updated_at DESC");
                    break;
                default:
                    $result->orderBy('price', 'asc');
                    break;
            }
        }
        else {
            $result->orderBy('updated_at', 'desc');
        }
        if(!$result)
        {
            return false;
        }
        $limit = 50;
        $offset = 0;
        if(isset($data['offset']) && !empty($data['offset'])){
            $offset = $data['offset'];
        }
        if(isset($data['limit']) && !empty($data['limit'])){
            $limit = $data['limit'];
        }

        return [
            'count' => $result->count('id'),
            'result' => $result->offset($offset)->limit($limit)->get()->toArray(),
        ];
    }
    //------process superMenu----------
    public function superMenu($shopID)
    {
        $result = $this->detail($shopID, true);
        if(!$result || !$shopID)
        {
            return false;
        }
        $categories = Category::where('shop_id', $shopID)
        ->where('enable', true)->with('translation')
        // ->with('products_super')
        ->get();

        $ratingService = new RatingService();
        foreach ($categories as $key => $category) {
            $productsArr = ProductCategory::where('category_id',$category['id'])->with('products')->limit(49)->orderBy('index','asc')->get()->toArray();
            foreach ($productsArr as $keyP => $product) {
                $productsArr[$keyP] = $product['products'];
                $productsArr[$keyP]['rating'] = $ratingService->calcAverageRating($productsArr[$keyP]['id'] ?? null);
            }
            $categories[$key]['products'] = $productsArr ?? null;
            // unset($categories[$key]['products_super']);
        }
        $result['categories'] = $categories;

        if(!$result)
        {
            return false;
        }
        return $result;
    }

    public function productListInCategory($shopID, $offset = 0, $limit = 5000)
    {
        $shop = $this->detail($shopID, true);
        if(!$shop || !$shop['id'])
        {
            return false;
        }



        $categories = Category::where('shop_id', $shop['id'])
        ->where('enable', true)
        ->orderBy('index','asc')
        ->with('translation')
        // ->with('products_super')
        ->get();


        $productCount = 0;
        $productTempArray = [];

        if(count($categories) > 0){
            foreach ($categories as $key => $category) {
                if($category['id'] == 'temp_cat') continue;
                $products = [];
                $categoriesNet = ProductCategory::where('category_id',$category['id'])->with('products_enable')->limit(500)->orderBy('index','asc')->get()->toArray();
                foreach ($categoriesNet as $keyP => $catNet) {
                    if(isset($catNet['products_enable']) && $catNet['products_enable'])
                    if($catNet['products_enable']){
                        $products[] = $catNet['products_enable'];
                        array_push($productTempArray, $catNet['products_enable']);
                        if(is_array($catNet['products_enable'])){
                            $productCount += count($catNet['products_enable']);
                        }
                    }
                }
                $categories[$key]['products'] = $products;
                // unset($categories[$key]['products_super']);
            }
        }

        $allProducts = Product::where('shop_id', $shopID)
            ->where('is_main', true)->with('translation')
            ->whereNull('parent_id')
            ->where('enable', true);
        $allProducts->orderBy('name', 'asc');
        $allProductsArr = $allProducts->offset($offset)->limit($limit)->get()->toArray();

        // all products if above null
        if($productCount == 0){
            $productTemp = [
                'id'  => 'temp_cat',
                'name'  => __('admin.all'),
                "shop_id"=>$shop['id'],
                "enable" => true,
                "slug" => 'all_cat',
                "index" => count($categories),
                'count'  => count($allProductsArr),
                'products' => array_values($allProductsArr),
            ];
            $categories->prepend($productTemp);
        }else{
            foreach ($allProductsArr as $key1 => $product1) {
                foreach ($productTempArray as $key2 => $product2) {
                    if($product2['id'] == $product1['id']){
                        unset($allProductsArr[$key1]);break;
                    }
                }
            }
            if(count($allProductsArr)> 0){
                $productTemp = [
                    'id'  => 'other_cat',
                    'name'  => __('admin.other'),
                    "shop_id"=>$shop['id'],
                    "enable" => true,
                    "slug" => 'other_cat',
                    "index" => count($categories),
                    'count'  => count($allProductsArr),
                    'products' => array_values($allProductsArr),
                ];
                $categories->push($productTemp);
            }

        }

        // others products if above not enough active product


        $shop['categories'] = $categories;


        return $shop;
    }

    public function getOpenHours($shopId)
    {
        $shop = Shop::findOrFail($shopId);
        return json_decode($shop->open_hours, true);
    }

    public function isShopOpen($shopId)
    {
        $openHours = $this->getOpenHours($shopId);
        $now = Carbon::now();
        $dayOfWeek = strtolower($now->englishDayOfWeek);
        $currentTime = $now->format('H:i');

        if (!isset($openHours[$dayOfWeek])) {
            return false;
        }

        foreach ($openHours[$dayOfWeek] as $period) {
            if ($currentTime >= $period[0] && $currentTime < $period[1]) {
                return true;
            }
        }

        return false;
    }

    public function getNextOpeningTime($shopId)
    {
        $openHours = $this->getOpenHours($shopId);
        $now = Carbon::now();
        $currentDay = strtolower($now->englishDayOfWeek);
        $currentTime = $now->format('H:i');

        for ($i = 0; $i < 7; $i++) {
            $checkDay = strtolower($now->addDays($i)->englishDayOfWeek);

            if (isset($openHours[$checkDay]) && !empty($openHours[$checkDay])) {
                foreach ($openHours[$checkDay] as $period) {
                    if ($checkDay === $currentDay && $currentTime < $period[0]) {
                        return $now->setTimeFromTimeString($period[0]);
                    } elseif ($checkDay !== $currentDay) {
                        return $now->setTimeFromTimeString($period[0]);
                    }
                }
            }
        }

        return null; // Shop doesn't open in the next 7 days
    }
    public function getFormattedOpeningHours($shopId)
    {
        $openHours = $this->getOpenHours($shopId);
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        $formatted = [];

        foreach ($days as $day) {
            if (!isset($openHours[$day]) || empty($openHours[$day])) {
                continue;
            }

            $dayName = ucfirst(substr($day, 0, 3));
            $periods = [];

            foreach ($openHours[$day] as $period) {
                $open = Carbon::createFromFormat('H:i', $period[0])->format('ga');
                $close = Carbon::createFromFormat('H:i', $period[1])->format('ga');
                $periods[] = "$open - $close";
            }

            $formatted[] = "$dayName " . implode(', ', $periods);
        }

        return $this->combineConsecutiveDays($formatted);
    }

    public function settingShop($shopId, $setting)
    {
        $shop = Shop::find($shopId);
        $settingCurrent = $shop->settings ?? [];

        $settingNew = array_merge($settingCurrent, $setting);
        $shop->update(['settings' => ($settingNew)]);
        return $this->detail($shopId,false);

    }

    private function combineConsecutiveDays($formattedDays)
    {
        $result = [];
        $currentGroup = null;

        foreach ($formattedDays as $day) {
            list($dayName, $hours) = explode(' ', $day, 2);

            if ($currentGroup && $currentGroup['hours'] === $hours) {
                $currentGroup['end'] = $dayName;
            } else {
                if ($currentGroup) {
                    $result[] = $this->formatDayGroup($currentGroup);
                }
                $currentGroup = ['start' => $dayName, 'end' => $dayName, 'hours' => $hours];
            }
        }

        if ($currentGroup) {
            $result[] = $this->formatDayGroup($currentGroup);
        }

        return implode(', ', $result);
    }

    private function formatDayGroup($group)
    {
        $days = $group['start'] === $group['end'] ? $group['start'] : "{$group['start']}-{$group['end']}";
        return "$days {$group['hours']}";
    }

    public function CheckShopHoliday($shopId)
    {
        $shop = Shop::find($shopId);
        if(!$shop) return false;
        $settings = $shop->settings ?? [];

        $daysOff = $settings['general']['holiday_settings']['days_off'] ?? [];
        $daysOfWeekOff = $settings['general']['holiday_settings']['days_of_week_off'] ?? [];
        $daysOfMonthLunar = $settings['general']['holiday_settings']['days_of_month_lunar'] ?? [];

        // Check if today is in days_off array
        $isClosed = false;
        $reason = null;
        $today = Carbon::today()->format('Y-m-d');
        // Check specific dates
        foreach ($daysOff as $dayOff) {
            if (isset($dayOff['date']) && $dayOff['date'] === $today) {
                $isClosed = true;
                $reason = $dayOff['reason']['en'] ?? 'Holiday';
                break;
            }
        }
        // Check days of week
        if (!$isClosed && in_array(strtolower(Carbon::today()->format('l')), $daysOfWeekOff)) {
            $isClosed = true;
            $reason = "Weekly off day (" . strtolower(Carbon::today()->format('l')) . ")";
        }

        // Check lunar calendar dates (recurring monthly lunar days)
        if (!$isClosed && !empty($daysOfMonthLunar)) {
            // Get today's lunar date
            $calendar = new Calendar();
            $lunarDate = $calendar->solar2lunar(Carbon::today()->year, Carbon::today()->month, Carbon::today()->day);
            if (isset($lunarDate['lunar_day']) && in_array((string)$lunarDate['lunar_day'], $daysOfMonthLunar)) {
                $isClosed = true;
                $reason = "Lunar Calendar every month: (Day " . ($lunarDate['lunar_day']) . ")";
            }
        }

        return [
            'isClosed' => $isClosed,
            'reason' => $reason,
        ];
    }
}
