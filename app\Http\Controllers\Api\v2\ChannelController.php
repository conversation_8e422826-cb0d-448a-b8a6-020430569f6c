<?php

namespace App\Http\Controllers\Api\v2;

use App\Channel;
use App\ChannelConnect;
use App\Http\Requests\Channel\ChannelGetIdRequest;
use App\Interaction;
use App\Services\Chat\ChannelConnectionService;
use App\Services\Chat\ChannelService;
use App\Services\Chat\MessageService;
use App\Shop;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
class ChannelController extends Controller
{
    public function create(Request $request){
        $data = $request->only([
           'id',
           'type',
           'members'
        ]);

        $service = new ChannelService($data['id'], $data['type']);
        $channel = $service->create($data['members']);
    }

    public function list(Request $request){
        $data = request()->only(['member_id','member_type', 'limit', 'offset', 'query_type']);
        $limit = $data['limit'] ?? 10;
        $offset = $data['offset'] ?? 0;
        $member_type = $data['member_type'] ?? 'user';
        $member_id = $data['member_id'] ?? '';
        $query_type = $data['query_type'] ?? 1;
        $member_id = ($member_type == 'shop' ? $member_id : Auth::user()->id);
        if(!$member_id){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'This shop is not exists or not belongs to your account.'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $service = new ChannelService($member_id);
        $channels = ($query_type == 1 ? $service->list($limit, $offset) : $service->getListChannels());
        $count = ChannelConnect::on('pgsqlReplica')->where([
            'member_id' => $member_id,
            'member_type' => $member_type
        ])->count();

        // Số channel chưa xem
        $unreadCount = Interaction::on('pgsqlReplica')->where([
            'object_type_a' => config('constants.object_type.channel'),
            'object_id_b' =>  $member_id,
            'interaction_type' => config('constants.interaction.unread')
        ])->count();

        $result = [
            'count' => $count,
            'unread_count' => $unreadCount,
            'result' => $channels
        ];
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'data' => $result
        ], Response::HTTP_OK);
    }

    //Trả về channel_id mà 2 thành viên trong request đều cùng sở hữu
    function getChannelIdByTwoMember (ChannelGetIdRequest $request){
        $data = $request->only(['member_a_id', 'member_a_type' , 'member_b_id']);
        $channelService= new ChannelService($data['member_a_id']);
        if(!ChannelConnectionService::checkAuthor($data['member_a_id'], $data['member_a_type'])){
            $response = [
                'status' => JsonResponse::HTTP_UNAUTHORIZED,
                'body' => [
                    'message' => 'Not Authorized.',
                ]
            ];
            return response() ->json($response, Response::HTTP_OK);
        }

        $channelId =  $channelService->getChannelWithReceiver($data['member_b_id']);

        if($channelId){
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $channelId
                ]];
        }else {
            $response = [
                'status' => JsonResponse::HTTP_NOT_FOUND,
                'body' => [
                    'data' => [],
                    'message' => 'Channel not found.'
                ]];
        }

        return response() ->json($response, Response::HTTP_OK);
    }

    public function countChannelUnread()
    {
        $userId = Auth::user()->id;

        $data = ChannelService::countChannelUnread($userId);

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $data
            ]
        ]);
    }

    public function update(Request $request){

    }

    public function delete(Request $request){

    }

}
