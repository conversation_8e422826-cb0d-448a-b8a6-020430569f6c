<?php

namespace App\Http\Requests\Product;

use App\Http\Requests\BaseRequest;

class ProductCheckIdRequest extends BaseRequest
{
   
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:products,id'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'Product_001_E_012',
    //         'id.uuid' => 'Product_002_E_013',
    //         'id.exists' => 'Product_003_E_014',
    //     ];
    // }
}
