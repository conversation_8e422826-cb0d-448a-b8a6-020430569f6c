<html lang="en">

<head>
    <meta charset="utf-8">
    <base href="/">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <style>
        .div-head {
            background-image: url("https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/head-image-forget.png");
            background-repeat: no-repeat, no-repeat;
            background-size: cover;
            margin: 0 auto;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: stretch;
            justify-content: flex-end;
            align-content: flex-end;
        }

        .div-head>div {
            background: rgb(13 113 255/95%);
            height: fit-content;
            width: 100%;
            display: flex;
            margin-top: 50%;
        }

        .div-intro {
            width: 100%;
            margin: 0 auto;
            color: white;
            background: #5b606c;
            display: flex;
            align-items: center;
        }

        .div-foot {
            background-image: url("https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/foot-image.png");
            height: 260px;
            min-height: unset;
            background-repeat: no-repeat, no-repeat;
            background-size: cover;
            margin: 0 auto;
        }

        @media screen and (min-width: 766px) {
            .div-container {
                width: 700px !important;
            }

            .div-foot {
                height: 140px !important;
                min-height: unset !important;
            }
        }
    </style>
</head>

<body>
    <div class="div-container" style="margin: 0 auto;
        font-family: 'Open Sans', Roboto, -apple-system, BlinkMacSystemFont, Oxygen, Ubuntu, Cantarell, sans-serif;
        background-color: white;
        padding: 0 0 25px 0; 
        font-size: 15px;
        max-width: 700px;
        width: 92vw;">
        <div class="div-intro" style="
            margin: 0 auto;
            color: white;
            background: #5b606c;
            display: flex;
            width: 100%;">
            <div style="padding: 20px; width: 100%; font-size: 13px;">
                <span>
                    Trợ lý bất động sản mạnh mẽ, minh bạch.<br>
                    Chúng tôi rất mong mỏi những phản hồi đóng góp, cải thiện từ bạn.
                </span>
            </div>

        </div>
        <div class="row header" style="margin: 0 auto;
            display: flex;
            align-items: center ;
            padding: 20px 0;
            width: 100%;
            background: #f5f6fa;">
            <img src="https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/email-type-3.png" 
                style="padding: 0px 10px;
                width: 40%;
                max-width: 125px;
                height: auto;
                margin: auto 0;
                image-rendering: -webkit-optimize-contrast;">
            <span style="margin: 5px 0px;
                font-size: 18px;
                font-weight: bolder;
                font-family: 'Open Sans', Roboto, -apple-system, BlinkMacSystemFont, Oxygen, Ubuntu, Cantarell, sans-serif;
                color: #5b606c;
                width:fit-content; 
                white-space: nowrap;">
                Kích hoạt tài khoản
            </span>
        </div>

        <div class="div-head" style="
                    background-image: url('https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/head-image.png');
                    background-size: 100% 100%;
                    background-position: center;
                    margin: 0 auto;
                    height: 400px;
                    color: white;
                    display: flex;
                    flex-direction: row;
                    align-items: flex-end;
                    justify-content: center;
                    width: 100%;">

            <div style="background: rgb(13, 113, 255, 95%);
                    height: fit-content;
                    margin-top: auto;
                    padding: 1em;
                    width: inherit;">
                <span>
                    Xin chào {{$name}}, bạn vừa mới tạo một tài khoản trên hệ thống sử dụng e-mail <span
                        style="color:white;">{!! $email !!}<span>
                        </span>
            </div>


        </div>

        <div class="confirm" style="
            background-color: white;
            height: fit-content; 
            padding: 1em;
            width: initial;">
            <div class="primary-action" style=" width: 100%;
                text-align: center;">
                <span style="font-size: 33px;
                    font-weight: bold;
                    color: #353b48;
                    padding-top: 13px;">
                    Nhấn vào nút xác thực bên dưới để hoàn thành bước cuối cùng & bắt đầu trải nghiệm
                </span>
                <br>
                <!-- <tr style="box-sizing: border-box; font-size: 14px; margin: 0;"> -->
                <p class="content-block" itemprop="handler" itemscope="" itemtype="http://schema.org/HttpActionHandler"
                    style="box-sizing: border-box; 
                            vertical-align: top; 
                            margin: 0; 
                            padding: 25px 0 0;
                            text-align: center;">
                    <a href="{{env('CLIENT_URL').$id.'/'.$token}}" class="btn-primary" itemprop="url" style="box-sizing: border-box; 
                        color: #FFF; 
                        text-decoration: none; 
                        line-height: 2em; 
                        font-weight: bold; 
                        font-size: 17px;
                        text-align: center; 
                        cursor: pointer; 
                        display: inline-block; 
                        border-radius: 5px; 
                        background-color: #0d71ff; 
                        margin: 0; 
                        border-color: #0d71ff; 
                        border-style: solid; 
                        border-width: 8px 16px;
                        width: 300px">Xác thực tài khoản</a>
                </p>
                <!-- </tr> -->
            </div>

            <br>

            <div style="padding: 0 1em;
                        width: initial;
                        font-weight: 500;
                        font-size: 15px;">
                <p style="color:#353b48;
                        word-break: break-word;">Trong trường hợp nút bên trên không hoạt động, hãy thử dán đường link
                    này vào trình duyệt: <span style="color: #146eed;">{{env('CLIENT_URL').$id."/".$token}}</span> </p>
            </div>
        </div>

        <!-- <div class="row div-foot" style="background-image: url(https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/foot-image.png);
            width: 100%;
            height: auto;
            min-height: 300px;
            background-repeat: no-repeat, no-repeat;
            background-size: contain;
            background-position: center;
            margin: 0 auto;">
            

        </div> -->
        <img src="https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/foot-image.png" width="100%" >
        <div class="social-contact" style="margin: 0 auto;
            background-color:#666666;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1em 15px;
            width: initial;
            color: white;">
            <!-- <img src="https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/follow.png" width="100%" > -->
            <div style="margin: auto 0; font-weight:500">
                Theo dõi NomNomLand
            </div>
            <div style="display: flex;
                    margin-left: auto;">
                <a style="margin-left: 1em;
                height: 2em;
                display: flex;
                align-items: center;" href="https://www.facebook.com/NomnomlandOfficialPage" target="_blank">
                    <img style="height: 100%; image-rendering: -webkit-optimize-contrast;" src="https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/1.png">
                </a>
                <a style="margin-left: 1em;
                height: 2em;
                display: flex;
                align-items: center;" href="https://zalo.me/3489447386936754569" target="_blank">
                    <img style="height: 100%; image-rendering: -webkit-optimize-contrast;" src="https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/3.png">
                </a>
                <a style="margin-left: 1em;
                height: 2em;
                display: flex;
                align-items: center;" href="https://www.youtube.com/channel/UCOpRrLHae4YSrGyXGycgV7w" target="_blank">
                    <img class="youtube" style="height: 80%; margin: auto 0; image-rendering: -webkit-optimize-contrast;"
                        src="https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/2.png">
                </a>

            </div>

        </div>

        <div style="font-weight: 500; padding: 1em; background: white; color: #353b48;">

            <span style="color:#353b48">
                Nếu bạn không phải là người thực hiện việc tạo tài khoản với email trên, vui lòng bỏ qua email xác thực
                này.<br>
                Xem qua <a href="{{env('CLIENT_SECURITY_URL')}}" style="color: #0d71ff;">Chính sách bảo mật</a> <span>&</span> <a
                    href="{{env('CLIENT_POLICY_URL')}}" style="color: #0d71ff;">Điều khoản sử dụng. </a><br>
            </span>
            <br>


            <div style="display: flex; justify-content: space-between;">
                <div style="width: 60%;color: #353b48; padding-right: 8px; font-size: 15px;">
                    © 2021 Nomnomland Inc.<br>
                    111/24 Hùng Vương, P.Lộc Thọ, TP. Nha Trang<br>
                    Khánh Hòa (650000)
                </div>
                <div style="
                            margin-left: auto;
                            padding-top: 1em;
                                ">
                    <img src="https://s3.nomnomland.com/pro.nomnomland.uploads/email-image/footer-logo.png"
                        style="width: 125px;
                            max-width: 125px;
                            margin-left: auto;
                            height: auto;
                            image-rendering: -webkit-optimize-contrast;">
                </div>
            </div>
        </div>
    </div>
</body>



</html>