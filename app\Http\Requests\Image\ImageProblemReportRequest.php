<?php

namespace App\Http\Requests\Image;

use  App\Http\Requests\BaseRequest;

class ImageProblemReportRequest extends BaseRequest
{

    public function rules()
    {

        return [
            'path' => 'bail|required|base64image|base64size',
            'object_type' => 'required|in:7',
            'parent_id' => 'required|uuid|exists:problem_reports,id',
            'title' => 'nullable|max:255',
            'description' => 'nullable|max:5000',
            'orientation'   => 'bail|nullable|in:0,1,2,3,4,5,6,7,8,9',
        ];
    }
    // public function messages()
    // {
    //     return [
    //         'path.required' => 'Image_001_E_001',
    //         'path.base64size' => 'Image_004_E_002',
    //         'path.base64image' => 'Image_002_E_003',

    //         'object_type.required' => 'Image_001_E_004',
    //         'object_type.in' => 'Image_002_E_005',
    //         // 'object_type.min' => 'Image_004_E_006',
    //         // 'object_type.max' => 'Image_004_E_007',

    //         'parent_id.required' => 'Image_001_E_008',
    //         'parent_id.uuid' => 'Image_002_E_009',
    //         'parent_id.exists' => 'Image_003_E_010',
    //         // 'orientation.integer' => 'Image_003_E_011',
    //         'orientation.in' => 'Image_002_E_011',

    //         'is_profile_picture.boolean' => 'Image_002_E_017',

    //         'description.max'          => 'Image_004_E_021',
    //         'title.max'             => 'Image_004_E_022',

    //         'orientation.in' => 'Image_002_E_023',


    //     ];
    // }
}
