<?php

namespace App\Http\Requests\Role;

use App\Http\Requests\BaseRequest;

class RoleCheckIdRequest extends BaseRequest
{
    
    public function rules()
    {
        return [
            'id' => 'required|integer|exists:roles,id'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'Role_001_E_003',
    //         'id.integer' => 'Role_002_E_004',
    //         'id.exists' => 'Role_003_E_005'
    //     ];
    // }
}
