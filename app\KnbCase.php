<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class KnbCase extends Model
{
    protected $connection = 'mysqlSP';
    protected $fillable = [
    	 'name', 'description', 'created_at', 'modified_at'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model){
            $timestamp = time();
            // SQL query:
            // INSERT INTO `case` (`id`, `name`, `deleted`, `status`, `priority`, `type`, `description`, `created_at`, `modified_at`, `account_id`, `lead_id`, `contact_id`, `inbound_email_id`, `created_by_id`, `modified_by_id`, `assigned_user_id`)
            // VALUES ('1676962742_63f46bb69e061', 'tesst casse', '0', 'New', 'Normal', '', 'description content', now(), now(), NULL, NULL, NULL, NULL, NULL, NULL, NULL);
            $model->id = $timestamp ."_".uniqid();//Str::uuid();
        });
    }

    public $incrementing = false;
    protected $primaryKey = 'id';
    public $timestamps = false;
    protected $table = 'case';
}
