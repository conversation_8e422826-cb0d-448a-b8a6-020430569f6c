<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->string('id');
            $table->string('name');
            $table->string('email')->unique()->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->text('token')->nullable();
            $table->smallInteger('role_id');
            $table->string('created_by')->nullable();
            $table->string('phone')->nullable();
            $table->string('user_name')->nullable();
            $table->boolean('gender')->default(0);
            $table->date('date_of_birth')->nullable();
            $table->string('identity_card')->nullable();
            $table->integer('province_id')->nullable();
            $table->integer('district_id')->nullable();
            $table->integer('ward_id')->nullable();
            $table->string('address')->nullable();
            $table->string('provider_name')->nullable();
            $table->string('provider_id')->nullable();
            $table->string('profile_picture')->nullable();
            $table->boolean('is_new')->nullable()->default(false);
            $table->text('property_like')->nullable();
            $table->boolean('enable')->default(true);
            $table->text('description')->nullable();
            $table->smallInteger('rating')->nullable()->default(10);
            $table->string('background_picture')->nullable();
            $table->string('organize_id')->nullable();
            $table->string('custom_path', 255)->nullable();
            $table->index('created_by');
            $table->index('enable');
            $table->index('updated_at');
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
