<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddColProvince extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('dvhc2021_tinh', function (Blueprint $table) {
            $table->string('a')->nullable();
            $table->string('b')->nullable();
            $table->string('f')->nullable();
            $table->string('LAM0')->nullable();
            $table->string('PHI0')->nullable();
            $table->string('E0')->nullable();
            $table->string('N0')->nullable();
            $table->string('F0')->nullable();
            $table->string('DX')->nullable();
            $table->string('DY')->nullable();
            $table->string('DZ')->nullable();
            $table->string('X_Rotation')->nullable();
            $table->string('Y_Rotation')->nullable();
            $table->string('Z_Rotation')->nullable();
            $table->string('Scale')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('dvhc2021_tinh', function (Blueprint $table) {
            //
        });
    }
}
