<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Channel extends Model
{
    protected $table = 'channels';

    protected $fillable = [
        'name',
        'type'
    ];

    protected $primaryKey = 'id';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }

    protected $hidden = ['updated_at', 'created_at'];

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function latestMessage()
    {
        return $this->hasMany(Message::class)->latest();
    }

    // Tin nhắn cuối cùng với mối quan hệ 1-1
    public function latest_message()
    {
        return $this->hasOne(Message::class)->latest();
    }

    // <PERSON>hai báo mối quan hệ với channel_connects
    public function channelConnects()
    {
        return $this->hasMany(ChannelConnect::class);
    }
    // <PERSON><PERSON> báo mối quan hệ với channel_connects nhưng title khác
    public function members()
    {
        return $this->hasMany(ChannelConnect::class);
    }
}
