<?php

namespace App;

use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Image;
use App\Rating;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;
// use Illuminate\Database\Eloquent\SoftDeletes;
use App\Helpers\Helper;
use Illuminate\Support\Facades\Log;

class User extends Authenticatable implements JWTSubject
{
    use Notifiable;
    // use SoftDeletes;
    CONST ROLE_ADMIN = 1;
    CONST ROLE_AGENT = 4;
    CONST ROLE_DRIVER = 5;
    CONST ROLE_USER = 6;
    CONST ROLE_SHOP_OWNER = 7;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */    protected $fillable = [
        'name', 'email', 'password',
        'role_id', 'gender', 'address',
        'phone', 'date_of_birth', 'identity_card',
        'province_id', 'district_id', 'ward_id',
        'provider_name', 'provider_id','token','user_name',
        'profile_picture', 'is_new', 'description',
        'created_by','background_picture','enable', 
        'custom_path', 'parent_id','deleted_at', 'notes',
        'latitude','longitude','user_status', 'total_rating', 
        'last_action_at', 'referral_code'
    ];


    protected $hidden = [
        'password', 'remember_token','token', 'created_at', 'updated_at',
        'email_verified_at'
    ];

    protected $keyType = 'string';
    public $incrementing = false;
    protected $primaryKey = 'id';
    protected $table = 'users';
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'total_rating' => 'float',

    ];

    protected static function boot()
    {
        // for ($i=0; $i <1000 ; $i++) {
        //     echo Str::uuid()."\n";
        // }
        // exit;
        parent::boot();        static::saving(function ($model) {
            $model->name = preg_replace('([\s]+)', ' ', $model->name);
            $model->email = $model->email != '' ? Str::lower($model->email) : null;
            $model->user_name = $model->user_name != '' ? Str::lower($model->user_name) : null;
            
            // Normalize referral code to uppercase if provided
            if ($model->referral_code) {
                $model->referral_code = strtoupper(trim($model->referral_code));
            }
            
            Helper::flushCacheByTag(env('APP_ENV').":".'dashboard');
        });
        static::creating(function ($model){
            $model->id = Str::uuid();
            // $model->created_by = Auth::id();
            $model->password = $model->password != null ? bcrypt($model->password) : null;
            $model->email = $model->email != '' ? Str::lower($model->email) : NULL;
            $model->user_name = $model->phone != '' ? $model->phone : Helper::generateShortUnique($model->name);
            // do {
            //     $model->user_name = $model->user_name != '' ? Str::lower($model->user_name) : Helper::generateShortUnique($model->name);
            // } while (self::where('user_name', $model->user_name)->exists());
        });

        // static::retrieved(function ($model) {
        //     $check = Image::where([['parent_id',$model->id],['object_type',config('constants.properties.user_image')]])->select('id')->first();
        //     $model->profile_picture = empty($check)?$check:$check->id;

        // });
    }


    // public function roles()
    // {
    //   return $this->belongsToMany(Role::class);
    // }
    // public function authorizeRoles($roles)
    // {
    //   if (is_array($roles)) {
    //       return $this->hasAnyRole($roles) ||
    //              abort(401, 'This action is unauthorized.');
    //   }
    //   return $this->hasRole($roles) ||
    //          abort(401, 'This action is unauthorized.');
    // }
    // /**
    // * Check multiple roles
    // * @param array $roles
    // */
    // public function hasAnyRole($roles)
    // {
    //   return null !== $this->roles()->whereIn('name', $roles)->first();
    // }
    // /**
    // * Check one role
    // * @param string $role
    // */
    // public function hasRole($role)
    // {
    //   return null !== $this->roles()->where('name', $role)->first();
    // }
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    public function roles()
    {
        return $this->belongsTo(Role::class, 'role_id');
    }
    public function districts()
    {
        return $this->belongsTo(District::class,'district_id');
    }
    public function provinces()
    {
        return $this->belongsTo(Province::class,'province_id');
    }
    public function wards()
    {
        return $this->belongsTo(Ward::class,'ward_id');
    }
    public function shop()
    {
        return $this->hasMany(Shop::class,'user_id');
    }
    public function rating()
    {
        return $this->hasMany(Rating::class,'object_id');
    }

    public function messages()
    {
        return $this->morphMany(Message::class, 'member');
    }

    public function channel()
    {
        return $this->morphMany(ChannelConnect::class, 'member');
    }

    // public function profile_picture()
    // {
    //     return $this->belongsTo(Image::class, 'profile_picture');//->select('id','path', 'title', 'parent_id');
    // }

    // public function Avatar()
    // {
    //     return $this->belongsTo(Image::class, 'profile_picture');
    // }

    // public function background()    // {
    //     return $this->belongsTo(Image::class, 'background_picture');    // }

    /**
     * Get the user who referred this user (if any) - this would require tracking the referrer per user
     * For now, this relationship is not implemented as we track referrals per order
     */
    public function referredBy()
    {
        // This relationship is not currently implemented
        // Referrals are tracked per order, not per user
        return null;
    }

    /**
     * Get users that were referred by this user (through orders)
     */
    public function referrals()
    {
        // Get unique users who have placed orders using this user's referral
        return $this->hasManyThrough(
            User::class,
            Order::class,
            'ref_id', // Foreign key on orders table
            'id',     // Foreign key on users table
            'id',     // Local key on this table
            'user_id' // Local key on orders table
        );
    }

    /**
     * Get orders that were referred by this user
     */
    public function referredOrders()
    {
        return $this->hasMany(Order::class, 'ref_id');
    }    /**
     * Generate a unique referral code for the user
     */
    public function generateReferralCode()
    {
        do {
            $code = 'REF' . strtoupper(Str::random(6));
        } while (User::where('referral_code', $code)->exists());
        
        $this->referral_code = $code;
        $this->save();
        
        return $code;
    }    /**
     * Get user by referral code (case-insensitive)
     */
    public static function findByReferralCode($code)
    {
        // Normalize to uppercase for case-insensitive search
        $normalizedCode = strtoupper(trim($code));
        return self::where('referral_code', $normalizedCode)->first();
    }    
    /**
     * Get referral statistics for this user
     */
    public function getReferralStats()
    {
        // Count unique users who placed orders using this user's referral
        $uniqueReferredUsers = $this->referredOrders()
            ->distinct('customer_id')
            ->count('customer_id');
            
        $totalReferredOrders = $this->referredOrders()->count();
        
        // Calculate total value of referred orders (if orders have a total_amount field)
        $totalReferredOrderValue = $this->referredOrders()->sum('grand_total');
        
        return [
            'referral_code' => $this->referral_code,
            'total_referrals' => $uniqueReferredUsers, // Unique users who used the referral code
            'total_referred_orders' => $totalReferredOrders,
            'successful_referrals' => $uniqueReferredUsers, // Same as total_referrals for now
            'total_referred_order_value' => $totalReferredOrderValue,            'recent_referred_orders' => $this->referredOrders()
                ->with('customer:id,name,email')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get()
        ];
    }
}
