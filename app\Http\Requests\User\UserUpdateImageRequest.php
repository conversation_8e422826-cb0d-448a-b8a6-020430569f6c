<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UserUpdateImageRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'id'                => 'required|uuid|exists:users,id',
            'file'              => 'nullable|base64image|base64size',
            'file_background'   => 'nullable|base64image|base64size',
            'orientation_avatar'=> 'bail|nullable|in:0,1,2,3,4,5,6,7,8',
        ];
    }

    // public function messages()
    // {
    //     return [

    //         'id.required'                   => 'User_001_E_030',
    //         'id.uuid'                       => 'User_002_E_031',
    //         'id.exists'                     => 'User_003_E_032',

    //         'file.base64image'              => 'User_002_E_043',
    //         'file.base64size'               => 'User_004_E_044',

    //         'file_background.base64image'   => 'User_002_E_058',
    //         'file_background.base64size'    => 'User_004_E_059',

    //         'orientation_avatar.in'         => 'User_002_E_060',
    //     ];
    // }
}
