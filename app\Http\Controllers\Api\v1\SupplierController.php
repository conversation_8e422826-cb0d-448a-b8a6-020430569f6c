<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Support\Facades\Auth;
use App\Supplier;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class SupplierController extends Controller
{
    private $_userId;

    public function list(Request $request){
        $data = $request->only([
            'shop_id'
        ]);
        $shopId = isset($data['shop_id']) ? $data['shop_id'] : '';
        if($shopId == ''){
            $this->_userId = Auth::check() ? Auth::user()->id : null;
            $user = User::where('id',$this->_userId)->with('shop')->first();
            if($user){
                if(isset($user->shop[0])){
                    $shopId = $user->shop[0]->id;
                };
            };
        }
        $result = [];
        if($shopId){
            $result = Supplier::where('shop_id', $shopId)->get();
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
    // -------create order---------------
    public function add(Request $request)
    {
        $data = $request->only([
            'name',
            'phone',
            'address',
            'email',
            'shop_id'
        ]);
        $shopId = isset($data['shop_id']) ? $data['shop_id'] : '';
        if($shopId == ''){
            $this->_userId = Auth::check() ? Auth::user()->id : null;
            $user = User::where('id',$this->_userId)->with('shop')->first();
            if($user){
                if(isset($user->shop[0])){
                    $shopId = $user->shop[0]->id;
                };
            };
        }
        $result = [];
        if($shopId){
            $supplier = [];
            $supplier['name'] = $data['name'];
            $supplier['phone'] = $data['phone'];
            $supplier['address'] = $data['address'];
            $supplier['email'] = $data['email'];
            $supplier['shop_id'] = $shopId;
            $result = Supplier::create($supplier);
        }
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Supplier  $supplier
     * @return \Illuminate\Http\Response
     */
    public function show(Supplier $supplier)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Supplier  $supplier
     * @return \Illuminate\Http\Response
     */
    public function edit(Supplier $supplier)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Supplier  $supplier
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Supplier $supplier)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Supplier  $supplier
     * @return \Illuminate\Http\Response
     */
    public function destroy(Supplier $supplier)
    {
        //
    }
}
