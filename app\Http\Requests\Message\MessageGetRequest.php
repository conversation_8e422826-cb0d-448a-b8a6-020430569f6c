<?php

namespace App\Http\Requests\Message;

use App\Http\Requests\BaseRequest;

class MessageGetRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'channel_id' => 'required|uuid|exists:channels,id' ,
            'limit'      => 'nullable|int',
            'offset'     => 'nullable|int',
            'language'   => 'nullable|string',
            'member_id'  => 'required_if:member_type,shop|uuid',
            'member_type'=> 'required|string|in:shop,user'
        ];
    }
}
