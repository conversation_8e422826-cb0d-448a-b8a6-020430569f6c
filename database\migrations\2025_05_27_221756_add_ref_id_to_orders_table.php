<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRefIdToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->uuid('ref_id')->nullable()->after('delivery_price_estimate');
            $table->string('referral_code')->nullable()->after('delivery_price_estimate');
            $table->index('ref_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['ref_id']);
            $table->dropColumn('ref_id');
            $table->dropColumn('referral_code');
        });
    }
}
