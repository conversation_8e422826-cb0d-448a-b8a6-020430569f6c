<?php 
namespace App\Admin\Actions;

use App\User; // Add this line
use App\Token; // Add this line
use App\Services\Token\TokenService;
use Encore\Admin\Actions\RowAction;

class EnableDisable extends RowAction
{
    public function name()
    {
        return $this->row->enable ? 'Disable' : 'Enable';
    }

    public function handle($model)
    {
        // Toggle the enabled status
        $actionName = $model->enable ? __('admin.disable') : __('admin.enable');
        $model->enable = !$model->enable;

        if ($model instanceof User) { // Check if the model is User
            if ($actionName == 'Disable') {
                $model->token = null;
                // Disable user's token in the Token table by user_id
                // Token::where('user_id', $model->id)->update(['token' => null]);
                TokenService::deleteList(['id' => $model->id]);
            }
        }
        $model->save();

        return $this->response()->success($actionName . ' Success!')->refresh();
    }
}