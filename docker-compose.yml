version: "3"

services:
    php:
        ports:
          - "8114:80"
        image: "registry.gitlab.com/nomnom-team/clomart-backend:pro-latest"
        volumes:
        #     - '~/source/clomart-backend:/workspace/clomart-backend'
            - '~/source/clomart-backend/storage/logs:/workspace/clomart-backend/storage/logs'
        #     - ./php/local.ini:/usr/local/etc/php/conf.d/local.ini
        container_name: api.clomart-backend
        environment:
            CONTAINER_ROLE: app
            TZ: Asia/Bangkok
            PHP_EXTENSION_MYSQLI: 1
            PHP_EXTENSION_GD: 1
            PHP_EXTENSION_IMAGICK: 1
            PHP_EXTENSION_EXIF: 1
            PHP_EXTENSION_ZIP: 1
            PHP_INI_MAX_EXECUTION_TIME: 3000
            PHP_INI_MAX_INPUT_TIME: 3000
            PHP_INI_MEMORY_LIMIT: 4G
            PHP_INI_POST_MAX_SIZE: 40M
            PHP_INI_UPLOAD_MAX_FILESIZE: 40M
            PHP_INI_ALLOW_URL_FOPEN: 1
            APACHE_DOCUMENT_ROOT: "public/"
            VIRTUAL_HOST: api.remagan.com
            LETSENCRYPT_HOST: api.remagan.com
            LETSENCRYPT_EMAIL: <EMAIL>
        restart: always
    queue-default:
        image: "registry.gitlab.com/nomnom-team/clomart-backend:pro-latest"
        # volumes:
        #       - '~/source/clomart-backend:/workspace/clomart-backend'
              # - '/workspace/clomart-backend/storage/logs'
        environment:
          CONTAINER_ROLE: queue
        restart: always
        deploy:
          replicas: 1
          # restart_policy:
          # condition: on-failure
          # max_attempts: 1
    crontab:
      image: "registry.gitlab.com/nomnom-team/clomart-backend:pro-latest"
      # volumes:
      #   - '~/source/clomart-backend:/workspace/clomart-backend'
        # - '/workspace/clomart-backend/storage/logs'
      environment:
        CONTAINER_ROLE: scheduler
      deploy:
        replicas: 1
        restart_policy:
          condition: on-failure
          max_attempts: 1 
networks:
  default:
    external:
      name: nginx-proxy-manager
