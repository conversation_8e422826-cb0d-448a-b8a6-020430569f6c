<?php

namespace App\Http\Controllers\Api\v1;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Token;
use App\Services\Auth\AuthService;
use App\Services\Auth\RegisterService;
use App\Http\Requests\User\UserUpdateRequest;
use App\Http\Requests\User\UserUpdatePasswordRequest;
use App\Http\Requests\User\ForgetPasswordRequest;
use App\Http\Requests\User\UpdateForgetPasswordRequest;
use App\Http\Requests\User\ConfirmCodeRequest;
use App\Http\Requests\User\UserUpdateImageRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use App\Http\Requests\User\CheckUniRequest;
use App\Http\Requests\User\RemoveListRequest;
use App\Http\Requests\User\RemoveRequest;
use App\Http\Requests\User\ListUserRemovedRequest;
use App\Http\Requests\User\DeleteListUserRequest;
use App\Http\Requests\User\RestoreListUserRequest;
use App\Http\Requests\User\RestoreUserRequest;
use App\Http\Requests\User\LoginZalo;
use Illuminate\Support\Facades\Redis;
use Tymon\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{
    public function userInfor()
    {
        $service = new AuthService(Auth::user()->id);
        $user = $service->getUserInfor();
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'data' => $user,
        ]);
    }
 
    public function list(Request $request)
    {
        $service = new AuthService(Auth::user()->id);
        $data = (array)$request->only([
            'limit',
            'offset',
        ]);
        var_dump($data);
        $list = $service->list($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'count'=> $list['count'],
                'data' => $list['result']
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
 
    public function update(UserUpdateRequest $request, AuthService $service)
    {
        $data = (array)$request->only([
            'name','email',
            'gender','phone','id',
            'date_of_birth', 'identity_card',
            'province_id', 'district_id',
            'ward_id', 'address','user_name',
            'description',
            'role_id', 'custom_path',
            'latitude', 'longitude',
        ]);

        $result = $service->update($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_011'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function updatePassword(UserUpdatePasswordRequest $request)
    {
        $service = new AuthService(Auth::user()->id);
        $data = (array)$request->only([
            'password'
        ]);

        $result = $service->updatePassword($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }


    public function logout(AuthService $service)
    {
        $user = Auth::user();
        $token = explode(' ', request()->header('Authorization'))[1];
        $payload = JWTAuth::parseToken()->getPayload();
        $userId  = $payload->get('sub');
        $jti     = $payload->get('jti');
        // $deviceId = request()->header('X-Device-Id');
        $deviceId = request()->input('device_id') ?? NULL;
        if($deviceId){
            $token = Token::where('device_id', $deviceId)->where('user_id', $user->id)->first();
            if(!$token){
                $response = [
                    'status' => JsonResponse::HTTP_NOT_FOUND,
                    'body' => 'Device not found',
                ];
                return response()->json($response, JsonResponse::HTTP_NOT_FOUND);
            }
            $token->delete();
        }else{
            Token::where('user_id', $user->id)->where('token', $token)->delete();
        }
        Redis::del(env('APP_ENV') . "_user_token:" . $user->id);
        // JWTAuth::invalidate($token); //blacklist mặc định của JWT
        Redis::setex(env('APP_ENV') . "_token_blacklist:" . $jti, 86400*30, true);

        // Auth::logout();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => 'Successfully logged out',
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function refresh()
    {
        return $this->respondWithToken(auth()->refresh());
    }

    protected function respondWithToken($token)
    {
        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => auth('api')->factory()->getTTL() * 60
        ]);
    }

    public function delete(UserDeleteRequest $request, AuthService $service)
    {
        $data = (array)$request->only([
            'id'
        ]);

        $result = $service->delete($data);

        if ($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_011',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function forgetPassword(ForgetPasswordRequest $request, AuthService $service)
    {
        $email = (array)$request->only(['email']);
        $result = $service->forget($email);
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_014',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'lock')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_003"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'verify')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_002"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' =>$result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function confirmCode(ConfirmCodeRequest $request,AuthService $service)
    {
        $data = (array)$request->only(['id','key']);
        $result = $service->confirmCode($data);
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_011',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'lock')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_003"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'verify')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_002"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' =>$result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function changeForgetPassword(UpdateForgetPasswordRequest $request,AuthService $service)
    {
        $data = (array)$request->only(['id','password','key']);
        $result = $service->updateForgetPassword($data);
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_032',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'lock')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_003"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'verify')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_002"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' =>$result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function detail($id = null, AuthService $service)
    {
        $result = $service->detail($id);

        if ($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_011',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function exportUUID()
    {
        for ($i=0; $i < 4000 ; $i++) {
            echo  Str::uuid()."\n";
        }
    }

    public function filter(Request $request,AuthService $service)
    {
        $data = (array)$request->only([
            'name',
            'email',
            'role',
            'gender',
            'phone',
            'phone_op',
            'limit',
            'offset'
        ]);
        $result = $service->filter($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'count' => $result['count'],
                'data'  => $result['result']
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function checkUnique(CheckUniRequest $request,AuthService $service)
    {
        $data = (array)$request->only([
            'email',
            'user_name',
            'status',
        ]);
        $result = $service->checkUnique($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function updateImage(UserUpdateImageRequest $request,AuthService $service)
    {
        $data = $request->only([
            'id',
            'file',
            'orientation_avatar'
        ]);
        $result = $service->updateImage($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function remove(RemoveRequest $request, AuthService $service)
    {
        $data = $request->only([
            'id'
        ]);
        $result = $service->remove($data);
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_001'
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => []
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function removeList(RemoveListRequest $request, AuthService $service)
    {
        $data = $request->only([
            'id'
        ]);
        $service->removeList($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
          ];

          return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function listRemoved(ListUserRemovedRequest $request, AuthService $service)
    {
        $data = $request->only([
            'limit',
            'offset',
            'name',
            'sort'
        ]);
        $result = $service->listRemoved($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'count'=> $result['count'],
                'data' => $result['result']
            ]
        ];

        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function deleteList(DeleteListUserRequest $request, AuthService $service)
    {
        $data = $request->only([
            'id'
          ]);

          $result = $service->deleteList($data);

          $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
          ];

          return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function restore(RestoreUserRequest $request, AuthService $service)
    {
        $data = $request->only([
            'id',
        ]);
        $result = $service->restore($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function restoreList(RestoreListUserRequest $request, AuthService $service)
    {
        $data = $request->only([
            'id'
        ]);
        $result = $service->restoreList($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function listUserCrawl(Request $request, AuthService $service)
    {
        $data = $request->only([
            'limit',
            'offset'
        ]);
        $result = $service->listUserCrawl($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'count' =>  $result['count'],
                'data'  => $result['result']
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function updateUserCrawl(Request $request, AuthService $service)
    {
        $data = $request->only([
            'id',
            'phone'
        ]);
        $result = $service->updateUserCrawl($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function detailUserCrawl($id = null, AuthService $service)
    {
        $result = $service->detailUserCrawl($id);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function areaManager(AuthService $service)
    {
        $result = $service->areaManager();
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function loginZalo(LoginZalo $request, RegisterService $service)
    {
        $request = (array)$request->only([
            'code'
        ]);

        $result = $service->loginZalo($request);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_001"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'lock')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_003"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'path'  => $result['path'],
                'data'  => $result['result']->token
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}