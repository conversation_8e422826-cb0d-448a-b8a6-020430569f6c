<?php
namespace App\Services\Brand;

use App\Brand;
use App\Image;
use App\PlaceType;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use App\Services\GeneralService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\User;

class BrandService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

     //-------process create brand--------
    public function create(array $brand)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $brand['created_by'] = $user->id;
        }

        $result = Brand::create($brand);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.create'),
        //                 $result->id,
        //                 config('constants.object_type.brand'),
        //                 $result,
        //                 json_encode($brand));

        return $result;
    }

    //-------process listing Brand--------------------
    public function list($all)
    {
        // $general = new GeneralService($this->_userId);
        if($all == true){
            $result = Brand::orderBy('created_at','asc')->get();
        }
        else {
            $result = Brand::where('enable', true)->orderBy('created_at','asc')->get();
        }

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //------process detail Brand----------
    public function detail($id)
    {
        $result = Brand::where('id',$id)
            ->where('enable', true)
            ->first();

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //-------process update brand--------
    public function update(array $brand)
    {
        $result = Brand::find($brand['id']);

        if(!$result)
        {
            return false;
        }

        $result->update($brand);


        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.brand'),
        //                 $result,
        //                 json_encode($brand));

        return $result;

    }

    //-----process remove brand----------
    public function remove(array $brand)
    {
        $result = Brand::where([
            ['id', $brand['id']],
            ['enable', true]
        ])->first();

        if(!$result)
        {
            return false;
        }

        $brand['enable'] = false;
        $this->update($brand);

        
        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.brand'),
        //                 $result,
        //                 json_encode($brand));

        return $result;
    }

    //-----process delete brand----------
    public function delete(array $brand)
    {
        $result = Brand::find($brand['id']);

        if(!$result)
        {
            return false;
        }

        //----------- set 'parent_id = NULL' to sub-categories--------------

        $sub_parent = Brand::where('parent_id', $brand['id'])->get();

        foreach ($sub_parent as $key => $value) {
            $data = [
                'id' => $value->id,
                'parent_id' => null,
            ];
            $this->update($data);
        }

        //----------- delete action ---------------
        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.brand'),
        //                 $result,
        //                 json_encode($brand));

        return true;
    }
}
