<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Order\UpdateStatusOrder;
use App\Order;
use App\User;
use App\Product;
use App\Shop;
use App\District;
use App\Province;
use App\Ward;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
Use Encore\Admin\Widgets\Table;
use App\Admin\Selectable\Products as ProductsSelectable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use App\Services\Token\TokenService;
use App\Services\Notification\NotificationService;

use App\Admin\Actions\OrderCancel;
use App\Admin\Actions\OrderAccept;
use App\Admin\Actions\BatchDelete;


use App\Admin\Actions\Replicate;

class OrderController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Order';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Order());

        $grid->column('id', __('Id'))->link(function () {
            return "orders/".$this->id.'/edit';
        });
        // $grid->column('id', 'id')->modal('Order items', function ($model) {

        //     $items = $model->items()->take(100)->get()->map(function ($item) {
        //         return $item->only(['name','pivot']);
        //     });
        //     $items = $items->toArray();
        //     // Log::info($items);
        //     foreach ($items as $itemsKey => $item) {
        //         if(isset($item['pivot'])){
        //             $items[$itemsKey]['price'] = number_format($item['pivot']['price'],2)."đ";
        //             $items[$itemsKey]['price_off'] = number_format($item['pivot']['price_off'],2)."đ";
        //             $items[$itemsKey]['quantity'] = number_format($item['pivot']['quantity'] , 2);
        //             $items[$itemsKey]['price_total'] = number_format($item['pivot']['price_total'], 2)."đ";
        //             $items[$itemsKey]['notes'] = $item['pivot']['notes'];
        //             unset($items[$itemsKey]['pivot']);
        //         }
        //     }
        //     return new Table([__('admin.product.name'),__('admin.product.price'),__('admin.product.price_off'),__('admin.product.quantity'),__('admin.product.price_total'),__('notes')], $items);
        // });

        $grid->column('short_code', __('admin.order.short_code'))->expand(function ($model) {

            $items = $model->items()->take(100)->get()->map(function ($item) {
                return $item->only(['name','pivot']);
            });
            $items = $items->toArray();
            foreach ($items as $itemsKey => $item) {
                if(isset($item['pivot'])){
                    $items[$itemsKey]['price'] = number_format($item['pivot']['price'],2)."đ";
                    $items[$itemsKey]['price_off'] = number_format($item['pivot']['price_off'],2)."đ";
                    $items[$itemsKey]['quantity'] = number_format($item['pivot']['quantity'] , 2);
                    $items[$itemsKey]['price_total'] = number_format($item['pivot']['price_total'], 2)."đ";
                    $items[$itemsKey]['notes'] = $item['pivot']['notes'];
                    unset($items[$itemsKey]['pivot']);
                }
            }

            return new Table([__('admin.product.name'),__('admin.product.price'),__('admin.product.price_off'),__('admin.product.quantity'),__('admin.product.price_total'),__('notes')], $items);
        });

        // $grid->column('short_code', __('admin.order.short_code'))->link(function () {
        //     return "orders/".$this->id;
        // });

        // $grid->column('status', __('Status'))->display(function ($status) {
        //     return $status == 1 ? 'New' : 'Processing';
        // });
        $grid->column('status', __('Status'))->using(config('constants.order.status_admin'))->dot([
            1 => 'danger',
            2 => 'info',
            3 => 'primary',
            4 => 'success',
        ], 'warning');
        $grid->column('notes', __('Notes'))->limit(150);
        $grid->column('address', __('Address'))->link(function () {
            if($this->shops){

                return url(env('CLIENT_SITE')."shop/{$this->shops->slug}");
            }
            return '';
        });;
        // $grid->provinces()->display(function ($province) {return $province['name'] ?? "";});
        // $grid->districts()->display(function ($province) {return $province['name'] ?? "";});
        $grid->wards()->display(function ($province) {return $province['name'] ?? "";})->hide();
        // $grid->column('district_id', __('District id'));
        // $grid->column('ward_id', __('Ward id'));
        // $grid->column('customer_id', __('User'))->link(function () {
        //     return "users/".$this->customer_id;
        // });
        $grid->column('shops', __('Shop'))->display(function ($shops) {if($this->shops) return "<a target='_blank' href='shops/".$this->shop_id."'>".(Shop::find($this->shop_id)->name) ."<a/>"; else return '';});
        // $grid->column('customer', __('User'))->display(function ($customer) {return $customer['name'] ?? "";});
        $grid->column('customer_name', __('Customer name'))->display(function ($customer) {if($this->customer_id) return "<a target='_blank' href='users/".$this->customer_id."'>".((User::find($this->customer_id)) ?  (User::find($this->customer_id)->name) : '') ."<a/>"; else return $this->customer_name;});
        $grid->column('customer_phone', __('Customer phone'));
        $grid->column('total_amount', __('Tổng giá trị'))->sortable()->display(function($price) {
            return number_format($price, 2).'đ';
        })->color('#0061a7')->limit(30);
        $grid->column('discount_amount', __('Tổng được giảm'))->sortable()->display(function($price) {
            return number_format($price, 2).'đ';
        })->color('#0061a7')->limit(30);
        $grid->column('grand_total', __('Tổng thanh toán'))->sortable()->display(function($price) {
            return number_format($price, 2).'đ';
        })->color('#f05976')->limit(30);

        $grid->column('delivery_type', __('admin.order.delivery_type'))->display(function ($delivery_type) {
            if($delivery_type) {
                return '<span class="label-danger" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.__('admin.delivery.pickup');
            }
            {
                return '<span class="label-success" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.__('admin.delivery.delivery_to_me');
            }
        });
        $grid->column('delivery_price', __('admin.order.delivery_price'))->display(function($delivery_price) {
            return number_format($delivery_price, 2).'đ';
        })->color('#0061a7')->limit(30);

        $grid->column('payment_method', __('admin.order.payment_method'))->display(function ($delivery_type) {
            switch ($delivery_type) {
                case 'cod':
                    return '<span class="label-success" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.__('admin.payment_method.cod');
                    break;
                case 'bank_transfer':
                    return '<span class="label-warning" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.__('admin.payment_method.bank_transfer');
                    break;
                case 'credit_card':
                    return '<span class="label-info" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.__('admin.payment_method.credit_card');
                    break;

                default:
                return '<span class="label-danger" style="width: 8px;height: 8px;padding: 0;border-radius: 50%;display: inline-block;"></span>  '.__('admin.payment_method.cod');
                    break;
            }

        });

        if (!request()->has('_sort')) {
            // Apply default sorting by created_at column in descending order
            $grid->model()->orderBy('created_at', 'desc');
        }
        $grid->column('created_at', __('Created at'))->display(function () {
            return "<span class='label' style='color:blue'>$this->created_at</span>";
        })->sortable();









        $grid->filter(function($filter){
            $filter->scope('new', 'Recently modified')
                ->whereDate('created_at', date('Y-m-d'))
                ->orWhere('updated_at', date('Y-m-d'));

            $filter->column(1/2, function ($filter) {
                // Remove the default id filter
                // $filter->disableIdFilter();
                // $filter->equal('status')->select(config('constants.order.status_admin'));
                // Add a column filter
                $filter->where( function ($query) {
                    $query->where('customer_name', 'ilike', "%{$this->input}%");

                }, __('customer_name'));

                $filter->where( function ($query) {
                    $query->where('address', 'ilike', "%{$this->input}%");

                }, __('address'));
                $filter->where( function ($query) {
                    $query->where('short_code', 'ilike', "%{$this->input}%");

                }, __('Short code'));

                $filter->group('price', function ($group) {
                    $group->gt('>');
                    $group->lt('<');
                    $group->nlt('>=');
                    $group->ngt('<=');
                    $group->equal('=');
                })->currency();
            });
            $filter->column(1/2, function ($filter) {

                $filter->in('shop_id', __('shop'))->multipleSelect(Shop::where('enable',true)->get()->pluck('name','id'));
            });

        });
        $grid->selector(function (Grid\Tools\Selector $selector) {
            $selector->select('price', 'price', ['0-99,000đ', '99,000đ-500,000đ', '500,000đ-900,000,000đ'], function ($query, $value) {
                $between = [
                    [0, 99000],
                    [99000, 500000],
                    [500000, 900000000],
                ];
                foreach ($value as $key_ => $value_) {
                    $query->orWhereBetween('price', $between[$value[$key_]]);
                }
            });
            $selector->select('status', 'Status', config('constants.order.status_admin'));
        });
        $grid->actions(function ($actions) {
            $actions->add(new Replicate);
            $actions->add(new OrderCancel);
            $actions->add(new OrderAccept);
            $actions->add(new UpdateStatusOrder);
        });
        // Add batch delete action
        $grid->batchActions(function ($batch) {
            $batch->add(new BatchDelete('Order')); // Ensure 'Shop' is the correct model type
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Order::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('short_code', __('admin.order.short_code'));
        $show->field('status', __('admin.order.status_title'))->using(config('constants.order.status_admin'))->label();
        $show->field('notes', __('admin.notes'));
        $show->field('address', __('admin.address.title'));
        $show->field('provinces', __('admin.address.province'))->as(function ($Province) {if($Province) return $Province->name;});
        $show->field('districts', __('admin.address.district'))->as(function ($districts) {
            if($districts) return $districts->name;
        });
        $show->field('wards', __('admin.address.ward'))->as(function ($wards) {
            if($wards) return $wards->name;
        });
        $show->field('customer', __('admin.customer.title'))->as(function ($customer ) {return isset($customer->name) ? $customer->name : 'Người dùng chưa đăng nhập';});
        $show->field('customer_name', __('admin.customer.name'));
        $show->field('customer_phone', __('admin.customer.phone'));
        $show->field('total_amount', __('admin.order.total_amount'))->as(function($price) {
            return number_format($price, 2).' đ';
        })->color('#0061a7')->limit(30);
        $show->field('discount_amount', __('admin.order.discount_amount'))->as(function($priceOff) {
            return number_format($priceOff, 2).' đ';
        })->color('#0061a7')->limit(30);
        $show->field('grand_total', __('admin.order.grand_total'))->as(function($priceOff) {
            return number_format($priceOff, 2).' đ';
        })->color('#0061a7')->limit(30);
        $show->field('delivery_type', __('admin.order.delivery_type'))->as(function ($delivery_type) {
            if($delivery_type) return __('admin.delivery.pickup');
            return __('admin.delivery.delivery_to_me');
        });
        $show->field('delivery_price', __('admin.order.delivery_price'));
        $show->field('payment_method', __('admin.order.payment_method'));
        $show->field('created_at', __('admin.order.created_at'));
        $show->field('updated_at', __('admin.order.updated_at'));


        $show->items('items',  function ($items) {
            $items->id();
            $items->name(__('admin.product.title'));
            $items->profile_picture(__('admin.product.image'))->image();
            // $items->pivot();

            $items->column('pivot', __('detail'))->sortable()->display(function($pivot) {
                return
                 "<p>".__('admin.product.price')  .":  " . number_format($pivot['price'], 2).'đ'."</p>"
                ."<p>".__('admin.product.price_off')  .":  ". number_format($pivot['price_off'], 2).'đ' ."</p>"
                ."<p>".__('admin.product.quantity')  .":  ". number_format($pivot['quantity'], 2)."</p>"
                ."<p>".__('admin.product.price_total')  .":  ". number_format($pivot['price_total'], 2).'đ' ."</p>"
                ."<p>".__('admin.notes')  .": ".$pivot['notes']."</p>";
                // return number_format($pivot['price'], 2).'đ';
            });

            $items->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $actions->disableView();
            });
        });

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Order());
        $currentId = Route::current()->parameter('order');
        global $currentOrder;   // Accessing global variable
        $currentOrder = Order::find($currentId);

        $form->column(1/2, function ($form) {
            global $currentOrder;
            // $form->text('status', __('Status'))->default('1');
            if($currentOrder){
                $form->text('id', __('admin.order.id'))->placeholder('mã dữ liệu')->disable();
                $form->text('short_code', __('admin.order.short_code'))->placeholder('mã ngắn đơn hàng')->disable();

            }
            $form->select('status', __('admin.status'))->options(config('constants.order.status_admin'))->rules('required', [
                'required' => __('Vui lòng chọn'),
            ])->default('1');
            $form->textarea('notes', __('admin.notes'))->rows(4)->rules('max:555')->placeholder('tối đa 555 ký tự');
            $form->text('address', __('admin.address.title'));
            $form->select('province_id', __('admin.address.province'))->options(Province::all()->pluck('name','id'))->load('district_id', '/admin_get/district');
            $form->select('district_id', __('admin.address.district'))->options(function ($id){
                global $currentOrder;
                // $district = District::find($id);if ($district) {return [$district->id => $district->name];}
                if($currentOrder){
                    return District::where("province_id", $currentOrder->province_id)->get()->pluck('name','id');
                }else{
                    return [];
                }
            })->load('ward_id', '/admin_get/ward');
            $form->select('ward_id', __('admin.address.ward'))->options(function ($id) {
                // $ward = Ward::find($id);if ($ward) {return [$ward->id => $ward->name];}
                global $currentOrder;
                if($currentOrder){
                    return Ward::where("district_id", $currentOrder->district_id)->get()->pluck('name','id');
                }else{
                    return [];
                }
            });
            $form->select('agent_id', __('admin.agent.title'))->options(User::where('role_id', 4)->pluck('name','id'));
            if($currentOrder){
                $form->datetime('created_at', __('admin.order.created_at'))->disable();
                $form->datetime('updated_at', __('admin.order.updated_at'))->disable();
            }
        });
        $form->column(1/2, function ($form) {
            $form->select('shop_id', __('admin.shop.title'))->options(Shop::where('enable',true)->get()->pluck('name','id'));
            $form->select('customer_id', __('admin.customer.title'))->options(User::all()->pluck('name','id'));
            // $form->text('customer_id', __('Customer id'));
            $form->text('customer_name', __('admin.customer.name'));
            $form->text('customer_phone', __('admin.customer.phone'));
            $form->decimal('total_amount', __('admin.order.total_amount'));
            $form->decimal('discount_amount', __('admin.order.discount_amount'));
            $form->decimal('grand_total', __('admin.order.grand_total'));
            $form->select('delivery_type', __('admin.order.delivery_type'))->options(['0' => __('admin.delivery.delivery_to_me'), '1'=> __('admin.delivery.pickup')])->default('1');
            $form->decimal('delivery_price', __('admin.order.delivery_price'));
            $form->text('payment_method', __('admin.order.payment_method'))->default('1');

        });

        $form->column(12, function ($form) {

            $form->belongsToMany('items', ProductsSelectable::class, __('admin.order.item'));
            // $form->hasMany('items', 'Order Items', function (Form\NestedForm $form) {
            //     $form->select('product_id', 'Product')
            //          ->options(Product::all()->pluck('name', 'id'))
            //          ->rules('required');

            //     $form->currency('price', 'Price')->symbol('$')->rules('required|numeric|min:0');
            //     $form->currency('price_off', 'Discount')->symbol('$')->rules('nullable|numeric|min:0');
            //     $form->currency('price_total', 'Total Price')->symbol('$')->rules('required|numeric|min:0');
            //     $form->number('quantity', 'Quantity')->rules('required|integer|min:1');
            //     $form->textarea('notes', 'Notes')->rules('nullable|string');
            // });
        });



        $form->submitted(function (Form $form) {

            // Log::info("Form submitted");
            // Log::info(json_encode($form->model()));

        });
        $form->saved(function (Form $form) {

            // Log::info($form->model()->id);
            // Log::info($form->model()->shop_id);

        });
        return $form;
    }
}
