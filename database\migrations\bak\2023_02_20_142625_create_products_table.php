<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // bảng Sản phẩm
        Schema::create('products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->comment("Tên sản phẩm");
            $table->boolean('is_main')->default(1)->comment("True: <PERSON>à sản phẩm chính, False: <PERSON><PERSON> sản phẩm đi kèm");
            $table->uuid('branch_id')->comment("Thương hiệu")->nullable();
            $table->string('profile_picture')->comment("Ảnh đại diện")->nullable();
            $table->float('latitude');
            $table->float('longitude');
            $table->text('notes')->nullable();
            $table->uuid('shop_id')->comment("Cửa hàng bán");
            $table->decimal('price', 10, 2)->comment("Giá bán");
            $table->decimal('price_off', 10, 2)->comment("Giảm giá")->nullable();
            $table->boolean('enable')->default(1);
            $table->uuid('created_by')->nullable();
            $table->string('slug');
            $table->smallint('type')->default(1)->comment("1;2;3 = base system ; system ; shop product");
            $table->boolean('is_suggest')->default(false);

            $table->index('name');
            $table->index('branch_id');
            $table->index('latitude');
            $table->index('longitude');
            $table->index('shop_id');
            $table->index('price');
            $table->index('price_off');
            $table->index('created_by');
            $table->index('type');
            $table->index('slug');
            $table->index('is_suggest');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
}
