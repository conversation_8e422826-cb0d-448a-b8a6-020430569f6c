<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use App\Setting;
use App\Services\HistoryMail\HistoryMailService;
use Illuminate\Support\Facades\Log;

use App\User;
use App\Services\Mqtt\MqttChatService;

class NotificationUser extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    private $_userId;
    private $_message;
    public function __construct($userId, $message)
    {
        $this->_userId = $userId;
        $this->_message = $message;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];

    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $mqtt = new MqttChatService();

        $data = $this->_message;
        $dataMqtt = $this->_message;
        $dataMqtt['type'] = 'new_notification';


        $mqtt->publish(['topic' => $this->_userId, 'message' => $dataMqtt]);

        return $data;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    // public function toMail($notifiable)
    // {
    //     $pattern = Setting::where('key', 'pattern_notify_user')->first();

    //     $organize = isset($this->_user->organize->title) ? $this->_user->organize->title : "Bạn chưa được cấp tổ chức";
    //     $historyMail = null;
    //     try {
    //         $historyMail = HistoryMailService::insert([
    //             'title' => 'Thông Báo NOMNOMLAND User',
    //             'content' => json_encode(['role' => $this->_user->roles->description, 'organize' => $organize, 'pattern' => $pattern['value']]),
    //             'status' => config('constants.status_history_mail.success')
    //         ]);

    //         return (new MailMessage)->subject('Thông Báo NOMNOMLAND')->view('emails.update_user_mail', ['role' => $this->_user->roles->description, 'organize' => $organize, 'pattern' => $pattern['value']]);
    //     } catch (\Exception $e) {
    //         Log::info("Mail Error: " . $e);

    //         $historyMail->update([
    //             'status' => config('constants.status_history_mail.fail'),
    //             'description' => $e
    //         ]);
    //     }


    // }


}
