<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Category\CategoryRequest;
use App\Http\Requests\Category\CategoryUpdateRequest;
use App\Http\Requests\Category\CategoryCheckIdRequest;
use App\Services\Category\CategoryService;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Helpers\Helper;
use Illuminate\Support\Facades\Redis;

class CategoryController extends Controller
{
    private $_userId;
    // -------create category---------------
    public function create(Request $request)
    {
        $data = $request->only([
            'name',
            'parent_id',
            'shop_id',
            'translation'
        ]);

        $service = new CategoryService(Auth::user()->id);
        $result = $service->create($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list category-----------------
    public function list($offset=0, $limit=20, $all = false, CategoryService $service)
    {
        $cacheKey = env('APP_ENV').":"."category_list_system"; // Define a cache key
        $flushCache = request()->get('flush_cache') ?? 'false'; // Check for flush_cache parameter

        if($flushCache == 'true'){
            Helper::flushCacheByTag($cacheKey); // Flush the cache if requested
        }

        if(!Redis::exists($cacheKey)){
            $result = $service->list($offset, $limit, $all); // Fetch data if not cached
            Helper::storeCacheWithTags($cacheKey, $result, [env('APP_ENV').":".'category_list_system',env('APP_ENV').":".'api_responses',], 86400); // Store in cache
        } else {
            $result = json_decode(Redis::get($cacheKey)); // Retrieve from cache
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list by level category-----------------
    public function listLevel($offset=0, $limit=20, $all = false, CategoryService $service)
    {
        $result = $service->listLevel($offset, $limit, $all);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list category-----------------
    public function listByShopId($shopId = null, CategoryService $service)
    {
        $result = $service->listByShopId($shopId);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------detail category-------------
    public function detail($id = null, CategoryService $service)
    {
        $result = $service->detail($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update category-----------
    public function update(Request $request, CategoryService $service)
    {
        $data = $request->only([
            'id',
            'name',
            'parent_id',
            'translation',
        ]);

        $result = $service->update($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------remove category----------------
    public function remove(Request $request, CategoryService $service)
    {
        $data = $request->only(['id']);

        $result = $service->remove($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------delete category--------------
    public function delete(CategoryCheckIdRequest $request, CategoryService $service)
    {
       $data = $request->only(['id']);

       $result = $service->delete($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update category's products--------------
    public function updateListProduct(Request $request, CategoryService $service)
    {
       $data = $request->only(['category_id', 'product_ids']);

       $result = $service->updateListProduct($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }
       

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }
    //-------update categories index--------------
    public function updateIndex(Request $request, CategoryService $service)
    {
        $data = $request->all();
        $shopId = null;
        $result = false;
        $this->_userId = Auth::check() ? Auth::user()->id : null;
        $user = User::where('id',$this->_userId)->with('shop')->first();
        if($user){
            if(isset($user->shop[0])){
                $shopId = $user->shop[0]->id;
            };
        };
        if($shopId){

            $result = $service->updateIndex($data, $shopId);
            Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $shopId);
        }else{
            $result = false; 
        }

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => [],
               'mesage' => 'failed! Make sure all category id are yours.'
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }
}
