<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Services\HistoryMail\HistoryMailService;

class SendMailUpdateMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $key;
    public function __construct($key)
    {
        //
        $this->key = $key;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $historyMail = null;
        try {
            $historyMail = HistoryMailService::insert([
                'title' => 'Thông báo xác nhận cập nhật email',
                'content' => json_encode(['key'=> $this->key]),
                'status' => config('constants.status_history_mail.success')
            ]);
            return $this->subject('Thông báo xác nhận cập nhật email')
            ->view('emails.update_email_mail')
            ->with(['key'=> $this->key]);


        } catch (\Exception $e) {
            Log::info("Mail Error: ".$e);

            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

    }

}
