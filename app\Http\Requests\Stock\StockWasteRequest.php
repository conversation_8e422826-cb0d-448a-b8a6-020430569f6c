<?php

namespace App\Http\Requests\Stock;

use App\Http\Requests\BaseRequest;

class StockWasteRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'product_id' => 'required|uuid|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'unit' => 'nullable|string|max:50',
            'reason' => 'required|string|max:500'
        ];
    }
}
