<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Requests\Token\RemoveTokenRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\Token\TokenService;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Auth;

class TokenController extends Controller
{
    public function insertOrUpdate(Request $request, TokenService $service)
    {
        $data = $request->only([
            'token',
            'token_type',
            'user_id',
        ]);
        $device = request()->header('User-Agent');
        $data["device"] = $device;
        $general = new GeneralService($data['user_id']);
        $data["address_ip"] = $general->getUserIpAddr();
        $result = $service->insertOrUpdate($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function listByUser(Request $request, TokenService $service)
    {
        $data = $request->only([
            'user_id',
            'token_type',
        ]);
        $result = $service->listByUser($data['user_id'], $data['token_type']);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function list(TokenService $service)
    {
        $result = $service->list();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function delete(Request $request, TokenService $service)
    {
        $data = $request->only([
            'id'
        ]);

        $result = $service->deleteList($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' =>
                []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' =>
            []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function deleteToken(Request $request, TokenService $service)
    {
        $data = $request->only([
            'token'
        ]);

        $result = $service->deleteToken($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' =>
                []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' =>
            []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    function deleteTokenByUser(RemoveTokenRequest $request, TokenService $service)
    {
        $userId = Auth::user()->id;
        $token = $request->token_excluded ?? null;
        switch ($request->selection){
            case 'many':{
                $result = $service::deleteManyTokens($request->token_ids, $userId, $request->token_type);
                break;
            }
            case 'all': {;
                $result = $service::deleteAllTokens($userId, $request->token_type, $token);
                break;
            }
        }

        if (!$result) {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => 'Bad request'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_NO_CONTENT
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
