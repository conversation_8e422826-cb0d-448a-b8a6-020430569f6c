<?php

namespace App\Http\Controllers\Api\v1\Agent;

use App\Http\Requests\Agent\ShopUpdateRequest;
use App\Http\Requests\Shop\ShopCheckIdRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\Agent\AgentService;
use App\Http\Requests\Agent\ShopRequest;
use App\Shop;
use App\Services\GeneralService;
use App\Services\Order\OrderService;
use Illuminate\Support\Facades\Config;


class IndexController extends Controller
{
    private $_user;
    public function __construct() {
        $this->middleware('jwt.verify');
        $this->middleware(function ($request, $next) {
            $this->_user = Auth::user();
            if($this->_user->role_id > 5){
                {
                    $response = [
                        'status' => JsonResponse::HTTP_BAD_REQUEST,
                        'body' => ["User role permission denied. "
                        ]
                    ];
        
                    return response()->json($response, JsonResponse::HTTP_OK);
                }
            }

            return $next($request);
        });
    }
    // -------create shop---------------
    public function create_shop(ShopRequest $request)
    {
        $data = $request->only([
            'name',
            'address',
            // 'user_id',
            // 'agent_id',
            // 'slug',
            'business_type_id',
            'latitude',
            'longitude',
            'province_id',
            'district_id',
            'ward_id',
            'currency',
            'language',
            'description',
            'phone',
            'open_hours',
        ]);
        $result = null;
        if(Auth::user()->role_id <= 5){
            $service = new AgentService(Auth::user()->id);
            $result = $service->create_shop($data);
        }else{
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => "Only agent can create shop"
                    ]
                ];
        }
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];
        // log request
        $general = new GeneralService($this->_user->id);
        $general->addLog(Config::get('constants.log_action.create', 1), $result->id, Config::get('constants.object_type.shop', 1), json_encode($result), json_encode($data));
        //
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list shop agent manage-----------------
    public function list_shop(Request $request, AgentService $service)
    {
        $data = $request->only(['limit','offset','search']);
        $data['limit']  = $data['limit'] ?? 100;
        $data['offset'] = $data['offset'] ?? 0;
        $data['search'] = $data['search'] ?? '';
        $result = $service->list_shop(Auth::user()->id, $data['offset'], $data['limit'], $data['search']);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'count' => 0,
                    'data' => []
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'count' => $result['count'],
                'data' => $result['result']
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------detail shop-------------
    public function shop_detail($id, AgentService $service)
    {
        if(!GeneralService::checkAgentShopRelate($this->_user->id, $id))
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'this Shop is not exists or not manage by this agent.'
                    ]
                ];
                
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $result = $service->shop_detail($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //-------shop_orders-------------
    public function shop_orders($id = null, AgentService $service)
    {
        $result = $service->shop_orders(Auth::user()->id, $id == null ? 'all' : $id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update shop-----------
    public function update_shop(ShopUpdateRequest $request, AgentService $service)
    {
        $data = $request->only([
            'id',
            'name',
            'address',
            'user_id',
            'slug',
            'business_type_id',
            'latitude',
            'longitude',
            'province_id',
            'district_id',
            'ward_id',
            'currency',
            'language',
            'description',
            'phone',
            'open_hours',
        ]);
        if(!GeneralService::checkAgentShopRelate($this->_user->id, $data['id']))
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'this Shop is not exists or not manage by this agent.'
                    ]
                ];
                
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $result = $service->update_shop($data);
        // log request
        $general = new GeneralService($this->_user->id);
        $general->addLog(Config::get('constants.log_action.update', 2),$data['id'], Config::get('constants.object_type.shop', 1), json_encode($result), json_encode($data));
        //
        

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //-------copy shop-----------
    public function copy_shop(Request $request, AgentService $service)
    {
        $data = $request->only([
            'shop_id'
        ]);


        if(!GeneralService::checkAgentShopRelate($this->_user->id, $data['shop_id']))
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'this Shop is not exists or not manage by this agent.'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }


        $result = $service->copy_shop($data['shop_id']);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------remove shop----------------
    public function disable_shop(ShopCheckIdRequest $request, AgentService $service)
    {
        $data = $request->only(['id']);
        $shop = Shop::where('id',$data['id'])->with('agent')->first();
        if($shop->agent->id != Auth::user()->id){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => "current shop not belong to this agent."
                    ]
                ];
        }
        $result = $service->disableShop($data);
        // log request
        $general = new GeneralService($this->_user->id);
        $general->addLog(Config::get('constants.log_action.delete', 1), $data['id'], Config::get('constants.object_type.shop', 1), json_encode($result), json_encode($data));
        //
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------delete shop--------------
    public function delete_shop(ShopCheckIdRequest $request, AgentService $service)
    {
       $data = $request->only(['id']);

       if(!GeneralService::checkAgentShopRelate($this->_user->id, $data['id']))
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'this Shop is not exists or not manage by this agent.'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
       $result = $service->delete_shop($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }
    
    //--------list order by agent-----------------
    public function list_order_by_agent(Request $request, AgentService $service)
    {
        $arrays = $request->only(['shop_id', 'offset', 'limit', 'status', 'search_text', 'start_date', 'end_date']);

        $result = $service->listOrderByAgent(
            Auth::user()->id, 
            $arrays["shop_id"],
            $arrays["offset"],
            $arrays["limit"],
            $arrays["status"],
            $arrays["search_text"],
            $arrays["start_date"],
            $arrays["end_date"]
        );

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------order detail-----------------
    public function order_detail($id = null)
    {
        if(Auth::user()->role_id <= 5){
            $service = new AgentService(Auth::user()->id);
            $result = $service->order_detail($id);
            $check_permission = $service->checkAgentShopRelate(Auth::user()->id, $result->shop_id);
            if($check_permission == false)
            {
                $response = [
                    'status' => JsonResponse::HTTP_BAD_REQUEST,
                    'body' => [
                        'message' => "this order is not exists or not belongs to your account"
                    ]
                ];

                return response()->json($response, JsonResponse::HTTP_OK);
            }

            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ]
            ];
        }else{
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => "this order is not exists or not belongs to your account"
                    ]
                ];
        }    

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------agent update order-----------
    public function order_update(Request $request, OrderService $service)
    {
        $data = $request->only([
            'id',
            'status',
            'short_code',
            'notes',
            'address',
            'province_id',
            'district_id',
            'ward_id',
            'customer_id',
            'customer_name',
            'customer_phone',
            'total_amount',
            'discount_amount',
            'grand_total',
            'delivery_type',
            'delivery_price',
            'payment_method',
            'items',
            'shop_id',
            'images',
            'image_delete'
        ]);
        if(!GeneralService::checkAgentShopRelate($this->_user->id, $data['shop_id']))
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'this Shop is not exists or not manage by this agent.'
                    ]
                ];
                
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        if(Auth::user()->role_id <= 5){
            $result = $service->update($data);
        }else{
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => "this order is not exists or not belongs to your account"
                    ]
                ];
        }    


        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------create private product---------------
    public function create_private_product(Request $request)
    {        $data = $request->only([
            'name',
            'is_main',
            'brand_id',
            'profile_picture',
            'latitude',
            'longitude',
            'notes',
            'shop_id',
            'price',
            'price_off',
            'category_ids',
            'is_feature',
            'translation',
            'commission_percent',
        ]);

        $service = new AgentService(Auth::user()->id);
        $result = $service->createPrivateProduct($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //---------add product from system to shop---------------
    public function add_product_from_system(Request $request)
    {   
        $data = $request->only(
            'list_product',
            'shop_id'
        );

        $service = new AgentService(Auth::user()->id);
        $result = $service->addProductFromSystem($data);

        if($result === false){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => ['This shop is not belongs to your account']
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update product in a shop-----------
    public function update_product(Request $request, AgentService $service)
    {        $data = $request->only([
            'id',
            'name',
            'is_main',
            'brand_id',
            'profile_picture',
            'latitude',
            'longitude',
            'notes',
            'shop_id',
            'price',
            'price_off',
            'category_ids',
            'is_feature',
            'enable',
            'translation',
            'stock',
            'commission_percent',
        ]);

        $result = $service->updateProduct($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    
    //-------update category's products--------------
    public function category_update_list_product(Request $request, AgentService $service)
    {
       $data = $request->only(['category_id', 'product_ids']);

       $result = $service->categoryUpdateListProduct($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }
    //-------update categories index--------------
    public function category_update_index(Request $request, AgentService $service)
    {
        $data = $request->all();
        $shopId = null;
        $result = false;
        $this->_userId = Auth::check() ? Auth::user()->id : null;
        $user = User::where('id',$this->_userId)->with('shop')->first();
        if($user){
            if(isset($user->shop[0])){
                $shopId = $user->shop[0]->id;
            };
        };
        if($shopId){

            $result = $service->categoryUpdateIndex($data, $shopId);
        }else{
            $result = false; 
        }

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => [],
               'mesage' => 'failed! Make sure all category id are yours.'
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }
}