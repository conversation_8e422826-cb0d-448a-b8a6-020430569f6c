<?php

namespace App\Http\Requests\Delivery;

use App\Http\Requests\BaseRequest;

class CreateDeliveryRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'order_id' => 'required|exists:orders,id',
            'driver_id' => 'nullable|exists:users,id',
            'status' => 'required|integer|min:1|max:8',
            'pickup_time' => 'nullable|date_format:Y-m-d H:i',
            'duration' => 'required|integer|min:15',
            'notes' => 'nullable|string|max:255',
            'latitude_from' => 'required|numeric|between:-90,90',
            'longitude_from' => 'required|numeric|between:-180,180',
            'latitude_to' => 'required|numeric|between:-90,90',
            'longitude_to' => 'required|numeric|between:-180,180',
            'distance' => 'numeric|min:0',
            'name_from' => 'required|string|max:255',
            'address_from' => 'required|string|max:255',
            'phone_from' => 'required|string|max:20',
            'name_to' => 'required|string|max:255',
            'address_to' => 'required|string|max:255',
            'phone_to' => 'required|string|max:20',
            'package_info' => 'nullable|string|max:255',
            'special_require' => 'nullable|string|max:255',
            'total_amount' => 'required|numeric|min:0',
            'discount_id' => 'nullable|exists:discounts,id',
            'discount_amount' => 'nullable|numeric|min:0',
        ];
    }
}


