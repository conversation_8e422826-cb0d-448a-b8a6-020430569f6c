<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class LogoutDeviceRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:tokens,id'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required' => 'User_001_E_001',
    //         'id.uuid' => 'User_002_E_002',
    //         'id.exists' => 'User_003_E_003',
    //     ];
    // }
}
