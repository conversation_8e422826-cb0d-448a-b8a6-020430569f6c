<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UserUpdatePasswordClientRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'password' => 'bail|required|string|min:6|max:20|regex:/^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};:\\|,.<>\/?])/',
        ];
    }


    // public function messages()
    // {

    //     return [

    //         'current_password.required' => 'User_001_E_033',
    //         'current_password.current_password' => 'User_002_E_034',

    //         'password.required' => 'User_001_E_006',
    //         'password.confirmed' => 'User_010_E_007',
    //         'password.min' => 'User_004_E_008',
    //         'password.max'      => 'User_004_E_065',
    //         'password.regex'      => 'User_002_E_066'

    //     ];
    // }
}
