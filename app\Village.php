<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Village extends Model
{
    protected $fillable = [
        'id',
        'name',
        'postcode',
        'ward_id',
        'longitude',
        'latitude',
        'slug'
    ];

    protected $primaryKey = 'id';

    protected $table = 'villages';
    protected $hidden = ['pivot'];

    public function wards()
    {
        return $this->belongsTo(Ward::Class, 'ward_id');
    }

}
