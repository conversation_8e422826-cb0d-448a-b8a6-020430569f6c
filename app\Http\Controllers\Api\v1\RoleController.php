<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\Role\RoleService;
use App\Http\Requests\Role\RoleRequest;
use App\Http\Requests\Role\RoleUpdateRequest;
use App\Http\Requests\Role\RoleCheckIdRequest;
use App\Role as R;

class RoleController extends Controller
{
    //--------list Role-----------
    /**
     * @OA\Get(
     *      path="/api/v1/role/list",
     *      operationId="Role List",
     *      tags={"Role"},
     *      summary="Get list of role",
     *      security={
     *       {"api_key": {}},
     *     },
     *      @OA\Response(
     *          response=200,
     *          description="Successful"
     *       ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad request"
     *      )
     *     )
     */
    public function list(RoleService $service)
    {
        // $this->authorize('show', R::class);
        $result = $service->list();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //----------insert role--------------------
    public function insert(RoleRequest $request, RoleService $service)
    {
        $data = (array)$request->only([
            'name',
            'description'
        ]);

        $result = $service->insert($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response,JsonResponse::HTTP_OK);
    }

    //-------------update role------------------
    public function update(RoleUpdateRequest $request, RoleService $service)
    {
        $data = (array)$request->only([
            'id',
            'name',
            'description'
        ]);

        $result = $service->update($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Role_003_E_005'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //------------detail role----------------
    public function detail($id = null, RoleService $service)
    {
        $result = $service->detail($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Role_003_E_005'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------delete role---------------

    public function delete(RoleCheckIdRequest $request, RoleService $service)
    {
        $data = (array)$request->only([
            'id'
        ]);

        $result = $service->delete($data);
        
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Role_003_E_005'
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
               
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
