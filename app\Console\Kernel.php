<?php

namespace App\Console;

use App\Console\Commands\UpdateProductTotalFollowAndLike;
use App\Console\Commands\UpdateShopTotalFollowAndLike;
use App\Console\Commands\CheckOrderStatusCommand;
use App\Console\Commands\DailyStockReportCommand;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        UpdateShopTotalFollowAndLike::class,
        UpdateProductTotalFollowAndLike::class,
        Commands\UpdateInactiveDriverStatus::class,
        DailyStockReportCommand::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command("command:checkRequestDaily")->dailyAt('00:00')->timezone('Asia/Ho_Chi_Minh');//delete request from user
        $schedule->command("command:shop-settings-checkup")->dailyAt('00:00')->timezone('Asia/Ho_Chi_Minh');//delete request from user
        $schedule->command("command:checkStatusDelivery")->cron('*/3 * * * *')->timezone('Asia/Ho_Chi_Minh');
        $schedule->command('command:checkorderstatus')->everyMinute();
        $schedule->command('stock:daily-report')->dailyAt('08:00')->timezone('Asia/Ho_Chi_Minh'); // Send daily stock reports at 8 AM
        
        // Run the command daily to update inactive driver status
        $schedule->command('drivers:update-inactive-status')
                 ->daily()->timezone('Asia/Ho_Chi_Minh')
                 ->appendOutputTo(storage_path('logs/inactive-drivers.log'));
    }
    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
