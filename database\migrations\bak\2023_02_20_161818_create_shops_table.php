<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateShopsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // bảng Cửa hàng
        Schema::create('shops', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->comment("Tên sản phẩm");
            $table->integer('province_id')->comment("Tỉnh")->nullable();
            $table->integer('district_id')->comment("Huyện")->nullable();
            $table->integer('ward_id')->comment("Xã")->nullable();
            $table->string('address')->comment("Địa chỉ")->nullable();
            $table->text('description')->comment("Mô tả")->nullable();
            $table->float('latitude');
            $table->float('longitude');
            $table->uuid('user_id')->comment("Chủ sở hữu");
            $table->string('slug')->nullable();
            $table->string('banner')->nullable()->commnent("ảnh banner");
            $table->uuid('business_type_id')->nullable()->commnent("loại hình kinh doanh");
            $table->boolean('enable')->default(1);
            $table->uuid('created_by')->nullable();

            $table->index('name');
            $table->index('province_id');
            $table->index('district_id');
            $table->index('ward_id');
            $table->index('latitude');
            $table->index('longitude');
            $table->index('business_type_id');
            $table->index('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shops');
    }
}
