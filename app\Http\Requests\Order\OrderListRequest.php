<?php

namespace App\Http\Requests\Order;

use App\Http\Requests\BaseRequest;

class OrderListRequest extends BaseRequest
{
    public function rules(){
        return [
            'customer_id' => ['required_if:mode,customer,both','uuid',
                function ($attribute, $value, $fail) {
                    if (request()->has('shop_id') && request()->input('mode') == 'customer') {
                        $fail('Cannot set shop_id if mode is customer.');
                    }
                }],
            'shop_id'     => ['required_if:mode,shop,both','uuid','exists:shops,id',
                function ($attribute, $value, $fail) {
                    if (request()->has('customer_id') && request()->input('mode') == 'shop') {
                        $fail('Cannot set customer_id if mode is shop.');
                    }
                }
            ],
            'offset'      => 'nullable',
            'limit'       => 'nullable',
            'status'      => 'nullable',
            'search_text' => 'nullable',
            'start_date'  => 'nullable',
            'end_date'    => 'nullable',
            'mode'        => 'required|in:shop,customer,both'
        ];
    }
}
