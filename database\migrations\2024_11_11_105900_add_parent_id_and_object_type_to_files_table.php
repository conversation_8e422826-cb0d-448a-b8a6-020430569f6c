<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddParentIdAndObjectTypeToFilesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('files', function (Blueprint $table) {
            $table->uuid('parent_id')->nullable();
            $table->smallInteger('object_type')
                ->nullable()
                ->comment("'user'=> 1,'product'=> 2,'shop'=> 3,'category'=> 4,'image' => 5,'brand' => 6,'option'=> 7,'role'=>8,'order' => 9,'rating'=> 10");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('files', function (Blueprint $table) {
            $table->dropColumn('parent_id');
            $table->dropColumn('object_type');
        });
    }
}
