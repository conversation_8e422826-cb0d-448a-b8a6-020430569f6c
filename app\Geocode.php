<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Geocode extends Model
{
    //
     //set time to false

    protected $fillable = [
    	'address',
        'latitude',
        'longitude',
        'province_id',
        'district_id',
        'ward_id',
        'unaccent_address',
        'lowercase_address',
    ];

    protected $primaryKey = 'id';

    protected $table = 'geocodes';
    // protected $table = 'districts';
    protected $hidden = ['created_at', 'updated_at'];

    public function provinces()
    {
        return $this->belongsTo(Province::class,'province_id');
    }


    public function districts()
    {
        return $this->belongsTo(District::class,'district_id');
    }

    public function wards()
    {
        return $this->belongsTo(Ward::class,'ward_id');
    }
    

}
