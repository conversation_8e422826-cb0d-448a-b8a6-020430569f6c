<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Category;
use App\OrderItem;
use App\Product;
use App\User;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Order extends Model
{
    protected $fillable = [
        'id',
        'status',
        'short_code',
        'notes',
        'address',
        'province_id',
        'district_id',
        'ward_id',
        'customer_id',
        'customer_name',
        'customer_phone',
        'total_amount',
        'discount_amount',
        'grand_total',
        'delivery_distance',
        'delivery_type',
        'delivery_price',
        'delivery_time',
        'payment_method',
        'shop_id',
        'agent_id',
        'customer_latitude',
        'customer_longitude',
        'extra_data',
        'pos_note',        'delivery_id',
        'delivery_partner_id',
        'delivery_discount',
        'delivery_price_estimate',
        'ref_id',
        'referral_code',
    ];
    protected $primaryKey = 'id';
    protected $table = 'orders';
    public $incrementing = false;
    // public $timestamps = false;
    // protected $keyType = 'string';
    protected $casts = [
        'total_amount' => 'float',
        'discount_amount' => 'float',
        'grand_total' => 'float',
        'delivery_price' => 'float',
        'delivery_distance' => 'float',
        'pos_note' => 'array',
        'delivery_discount' => 'float',
        'delivery_price_estimate' => 'float'
    ];
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = $model->id ?? Str::uuid();
            // $model->short_code = uniqid();
            do {
                $datePrefix = date('ymd'); // Add current year, month, and day as prefix
                $randomAlphabet = strtoupper(Str::random(2)); // Generate two random uppercase letters
                $uniqueIdentifier = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT); // Generates a unique 4-digit numeric identifier

                // Combine the date prefix, random uppercase alphabet characters, and the unique identifier
                $orderID = $datePrefix . '-' . $randomAlphabet . $uniqueIdentifier;

                $model->short_code = $orderID;
            } while (self::where('short_code', $model->short_code)->exists());
        });

        static::deleting(function ($model) {
            $model->items()->detach();
        });
    }


    // protected $hidden = ['updated_at', 'created_at'];
    public function provinces()
    {
        return $this->belongsTo(Province::class,'province_id')->select('id','name','address');
    }


    public function districts()
    {
        return $this->belongsTo(District::class,'district_id')->select('id','name','address','province_id');
    }

    public function wards()
    {
        return $this->belongsTo(Ward::class,'ward_id')->select('id','name','address','district_id');
    }

    public function customer()
    {
        return $this->belongsTo(User::class,'customer_id');
    }

    public function items()
    {
        return $this->belongsToMany(Product::class, 'order_items', 'order_id', 'product_id')
        ->using(OrderItem::class)
        ->with('parent_product')
        ->withPivot('price','price_off','price_total', 'quantity', 'notes')->distinct();
    }

    public function shops()
    {
        return $this->belongsTo(Shop::class, 'shop_id');
    }

    public function agent()
    {
        return $this->belongsTo(User::class, 'agent_id');
    }

    public function delivery()
    {
        return $this->belongsTo(Delivery::class, 'delivery_id', 'short_code')->with('driver');
    }
    public function images()
    {
        return $this->hasMany(Image::class, 'parent_id')->orderBy('index','desc');
    }    
    public function deliveryPartner()
    {
        return $this->belongsTo(DeliveryPartner::class, 'delivery_partner_id')->select('id', 'name', 'notes', 'information' );
    }

    /**
     * Get the user who referred this order
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'ref_id');
    }
}
