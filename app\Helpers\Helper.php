<?php
namespace App\Helpers;
use Illuminate\Support\Str;
use phpDocumentor\Reflection\PseudoTypes\LowercaseString;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class Helper
{
    public static function distance($lat1, $lon1, $lat2, $lon2, $unit = 'm')
    {
        //Haversine formula
        $theta = $lon1 - $lon2;
        $distance = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +  cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
        $distance = max(-1, min(1, $distance));
        $distance = acos($distance);
        $distance = rad2deg($distance);
        $distance = $distance * 60 * 1.1515;


        if ($unit == 'km') {
            $distance = $distance * 1.609344;
        } else if ($unit == 'm') {
            $distance = $distance * 1609.344;
        }

        return round($distance,2);
    }
    static function makeSlug($text, $unique = true) {
        if($unique) return Str::slug(preg_replace('/[^a-zA-Z0-9áàảạãÁÀẢẠÃắằẳặẵẮẰẲẶẴấầẩậẫẤẦẨẬẪĐđéèẻẹẽÉÈẺẸẼếềểệễẾỀỂỆỄíìỉịĩÍÌỈỊĨóòỏọõÓÒỎỌÕốồổộỗỐỒỔỘỖớờởợỡỚỜỞỢỠúùủụũÚÙỦỤŨứừửựữỨỪỬỰỮăĂâÂêÊôÔơƠưƯýỳỷỵỹÝỲỶỴỸếẾÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạẩầảấấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửứữựỳỵỷỹ]/', ' ', $text))."_".uniqid();
        return Str::slug(preg_replace('/[^a-zA-Z0-9áàảạãÁÀẢẠÃắằẳặẵẮẰẲẶẴấầẩậẫẤẦẨẬẪĐđéèẻẹẽÉÈẺẸẼếềểệễẾỀỂỆỄíìỉịĩÍÌỈỊĨóòỏọõÓÒỎỌÕốồổộỗỐỒỔỘỖớờởợỡỚỜỞỢỠúùủụũÚÙỦỤŨứừửựữỨỪỬỰỮăĂâÂêÊôÔơƠưƯýỳỷỵỹÝỲỶỴỸếẾÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạẩầảấấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửứữựỳỵỷỹ]/', ' ', $text));
    }

    static function unaccentVietnamese($text, $lowercase = false) {
        $from = [];
        $to = [];
        return Str::slug($text,' ');
        /*
        // Accented characters
        $accents   = 'áàảãạâấầẩẫậăắằẳẵặđéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵÁÀẢÃẠÂẤẦẨẪẬĂẮẰẲẴẶĐÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴ';
        // Corresponding unaccented characters
        $unaccents = 'aaaaaaaaaaaaaaaaadeeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuuyyyyyAAAAAAAAAAAAAAAAADEEEEEEEEEEEIIIIIOOOOOOOOOOOOOOOOOUUUUUUUUUUUYYYYY';
        // Generate $from and $to arrays
        $len = mb_strlen($accents, 'UTF-8');
        for ($i = 0; $i < $len; $i++) {
            $from[] = mb_substr($accents, $i, 1, 'UTF-8');
            $to[] = mb_substr($unaccents, $i, 1, 'UTF-8');
        }
        */
        $from = [0 => 'á',1 => 'à',2 => 'ả',3 => 'ã',4 => 'ạ',5 => 'â',6 => 'ấ',7 => 'ầ',8 => 'ẩ',9 => 'ẫ',10 => 'ậ',11 => 'ă',12 => 'ắ',13 => 'ằ',14 => 'ẳ',15 => 'ẵ',16 => 'ặ',17 => 'đ',18 => 'é',19 => 'è',20 => 'ẻ',21 => 'ẽ',22 => 'ẹ',23 => 'ê',24 => 'ế',25 => 'ề',26 => 'ể',27 => 'ễ',28 => 'ệ',29 => 'í',30 => 'ì',31 => 'ỉ',32 => 'ĩ',33 => 'ị',34 => 'ó',35 => 'ò',36 => 'ỏ',37 => 'õ',38 => 'ọ',39 => 'ô',40 => 'ố',41 => 'ồ',42 => 'ổ',43 => 'ỗ',44 => 'ộ',45 => 'ơ',46 => 'ớ',47 => 'ờ',48 => 'ở',49 => 'ỡ',50 => 'ợ',51 => 'ú',52 => 'ù',53 => 'ủ',54 => 'ũ',55 => 'ụ',56 => 'ư',57 => 'ứ',58 => 'ừ',59 => 'ử',60 => 'ữ',61 => 'ự',62 => 'ý',63 => 'ỳ',64 => 'ỷ',65 => 'ỹ',66 => 'ỵ',67 => 'Á',68 => 'À',69 => 'Ả',70 => 'Ã',71 => 'Ạ',72 => 'Â',73 => 'Ấ',74 => 'Ầ',75 => 'Ẩ',76 => 'Ẫ',77 => 'Ậ',78 => 'Ă',79 => 'Ắ',80 => 'Ằ',81 => 'Ẳ',82 => 'Ẵ',83 => 'Ặ',84 => 'Đ',85 => 'É',86 => 'È',87 => 'Ẻ',88 => 'Ẽ',89 => 'Ẹ',90 => 'Ê',91 => 'Ế',92 => 'Ề',93 => 'Ể',94 => 'Ễ',95 => 'Ệ',96 => 'Í',97 => 'Ì',98 => 'Ỉ',99 => 'Ĩ',100 => 'Ị',101 => 'Ó',102 => 'Ò',103 => 'Ỏ',104 => 'Õ',105 => 'Ọ',106 => 'Ô',107 => 'Ố',108 => 'Ồ',109 => 'Ổ',110 => 'Ỗ',111 => 'Ộ',112 => 'Ơ',113 => 'Ớ',114 => 'Ờ',115 => 'Ở',116 => 'Ỡ',117 => 'Ợ',118 => 'Ú',119 => 'Ù',120 => 'Ủ',121 => 'Ũ',122 => 'Ụ',123 => 'Ư',124 => 'Ứ',125 => 'Ừ',126 => 'Ử',127 => 'Ữ',128 => 'Ự',129 => 'Ý',130 => 'Ỳ',131 => 'Ỷ',132 => 'Ỹ',133 => 'Ỵ'];
        $to   = [0 => 'a',1 => 'a',2 => 'a',3 => 'a',4 => 'a',5 => 'a',6 => 'a',7 => 'a',8 => 'a',9 => 'a',10 => 'a',11 => 'a',12 => 'a',13 => 'a',14 => 'a',15 => 'a',16 => 'a',17 => 'd',18 => 'e',19 => 'e',20 => 'e',21 => 'e',22 => 'e',23 => 'e',24 => 'e',25 => 'e',26 => 'e',27 => 'e',28 => 'e',29 => 'i',30 => 'i',31 => 'i',32 => 'i',33 => 'i',34 => 'o',35 => 'o',36 => 'o',37 => 'o',38 => 'o',39 => 'o',40 => 'o',41 => 'o',42 => 'o',43 => 'o',44 => 'o',45 => 'o',46 => 'o',47 => 'o',48 => 'o',49 => 'o',50 => 'o',51 => 'u',52 => 'u',53 => 'u',54 => 'u',55 => 'u',56 => 'u',57 => 'u',58 => 'u',59 => 'u',60 => 'u',61 => 'u',62 => 'y',63 => 'y',64 => 'y',65 => 'y',66 => 'y',67 => 'A',68 => 'A',69 => 'A',70 => 'A',71 => 'A',72 => 'A',73 => 'A',74 => 'A',75 => 'A',76 => 'A',77 => 'A',78 => 'A',79 => 'A',80 => 'A',81 => 'A',82 => 'A',83 => 'A',84 => 'D',85 => 'E',86 => 'E',87 => 'E',88 => 'E',89 => 'E',90 => 'E',91 => 'E',92 => 'E',93 => 'E',94 => 'E',95 => 'E',96 => 'I',97 => 'I',98 => 'I',99 => 'I',100 => 'I',101 => 'O',102 => 'O',103 => 'O',104 => 'O',105 => 'O',106 => 'O',107 => 'O',108 => 'O',109 => 'O',110 => 'O',111 => 'O',112 => 'O',113 => 'O',114 => 'O',115 => 'O',116 => 'O',117 => 'O',118 => 'U',119 => 'U',120 => 'U',121 => 'U',122 => 'U',123 => 'U',124 => 'U',125 => 'U',126 => 'U',127 => 'U',128 => 'U',129 => 'Y',130 => 'Y',131 => 'Y',132 => 'Y',133 => 'Y'];

        $result = str_replace($from, $to, $text);
        if($lowercase) {
            $result = strtolower($result);
        }
        return $result;
    }

    static function generateShortUnique($string){
        return Str::slug($string,"")."_".strtolower(Str::random(2)) . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    static function storeCacheWithTags($key, $data, $tags, $expiration = 86400)
    {
        // Store the actual data
        Redis::setex($key, $expiration, json_encode($data));

        // Store the keys in the corresponding tag sets
        foreach ($tags as $tag) {
            Redis::sadd('tag:' . $tag, $key);
        }
    }

    static function flushCacheByTag($tag)
    {
        $keys = Redis::smembers('tag:' . $tag);

        if (!empty($keys)) {
            Redis::del($keys);
        }

        // Delete the tag set itself
        Redis::del('tag:' . $tag);
    }

    static function getBoundByRadius($latitude, $longitude, $radius = 7){
        // 111.18957696 = 60 * 1.1515 * 1.609344: để quy đổi km ra độ lệch của tọa độ map
        $r = $radius / 111.18957696 ;
        $data = [];
        $data['latitude_s']  = $latitude - $r;
        $data['latitude_b']  = $latitude + $r;
        $data['longitude_s'] = $longitude - $r;
        $data['longitude_b'] = $longitude + $r;
        return $data;
    }

    static function publishMessage()
    {
        $data = [
            'event' => 'messageSent',
            'data' => [
                'message' => 'Hello from Laravel!'
            ]
        ];

        Redis::publish('driver-channel', json_encode($data));

        return true;
    }

    static function decrypt($data) {
        $salt = hex2bin('31323334363738394041724b7276736f');
        $iv   = hex2bin("654f31507345445a65703459614f334e");

        // Decrypt the data
        return trim(openssl_decrypt($data, 'AES-128-CBC', $salt, OPENSSL_ZERO_PADDING, $iv));
    }
    static function encrypt($data)
    {
        $salt = hex2bin('31323334363738394041724b7276736f');
        $iv   = hex2bin("654f31507345445a65703459614f334e");

        // Encrypt the data
        return openssl_encrypt($data, 'AES-128-CBC', $salt, OPENSSL_ZERO_PADDING, $iv);
    }

}
