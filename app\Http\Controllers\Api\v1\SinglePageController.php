<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Tymon\JWTAuth\Facades\JWTAuth;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\SinglePage\SinglePageRequest;
use App\Http\Requests\SinglePage\SinglePageUpdateRequest;
use App\Services\SinglePage\SinglePageService;

class SinglePageController extends Controller
{
    //---------------------insert Single Page----------------------------

    public function insert(SinglePageRequest $request)
    {
        $data = (array)$request->only([
            'title',
            'content',
            'slug',
            'keywords',
            'description'
        ]);

        $user = JWTAuth::getToken() && JWTAuth::check() ? JWTAuth::toUser(JWTAuth::getToken()) : null;
        $service = $user ? new SinglePageService($user->id) : new SinglePageService;
        $result = $service->add($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' =>[
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //---------------------update Single Page----------------------------
    public function update(SinglePageUpdateRequest $request)
    {
        $data = (array)$request->only([
            'id',
            'title',
            'content',
            'slug',
            'keywords',
            'description'
        ]);

        $user = JWTAuth::getToken() && JWTAuth::check() ? JWTAuth::toUser(JWTAuth::getToken()) : null;
        $service = $user ? new SinglePageService($user->id) : new SinglePageService;
        $result = $service->update($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' =>[
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //---------------------------delete direction---------------------

    public function delete(Request $request)
    {
        $user = JWTAuth::getToken() && JWTAuth::check() ? JWTAuth::toUser(JWTAuth::getToken()) : null;
        $service = $user ? new SinglePageService($user->id) : new SinglePageService;
        $data = (array)$request->only([
            'id'
        ]);

        $result = $service->delete($data);

        if($result == false)
        {
            $response =[
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'SinglePage_003_E_005',
            ];
            return response()->json($response,JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
            ],
        ];
        return response()->json($response,JsonResponse::HTTP_OK);

    }

    //-------------detail single page--------------

    public function detail($id = "aaa")
    {
        $user = JWTAuth::getToken() && JWTAuth::check() ? JWTAuth::toUser(JWTAuth::getToken()) : null;
        $service = $user ? new SinglePageService($user->id) : new SinglePageService;
        $result = $service->detail($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'EstimateTemplate_003_E_004'
            ];
            return response()->json($response,JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);

    }

    //-------------detail single page by slug --------------
    public function detailBySlug(Request $request)
    {
        $slug = $request->only([
            'slug'
        ]);
        $service = new SinglePageService;
        $result = $service->detailBySlug($slug);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'SinglePage_003_E_004'
            ];
            return response()->json($response,JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);

    }

    //------------filter estimate property template
    public function filter(Request $request, SinglePageService $service)
    {
        $data = (array)$request->only([
            'limit',
            'offset',
            'title',
            'keywords',
            'slug',
            'content'
        ]);

        $result = $service->filter($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'count' => $result['count'],
                'data' => $result['result']
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
