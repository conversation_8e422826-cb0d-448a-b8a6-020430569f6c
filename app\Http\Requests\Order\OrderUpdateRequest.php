<?php

namespace App\Http\Requests\Shop;

use App\Http\Requests\BaseRequest;

class ShopUpdateRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'name'              => 'required|max:255',
            'address'           => 'nullable|max:255',
            'province_id'       => 'nullable|exists:dvhc2021_tinh,id',
            'district_id'       => 'nullable|exists:dvhc2021_huyen,id',
            'ward_id'           => 'nullable|exists:dvhc2021_xa,id',
            'id'                => 'required|exists:shops,id'
        ];
    }

    // public function messages()
    // {
    //     return [
            // 'name.required'             => 'Shop_001_E_001',
            // 'name.max'                  => 'Shop_001_E_002',
            // 'address.max'               => 'Shop_002_E_003',
            // 'province_id.required'      => 'Shop_003_E_004',
            // 'province_id.exists'        => 'Shop_003_E_005',
            // 'district_id.required'      => 'Shop_004_E_006',
            // 'district_id.exists'        => 'Shop_004_E_007',
            // 'ward_id.required'          => 'Shop_005_E_008',
            // 'ward_id.exists'            => 'Shop_005_E_009',
            // 'latitude.required'         => 'Shop_006_E_010',
            // 'longitude.required'        => 'Shop_007_E_011',
            // 'user_id.required'          => 'Shop_008_E_012',
            // 'user_id.exists'            => 'Shop_008_E_013',
            // 'slug.max'                  => 'Shop_009_E_014',
            // 'business_type_id.required' => 'Shop_011_E_015',
            // 'business_type_id.exists'   => 'Shop_011_E_016',
            // 'id'                        => 'Shop_011_E_017'
    //     ];
    // }
}
