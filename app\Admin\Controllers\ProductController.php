<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\EnableDisable2;
use App\Admin\Actions\SuggestDisable;
use App\Category;
use App\Product;
use App\Shop;
use App\User;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Illuminate\Support\Str;
// Use Encore\Admin\Widgets\Table;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Log;
use phpDocumentor\Reflection\DocBlock\Tags\Var_;
use App\Admin\Actions\BatchDelete;

class ProductController extends AdminController
{


    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Product';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Product());

        $grid->column('id', __('Id'))->sortable()->limit(10);
        $grid->column('name', __('admin.product.name'))->sortable()->link(function () {
            return "products/".$this->id;
        })->limit(110);
        $grid->categories()->pluck('name')->label('primary');// Set the color, the default `success`, optional `danger`, `warning`, `info`, `primary`, `default`, `success`
        $grid->column('profile_picture', __('admin.product.profile_picture'))->image();
        $grid->column('notes', __('admin.product.notes'))->display(function () {
            $productName = "Mở link sản phẩm";
            $productLink = url(env('APP_URL')."product/{$this->id}"); // Assuming the ID is accessible via $product['id']
            return "<a target='_blank' href='{$productLink}'>{$productName}</a>";

            return '';
        });
        $grid->shop()
        ->display(function ($shop) {
            if($shop){

                $shopName = $shop['name'] ?? "";
                $shopLink = url("admin/shops/{$shop['id']}"); // Assuming the ID is accessible via $shop['id']
                return "<a target='_blank' href='{$shopLink}'>{$shopName}</a>";
            }
            return '';
        })
        ;
        $grid->column('price', __('admin.product.price'))->sortable()->display(function($price) {
            return number_format($price, 0).'đ';
        })->color('#0061a7')->limit(30);
        $grid->column('price_root', __('admin.product.price_root'))->sortable()->display(function($price_root) {
            return $price_root ? number_format($price_root, 0).'đ' : 'N/A';
        })->color('#28a745')->limit(30);
        $grid->column('price_off', __('admin.product.price_off'))->sortable()->display(function($price_off) {
            return number_format($price_off, 0).'đ';
        })->color('#f05976')->limit(30);
        $grid->column('is_main', __('admin.product.is_main'))->icon([
            0 => 'toggle-off',
            1 => 'toggle-on',
        ], $default = '')->hide();
        $grid->column('enable', __('admin.product.enable'))->action(new EnableDisable2);
        $grid->column('is_suggest', __('admin.product.is_suggest'))->action(new SuggestDisable);
        $grid->column('created_by', __('admin.product.created_by'))->hide();
        if (!request()->has('_sort')) {
            // Apply default sorting by created_at column in descending order
            $grid->model()->orderBy('created_at', 'desc');
        }
        $grid->column('created_at', __('admin.product.created_at'))->display(function () {
            return "<span class='label' style='color:blue'>$this->created_at</span>";
        })->sortable()->hide();;

        $grid->column('brand_id', __('admin.product.brand'))->hide();;
        $grid->column('latitude', __('admin.product.latitude'))->hide();;
        $grid->column('longitude', __('admin.product.longitude'))->hide();;

        // Operate on the `$grid` instance
        $grid->filter(function($filter){
            $filter->scope('new', 'Recently modified')
                ->whereDate('created_at', date('Y-m-d'))
                ->orWhere('updated_at', date('Y-m-d'));
            $filter->scope('type', 'Base System product')->where('type', 1);

            $filter->column(1/2, function ($filter) {
                // Remove the default id filter
                // $filter->disableIdFilter();

                // Add a column filter
                $filter->where( function ($query) {
                    $query->where('name', 'ilike', "%{$this->input}%");

                }, __('name'));
                $filter->group('price', function ($group) {
                    $group->gt('>');
                    $group->lt('<');
                    $group->nlt('>=');
                    $group->ngt('<=');
                    $group->equal('=');
                })->currency();
                $filter->in('shop_id', __('admin.product.shop'))->multipleSelect('/admin_get/shops');
            });
            $filter->column(1/2, function ($filter) {
                $filter->in('is_main')->checkbox([
                    '1'    => 'Yes',
                    '0'    => 'No',
                ]);
                $filter->equal('type')->checkbox([
                    '1'    => 'Base System Product',
                    '2'    => 'System Product',
                    '3'    => 'Shop Product',
                ]);
                $filter->equal('enable')->radio([
                    1 => 'YES',
                    0 => 'NO',
                ]);
                // $filter->in('shop_id', __('shop'))->multipleSelect(Shop::where('enable',true)->get()->pluck('name','id'));
            });

        });
        // Add batch delete action
        $grid->batchActions(function ($batch) {
            $batch->add(new BatchDelete('Shop')); // Ensure 'Shop' is the correct model type
        });
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Product::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('name', __('admin.product.name'));
        $show->field('is_main', __('admin.product.is_main'))->as(function ($is_main) {
            if($is_main) return __('admin.product.main_product');
            return __('admin.product.child_product');
        });
        $show->field('brand_id', __('admin.product.brand'));
        // $show->categories()->get('name')->pluck('name');
        $show->field('categories', trans('admin.product.category'))->as(function ($categories) {
            return $categories->pluck('name');
        })->label();
        $show->field('profile_picture', __('admin.product.profile_picture'))->image();
        $show->field('latitude', __('admin.product.latitude'));
        $show->field('longitude', __('admin.product.longitude'));
        $show->field('notes', __('admin.product.notes'));
        $show->field('shop', __('admin.product.shop'))->as(function ($shop) {
            return $shop->name;
        });
        $show->field('price', __('admin.product.price'));
        $show->field('price_off', __('admin.product.price_off'));
        $show->field('enable', __('admin.product.enable'));
        $show->field('is_suggest', __('admin.product.is_suggest'));
        $show->field('type', __('admin.product.type'))->using(config('constants.product.type_admin'))->label();
        $show->field('created_by', __('admin.product.created_by'));
        $show->field('created_at', __('admin.product.created_at'));
        $show->field('updated_at', __('admin.product.updated_at'));
// Add batch delete action
        $grid->batchActions(function ($batch) {
            $batch->add(new BatchDelete('Shop')); // Ensure 'Shop' is the correct model type
        });
        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Product());
        /*$currentId = Route::current()->parameter('product');
        if($currentId){
            $product = Product::findOrFail($currentId);
            $selectedCategories = $product->categories()->pluck('category_id')->toArray();
            // var_dump($selectedCategories);
            // $form->multipleSelect('categories', 'Category')->options(Category::with('products')->get()->pluck('name', 'id'))->selected($selectedCategories);
            // $form->multipleSelect('categories', 'Category')->options(Category::with('products')->get()->pluck('name', 'id'))->selected($product->categories->pluck('id')->toArray());
        }*/
        $form->text('name', __('admin.product.name'));
        // $form->switch('is_main', __('Is main'))->default(1);
        // $form->text('brand_id', __('brand'));

        $form->multipleSelect('categories', __('admin.product.category'))->options(Category::all()->pluck('name', 'id'));

        $form->image('profile_picture', __('admin.product.profile_picture'))
        ->removable()->downloadable()
        ->move("product/".date("Y")."/".date("m"), function ($form__) {
            // Log::channel('single')->info('This message will be logged to mylog.log');
            #Log::info($form__->model());
           # return Str::slug($form__->name)."_".uniqid().'.jpg';
         })
        ->rules('mimes:jpeg,png,jpg,gif,svg,webp')
        // ->thumbnail('small', 300, 300)
        ;

        $form->select('shop_id', __('admin.product.shop'))->options(Shop::where('enable',true)->get()->pluck('name','id'));
        $form->text('latitude',  __('admin.product.latitude'))->placeholder('Vĩ độ - VN ~ 8->23.3')->default(12.253121234459819);
        $form->text('longitude', __('admin.product.longitude'))->placeholder('Kinh độ - VN ~ 102->109.5')->default(109.57214355468751);
        $form->textarea('notes', __('admin.product.notes'))->rows(4)->rules('max:555');
        $form->currency('price', __('admin.product.price'))->symbol('đ');
        $form->text('price_off', __('admin.product.price_off'))->rules('nullable')->placeholder('(có thể để trống)');
        $form->switch('enable',  __('admin.product.enable'))->default(1);
        $form->select('type',    __('admin.product.type'))->options(config('constants.product.type_admin'));
        $form->select('created_by', __('admin.product.created_by'))->options(User::where('enable',true)->get()->pluck('name','id'));

        $form->saving(function (Form $form) {
            // Get the selected shop ID
            $shopId = $form->shop_id ?? request()->input('shop_id');

            // Find the shop by ID
            $shop = Shop::find($shopId);

            // If the shop is found, update the latitude and longitude fields of the form model
            if ($shop) {
                $form->latitude = $shop->latitude;
                $form->longitude = $shop->longitude;
            }
        });
        return $form;
    }
}
