<?php

namespace App\Http\Requests\DeliveryPartner;

use App\Http\Requests\BaseRequest;

class CheckPriceDeliveryRequest extends BaseRequest
{
    function rules()
    {
        return [
            'partner' => 'required|string|in:ahamove,j&t,remagan',
            'shop_id' => 'required|uuid|exists:shops,id',
//            'delivery_type' => 'required|string|in:fast,super_fast,super_cheap',
            'path' => 'required|array',
            'path.*.lat' => 'required|numeric',
            'path.*.lng' => 'required|numeric',
            'path.*.address' => 'required|string',
            'path.*.name' => 'required|string',
            'path.*.cod' => 'nullable|numeric',

            'order' => 'nullable|array',
            'order.address'           => 'nullable|max:5000',
            'order.province_id'       => 'nullable|exists:dvhc2021_tinh,id',
            'order.district_id'       => 'nullable|exists:dvhc2021_huyen,id',
            'order.ward_id'           => 'nullable|exists:dvhc2021_xa,id',
            'order.customer_id'       => 'nullable',
            'order.customer_name'     => 'nullable|max:255',
            'order.customer_phone'    => 'nullable|digits_between:10,11',
            // 'price'             => 'required|numeric|digits_between:0,15|min:0',
            // 'price_off'         => 'nullable|numeric|digits_between:0,15',
            'order.delivery_type'     => 'nullable',
            'order.delivery_price'    => 'nullable|numeric|digits_between:0,15',
            'order.payment_method'    => 'nullable',
            'order.notes'             => 'nullable|max:5000',
            'order.delivery_partner_id' => 'nullable|uuid|exists:delivery_partners,id'
        ] ;
    }
}
