<?php

namespace App\Http\Requests\Video;

use App\Http\Requests\BaseRequest;

class VideoRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'video' => 'required|file|mimes:mp4,avi,mkv|max:100000', // Add rules for image upload
            'name' => 'required|string|max:500', // Add rules for image upload
            'image' => 'file|mimes:jpeg,png,jpg,gif|max:2048', // Add rules for image upload
            'parent_id' => 'nullable|uuid',
            'object_type' => 'required_with:parent_id|integer',
        ];
    }

}
