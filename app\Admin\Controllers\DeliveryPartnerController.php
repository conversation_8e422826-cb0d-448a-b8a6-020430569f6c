<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\DeliveryPartnerDisable;
use Encore\Admin\Controllers\AdminController;
use App\DeliveryPartner;
use App\Image;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Form;

class DeliveryPartnerController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Delivery Partner';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */

    protected function grid(){
        $grid = new Grid(new DeliveryPartner());
        $grid->column('id', __('Id'));
        $grid->column('name', __('Name'));
        $grid->column('contact_number', __('Phone'))->display(function($value) {
            return $value ?? 'Empty';
        });
        $grid->column('email', __('Email'))->display(function($value) {
            return $value ?? 'Empty';
        });
        $grid->column('address', __('Address'))->display(function($value) {
            return $value ?? 'Empty';
        });
        $grid->column('api_key', __('Api key'))->display(function($value) {
            return $value ?? 'Empty';
        });
        $grid->column('api_secret', __('Api secret'))->display(function($value) {
            return $value ?? 'Empty';
        });
        $grid->column('api_host', __('Api host'))->display(function($value) {
            return $value ?? 'Empty';
        });
        $grid->column('notes', __('Notes'))->display(function($value) {
            return $value ?? 'Empty';
        });
        $grid->column('is_active',  __('admin.enable'))->action(new DeliveryPartnerDisable);
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed   $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(DeliveryPartner::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('name', __('Name'));
        $show->field('contact_number', __('Contact number'));
        $show->field('email', __('Email'));
        $show->field('address', __('Address'));
        $show->field('api_key', __('Api key'));
        $show->field('api_secret', __('Api secret'));
        $show->field('api_host', __('Api host'));
        $show->field('notes', __('Notes'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    protected function form()
    {
        $form = new Form(new DeliveryPartner());
        $form->text('name', __('Name'));
        $form->text('contact_number', __('Phone'));
        $form->email('email', __('Email'));
        $form->text('address', __('Address'));
        $form->text('api_key', __('Api key'));
        $form->text('api_secret', __('Api secret'));
        $form->text('api_host', __('Api host'));
        $form->text('notes', __('Notes'));
        return $form;
    }
}
