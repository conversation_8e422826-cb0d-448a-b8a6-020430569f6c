<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\UserAddress\UserAddressService;
use Illuminate\Http\JsonResponse;
use App\Http\Requests\UserAddress\UserAddressCheckIdRequest;
use App\Http\Requests\UserAddress\UserAddressCreateRequest;
use App\Http\Requests\UserAddress\UserAddressUpdateRequest;
class UserAddressController extends Controller
{
    public function myAddresses()
    {
        $service = new UserAddressService(Auth::user()->id);

        $result = $service->myAddresses();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------detail product-------------
    public function detail($id = null)
    {
        $service = new UserAddressService(Auth::user()->id);
        $result = $service->detail($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------create user address---------------
    public function create(UserAddressCreateRequest $request)
    {
        $data = $request->only([
            'name',
            'phone',
            'address',
            'ward_id',
            'district_id',
            'province_id',
            'latitude',
            'longitude',
            'title',
            'is_default',
            'note'
        ]);

        $service = new UserAddressService(Auth::user()->id);
        $result = $service->create($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------update user address---------------
    public function update(UserAddressUpdateRequest $request)
    {
        $data = $request->only([
            'id',
            'name',
            'phone',
            'address',
            'ward_id',
            'district_id',
            'province_id',
            'latitude',
            'longitude',
            'title',
            'is_default',
            'note',
            'images',
            'image_delete'
        ]);

        $service = new UserAddressService(Auth::user()->id);
        $result = $service->update($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------delete user address--------------
    public function delete(UserAddressCheckIdRequest $request)
    {
        $service = new UserAddressService(Auth::user()->id);
        $data = $request->only(['id']);

        $result = $service->delete($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------set default user address---------------
    public function setDefault(UserAddressCheckIdRequest $request)
    {
        $data = $request->only([
            'id',
        ]);

        $service = new UserAddressService(Auth::user()->id);
        $result = $service->setDefault($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
