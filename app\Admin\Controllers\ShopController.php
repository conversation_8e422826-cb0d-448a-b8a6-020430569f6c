<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Shop\ApprovedCreateShop;
use App\District;
use App\Province;
use App\User;
use App\Role;
use App\Ward;
use App\Shop;
use App\BusinessType;
use App\Image;
use App\Admin\Selectable\Logo as LogoSelectable;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Illuminate\Support\Facades\Route;
use App\Admin\Actions\EnableDisable;
use App\Admin\Actions\EnableDisable2;
use App\Admin\Actions\Replicate;
use App\Admin\Actions\QRGenerate;
use App\Admin\Actions\BatchDelete;

class ShopController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Shop';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Shop());

        $grid->column('id', __('Id'));
        $grid->column('logo', __('Logo'))->display(function ($logo) {

            // Check if the image exists
            if (isset($logo['path'])) {
                return '<img src="' . env('S3_HOST_PATH').($logo['path']) . '" style="max-width:100px;max-height:100px" />';
            } else {
                return 'Image Not Found';
            }
        });
        $grid->column('banner', __('banner'))->display(function ($banner) {

            // Check if the image exists
            if (isset($banner['path'])) {
                return '<img src="' . env('S3_HOST_PATH').($banner['path']) . '" style="max-width:100px;max-height:100px" />';
            } else {
                return 'Image Not Found';
            }
        })->hide();

        $grid->column('name', __('admin.shop.name'))->sortable()->link(function () {
            return "shops/".$this->id;
        });
        $grid->provinces(__("admin.address.province"))->display(function ($province) {return $province['name'] ?? "";});
        $grid->column('description', __('admin.product.notes'))->display(function () {
            $shopName = "Mở link shop";
            $shopLink = url(env('APP_URL')."shop/{$this->slug}"); // Assuming the ID is accessible via $shop['id']
            return "<a target='_blank' href='{$shopLink}'>{$shopName}</a>";

            return '';
        });
        $grid->column('phone', __('phone'));
        $grid->column('address', __('admin.shop.address'))->editable();
        $grid->column('latitude', __('Latitude'))->hide();
        $grid->column('longitude', __('Longitude'))->hide();
        $grid->column('owner', __('admin.user.title'))->display(function ($owner) {return $owner['name'] ?? "";});
        $grid->column('agent', __('admin.agent.title'))->display(function ($agent) {return $agent['name'] ?? "";});
        $grid->column('slug', __('Slug'))->link(function () {
            return "shops/".$this->id;
        });
        // $grid->column('banner', __('banner'));
        // $grid->column('banner_image', __('Banner'))->display(function ($banner_image) {

        //     // Check if the image exists
        //     if (isset($banner_image['path'])) {
        //         return '<img src="' . env('S3_HOST_PATH').($banner_image['path']) . '" style="max-width:100px;max-height:100px" />';
        //     } else {
        //         return 'Image Not Found';
        //     }
        // });

        $grid->column('business_types', __('admin.shop.business_type'))->display(function ($business_types) {return $business_types['name'] ?? "";});;
        // $grid->column('enable', __('admin.enable'))->bool();
        $grid->column('enable', __('admin.enable'))->action(new EnableDisable2);

        $grid->column('status', __('admin.shop.approved'))->display(function ($status) {
            // Define an array mapping status values to their inline color styles
            $display = [
                config('constants.shop.status.in_process') => 'color: #E26F20',
                config('constants.shop.status.accepted') => 'color: #4AC97E',
                config('constants.shop.status.rejected') => 'color: #D0302F'
            ];

            return '<span style="'. $display[$status] .'">'. __('admin.shop.status.' . $status) .'</span>';
        });



        // $grid->column('enable', __('admin.enable'))->icon([
        //     0 => 'toggle-off',
        //     1 => 'toggle-on',
        // ], $default = '');
        // $grid->column('created_by', __('Created by'));
        if (!request()->has('_sort')) {
            // Apply default sorting by created_at column in descending order
            $grid->model()->orderBy('created_at', 'desc');
        }
        $grid->column('created_at', __('Created at'))->display(function () {
            return "<span class='label' style='color:blue'>$this->created_at</span>";
        })->sortable();

        $grid->column('qr_code', __('QR-CODE'))->image();

        $grid->quickSearch('name','address','description');

        $grid->filter(function($filter){
            $filter->column(1/2, function ($filter) {
                $filter->like('name', __('admin.shop.name'));
                $filter->like('address', __('admin.shop.address'));
                $filter->like('phone', __('admin.shop.phone'));
                $filter->column(1/2, function ($filter) {
                    $filter->equal('enable',__('admin.enable'))->radio([
                        1 => 'YES',
                        0 => 'NO',
                    ]);
                    // $filter->in('shop_id', __('shop'))->multipleSelect(Shop::where('enable',true)->get()->pluck('name','id'));
                });
                $filter->in('user_id', __('User'))->multipleSelect('/admin_get/users');
                $filter->in('agent_id', __('Agent'))->multipleSelect('/admin_get/agents');
            });

            $filter->column(1/2, function ($filter) {
            });
        });


        // action
        $grid->actions(function ($actions) {
            $actions->add(new Replicate);
            $actions->add(new QRGenerate);
            $actions->add(new ApprovedCreateShop());
        });

        // Add batch delete action
        $grid->batchActions(function ($batch) {
            $batch->add(new BatchDelete('Shop')); // Ensure 'Shop' is the correct model type
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Shop::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('name', __('Name'));
        $show->field('logo', __('logo'))->as(function ($logo) {
            return $logo['path'] ?? 'Not found';
            return '<img src="' . env('S3_HOST_PATH').($logo['path'] ?? '') . '" style="max-width:100px;max-height:100px" />';
        })->image();
        $show->field('banner', __('Banner'))->as(function ($banner) {
            return $banner['path'] ?? '';
            return '<img src="' . env('S3_HOST_PATH').($banner['path'] ?? '') . '" style="max-width:100px;max-height:100px" />';
        })->image();
        $show->field('provinces', __('Province'))->as(function ($Province) {if($Province) return $Province->name;});
        // $show->provinces('Provinces', function ($provinces) {
        //     $provinces->setResource('/admin/provinces');
        //     // $provinces->id();
        //     $provinces->name();
        // });
        $show->field('districts', __('District'))->as(function ($districts) {
            if($districts) return $districts->name;
        });
        $show->field('wards', __('Ward'))->as(function ($wards) {
            if($wards) return $wards->name;
        });
        $show->field('address', __('Address'));
        $show->field('phone', __('Phone'));
        $show->field('latitude', __('Latitude'));
        $show->field('longitude', __('Longitude'));
        $show->field('owner', __('Owner'))->as(function ($owner) {return $owner ? $owner->name : '';});
        $show->field('agent', __('Agent'))->as(function ($agent) {return $agent ? $agent->name : '';});
        $show->field('slug', __('Slug'));
        $show->field('description', __('Description'));
        $show->field('business_type_id', __('business type id'));
        $show->field('enable', __('Enable'));
        // $show->field('created_by', __('Created by'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));
        $show->products('Products', function ($products) {

            // $products->resource('/admin/products');

            $products->id();
            $products->column('name', __('Name'))->sortable()->link(function () {
                return "/admin/products/".$this->id;
            })->filter('like')->limit(100);
            $products->column('profile_picture', __('Profile picture'))->image();
            $products->column('notes', __('Notes'))->limit(30);
            $products->shop()->display(function ($shop) {return $shop['name'] ?? "";});
            $products->column('price', __('Price'))->sortable()->display(function($price) {
                return number_format($price, 0).'đ';
            })->color('#0061a7')->limit(30);
            $products->content()->limit(100);
            $products->created_at()->display(function () {
                return "<span class='label' style='color:blue'>$this->created_at</span>";
            })->sortable();

            // $products->filter(function ($filter) {
            //     $filter->like('name');
            // });
        });

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Shop());

        $currentId = Route::current()->parameter('shop');
        global $currentShop;   // Accessing global variable
        $currentShop = Shop::find($currentId);
        $form->column(1/2, function ($form) {
            $form->text('name', __('Name'));
            $form->textarea('description', __('Description'))->rows(4)->rules('max:555')->placeholder('tối đa 555 ký tự');
            // $form->select('province_id', __('Province id'))->options(Province::all()->pluck('name','id'));
            // $form->select('district_id', __('District id'))->options(District::all()->pluck('name','id'));
            // $form->select('ward_id', __('Ward id'))->options(Ward::all()->pluck('name','id'));
            $form->select('province_id', __('Province id'))->options(Province::all()->pluck('name','id'))->load('district_id', '/admin_get/district');
            $form->select('district_id', __('District id'))->options(function ($id){
                global $currentShop;
                // $district = District::find($id);if ($district) {return [$district->id => $district->name];}
                if($currentShop){
                    return District::where("province_id", $currentShop->province_id)->get()->pluck('name','id');
                }else{
                    return [];
                }
            })->load('ward_id', '/admin_get/ward');
            $form->select('ward_id', __('Ward id'))->options(function ($id) {
                // $ward = Ward::find($id);if ($ward) {return [$ward->id => $ward->name];}
                global $currentShop;
                if($currentShop){
                    return Ward::where("district_id", $currentShop->district_id)->get()->pluck('name','id');
                }else{
                    return [];
                }
            });
            $form->text('address', __('Address'));
            $form->text('phone', __('Phone'));
            $form->text('latitude', __('Latitude'))->rules('required', [
                'required' => __('Vui lòng nhập'),
                ])->placeholder('Vĩ độ - VN ~ 8->23.3');
            $form->text('longitude', __('Longitude'))->rules('required', [
                'required' => __('Vui lòng nhập'),
                ])->placeholder('Kinh độ - VN ~ 102->109.5');
            $form->select('business_type_id', __('business_type'))->options(BusinessType::all()->pluck('name','id'));
            $form->switch('enable', __('Enable'))->default(1);
        });

        $form->column(1/2, function ($form) {
            $form->select('user_id', __('admin.shop.owner'))->options(User::all()->pluck('name','id'));
            // ->rules('required', [
            //     'required' => __('Vui lòng nhập'),
            // ]);
            $form->select('agent_id', __('admin.agent.title'))->options(User::where('role_id', 4)->get()->pluck('name','id'));
            $form->text('slug', __('Slug'))->creationRules(["unique:shops"], [
                'unique' => __('Slug unique.'),
            ])
            ->updateRules(["unique:shops,slug,{{id}}"]);
            // $form->image('banner', __('Banner'))->removable()->downloadable()->move("shop")
            // ->rules('mimes:jpeg,png,jpg,gif,svg,webp');
            // $form->select('logo_id', __('logo'))->options(Image::where('object_type', config('constants.image_object_type.shop'))->get()->pluck('title','id','path'));
            // $form->select('banner_id', __('banner'))->options(Image::where('object_type', config('constants.image_object_type.shop'))->get()->pluck('title','id','path'));
            // $form->text('created_by', __('Created by'));

        });

        $form->column(12, function ($form) {});
        $form->column(1/2, function ($form) {
            $form->belongsTo('logo_id', LogoSelectable::class, __('admin.shop.logo'));
        });
        $form->column(1/2, function ($form) {
            $form->belongsTo('banner_id', LogoSelectable::class, __('admin.shop.banner'));
        });

        return $form;
    }
}
