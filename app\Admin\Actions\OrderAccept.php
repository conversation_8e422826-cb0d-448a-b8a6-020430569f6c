<?php

namespace App\Admin\Actions;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class OrderAccept extends RowAction
{
    public function name()
    {
        return __('admin.order.status_admin.2');
    }

    public function handle (Model $model)
    {
        $model->status = config('constants.order.status.in_process', 2);
        $model->notes = $model->notes . " Xác nhận từ Admin.";
        $model->save();

        return $this->response()->success(__('admin.order.status_admin.2'))->refresh();
    }

}