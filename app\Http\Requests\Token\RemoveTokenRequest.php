<?php

namespace App\Http\Requests\Token;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class RemoveTokenRequest extends BaseRequest {
    public function rules()
    {
        return [
            'selection' => 'required|in:all,many',
            'token_ids' => 'required_if:selection,many|array',
            'token_type' => 'required|integer|in:1,2',
            'token_excluded' => 'nullable|string',
        ];
    }
}
