<?php

namespace App\Admin\Controllers;

use App\Delivery;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Widgets\Table;
use function foo\func;

class DeliveryController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Delivery controller';
    protected function grid(){
        $grid = new Grid(new Delivery());
        $grid->column('id', __('Id'))->expand(function ($model) {
            $statusTime = json_decode($model->status_time, true);

            // Nếu không có dữ liệu, hiển thị thông báo
            if (!$statusTime || !is_array($statusTime)) {
                return "<p>Không có thông tin trạng thái.</p>";
            }
            $statuses = config('constants.delivery_admin');

            // Chuẩn bị tiêu đề và nội dung cho bảng
            $headers = ['Trạng thái', 'Thời gian', "Ghi chú"];
            $rows = [];

            foreach ($statusTime as $item) {
                $statusName = $statuses[$item['status']] ?? "Unknown Status";
                $rows[] = [$statusName, $item['time'], $item['notes']];
            }
            return new Table($headers, $rows);
        });
        ;
        $grid->column('short_code', __('Short Code'));

        $grid->column('pickup_time', __('Pickup time'))->display(function($pickup_time) {
            if (!$pickup_time) return "Không xác định";
            return htmlspecialchars($pickup_time);
        });

        $grid->column('address_from',  __('Address form'))->display(function($address_from) {
            if(!$address_from) return "Không xác định";
            return htmlspecialchars($address_from);
        });
        $grid->column('name_from',  __('Name form'))->display(function($name_from) {
            if (!$name_from) return "Không xác định";
            return htmlspecialchars($name_from);
        });
        $grid->column('phone_from',  __('Phone form'))->display(function($phone_from) {
            if (!$phone_from) return "Không xác định";
            return htmlspecialchars($phone_from);
        });

        $grid->column('address_to',  __('Address to'))->display(function($address_to) {
            if (!$address_to) return "Không xác định";
            return htmlspecialchars($address_to);
        });
        $grid->column('name_to', __('Name to'))->display(function($name_to) {
            if (!$name_to) return "Không xác định";
            return htmlspecialchars($name_to);
        });
        $grid->column('phone_to',  __('Phone to'))->display(function($phone_to) {
            if (!$phone_to) return "Không xác định";
            return htmlspecialchars($phone_to);
        });

        $grid->column('notes', __('Notes'));

        $grid->column("cod_price", __('COD'))->display(function($cod_price) {
            if (!$cod_price) return 0;
            return number_format($cod_price, 0, ",", ".");
        });

        $grid->column("grand_total", __('Grand total'))->display(function($grand_total) {
            if (!$grand_total) return 0;
            return number_format($grand_total, 0, ",", ".");
        });

        $grid->column("total_amount", __('Total amount'))->display(function($total_amount) {
            if (!$total_amount) return 0;
            return number_format($total_amount, 0, ",", ".");
        });

        $grid->column('status', __('Status'))->using(config('constants.delivery_admin'))->dot([
            1 => 'danger',
            2 => 'info',
            3 => 'primary',
            6 => 'success',
            7 => 'danger',
            8 => 'danger'
        ], 'warning');
        return $grid;
    }

    protected function form()
    {
        $form = new Form(new Delivery());

        $form->text('address_from', __('Address form'))->required();
        $form->text('name_from', __('Name form'))->required();
        $form->text('phone_from', __('Phone form'))->required();
        $form->text('address_to', __('Address to'))->required();
        $form->text('name_to', __('Name to'))->required();
        $form->text('phone_to', __('Phone to'))->required();
        $form->text('notes', __('Notes'));

        $form->datetime('pickup_time', __('Pickup time'));

        $form->number('cod_price', __('COD'));
        $form->number('grand_total', __('Grand total'));
        $form->number('total_amount', __('Total amount'));
        $form->select('status', __('Status'))->options([
            1 => 'Pending',
            2 => 'Confirmed',
            3 => 'Prepared',
            4 => 'Picked_up',
            5 => 'Transition',
            6 => 'Delivered',
            7 => 'Cancelled',
            8 => 'Failed',
        ])->default(1)->required();
        return $form;
    }
}
