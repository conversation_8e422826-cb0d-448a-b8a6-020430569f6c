<?php

// Test script to verify referral system functionality
require_once __DIR__ . '/../../vendor/autoload.php';
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\User;
use App\Order;
use Illuminate\Support\Facades\DB;

echo "Starting referral system test...\n";

try {
    echo "=== Testing Referral System ===\n\n";

    // Test 1: Check if referral_code field exists in users table
    echo "1. Testing referral_code field in users table...\n";
    $columns = DB::getSchemaBuilder()->getColumnListing('users');
    if (in_array('referral_code', $columns)) {
        echo "✓ referral_code field exists in users table\n";
    } else {
        echo "✗ referral_code field missing from users table\n";
    }

    // Test 2: Check if ref_id field exists in orders table
    echo "\n2. Testing ref_id field in orders table...\n";
    $columns = DB::getSchemaBuilder()->getColumnListing('orders');
    if (in_array('ref_id', $columns)) {
        echo "✓ ref_id field exists in orders table\n";
    } else {
        echo "✗ ref_id field missing from orders table\n";
    }

    // Test 3: Test referral code generation
    echo "\n3. Testing referral code generation...\n";
    $testUser = User::first();
    if ($testUser) {
        echo "Found test user: {$testUser->name} (ID: {$testUser->id})\n";
        if (!$testUser->referral_code) {
            $code = $testUser->generateReferralCode();
            echo "✓ Generated referral code: {$code}\n";
        } else {
            echo "✓ User already has referral code: {$testUser->referral_code}\n";
        }
    } else {
        echo "✗ No users found to test referral code generation\n";
    }

    // Test 4: Test findByReferralCode method
    echo "\n4. Testing findByReferralCode method...\n";
    if ($testUser && $testUser->referral_code) {
        $foundUser = User::findByReferralCode($testUser->referral_code);
        if ($foundUser && $foundUser->id === $testUser->id) {
            echo "✓ findByReferralCode method works correctly\n";
        } else {
            echo "✗ findByReferralCode method failed\n";
        }
    } else {
        echo "⚠ Cannot test findByReferralCode - no user with referral code\n";
    }

    // Test 5: Test relationships
    echo "\n5. Testing model relationships...\n";
    
    // Test referredOrders relationship
    if ($testUser) {
        try {
            $referredOrders = $testUser->referredOrders;
            echo "✓ referredOrders relationship works (count: " . $referredOrders->count() . ")\n";
        } catch (Exception $e) {
            echo "✗ referredOrders relationship failed: " . $e->getMessage() . "\n";
        }
    }

    // Test Order referrer relationship
    $testOrder = Order::first();
    if ($testOrder) {
        try {
            $referrer = $testOrder->referrer;
            echo "✓ Order referrer relationship works\n";
        } catch (Exception $e) {
            echo "✗ Order referrer relationship failed: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠ No orders found to test referrer relationship\n";
    }

    // Test 6: Test referral statistics
    echo "\n6. Testing referral statistics...\n";
    if ($testUser) {
        try {
            $stats = $testUser->getReferralStats();
            echo "✓ Referral statistics generated successfully\n";
            echo "  - Referral code: " . $stats['referral_code'] . "\n";
            echo "  - Total referrals: " . $stats['total_referrals'] . "\n";
            echo "  - Total referred orders: " . $stats['total_referred_orders'] . "\n";
            echo "  - Successful referrals: " . $stats['successful_referrals'] . "\n";
            echo "  - Total referred order value: " . $stats['total_referred_order_value'] . "\n";
        } catch (Exception $e) {
            echo "✗ Referral statistics failed: " . $e->getMessage() . "\n";
        }
    }

    echo "\n=== Test Complete ===\n";

} catch (Exception $e) {
    echo "Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
