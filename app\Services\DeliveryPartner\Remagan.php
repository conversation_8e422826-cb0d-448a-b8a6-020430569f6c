<?php
namespace App\Services\DeliveryPartner;

use App\Delivery;
use App\DeliveryPartner as DeliveryPartnerModel;
use App\Helpers\Helper;
use App\Notifications\NotificationUser;
use App\Order;
use App\Services\Delivery\DeliveryService;
use App\Services\DeliveryPartner\DeliveryPartner;
use App\Services\Mqtt\MqttChatService;
use App\Services\Notification\NotificationService;
use App\Services\Token\TokenService;
use App\Shop;
use App\ShopDeliveryPartner;
use App\User;
use Carbon\Carbon;

class Remagan extends DeliveryPartner{

    public function __construct($shopId = null)
    {
        $this->shopId = $shopId;
        $this->partner = DeliveryPartnerModel::where('name', 'remagan')->first();
    }

    function checkPrice($data)
    {
        $shop = Shop::find($this->shopId);
        $price = DeliveryService::checkPrice($data);
        $data['order']['delivery_distance'] = $data['distance'];
        $data['order']['grand_total'] = $data['order']['grand_total'] ?? ($data['order']['price_off'] ?? 0);

        $result = [
            'service_id' => 'RMG', // Default
            'name'  => 'Mặc định',
            'name_vi_vn' => 'Mặc định',
            'name_en_us' => 'Default',
            'price' => $price,
            'discount' => DeliveryService::checkDiscountBySettingShop($price, $data['order'], $shop),
            'distance' => $data['distance'],
            'duration' => $data['duration'] ?? 30,
        ];

        if(!$price){
            if(isset($shop->settings['order']['delivery_default_message'])){
            $result['error'] = [
                "message" => $shop->settings['order']['delivery_default_message']
            ];
            }else{
                $result['error'] = [
                    "message" => [
                        'vi' => "Thông báo phí vận chuyển sau khi đặt hàng",
                        'en' => "Shipping fee provided after order placement"
                    ]
                ];
            }

        }

        return [$result];
        }

    function createDelivery($data, $orderId)
    {
        $errorService = new DeliveryErrorService();

        try {
            $order = $this->getOrder($orderId);
            $shop = Shop::find($this->shopId);

            // Log the delivery creation attempt
            $errorService->logApiCall('remagan', $orderId, 'createDelivery', $data);

        if($order->delivery_id) {
            $deliveryPartner =  DeliveryPartnerModel::where('id', $order->delivery_partner_id)->first();
            $deliveryService = new DeliveryPartnerService();
            $deliveryService = $deliveryService->setDeliveryPartner(strtolower($deliveryPartner->name), $this->shopId);
            $deliveryDetail = $deliveryService->detailDelivery($orderId);

            if(isset($deliveryDetail['message'])) {
                return [
                    'message' => 'Delivery has been created already!',
                    'data'    =>  null,
                ];
            }

            if($deliveryDetail['status'] != config('constants.delivery.cancelled')) {
                return [
                    'message' => 'Delivery has been created already!',
                    'data'    =>  $deliveryDetail,
                ];
            }
        };

        // Dữ liệu dùng để tạo vận đơn được lấy từ data
        $path = $data['path'];
        $startPoint = $path[0];
        $endPoint = $path[count($path) - 1];
        // Thời gian cho phép chờ nhận đơn
        $pending_time = $data['pending_time'] ?? null;

        $dataCreate = [
            "order_id" => $orderId,
            "driver_id" => $data['driver_id'] ?? null,
            "duration" => $data['duration'] ?? 30,
            "pickup_time" => $data['pickup_time'] ?? null,
            "notes"    => $data['note'] ?? null,
            "latitude_from" => $startPoint['lat'],
            "longitude_from" => $startPoint['lng'],
            "latitude_to" => $endPoint['lat'],
            "longitude_to" => $endPoint['lng'],
            "distance" => $data['distance'] ?? 0,
            "address_from" => $startPoint['address'],
            "address_to" => $endPoint['address'],
            "phone_from" => $startPoint['phone'] ?? null,
            "phone_to" => $endPoint['phone'] ?? null,
            "name_from" => $startPoint['name'],
            "name_to" => $endPoint['name'],
            "cod_price" => $endPoint['cod'] ?? 0,
            "package_info" => $data['package_info'] ?? null,
            "special_require" => $data['special_require'] ?? "[]",
            "total_amount" => $data['total_amount'] ?? 0,
            "grand_total" => $data['grand_total'] ?? $data['total_amount'],
            "payment_method" => $data['payment_method'],
            "pending_time"   => $pending_time
        ];
        $service = new DeliveryService();
        $result = $service->create($dataCreate);
        if($result){
            $order->delivery_partner_id = $this->partner->id;
            $order->delivery_id = $result['short_code'];
            $order->delivery_price = $result['grand_total'];
            $order->delivery_discount = DeliveryService::checkDiscountBySettingShop($result['grand_total'], $order, $shop);
            $order->save();
            $msgToDriver = [
                'title' => "Vận đơn mới",
                'body'  => 'Bạn có vận đơn mới từ cửa hàng '. $shop->name,
                'url'   => $result->id,
                'target_url' => '/driver-tools/delivery-history/'. $result->id,
                'type'  => 'delivery_new',
            ];

            $msgToCustomer = [
                'title' => "Đơn hàng đã được vận đơn",
                'body'  => 'Đơn hàng '.$order->short_code .' từ cửa hàng '. $shop->name .'đã được vận đơn',
                'url'   => $order->id,
                'target_url' => '/my-orders/'. $order->short_code,
                'type'  => 'delivery_new',
            ];
            $mqtt = new MqttChatService();

            if($result->driver_id){
                $tokenService = new TokenService();
                $notiService = new NotificationService;
                $driverTokens = $tokenService->listByUser($result->driver_id, 2);
                if(count($driverTokens) > 0){
                    $notiService->sendBatchNotification($driverTokens, $msgToDriver);
                }
                $mqtt->publish(['topic' => $result->driver_id , 'message' => $msgToDriver]);

                # nhận thông báo và lưu vào table notifications
                $driver_obj = User::find($result->driver_id);
                if($driver_obj){
                    $driver_obj->notify(new NotificationUser($result->driver_id, $msgToDriver));
                }
            }
            $mqtt->publish(['topic' => 'remagan_order/'. $order->id , 'message' => $msgToCustomer]);

            // Log successful delivery creation
            $errorService->logSuccess('remagan', $orderId, $result['short_code'], [
                'delivery_price' => $result['grand_total'],
                'delivery_discount' => $order->delivery_discount,
                'driver_id' => $result->driver_id ?? null
            ]);

            $deliveryService = new DeliveryService();
            return $deliveryService->detail($result['id']);
        }

        // Log delivery service failure
        $errorService->logAndNotify(
            'DELIVERY_SERVICE_FAILED',
            'Remagan delivery service returned false or null result',
            'remagan',
            $orderId,
            [
                'request_data' => $dataCreate,
                'shop_id' => $this->shopId
            ]
        );

        return [
            'message' => 'Create order delivery failed'
        ];

        } catch (\Exception $e) {
            // Log the error with full context
            $errorService->logAndNotify(
                'DELIVERY_CREATION_EXCEPTION',
                'Exception occurred during Remagan delivery creation: ' . $e->getMessage(),
                'remagan',
                $orderId,
                [
                    'request_data' => $data ?? [],
                    'shop_id' => $this->shopId,
                    'order_details' => isset($order) ? [
                        'id' => $order->id,
                        'short_code' => $order->short_code ?? null,
                        'existing_delivery_id' => $order->delivery_id ?? null
                    ] : null,
                    'delivery_data' => isset($dataCreate) ? $dataCreate : null
                ],
                $e
            );

            return [
                'message' => 'Create order delivery failed due to system error'
            ];
        }
    }

    function detailDelivery($orderId)
    {
        $order = $this->getOrder($orderId);
        if (!$order->delivery_id) {
            return ['message' => 'Delivery not created'];
        }

        $delivery = Delivery::where("short_code", $order->delivery_id)->first();
        if (!$delivery) {
            return ['message' => 'Delivery not created'];
        }
        $service = new DeliveryService();
        $result = $service->detail($delivery->id);
        if($result){
            $result->shared_link = null;
        }
        return $result;
    }

    function cancelDelivery($data, $orderId)
    {
        $order = $this->getOrder($orderId);
        $mqtt = new MqttChatService();

        if (!$order->delivery_id) {
            return ['message' => 'Delivery not created'];
        }

        $delivery = Delivery::where("short_code", $order->delivery_id)->first();
        if (!$delivery) {
            return ['message' => 'Delivery not created'];
        }

        $allowedStatuses = [
            config('constants.delivery.pending'),
            config('constants.delivery.failed'),
            config('constants.delivery.cancelled'),
        ];

        if (!in_array($delivery->status, $allowedStatuses)) {
            return ['message' => "Failed to cancel delivery"];
        }

        $delivery->update([
            'status' => config('constants.delivery.cancelled')
        ]);

        if($delivery->driver_id){
            $msg = [
                'title' => "Vận đơn đã bị huỷ",
                'url'   => $order->id,
                'extra' => json_encode([
                    'shop_id'   => $order->shop_id
                ]),
                'type' => 'delivery_cancel'
            ];
            $mqtt->publish(['topic' => $delivery->driver_id, $msg]);
        }

        $order->update([
            'delivery_id' => null
        ]);
        return true;
    }

    function connectDeliveryPartner($data)
    {
        $connection = $this->getShopDeliveryPartner();
        if(!$connection){
            return $this->createConnect($data);
        }
        return $this->updateConnect($data, $connection);
    }

    private function createConnect($data)
    {
        return ShopDeliveryPartner::create([
            'shop_id' => $this->shopId,
            'delivery_partner_id' => $this->partner->id,
            'connect_data' => $data,
            'is_default' => !$this->hasConnectedToDeliveryPartner()
        ]);
    }

    /**
     * Update existing delivery partner connection
     */
    private function updateConnect($data, $connection)
    {
        $connection->update([
            'connect_data' => $data,
        ]);

        return $this->getShopDeliveryPartner();
    }


    function notification($data, $orderId)
    {
        // TODO: Implement notification() method.
    }
}
