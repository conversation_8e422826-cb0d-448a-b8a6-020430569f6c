<?php

return [
    'status'                => 'Status',
    'qr_maker'              => 'Create QR code',
    'copy'                  => 'Create a copy',
    'enable'                => 'Enable',
    'disable'               => 'Disable',
    'is_enable'             => 'Currently Enabled',
    'is_disable'            => 'Currently Disable',
    'online'                => 'Online',
    'login'                 => 'Login',
    'logout'                => 'Logout',
    'setting'               => 'Setting',
    'name'                  => 'Name',
    'username'              => 'Username',
    'password'              => 'Password',
    'password_confirmation' => 'Password confirmation',
    'remember_me'           => 'Remember me',
    'user_setting'          => 'User setting',
    'avatar'                => 'Avatar',
    'list'                  => 'List',
    'new'                   => 'New',
    'create'                => 'Create',
    'delete'                => 'Delete',
    'remove'                => 'Remove',
    'edit'                  => 'Edit',
    'view'                  => 'View',
    'continue_editing'      => 'Continue editing',
    'continue_creating'     => 'Continue creating',
    'detail'                => 'Detail',
    'browse'                => 'Browse',
    'reset'                 => 'Reset',
    'export'                => 'Export',
    'batch_delete'          => 'Batch delete',
    'save'                  => 'Save',
    'refresh'               => 'Refresh',
    'order'                 => 'Order',
    'expand'                => 'Expand',
    'collapse'              => 'Collapse',
    'filter'                => 'Filter',
    'search'                => 'Search',
    'close'                 => 'Close',
    'show'                  => 'Show',
    'entries'               => 'entries',
    'captcha'               => 'Captcha',
    'action'                => 'Action',
    'title'                 => 'Title',
    'description'           => 'Description',
    'back'                  => 'Back',
    'back_to_list'          => 'Back to List',
    'submit'                => 'Submit',
    'menu'                  => 'Menu',
    'input'                 => 'Input',
    'succeeded'             => 'Succeeded',
    'failed'                => 'Failed',
    'delete_confirm'        => 'Are you sure to delete this item ?',
    'delete_succeeded'      => 'Delete succeeded !',
    'delete_failed'         => 'Delete failed !',
    'update_succeeded'      => 'Update succeeded !',
    'save_succeeded'        => 'Save succeeded !',
    'refresh_succeeded'     => 'Refresh succeeded !',
    'login_successful'      => 'Login successful',
    'choose'                => 'Choose',
    'choose_file'           => 'Select file',
    'choose_image'          => 'Select image',
    'more'                  => 'More',
    'deny'                  => 'Permission denied',
    'administrator'         => 'Administrator',
    'roles'                 => 'Roles',
    'permissions'           => 'Permissions',
    'slug'                  => 'Slug',
    'created_at'            => 'Created At',
    'updated_at'            => 'Updated At',
    'alert'                 => 'Alert',
    'parent_id'             => 'Parent',
    'icon'                  => 'Icon',
    'uri'                   => 'URI',
    'operation_log'         => 'Operation log',
    'parent_select_error'   => 'Parent select error',
    'pagination'            => [
        'range' => 'Showing :first to :last of :total entries',
    ],
    'role'                  => 'Role',
    'permission'            => 'Permission',
    'route'                 => 'Route',
    'confirm'               => 'Confirm',
    'cancel'                => 'Cancel',
    'http'                  => [
        'method' => 'HTTP method',
        'path'   => 'HTTP path',
    ],
    'all_methods_if_empty'  => 'All methods if empty',
    'all'                   => 'All',
    'other'                 => 'Other',
    'current_page'          => 'Current page',
    'selected_rows'         => 'Selected rows',
    'upload'                => 'Upload',
    'new_folder'            => 'New folder',
    'time'                  => 'Time',
    'size'                  => 'Size',
    'listbox'               => [
        'text_total'         => 'Showing all {0}',
        'text_empty'         => 'Empty list',
        'filtered'           => '{0} / {1}',
        'filter_clear'       => 'Show all',
        'filter_placeholder' => 'Filter',
    ],
    'grid_items_selected'    => '{n} items selected',

    'menu_titles'            => [],
    'prev'                   => 'Prev',
    'next'                   => 'Next',
    'quick_create'           => 'Quick create',
    'user'                   => [
    'title'               => 'User',
    'role'                => 'User Role',
    'user_name'           => 'Username',
    'username'            => 'Username',
    'name'                => 'Full Name',
    'provider'            => 'Platform',
    'address'             => 'Address',
    'gender'              => 'Gender',
    'profile_picture'     => 'Profile Picture',
    'notes'               => 'Description',
    'date_of_birth'       => 'Date of Birth',
    'identify_card'       => 'ID Card',
    ],
    'product'                => [
        'title'               => 'Product',
        'name'                => 'Product Name',
        'image'               => 'Product Image',
        'is_main'             => 'Main Product',
        'main_product'        => 'Main Product',
        'child_product'       => 'Child Product',
        'brand'               => 'Brand',
        'profile_picture'     => 'Product Image',
        'category'            => 'Category',
        'quantity'            => 'Quantity',
        'price'               => 'Original Price',
        'price_root'          => 'Import Price (Owner/Agent Only)',
        'price_off'           => 'Discounted Price',
        'shop'                => 'Shop',
        'type'                => 'Product Data Type',
        'created_by'          => 'Created By (optional)',
        'created_at'          => 'Created At',
        'updated_at'          => 'Updated At',
        'latitude'            => 'Latitude (8->23)',
        'longitude'           => 'Longitude (102->109)',
        'notes'               => 'Description',
        'enable'              => 'Display',
        'price_total'         => 'Total Product Price (after discount)',
        'is_suggest'          => 'Featured Product',
    ],
    'order'                => [
        'title'               => 'Order',
        'total_amount'        => 'Total Product Value',
        'discount_amount'     => 'Discount Amount',
        'grand_total'         => 'Grand Total',
        'delivery_type'       => 'Delivery Method',
        'delivery_price'      => 'Delivery Fee',
        'payment_method'      => 'Payment Method',
        'created_at'          => 'Created At',
        'updated_at'          => 'Updated At',
        'id'                  => 'UUID',
        'short_code'          => 'Order Code',
        'item'                => 'Order Item',
        'notes'               => 'Order Notes',
        'address'             => 'Delivery Address',
        'customer_phone'      => 'Customer Phone',
        'customer_name'       => 'Customer Name',
        'status_title'        => 'Status',
        'status'              => [
            'new'             => 1,
            'in_process'      => 2,
            'ready_to_deliver'=> 3,
            'done'            => 4,
            'return'          => 5,
            'cancel'          => 6,
        ],
        'status_admin'        => [
            '1' => 'Pending Confirmation',
            '2' => 'Confirmed',
            '3' => 'Ready to Deliver',
            '4' => 'Delivered (Completed)',
            '5' => 'Returned',
            '6' => 'Cancelled',
        ],
    ],
    'image'                => [
        'title'               => 'Title',
        'enable'              => 'Display',
        'object_type'         => 'Image Type',
        'parent_id'           => 'Main Entity ID',
        'path'                => 'Path',
        'description'         => 'Description',
    ],
    'notes'                => 'Notes',
    'shop'                 => [
        'title'               => 'Shop',
        'phone'               => 'Phone Number',
        'owner'               => 'Shop Owner',
        'business_type'       => 'Business Type',
        'notes'               => 'Description',
        'name'                => 'Shop Name',
        'created_at'          => 'Created At',
        'address'             => 'Address',
        'approved'            => 'Approved',
        'status'    => [
            '1'     => "In Process",
            '2'     => "Accept",
            '3'     => "Reject"
        ]

    ],
    'customer'             => [
        'title'               => 'Customer',
        'name'                => 'Customer Name',
        'phone'               => 'Phone Number',
        'email'               => 'Email',
    ],
    'agent'                => [
        'title'               => 'Agent',
        'set'                 => 'Set as Agent',
    ],
    'driver'               => [
        'title'               => 'Driver',
        'set'                 => 'Set as Driver',
    ],
    'address'              => [
        'title'               => 'Address',
        'province'            => 'Province',
        'district'            => 'District',
        'ward'                => 'Ward',
    ],
    'delivery'             => [
        'title'               => 'Delivery',
        'pickup'              => 'Pickup',
        'delivery_to_me'      => 'Deliver to Me',
    ],
    'payment_method'       => [
        'title'               => 'Payment Method',
        'cod'                 => 'Cash on Delivery (COD)',
        'bank_transfer'       => 'Bank Transfer',
        'credit_card'         => 'Credit Card or Visa',
    ],
    'reason'               => [
        'title'               => 'Reason',
        'placeholder'         => 'Enter reason',
    ],
    'queue'                => [
        'order_new'           => 'New Order to Shop Owner',
        'agent_order_new'     => 'New Order Notified to Shop Manager',
        'delivery_new'        => 'New Order to Driver',
        'order_failed'        => 'Order Cancelled',
        'order_cancel'        => 'Order Rejected',
        'order_status'        => 'Order Status Notification to Customer',
    ],
    'delivery_partner'     => [
        'name'                => 'Delivery Partner Name',
    ],

];
