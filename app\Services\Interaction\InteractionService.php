<?php

namespace App\Services\Interaction;

use App\Interaction;
use App\Product;
use App\Shop;

class InteractionService
{
    public function create(array $data, string $type = null)
    {
        if(isset($type)){
            switch ($data["object_type_b"]) {
                case config("constants.object_type.product"): {
                    Product::where("id", $data['object_id_b'])->increment($type);
                    break;
                }
                case config("constants.object_type.shop"): {
                    Shop::where("id", $data['object_id_b'])->increment($type);
                    break;
                }
            }
        }

        return Interaction::updateOrCreate(
            [
                // 'object_type_a' => $data['object_type_a'],
                'object_id_a' => $data['object_id_a'],
                // 'object_type_b' => $data['object_type_b'],
                'object_id_b' => $data['object_id_b'],
                'interaction_type' => $data['interaction_type'],
            ],
            $data
        );
    }

    public function delete(array $data)
    {
        return Interaction::where($data)->delete();
    }

    public function getInteractions(array $data)
    {
        $query = Interaction::query();

        if ($data['object_type'] == 1) {
            $query->where('object_type_a', $data['object_type'])
                  ->where('object_id_a', $data['object_id']);
        } else {
            $query->where('object_type_b', $data['object_type'])
                  ->where('object_id_b', $data['object_id']);
        }

        $query->where('interaction_type', $data['interaction_type']);

        return $query->get();
    }

    public function createOrDelete(array $data)
    {
        $existingInteraction = Interaction::where([
            'object_id_a' => $data['object_id_a'],
            'object_id_b' => $data['object_id_b'],
            'interaction_type' => $data['interaction_type'],
        ])->first();

        switch ($data['interaction_type']) {
            case config("constants.interaction.like"): $type = "likes"; break;
            case config("constants.interaction.follow"): $type = "follows"; break;
            default: $type = null; break;
        }

        if ($existingInteraction) {
            Interaction::where([
                'object_id_a' => $data['object_id_a'],
                'object_id_b' => $data['object_id_b'],
                'interaction_type' => $data['interaction_type'],
            ])->delete();

            // Decrement total follows or like of object_b
            if(isset($type)){
                switch ($data["object_type_b"]) {
                    case config("constants.object_type.product"): {
                        Product::where("id", $data['object_id_b'])->decrement($type);
                        break;
                    }
                    case config("constants.object_type.shop"): {
                        Shop::where("id", $data['object_id_b'])->decrement($type);
                        break;
                    }
                }
            }
            return null;
        }
        if ($data['interaction_type'] != config('constants.interaction.unread')) {
            return $this->create($data, $type);
        }
        return null;   }

    public function getUserInteractions($userId, $type = 2)
    {
        return Interaction::where('object_type_a', 1)
            ->where('object_id_a',$userId)
            ->where('interaction_type', $type)
            ->get();
    }

    public function getShopInteractions($shopId)
    {
        return Interaction::where('object_type_a', 2)
        ->where('object_id_b', $shopId)
            ->where('interaction_type', 1)
            ->get();
    }

    // Kiểm tra thông tin tương tác giữa 2 đối tượng
    public function checkInteractionBetweenTwoObjects($objectIdA, $objectIdB, $interaction_type)
    {
        return Interaction::on("pgsqlReplica")
            ->where('object_id_a', $objectIdA)
            ->where('object_id_b' ,$objectIdB)
            ->where('interaction_type', $interaction_type)
            ->exists();
    }
}
