<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class RemoveListRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'id' => 'nullable|array',
            'id.*' => 'required|uuid|exists:users,id'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.array' =>'User_002_E_004',

    //         'id.*.exists' => 'User_003_E_001',
    //         'id.*.uuid' => 'User_002_E_002',
    //         'id.*.required' => 'User_001_E_003',
    //     ];
    // }
}
