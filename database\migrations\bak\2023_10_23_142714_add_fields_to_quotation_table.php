<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFieldsToQuotationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('quotations', function (Blueprint $table) {
            $table->smallInteger('status')->default(1)->comment("trạng thái báo giá: 1 - đang yêu cầu, 2 - đ<PERSON> phản hồi, 3 - đã lưu");
            $table->string('link', 255)->comment("liên kết của báo giá")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('quotations', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('link');
        });
    }
}
