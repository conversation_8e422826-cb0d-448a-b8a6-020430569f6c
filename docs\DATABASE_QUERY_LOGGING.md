# Database Query Logging Setup

This document explains how to use the enhanced database query logging system for backup safety and debugging purposes.

## Overview

The system logs CREATE (INSERT), UPDATE, and DELETE database queries to daily files with the format `query-yyyy-mm-dd.log` in the `storage/logs/` directory when `DB_QUERY_LOG=true` is set in your environment file. SELECT queries are NOT logged to reduce log volume.

## Configuration

### Environment Variables

Add or update these variables in your `.env` file:

```env
# Enable/disable database query logging
DB_QUERY_LOG=true

# Logging channel (already configured)
LOG_CHANNEL=stack
```

### Log Files Location

Query logs are stored in: `storage/logs/query-yyyy-mm-dd.log`

Examples:
- `storage/logs/query-2025-06-23.log` (for June 23, 2025)
- `storage/logs/query-2025-06-24.log` (for June 24, 2025)

## Log Format

Each query log entry includes comprehensive restoration data with **executable SQL** and **JWT user context**:

```json
{
    "operation_type": "UPDATE",
    "table_name": "users",
    "timestamp": "2025-06-23 14:30:45",
    "user_id": "uuid-456",
    "user_email": "<EMAIL>",
    "user_name": "john_doe",
    "user_role": 6,
    "request_id": "unique-request-id",
    "ip_address": "*************",
    "user_agent": "Mozilla/5.0...",
    "execution_time": "15.23ms",
    "connection": "pgsql",
    "executable_sql": "UPDATE users SET updated_at = '2025-06-23 14:30:45' WHERE id = 123",
    "raw_sql_with_placeholders": "UPDATE users SET updated_at = ? WHERE id = ?",
    "bindings": ["2025-06-23 14:30:45", 123]
}
```

**Key Features**:
- **JWT Authentication Support**: Properly extracts user info from JWT tokens
- **Executable SQL**: Ready-to-run queries with all parameters filled in
- **Complete User Context**: User ID, email, username, and role from JWT payload

## Query Types Logged

✅ **Logged:**
- INSERT queries (User::create(), DB::insert())
- UPDATE queries (User::update(), DB::update())
- DELETE queries (User::delete(), DB::delete())
- CREATE/ALTER/DROP/TRUNCATE (schema changes)

❌ **NOT Logged:**
- SELECT queries (User::find(), DB::select(), etc.)
- This reduces log volume significantly

## Management Commands

### View Query Logs

View today's query logs:
```bash
php artisan logs:view-queries
```

View specific date logs:
```bash
php artisan logs:view-queries --date=2025-06-23
```

View last 100 lines:
```bash
php artisan logs:view-queries --tail=100
```

### Analyze Query Logs for Restoration

Analyze today's logs:
```bash
php artisan logs:analyze-queries
```

Analyze specific date with filters:
```bash
php artisan logs:analyze-queries --date=2025-06-23 --table=users --operation=DELETE
```

Filter by user:
```bash
php artisan logs:analyze-queries --user=123
```

### Generate Restoration Plan

Generate restoration plan for today:
```bash
php artisan logs:generate-restoration-plan
```

Generate plan for specific date and table:
```bash
php artisan logs:generate-restoration-plan --date=2025-06-23 --table=orders
```

This creates a SQL file with restoration queries and instructions.

### Extract Raw Executable Queries

Extract all raw queries for today:
```bash
php artisan logs:extract-raw-queries
```

Extract queries for specific date and table:
```bash
php artisan logs:extract-raw-queries --date=2025-06-23 --table=users
```

Extract only INSERT operations:
```bash
php artisan logs:extract-raw-queries --operation=INSERT --output=/path/to/inserts.sql
```

This creates a `.sql` file with executable queries that you can run directly in your database.

### Example Raw Query Output

The extracted SQL file (`raw-queries-2025-06-23.sql`) contains:

```sql
-- Raw SQL Queries Extracted from Database Logs
-- Date: 2025-06-23
-- Generated at: 2025-06-23 15:30:45
-- Total queries: 3
--
-- WARNING: These are the EXACT queries that were executed.
-- Review carefully before running, especially DELETE operations!
--

-- ========================================
-- Query #1
-- Timestamp: 2025-06-23 14:30:45
-- Operation: INSERT
-- Table: users
-- User: <EMAIL> (admin_user, Role: 1)
-- IP: *************
-- ========================================
INSERT INTO users (name, email, created_at, updated_at) VALUES ('John Doe', '<EMAIL>', '2025-06-23 14:30:45', '2025-06-23 14:30:45');

-- ========================================
-- Query #2
-- Timestamp: 2025-06-23 14:31:20
-- Operation: UPDATE
-- Table: users
-- User: <EMAIL> (shop_owner, Role: 7)
-- IP: *************
-- ========================================
UPDATE users SET status = 'active', updated_at = '2025-06-23 14:31:20' WHERE id = 123;

-- End of extracted queries
```

**Perfect for Emergency Restoration**: If data gets cleared suddenly, you can immediately run these exact queries to restore your data!

### Clean Old Query Logs

Clean logs older than 30 days (default):
```bash
php artisan logs:clean-queries
```

Clean logs older than 7 days:
```bash
php artisan logs:clean-queries --days=7
```

## Testing

### Test Route (Development Only)

A test route is available at `/test-query-log` that:
1. Executes sample queries
2. Returns information about log file creation
3. Helps verify the logging system is working

**Important**: Remove `routes/test-query-log.php` in production!

### Manual Testing

1. Enable logging: `DB_QUERY_LOG=true`
2. Make some database queries in your application
3. Check for log file: `storage/logs/query-{today's date}.log`
4. View logs: `php artisan logs:view-queries`

## Production Considerations

### Performance Impact

- Query logging adds minimal overhead
- Each query logs ~200-500 bytes
- Consider disk space for high-traffic applications

### Security

- Log files may contain sensitive data in query bindings
- Ensure proper file permissions (644 or 664)
- Consider log rotation and secure deletion

### Disk Space Management

- Set up automated cleanup with cron jobs
- Monitor disk usage in `/storage/logs/`
- Consider compressing old log files

### Recommended Cron Job

Add to your crontab to clean logs weekly:

```bash
# Clean query logs older than 30 days every Sunday at 2 AM
0 2 * * 0 cd /path/to/your/laravel/app && php artisan logs:clean-queries --days=30
```

## Backup Strategy

### Daily Backup Script Example

```bash
#!/bin/bash
# Backup yesterday's query log
YESTERDAY=$(date -d "yesterday" +%Y-%m-%d)
LOG_FILE="/path/to/storage/logs/query-${YESTERDAY}.log"

if [ -f "$LOG_FILE" ]; then
    # Compress and move to backup location
    gzip "$LOG_FILE"
    mv "${LOG_FILE}.gz" "/backup/location/query-logs/"
    echo "Backed up query log for $YESTERDAY"
fi
```

## Data Restoration Features

### Restoration Data Structure

Each logged operation includes detailed restoration information:

- **Operation Context**: User, IP, timestamp, request ID
- **Formatted SQL**: Human-readable query with actual values
- **Affected Data**: Specific guidance for restoration
- **Risk Assessment**: Automatic risk level classification

### Restoration Process

1. **Analyze the logs**:
   ```bash
   php artisan logs:analyze-queries --date=2025-06-23
   ```

2. **Generate restoration plan**:
   ```bash
   php artisan logs:generate-restoration-plan --date=2025-06-23
   ```

3. **Review the generated SQL file** in `storage/logs/restoration-plan-{date}.sql`

4. **Execute restoration queries** (after careful review)

### Risk Levels

- **LOW**: INSERT operations (can be easily reversed with DELETE)
- **MEDIUM**: UPDATE operations (requires original values)
- **HIGH**: DELETE operations (requires backup data)

### Example Restoration Plan Output

```sql
-- Database Restoration Plan for 2025-06-23
-- Generated at: 2025-06-23 15:30:45
-- IMPORTANT: Review all queries before execution!

-- ========================================
-- Restoration #1
-- Type: DELETE_TO_RESTORE_INSERT
-- Risk Level: LOW
-- Description: Delete records that were inserted at 2025-06-23 14:30:45 by <EMAIL>
-- Note: This will remove the inserted data
-- Original Operation: INSERT INTO users (name, email) VALUES ('John Doe', '<EMAIL>')
-- ========================================

DELETE FROM users WHERE name = 'John Doe' AND email = '<EMAIL>';
```

## Troubleshooting

### Log Files Not Created

1. Check `DB_QUERY_LOG=true` in `.env`
2. Verify `storage/logs/` directory is writable
3. Check Laravel cache: `php artisan config:clear`

### Large Log Files

1. Implement log rotation
2. Use the cleanup command regularly
3. Consider filtering sensitive queries

### Performance Issues

1. Disable logging in production if not needed
2. Use asynchronous logging for high-traffic apps
3. Monitor disk I/O

## File Structure

```
storage/logs/
├── query-2025-06-23.log
├── query-2025-06-24.log
├── query-2025-06-25.log
└── ...
```

## Related Files

- `app/Providers/AppServiceProvider.php` - Query listener setup

- `config/logging.php` - Logging configuration
- `app/Console/Commands/CleanQueryLogs.php` - Cleanup command
- `app/Console/Commands/ViewQueryLogs.php` - View command
