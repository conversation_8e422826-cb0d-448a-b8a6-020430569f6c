<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Support\Facades\Auth;
use App\Material;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Services\MaterialAndQuotation\MaterialAndQuotationService;

class MaterialController extends Controller
{
    private $_userId;

    public function list(Request $request){
        $data = $request->only([
            'shop_id'
        ]);
        $shopId = isset($data['shop_id']) ? $data['shop_id'] : '';
        if($shopId == ''){
            $this->_userId = Auth::check() ? Auth::user()->id : null;
            $user = User::where('id',$this->_userId)->with('shop')->first();
            if($user){
                if(isset($user->shop[0])){
                    $shopId = $user->shop[0]->id;
                };
            };
        }
        $result = [];
        if($shopId){
            $result = Material::where('shop_id', $shopId)->get();
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------importHistory from quotation---------------
    public function importHistory(Request $request)
    {
        $data = $request->only([
            'material_quotations'
        ]);
        $shopId = '';
        if($shopId == ''){
            $this->_userId = Auth::check() ? Auth::user()->id : null;
            $user = User::where('id',$this->_userId)->with('shop')->first();
            if($user){
                if(isset($user->shop[0])){
                    $shopId = $user->shop[0]->id;
                };
            };
        }
        $data['material_quotations'] = isset($data['material_quotations']) ? $data['material_quotations'] : [];
        $data['notes'] = isset($data['notes']) ? $data['notes'] : '';
        $responseStatus = JsonResponse::HTTP_OK;
        $result = NULL;

        $MaterialAndQuotationService = new MaterialAndQuotationService();
        $result = $MaterialAndQuotationService->materialImportHistory($data['material_quotations'], $shopId);
        $responseStatus = ($result['status'] == true ? JsonResponse::HTTP_OK : JsonResponse::HTTP_BAD_REQUEST);
        $response = [
            'status' => $responseStatus,
            'body' => [
                'data' => $result['message'],
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
}