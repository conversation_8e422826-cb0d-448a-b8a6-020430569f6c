<?php
namespace App\Services\Option;

use App\Services\Address\AddressClientService;
use App\PropertyType;
use App\Setting;
use App\Option;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;

class OptionService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    // ------------process listing Option-----------------
    public function list($all)
    {
        // $general = new GeneralService($this->_userId);
        if($all == true){
            $result = Option::orderBy('created_at','asc')->get();
        }
        else {
            $result = Option::where('enable', true)->orderBy('created_at','asc')->get();
        }

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //-------------process detail Option--------------

    public function detail($id)
    {
        // $general = new GeneralService($this->_userId);
        $result = Option::find($id);

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    // ------------process add Option-----------------
    public function create(array $optionInfo)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $optionInfo['created_by'] = $user->id;
        }
        // $log = $optionInfo;

        $result = Option::create($optionInfo);

        //--------add into log-----------
        // $general = new GeneralService;
        // $general->addLog(
        //     Config::get('constants.log_action.create',1),
        //     $result->id,
        //     Config::get('constants.object_type.estimate_option', 34),
        //     $result,
        //     json_encode($log));

        return $result;
    }

    //--------------process update Option---------------
    public function update(array $optionInfo)
    {
        $result = Option::find($optionInfo['id']);

        if(!$result)
        {
            return false;
        }

        $result->update($optionInfo);

        // $general = new GeneralService;

        // $general->addLog(
        //     Config::get('constants.log_action.update', 2),
        //     $result->id,
        //     Config::get('constants.object_type.single_page', 35),
        //     $result,
        //     json_encode($Option)
        // );

        $result = $this->detail($result->id);
        return $result;
    }

    //--------------process remove Option---------------
    public function remove(array $optionInfo)
    {

        $result = Option::find($optionInfo['id']);

        if(!$result){
            return false;
        }

        $result->enable = false;
        $this->update($result);

        //------add into log--------------
        // $general = new GeneralService($this->_userId);
        // $general->addLog(
        //     Config::get('constants.log_action.delete', 3),
        //     $Option['id'],
        //     Config::get('constants.object_type.single_page', 35),
        //     $result,
        //     json_encode($Option));

        return $this->detail($result->id);
    }

    //--------------process delete Option---------------
    public function delete(array $optionInfo)
    {

        $result = Option::find($optionInfo['id']);

        if(!$result){
            return false;
        }

        return $result->delete();

        //------add into log--------------
        // $general = new GeneralService($this->_userId);
        // $general->addLog(
        //     Config::get('constants.log_action.delete', 3),
        //     $Option['id'],
        //     Config::get('constants.object_type.single_page', 35),
        //     $result,
        //     json_encode($Option));

        return $this->detail($result->id);
    }

    


}
