# NuxtJS Stock Management Integration Prompt

## Project Overview
Integrate the new Stock Management API into an existing NuxtJS food shop application. The API provides inventory management functionality including stock import (purchase), export (sale), waste tracking, and reporting.

## API Endpoints Available
- `POST /api/v1/stock/import` - Import stock (purchase)
- `POST /api/v1/stock/export` - Export stock (sale) 
- `POST /api/v1/stock/waste` - Record waste/discard
- `GET /api/v1/stock/history/{product_id}` - Get stock history
- `POST /api/v1/stock/daily-summary` - Get daily summary
- `GET /api/v1/stock/my-shops-summary` - Get user's shops summary

## Requirements

### 1. API Integration Layer
Create a composable or service to handle all stock management API calls:

```javascript
// composables/useStockApi.js
export const useStockApi = () => {
  const { $fetch } = useNuxtApp()
  const { token } = useAuth() // Assuming existing auth composable
  
  const stockImport = async (data) => {
    return await $fetch('/api/v1/stock/import', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token.value}`,
        'Content-Type': 'application/json'
      },
      body: data
    })
  }
  
  // Add other methods...
  
  return {
    stockImport,
    stockExport,
    stockWaste,
    getStockHistory,
    getDailySummary,
    getMyShopsSummary
  }
}
```

### 2. UI Components Needed

#### Stock Import Form Component
- Product selection dropdown
- Quantity input (number)
- Unit selection (kg, pieces, liters, etc.)
- Unit price input
- Notes textarea
- Submit button with loading state

#### Stock Export Form Component  
- Product selection dropdown
- Quantity input with stock validation
- Order ID input (optional)
- Unit and price inputs
- Notes textarea

#### Stock Waste Form Component
- Product selection dropdown
- Quantity input with stock validation
- Unit selection
- Reason textarea (required)

#### Stock History Table Component
- Product information display
- Filterable/sortable table with columns:
  - Date/Time
  - Operation (Import/Export/Waste)
  - Quantity & Unit
  - Price (if applicable)
  - Executor name
  - Notes/Reason
- Pagination support

#### Daily Summary Dashboard
- Date picker for report selection
- Summary cards showing:
  - Total imports (quantity & value)
  - Total exports (quantity & value) 
  - Total waste (quantity)
  - Net stock change
- Charts/graphs for visual representation

### 3. Pages to Create/Update

#### `/pages/inventory/index.vue` - Main Inventory Dashboard
- Overview of all products with current stock levels
- Quick action buttons (Import/Export/Waste)
- Recent stock movements
- Low stock alerts

#### `/pages/inventory/import.vue` - Stock Import Page
- Bulk import functionality
- Single product import
- Import history

#### `/pages/inventory/export.vue` - Stock Export Page
- Sales recording
- Order-based exports
- Export history

#### `/pages/inventory/waste.vue` - Waste Management Page
- Waste recording form
- Waste categories/reasons
- Waste history and analytics

#### `/pages/inventory/reports.vue` - Reports & Analytics
- Daily/weekly/monthly summaries
- Stock movement trends
- Export reports to PDF/Excel

#### `/pages/inventory/history/[productId].vue` - Product Stock History
- Detailed history for specific product
- Timeline view of all movements
- Stock level charts

### 4. State Management (Pinia Store)

```javascript
// stores/stock.js
export const useStockStore = defineStore('stock', () => {
  const products = ref([])
  const stockHistory = ref([])
  const dailySummary = ref(null)
  const loading = ref(false)
  
  const fetchProducts = async () => {
    // Fetch products with current stock levels
  }
  
  const importStock = async (data) => {
    // Handle stock import and update local state
  }
  
  const exportStock = async (data) => {
    // Handle stock export and update local state
  }
  
  const recordWaste = async (data) => {
    // Handle waste recording and update local state
  }
  
  return {
    products,
    stockHistory,
    dailySummary,
    loading,
    fetchProducts,
    importStock,
    exportStock,
    recordWaste
  }
})
```

### 5. Form Validation
Implement client-side validation using VeeValidate or similar:
- Required fields validation
- Numeric validation for quantities and prices
- Stock availability validation for exports/waste
- UUID validation for product/order IDs

### 6. Error Handling & User Feedback
- Toast notifications for success/error messages
- Loading states for all API calls
- Proper error message display
- Confirmation dialogs for destructive actions

### 7. Permissions & Security
- Role-based access control (shop owners, agents, admin)
- Route guards for inventory pages
- API permission validation
- Secure token handling

### 8. Real-time Updates (Optional)
- WebSocket integration for real-time stock updates
- Auto-refresh of stock levels
- Live notifications for low stock

### 9. Mobile Responsiveness
- Mobile-friendly forms
- Touch-optimized interfaces
- Responsive tables/charts
- Progressive Web App features

### 10. Performance Optimization
- Lazy loading of components
- API response caching
- Pagination for large datasets
- Image optimization for product photos

## Implementation Steps

1. **Setup API Integration**
   - Create composables for API calls
   - Configure axios/fetch interceptors
   - Handle authentication headers

2. **Create Base Components**
   - Form components with validation
   - Table components with sorting/filtering
   - Modal/dialog components

3. **Implement Core Pages**
   - Start with main inventory dashboard
   - Add import/export/waste pages
   - Create history and reports pages

4. **Add State Management**
   - Setup Pinia stores
   - Implement reactive state updates
   - Handle optimistic updates

5. **Enhance UX**
   - Add loading states and animations
   - Implement error handling
   - Add confirmation dialogs

6. **Testing & Optimization**
   - Unit tests for components
   - Integration tests for API calls
   - Performance optimization

## Technical Considerations

- **Authentication**: Ensure JWT tokens are properly managed and refreshed
- **Validation**: Both client-side and server-side validation
- **Caching**: Implement appropriate caching strategies
- **Offline Support**: Consider offline capabilities for mobile users
- **Accessibility**: Ensure WCAG compliance
- **Internationalization**: Support multiple languages if needed

## Expected Deliverables

1. Complete inventory management interface
2. All CRUD operations for stock management
3. Comprehensive reporting dashboard
4. Mobile-responsive design
5. Proper error handling and user feedback
6. Documentation for components and usage
7. Unit tests for critical functionality

This integration should provide a complete inventory management solution within the existing NuxtJS food shop application, following modern Vue.js/Nuxt.js best practices and providing an excellent user experience.
