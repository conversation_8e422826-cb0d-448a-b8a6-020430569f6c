<?php

namespace App\Http\Requests\ShopDeliveryPartner;

use App\Http\Requests\BaseRequest;

class ShopDeliveryPartnerRequest extends BaseRequest
{
    function rules()
    {
        return [
            'shop_id' => 'required|uuid|exists:shops,id',
            'delivery_partner_id' => 'required|uuid|exists:delivery_partners,id',
            'is_enabled' => 'nullable|boolean',
            'connect_data' => 'nullable|array',
            'connect_data.name'  => 'required_with:connect_data|string',
            'connect_data.phone'      => 'required_with:connect_data|string|min:10',
            'connect_data.address'    => 'required_with:connect_data|string',
            'connect_data.email'      => 'required_with:connect_data|email',
            'is_default' => 'nullable|boolean|in:1',
        ];
    }
}
