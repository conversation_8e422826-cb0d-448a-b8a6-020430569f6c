<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Message extends Model
{

    protected $table = 'messages';

    protected $fillable = [
        'channel_id',
        'member_id',
        'member_type',
        'content',
        'status'
    ];
    protected $casts = [
        'content' => 'array',
    ];
    protected $primaryKey = 'id';
    public $incrementing  = false;

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }

    public function channel()
    {
        return $this->belongsTo(Channel::class);
    }

    // <PERSON><PERSON> báo mối quan hệ với Member (users, shops)
    public function member()
    {
        return $this->morphTo();
    }

    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function getMemberType()
    {
        switch ($this->member_type) {
            case 1:
                return User::class;
            case 2:
                return Shop::class;
            default:
                return null;
        }
    }
}
