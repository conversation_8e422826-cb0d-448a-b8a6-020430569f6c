<?php

namespace App\Http\Requests\SinglePage;

use App\Http\Requests\BaseRequest;

class SinglePageRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'title' => 'required|max:255',
            'slug' => 'required|max:255',
            'content' => 'required',
        ];
    }


    public function messages()
    {

        return [

            'title.required' => 'SinglePage_001_E_001',
            'title.max'  => 'SinglePage_004_E_002',

            'content.required' => 'SinglePage_001_E_003',
            'content.max' => 'SinglePage_001_E_004',

            'slug.required'  => 'SinglePage_001_E_005',
            'slug.max' => 'SinglePage_001_E_006',

        ];
    }
}
