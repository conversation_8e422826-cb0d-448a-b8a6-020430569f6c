<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UpdateImageDriver extends BaseRequest
{

    public function rules()
    {
        return [
            // 'id'    => 'required|uuid|exists:users,id',
            'file'  => 'required|base64image|base64size',
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'id.required'       => 'User_001_E_009',
    //         'id.uuid'           => 'User_002_E_010',
    //         'id.exists'         => 'User_003_E_011',

    //         'file.required'     => 'User_001_E_042',
    //         'file.base64image'  => 'User_002_E_043',
    //         'file.base64size'   => 'User_004_E_044',
    //     ];
    // }
}
