<?php

namespace App\Http\Requests\Image;

use App\Http\Requests\BaseRequest;

class ImageCheckIdRequest extends BaseRequest
{
    
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:images,id'
        ];
    }
    // public function messages()
    // {
    //     return [
    //         'id.required' => 'Image_001_E_011',
    //         'id.uuid' => 'Image_002_E_012',
    //         'id.exists' => 'Image_003_E_013',
    //     ];
    // }
}
