<?php

namespace App\Services\DeliveryPartner;

use App\Mail\DeliveryErrorMail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class DeliveryErrorService
{
    /**
     * Log delivery error and send email notification to admin
     *
     * @param string $errorType
     * @param string $errorMessage
     * @param string $deliveryPartner
     * @param string $orderId
     * @param array $context
     * @param \Exception|null $exception
     * @return void
     */
    public function logAndNotify($errorType, $errorMessage, $deliveryPartner, $orderId, $context = [], $exception = null)
    {
        try {
            // Prepare context data
            $logContext = array_merge($context, [
                'delivery_partner' => $deliveryPartner,
                'order_id' => $orderId,
                'error_type' => $errorType,
                'timestamp' => now()->toDateTimeString(),
            ]);

            // Add exception details if provided
            if ($exception) {
                $logContext['exception'] = [
                    'message' => $exception->getMessage(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine(),
                    'trace' => $exception->getTraceAsString(),
                ];
            }

            // Log to delivery channel
            Log::channel('delivery')->error($errorMessage, $logContext);

            // Send email notification to admin
            $this->sendEmailNotification($errorType, $errorMessage, $deliveryPartner, $orderId, $logContext);

        } catch (\Exception $e) {
            // Fallback logging if delivery logging fails
            Log::error("DeliveryErrorService failed: " . $e->getMessage(), [
                'original_error' => $errorMessage,
                'delivery_partner' => $deliveryPartner,
                'order_id' => $orderId,
            ]);
        }
    }

    /**
     * Send email notification to admin
     *
     * @param string $errorType
     * @param string $errorMessage
     * @param string $deliveryPartner
     * @param string $orderId
     * @param array $context
     * @return void
     */
    private function sendEmailNotification($errorType, $errorMessage, $deliveryPartner, $orderId, $context = [])
    {
        try {
            $adminEmail = env('ADMIN_EMAIL');
            
            if (!$adminEmail) {
                Log::channel('delivery')->warning('ADMIN_EMAIL not configured, skipping email notification');
                return;
            }

            Mail::to($adminEmail)->send(new DeliveryErrorMail(
                $errorType,
                $errorMessage,
                $deliveryPartner,
                $orderId,
                $context
            ));

            Log::channel('delivery')->info("Delivery error email sent successfully", [
                'error_type' => $errorType,
                'delivery_partner' => $deliveryPartner,
                'order_id' => $orderId,
                'admin_email' => $adminEmail,
            ]);

        } catch (\Exception $e) {
            Log::channel('delivery')->error("Failed to send delivery error email: " . $e->getMessage(), [
                'error_type' => $errorType,
                'delivery_partner' => $deliveryPartner,
                'order_id' => $orderId,
            ]);
        }
    }

    /**
     * Log delivery API request/response for debugging
     *
     * @param string $deliveryPartner
     * @param string $orderId
     * @param string $endpoint
     * @param array $request
     * @param mixed $response
     * @param string $method
     * @return void
     */
    public function logApiCall($deliveryPartner, $orderId, $endpoint, $request = [], $response = null, $method = 'POST')
    {
        try {
            Log::channel('delivery')->info("Delivery API Call", [
                'delivery_partner' => $deliveryPartner,
                'order_id' => $orderId,
                'endpoint' => $endpoint,
                'method' => $method,
                'request' => $request,
                'response' => $response,
                'timestamp' => now()->toDateTimeString(),
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to log delivery API call: " . $e->getMessage());
        }
    }

    /**
     * Log successful delivery creation
     *
     * @param string $deliveryPartner
     * @param string $orderId
     * @param string $deliveryId
     * @param array $context
     * @return void
     */
    public function logSuccess($deliveryPartner, $orderId, $deliveryId, $context = [])
    {
        try {
            Log::channel('delivery')->info("Delivery created successfully", array_merge($context, [
                'delivery_partner' => $deliveryPartner,
                'order_id' => $orderId,
                'delivery_id' => $deliveryId,
                'timestamp' => now()->toDateTimeString(),
            ]));
        } catch (\Exception $e) {
            Log::error("Failed to log delivery success: " . $e->getMessage());
        }
    }
}
