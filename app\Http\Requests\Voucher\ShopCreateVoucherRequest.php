<?php

namespace App\Http\Requests\Voucher;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class ShopCreateVoucherRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'shop_id' => 'bail|required|exists:shops,id',
            'name' => [
                'required',
                'string',
                'max:10',
                Rule::unique('vouchers')->where(function ($query) {
                    return $query->where('shop_id', $this->shop_id);
                }),
            ],
            'type' => 'required|in:percent,fixed',
            'value' => 'required|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'valid_from' => 'nullable|date',
            'valid_until' => 'nullable|date|after:valid_from',
            'is_active' => 'boolean',
        ];
    }
}
