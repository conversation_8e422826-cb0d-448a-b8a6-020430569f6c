<?php

namespace App\Http\Controllers\Api\v1\Client;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use App\Http\Requests\User\CustomerLoginRequest;
use App\Http\Requests\User\UpdateImageDriver;
use App\Http\Requests\User\LoginZalo;
use Socialite;
use App\User;
use App\Services\Auth\RegisterService;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{

}
