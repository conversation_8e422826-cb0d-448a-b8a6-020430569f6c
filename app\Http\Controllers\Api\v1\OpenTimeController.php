<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OpenTimeController extends Controller
{
    // -------create open_time---------------
    public function create(OpenTimeRequest $request)
    {
        $data = $request->only([
            'object_id',
            'object_type',
            'from',
            'to'
        ]);

        $service = new OpenTimeService(Auth::user()->id);
        $result = $service->create($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list open_time-----------------
    public function list($all = false, OpenTimeService $service)
    {
        $result = $service->list($all);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------detail open_time-------------
    public function detail($id = null, OpenTimeService $service)
    {
        $result = $service->detail($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update open_time-----------
    public function update(OpenTimeUpdateRequest $request, OpenTimeService $service)
    {
        $data = $request->only([
            'id',
            'object_id',
            'object_type',
            'from',
            'to'
        ]);

        $result = $service->update($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------remove open_time----------------
    public function remove(OpenTimeCheckIdRequest $request, OpenTimeService $service)
    {
        $data = $request->only(['id']);

        $result = $service->remove($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------delete open_time--------------
    public function delete(OpenTimeCheckIdRequest $request, OpenTimeService $service)
    {
       $data = $request->only(['id']);

       $result = $service->delete($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }
}
