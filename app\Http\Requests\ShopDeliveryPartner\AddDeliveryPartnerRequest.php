<?php

namespace App\Http\Requests\ShopDeliveryPartner;

use App\Http\Requests\BaseRequest;

class AddDeliveryPartnerRequest extends BaseRequest
{
    function rules(): array
    {
        return [
          'shop_id' => 'required|uuid|exists:shops,id',
          'partner' => 'required|string|in:ahamove,j&t,remagan',
          'connect_data'            => 'required|array',
          'connect_data.name'  => 'required|string',
          'connect_data.phone'      => 'required|string|min:10',
          'connect_data.address'    => 'required|string',
          'connect_data.email'      => 'required|email',
        ];
    }
}
