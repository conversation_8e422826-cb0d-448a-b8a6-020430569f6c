<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTableOrders extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->enum('status', [1,2,3,4,5,6])->default(1);
            $table->text('notes')->nullable();
            $table->string('address', 500);
            $table->string('short_code', 191);
            $table->integer('province_id')->nullable();
            $table->integer('district_id')->nullable();
            $table->integer('ward_id')->nullable();
            $table->uuid('customer_id')->nullable();
            $table->string('customer_name', 191);
            $table->string('customer_phone', 191);
            $table->decimal('price', 19, 2);
            $table->decimal('price_off', 19, 2)->nullable();
            $table->boolean('delivery_type')->default(true);
            $table->decimal('delivery_price', 19, 2)->default(0);
            $table->enum('payment_method', [1,2,3,4,5])->default(1);
            $table->uuid('shop_id')->nullable();
            $table->timestamps();

            $table->index('short_code');
            $table->index('customer_phone');
            $table->index('shop_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
}
