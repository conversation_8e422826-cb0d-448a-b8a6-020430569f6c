<?php
namespace App\Console\Commands;

use App\Notifications\NotificationUser;
use App\User;
use Illuminate\Console\Command;
use App\Order;
use App\Shop;
use Illuminate\Support\Facades\DB;
use App\Services\Token\TokenService;
use App\Services\Notification\NotificationService;
use App\Services\Order\OrderService;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
class CheckOrderStatusCommand extends Command
{
    protected $signature = 'command:checkorderstatus';
    protected $description = 'Check for orders with status=1 and notify observation operator';

    protected $orderService;

    public function __construct(OrderService $orderService)
    {
        parent::__construct();
    }

    public function handle()
    {
        $checkList = Order::on('pgsqlReplica')->where('status', 1)
            ->where('created_at', '<', now()->subMinutes(5))
            ->where('created_at', '>', now()->subMinutes(1200))
            ->pluck('shop_id')
            ->countBy(function($shop_id) {
                return $shop_id;
            });
        
        foreach ($checkList as $shop_id => $count) {
            $redisKey = "order_notification_resend_" . $shop_id;
            $resendCount = Redis::incr($redisKey);
            Redis::expire($redisKey, 1800); // 30 minutes TTL

            if ($resendCount > 3) {
                continue; // Skip if maximum resend limit is reached
            }
            if(!$shop_id) continue;
            $shop = Shop::find($shop_id);
            if(!$shop) continue;
            $shop_owner = $shop->user_id ?? null;
            $shop_agent = $shop->agent_id ?? null;
            $messToOwner = [
                'title' => "Bạn có đơn hàng mới",
                'body'  => "Có $count đơn hàng mới chưa được xử lý.",
                // 'image' => $image,
                'url'   => '',
                'target_url' => '/my-shop/orders/',
                'type'  => 'order_new',
            ];
            Log::channel('notify')->info("resend new order Notify of: ".$shop->name."\n");
            echo "resend new order Notify of: ".$shop->name."\n";
            $messToAgent = [
                'title' => $shop->name." có $count đơn hàng mới chưa được xử lý.",
                'body'  => "Có $count đơn hàng mới chưa được xử lý.",
                // 'image' => $image,
                'url'   => '',
                'target_url' => '/agent/shop/'. $shop->slug .'/orders/',
                'type'  => 'agent_order_new',
            ];
            # tự nhận đơn
            $tokenService = new TokenService();
            $tokens = $tokenService->listByUser($shop_owner, 2);

            $notiService = new NotificationService;
            if(count($tokens) > 0){
                $notiService->sendBatchNotification($tokens, $messToOwner);
            }
            if ($shop_owner == $shop_agent) {
                continue;
            }
            $agentTokens = $tokenService->listByUser($shop_agent, 2);            
            if(count($agentTokens) > 0){
                $notiService->sendBatchNotification($agentTokens, $messToAgent);
            }

            # nhận thông báo và lưu vào table notifications
            $shop_owner_obj = User::find($shop_owner);
            if($shop_owner_obj){
                $shop_owner_obj->notify(new NotificationUser($shop_owner, $messToOwner));
            }
            
            $shop_agent_obj = User::find($shop_agent);
            if($shop_agent_obj){
                $shop_agent_obj->notify(new NotificationUser($shop_agent, $messToAgent));
            }
        }
    }
}
