<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Category;
use App\OrderItem;
use App\Product;
use App\User;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Material extends Model
{
    protected $primaryKey = 'id';
    public $incrementing = false;

    protected $fillable = [
        'name', 'name_en', 'packing', 'brand', 'origin', 'unit', 'barcode', 'shop_id'
        // Add other fillable columns
    ];
    protected $table = 'materials';
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
        
        static::deleting(function ($model) {
            $model->materialQuotations()->detach();
        });
    }
    protected $hidden = ['updated_at', 'created_at'];
    // public $timestamps = false;
    // protected $keyType = 'string';
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'material_categories', 'material_id', 'category_id')->distinct()->with(['translation']);
    }

    public function materialQuotations()
    {
        return $this->hasMany(MaterialQuotation::class)->with('quotation')->orderBy('price','ASC');
    }
    public function materialQuotationsDESC()
    {
        return $this->hasMany(MaterialQuotation::class)->with('quotation')->orderBy('price','DESC');
    }
    public function materialQuotationsDate()
    {
        return $this->hasMany(MaterialQuotation::class)->with('quotation')->orderBy('created_at','ASC');
    }
    public function materialQuotationsDateDESC()
    {
        return $this->hasMany(MaterialQuotation::class)->with('quotation')->orderBy('created_at','DESC');
    }


    public function importHistories()
    {
        return $this->hasMany(ImportHistory::class);
    }
}