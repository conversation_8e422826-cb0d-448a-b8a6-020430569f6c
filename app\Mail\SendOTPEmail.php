<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Services\HistoryMail\HistoryMailService;
use Illuminate\Support\Facades\Log;

class SendOTPEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $email;
    protected $OTP;
    protected $OTPLifeTime;
    protected $otpType;
    public function __construct($email, $OTP, $OTPLifeTime, $otpType = 'default')
    {
        //
        $this->email = $email;
        $this->OTP = $OTP;
        $this->OTPLifeTime = $OTPLifeTime;
        $this->otpType = $otpType;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {

        $historyMail = null;
        try {
            $header = $title = $footer = '';
            if($this->otpType == 'reset_password'){
                $header = __('messages.otp.email.reset_password.header');
                $title = __('messages.otp.email.reset_password.title');
                $footer = __('messages.otp.email.reset_password.footer', ['otp_life_time' => $this->OTPLifeTime/60]);
            }else{
                $header = __('messages.otp.email.default.header');
                $title = __('messages.otp.email.default.title');
                $footer = __('messages.otp.email.default.footer', ['otp_life_time' => $this->OTPLifeTime/60]);
            }

            return $this->subject($this->OTP . ' là mã xác minh của bạn')
            ->view('emails.otp_mail')
            ->with([
                'email'=> $this->email,
                'OTP'   => $this->OTP,
                'header'   => $header,
                'title'   => $title,
                'footer'   => $footer,
            ]);


        } catch (\Exception $e) {
            Log::info("Send OTp Mail Error: ".$e."\n".$this->email);
        }

    }
}
