<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddViewsLikesFollowsToProducts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->integer('views')->default(0)->index();
            $table->integer('likes')->default(0)->index();
            $table->integer('follows')->default(0)->index();
            $table->decimal('ratings', 2, 1)->default(0.0)->comment('Shop rating from 0.0 to 5.0');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('views');
            $table->dropColumn('likes');
            $table->dropColumn('follows');
            $table->dropColumn('ratings');
        });
    }
}
