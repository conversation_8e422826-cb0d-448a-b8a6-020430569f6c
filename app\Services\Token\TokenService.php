<?php
namespace App\Services\Token;

use App\Token;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TokenService
{
    public static function insert(array $data)
    {
        $data['token_expired'] = Carbon::now()->addMinutes(env('JWT_TTL'))->toDateTimeString();
        // $data['address_ip'] = request()->ip();

        Token::create($data);
        return true;
    }

    public static function insertOrUpdate(array $data)
    {
        $expirationTime = Carbon::now()->addMinutes(env('JWT_TTL'));

        $token = Token::updateOrCreate(
            [
                'token' => $data['token'],
                'token_type' => $data['token_type'],
            ],
            [
                'user_id' => $data['user_id'],
                'device' => $data['device'],
                'token' => $data['token'],
                'token_expired' => $expirationTime,
                'updated_at' => now(),
            ]
        );

        return $token;
    }

    public function list()
    {
        $result = Token::orderBy('created_at', 'desc')->get();

        return $result;
    }

    public function listByUser($userId, $token_type = 1)
    {
        if($userId){
            $result = Token::select('token')
            ->where('user_id', $userId)
            ->where('token_type',$token_type)
            // ->where('token_expired','>=',now())
            ->pluck('token')
            ->toArray();
        }
        else {
            $result = [];
        }
        return $result;
    }



    public static function delete(array $data)
    {
        Token::where([
            ['user_id', $data['user_id']],
            ['token', $data['token']]
            ])->delete();
        return true;
    }

    public static function deleteList(array $data)
    {

        $check = User::find($data['id']);

        if($check)
        {
            $token = Token::where('user_id', $data['id'])->get('id');
            foreach ($token as $key => $value) {
                Token::where('id',$value['id'])->delete();
            }
            return true;
        }

        return false;
    }

    public static function deleteToken(array $data)
    {

        $check = Token::where('token',$data['token'])->first();

        if($check)
        {
            $check->delete();
            return true;
        }

        return false;
    }

    /**
     * Remove all token in list tokens in account except token request.
     * @param  mixed  $userId
     * @param  mixed  $tokenType
     * @param  mixed  $excludedToken  Token that should not be deleted
     * @return bool
     */
    static function deleteAllTokens($userId, $tokenType, $excludedToken = null){
        try {
            $query = Token::where('user_id', $userId)
                ->where('token_type', $tokenType);


            if (!is_null($excludedToken)) {
                $query->where('token', '!=', $excludedToken);
            }

            $result = $query->delete();
            return $result > 0;
        } catch (\Exception $e) {
            Log::error("Failed to delete tokens for user $userId: " . $e->getMessage());
            return false;
        }
    }


    /**
     * Remove many devices in list devices login in account.
     *
     * @param  mixed  $tokenIds  List ID token need remove
     * @param  mixed  $userId
     * @param  mixed  $tokenType
     * @return bool
     */
    static function deleteManyTokens($tokenIds, $userId, $tokenType){
        try {
            $result = Token::where('user_id', $userId)
                ->where('token_type', $tokenType)
                ->whereIn('id', $tokenIds)
                ->delete();
            return $result > 0;
        } catch (\Exception $e) {
            Log::error("Failed to delete tokens for user $userId: " . $e->getMessage());
            return false;
        }
    }
}
