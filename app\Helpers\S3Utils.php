<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class S3Utils
{
    /**
     * Upload file base_64 to s3
     *
     * @param [string] $file     : file content
     * @param [string] $filePath : url is S3_HOST_PATH + $filePath.
     * @return void
     */
    public static function upload($file, $filePath)
    {
        $s3 = Storage::disk(env('STORAGE_SERVICE', 'minio'));
        $uploadS3 = $s3->put($filePath, $file, 'public');
        return $uploadS3;
    }
    //----upload file-----------------
    public static function uploadFile($file, $filePath,$name)
    {
        $s3 = Storage::disk(env('STORAGE_SERVICE', 'minio'));
        $uploadS3 = $s3->putFileAs($filePath, $file, $name);
        return $uploadS3;
    }

    //----upload file-----------------
    public static function uploadFileCustom($file, $filePath,$name, $drive = 'public')
    {
        $s3 = Storage::disk($drive);
        $uploadS3 = $s3->putFileAs($filePath, $file, $name);
        return $uploadS3;
    }

    /**
     * Delete file from s3
     *
     * @param [string] $filePath : url is S3_HOST_PATH + $filePath.
     * @return void
     */
    public static function deleteIfExist($filePath)
    {
        if (!is_null($filePath)) {
            $pos = strpos($filePath, env('URL_IMAGE'));
            if($pos !== false)
            {
                list($blank, $result) = explode(env('URL_IMAGE'), $filePath);
                // $s3 = Storage::disk(env('STORAGE_SERVICE', 'minio'));
                if (Storage::disk(env('STORAGE_SERVICE', 'minio'))->exists($result)) {
                    return Storage::disk(env('STORAGE_SERVICE', 'minio'))->delete($result);

                }
            }
        }
        return false;
    }
    public static function fileExist($filePath)
    {
        if (!is_null($filePath)) {
            return Storage::disk(env('STORAGE_SERVICE', 'minio'))->exists($filePath);
        }
    }
    public static function deleteFile($filePath)
    {
        if (!is_null($filePath)) {
            Storage::disk(env('STORAGE_SERVICE', 'minio'))->delete($filePath);
        }
    }

    public static function getFile($filePath)
    {
        if (!is_null($filePath)) {
            if (Storage::disk(env('STORAGE_SERVICE', 'minio'))->exists($filePath)) {
                return Storage::disk(env('STORAGE_SERVICE', 'minio'))->get($filePath);
            }
        }
    }
    /**
     * Delete file from s3
     *
     * @param [string] $filePath : url is S3_HOST_PATH + $filePath.
     * @return bool
     */
    public static function deleteFileFromS3($filePath)
    {
        if (!is_null($filePath)) {
            $storage = Storage::disk(env('STORAGE_SERVICE', 'minio'));
            if ($storage->exists($filePath)) {
                Log::info('File exists, process to <<<DELETE>>> file::: ' . $filePath);
                return $storage->delete($filePath);
            } else {
                Log::info('Not found file to delete:: ' . $filePath);
                return false;
            }
        }
        return false;
    }

    public static function url($filePath)
    {
        return Storage::disk(env('STORAGE_SERVICE', 'minio'))->url($filePath);
    }
}
