<?php

namespace App\Http\Controllers;

use App\Http\Requests\PropertyRequest;
use App\Property;
use App\Province;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PropertyController extends Controller
{
    //
    public function propertyInsert(Request $request)
    {
       $rule = new PropertyRequest();
        $validator = Validator::make($request->all(),$rule->rules());
        if($validator->fails())
        {
            return response()->json($validator->errors(), 404);
        }
        $property = Property::create($request->all());
        return response()->json($property,201);
    }
    public function propertyAdd($id)
    {
        $provinces = Province::find($id);
        return response()->json($provinces,200);
    }
}
