<?php

use App\Shop;
use Illuminate\Database\Seeder;

class DefaultSettingShop extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $settingDefault = [
            'general' => [
                'is_open' => [
                    'value' => true,
                ],
                'pending_time' => [
                    'value' => NULL,
                    'unit' => 'minutes',
                    'enabled' => false,
                ],
                'waiting_time_for_delivery' => [
                    'value' => NULL,
                    'unit' => 'minutes',
                    'enabled' => false,
                ],
            ],
            'order' => [
                'order_online' => [
                    'value' => true,
                ],
                'delivery' => [
                ],
                'delivery_default_message' => [
                    'vi' => 'Thông báo phí vận chuyển sau khi đặt hàng',
                    'en' => 'Shipping fee provided after order placement',
                ],
            ],
        ];

        Shop::orderBy('created_at', 'desc')->chunk(100, function ($shops) use ($settingDefault) {
            foreach ($shops as $shop) {
                $settingCurrent = $shop->settings ?? [];
                $setting = array_replace_recursive($settingDefault, $settingCurrent);
                $shop->settings = $setting;
                $shop->save();
            }
        });

    }
}
