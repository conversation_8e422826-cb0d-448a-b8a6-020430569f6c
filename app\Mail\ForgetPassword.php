<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Carbon\Carbon;
use App\Services\HistoryMail\HistoryMailService;
use App\Services\ConfirmCode\ConfirmCodeService;
use Illuminate\Support\Facades\Log;

class ForgetPassword extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $_result;
    protected $email;
    protected $code;
    public function __construct($result,$email,$code)
    {
        $this->_result = $result;
        $this->email = $email;
        $this->code = $code;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $historyMail = null;
        try {
            $historyMail = HistoryMailService::insert([
                'title' => 'Xác nhận đặt lại mật khẩu',
                'content' => json_encode(['name'=> $this->_result->name,'id'=> $this->_result->id,'email' => $this->email, 'code' => $this->code]),
                'status' => config('constants.status_history_mail.success')
            ]);

            $confirmCode = ConfirmCodeService::insert([
                'code' => $this->code,
                'action_type' => config('constants.confirm_code_action.forget_password'),
                'user_id' => $this->_result->id,
                'expire_time' => Carbon::now()->addMinutes(60)->toDateTimeString()
            ]);

            return $this->subject('Xác nhận đặt lại mật khẩu')
            ->view('emails.forget_password')
            ->with(['name'=> $this->_result->name,'id'=> $this->_result->id,'email' => $this->email, 'code' => $this->code]);

        } catch (\Exception $e) {
            Log::info("Mail Error: ".$e);

            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

    }
}
