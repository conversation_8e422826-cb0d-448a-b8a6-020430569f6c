<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UserContactRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'name'      => 'required|max:255',
            'phone'     => 'required|max:255',
            'created_by'=> 'required|uuid|exists:users,id',
            'email'     => 'nullable|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u',
            'content'   => 'nullable|max:5000',
            'id_property'=> 'nullable|max:255',
            'name_property'=> 'nullable|max:255',
            'url_property' => 'nullable|max:5000',
            'knb_id'    => 'nullable|max:255',
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'name.required'     => 'User_001_E_001',

    //         'email.required'    => 'User_001_E_003',
    //         'email.regex'       => 'User_002_E_004',

    //         'created_by.required' => 'User_001_E_009',
    //         'created_by.uuid' => 'User_002_E_010',
    //         'created_by.exists' => 'User_003_E_011',

    //         'phone.required'    => 'User_001_E_024',
    //         'phone.digits_between' => 'User_004_E_025',

    //         'name.max'     => 'User_004_E_070',
    //         'email.max'     => 'User_004_E_071',

    //         'phone.max'         => 'User_004_E_072',
    //         'content.max'       => 'User_004_E_073',
    //         'id_property.max'   => 'User_004_E_074',
    //         'name_property.max' => 'User_004_E_075',
    //         'url_property.max'  => 'User_004_E_076',
    //         'knb_id.max'        => 'User_004_E_077',

    //     ];
    // }
}
