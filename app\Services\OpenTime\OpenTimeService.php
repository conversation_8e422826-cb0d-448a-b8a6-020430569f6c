<?php
namespace App\Services\OpenTime;

use App\OpenTime;
use App\Image;
use App\PlaceType;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use App\Services\GeneralService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\User;

class OpenTimeService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

     //-------process create open_time--------
    public function create(array $open_time)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $open_time['created_by'] = $user->id;
        }

        $result = OpenTime::create($open_time);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.create'),
        //                 $result->id,
        //                 config('constants.object_type.open_time'),
        //                 $result,
        //                 json_encode($open_time));

        return $result;
    }

    //-------process listing open_time--------------------
    public function list($all)
    {
        // $general = new GeneralService($this->_userId);
        if($all == true){
            $result = OpenTime::orderBy('created_at','asc')
                ->with('objects','created_by')
                ->get();
        }
        else {
            $result = OpenTime::with('objects','created_by')
                ->where('enable', true)
                ->orderBy('created_at','asc')->get();
        }

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //------process detail open_time----------
    public function detail($id)
    {
        $result = OpenTime::where('id',$id)
            ->with('objects','created_by')
            ->where('enable', true)
            ->first();

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //-------process update open_time--------
    public function update(array $open_time)
    {
        $result = OpenTime::find($open_time['id']);

        if(!$result)
        {
            return false;
        }

        $result->update($open_time);


        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.open_time'),
        //                 $result,
        //                 json_encode($open_time));

        return $result;

    }

    //-----process remove open_time----------
    public function remove(array $open_time)
    {
        $result = OpenTime::where([
            ['id', $open_time['id']],
            ['enable', true]
        ])->first();

        if(!$result)
        {
            return false;
        }

        $open_time['enable'] = false;
        $this->update($open_time);

        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.open_time'),
        //                 $result,
        //                 json_encode($open_time));

        return $result;
    }

    //-----process delete open_time----------
    public function delete(array $open_time)
    {
        $result = OpenTime::find($open_time['id']);

        if(!$result)
        {
            return false;
        }

        //----------- delete action ---------------
        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.open_time'),
        //                 $result,
        //                 json_encode($open_time));

        return true;
    }
}
