<!DOCTYPE html>
<html>
<head>
    <title>Delivery Error Report</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f4f4f4; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .error-type { background-color: #ff6b6b; color: white; padding: 10px; border-radius: 5px; }
        .delivery-info { background-color: #4ecdc4; color: white; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .details { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .timestamp { color: #666; font-size: 0.9em; }
        .order-id { font-weight: bold; color: #2c3e50; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚚 Delivery Error Report</h1>
        </div>
        
        <div class="content">
            <div class="delivery-info">
                <strong>Delivery Partner:</strong> {{ strtoupper($deliveryPartner) }}
            </div>
            
            <div class="delivery-info">
                <strong>Order ID:</strong> <span class="order-id">{{ $orderId }}</span>
            </div>
            
            <div class="error-type">
                <strong>Error Type:</strong> {{ $errorType }}
            </div>
            
            <div class="details">
                <h3>Error Details:</h3>
                <p><strong>Message:</strong> {{ $errorMessage }}</p>
                <p class="timestamp"><strong>Timestamp:</strong> {{ $timestamp }}</p>
            </div>
            
            @if(!empty($context))
            <div class="details">
                <h3>Context Information:</h3>
                @foreach($context as $key => $value)
                    <p><strong>{{ ucfirst(str_replace('_', ' ', $key)) }}:</strong> 
                        @if(is_array($value) || is_object($value))
                            <pre>{{ json_encode($value, JSON_PRETTY_PRINT) }}</pre>
                        @else
                            {{ $value }}
                        @endif
                    </p>
                @endforeach
            </div>
            @endif
            
            <div class="details">
                <h3>Recommended Actions:</h3>
                <ul>
                    <li>Check the delivery partner API status</li>
                    <li>Verify order details and delivery information</li>
                    <li>Review delivery partner configuration</li>
                    <li>Contact delivery partner support if needed</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
