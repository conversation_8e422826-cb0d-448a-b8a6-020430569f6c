<?php

namespace App\Http\Requests\Setting;

use App\Http\Requests\BaseRequest;

class SettingRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'key' => 'required|max:255|unique:settings,key',
            'value' => 'required',
            'description' => 'nullable|max:255',
            'reference_value'=> 'nullable|max:255',
            'object_type'   => 'nullable|integer',
        ];
    }

    // public function messages()
    // {
    //     return [
            // 'key.required' => 'Setting_001_E_001',
            // 'key.unique' => 'Setting_005_E_002',

            // 'value.required' => 'Setting_001_E_003',

            // 'key.max' => 'Setting_004_E_007',
            // 'value.max' => 'Setting_004_E_008',
            // 'description.max' => 'Setting_004_E_009',
            // 'reference_value.max' => 'Setting_004_E_010',
            // 'object_type.integer' => 'Setting_002_E_011',
    //     ];
    // }
}
