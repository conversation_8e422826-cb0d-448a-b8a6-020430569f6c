<?php

namespace App\Http\Controllers\Api\v1\Client;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\Address\AddressClientService;
use Illuminate\Http\JsonResponse;

class AddressClientController extends Controller
{
    public function List()
    {
        $list = new AddressClientService();
        $input = $list->list();

        $reponse = [
          'status' => JsonResponse::HTTP_OK,
          'body'=> [
              'data' => $input,
          ],
        ];
        return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    /**
     * @OA\Get(
     *      path="/api/v1/address/province/list",
     *      operationId="province_list",
     *      tags={"Address"},
     *      summary="Get list of Province",
     *      security={
     *       {"api_key": {}},
     *     },
     *      @OA\Response(
     *          response=200,
     *          description="Successful",
     *          @OA\JsonContent(ref="#/components/schemas/ProvinceModel")
     *       ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad request"
     *      )
     *     )
     */
    public function province(){
        $list = new AddressClientService();
        $list = $list->getProvinceList();
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $list,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    /**
     * @OA\Get(
     *      path="/api/v1/address/province",
     *      operationId="province_detail",
     *      tags={"Address"},
     *      summary="Get list of district by province id",
     *      security={
     *       {"api_key": {}},
     *     },
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="province id",
     *         required=true,
     *         @OA\Schema(
     *             type="string",
     *         )
     *     ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="id", type="integer"
     *          ),
     *              @OA\Property(
     *                  property="name", type="string"
     *          ),
     *              @OA\Property(
     *                  property="district", type="list",ref="#/components/schemas/DistrictModel"
     *          ),
     *      )
     *       ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad request"
     *      )
     *     )
     */
    public function provinceList(Request $request)
    {
        // if($this->checkIdDetail($request->id) == false)
        // {
        //     $response = [
        //         'status' => JsonResponse::HTTP_BAD_REQUEST,
        //         'body' => 'Province_002_E_004'
        //     ];
        //     return response()->json($response, JsonResponse::HTTP_OK);
        // }
        $address = new AddressClientService();
        $result = $address->getProvince(isset($request->id) ? $request->id : (isset($request->q) ? $request->q : null));
        if($result == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'=> [
                    'Province_003_E_003'
                ],
              ];
              return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
          ];
        return response()->json($reponse,JsonResponse::HTTP_OK);
    }
    /**
     * @OA\Get(
     *      path="/api/v1/address/district",
     *      operationId="distrct_detail",
     *      tags={"Address"},
     *      summary="Get list of ward by district id",
     *      security={
     *       {"api_key": {}},
     *     },
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="district id",
     *         required=true,
     *         @OA\Schema(
     *             type="string",
     *         )
     *     ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="id", type="integer"
     *          ),
     *              @OA\Property(
     *                  property="name", type="string"
     *          ),
     *              @OA\Property(
     *                  property="province_id", type="integer"
     *          ),
     *              @OA\Property(
     *                  property="district", type="list",ref="#/components/schemas/WardModel"
     *          ),
     *      )
     *       ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad request"
     *      )
     *     )
     */
    public function distrctList(Request $request)
    {
        // if($this->checkIdDetail($request->id) == false)
        // {
        //     $response = [
        //         'status' => JsonResponse::HTTP_BAD_REQUEST,
        //         'body' => 'District_002_E_002'
        //     ];
        //     return response()->json($response, JsonResponse::HTTP_OK);
        // }
        $address = new AddressClientService();
        $address = $address->getDistrict($request->id);
        if($address == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'=> [
                    'District_003_E_001'
                ],
              ];
              return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $address,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }

    public function wardList(Request $request)
    {
        // if($this->checkIdDetail($request->id) == false)
        // {
        //     $response = [
        //         'status' => JsonResponse::HTTP_BAD_REQUEST,
        //         'body' => 'Ward_002_E_002'
        //     ];
        //     return response()->json($response, JsonResponse::HTTP_OK);
        // }
        $address = new AddressClientService();
        $address = $address->getWard($request->id);
        if($address == false)
        {
            $reponse = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'=> [
                    'Ward_003_E_001'
                ],
              ];
              return response()->json($reponse,JsonResponse::HTTP_OK);
        }
        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $address,
            ],
          ];
          return response()->json($reponse,JsonResponse::HTTP_OK);
    }

    public function provinceListDictrict()
    {
        $address = new AddressClientService();
        $address = $address->provinceListDictrict();

        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $address,
            ],
          ];
        return response()->json($reponse,JsonResponse::HTTP_OK);
    }

    public function updateCoordinate($id = 1)
    {
        $address = new AddressClientService();
        $address = $address->updateCoordinate($id);

        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => [],
            ],
          ];
        return response()->json($reponse,JsonResponse::HTTP_OK);
    }

    //-----------detail area------------------
    public function detailArea($slug = "aaa")
    {

        $service = new AddressClientService();
        $result = $service->detailArea($slug);

        $reponse = [
            'status' => JsonResponse::HTTP_OK,
            'body'=> [
                'data' => $result,
            ],
          ];
        return response()->json($reponse,JsonResponse::HTTP_OK);
    }
}
