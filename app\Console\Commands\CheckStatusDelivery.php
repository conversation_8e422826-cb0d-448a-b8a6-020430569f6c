<?php

namespace App\Console\Commands;

use App\Delivery;
use App\Jobs\CancelDelivery;
use App\Services\DeliveryPartner\DeliveryPartnerService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckStatusDelivery extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:checkStatusDelivery';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Lấy ra các vận đơn đang ở trạng thái chuẩn bị và có thời gian cập nhật <= (thời gian hiện tại - số phút cho phép chờ)
        $deliveries = Delivery::where('status', config('constants.delivery.pending'))
            ->whereNotNull('pending_time')
            ->whereRaw("EXTRACT(EPOCH FROM (NOW() AT TIME ZONE 'Asia/Ho_Chi_Minh') - updated_at) >= (pending_time * 60)")
            ->chunk(100, function ($deliveries) {
                $deliveries->each(function ($delivery) {
                    CancelDelivery::dispatch($delivery, "Huỷ vận đơn do quá thời gian chờ");
                });
            });
    }
}
