<?php

namespace App\Http\Requests\Brand;

use App\Http\Requests\BaseRequest;

class BrandUpdateRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'id' => 'required|uuid',
            'name' => 'required|max:255',
        ];
    }

    public function messages()
    {
        return [
            // 'name.required' => 'Brand_001_E_001',
            // 'name.max' => 'Brand_001_E_002',

            // 'id.uuid' => 'Brand_002_E_003',
            // 'id.required' => 'Brand_002_E_004',
        ];
    }
}
