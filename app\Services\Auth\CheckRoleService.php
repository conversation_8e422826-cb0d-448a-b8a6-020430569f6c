<?php
namespace App\Services\Auth;
use App\Property;
use App\Http\Requests\PropertyRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\User;
use App\Role;
class CheckRoleService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    // public function checkRole()
    // {
    //     $user = Auth::user();
    //     $role_id = $user->role_id;
    //     if($role_id != 1)
    //     {
    //         return false;
    //     }
    //     return true;
    // }
}
