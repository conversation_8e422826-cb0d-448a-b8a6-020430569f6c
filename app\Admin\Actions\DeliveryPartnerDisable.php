<?php

namespace App\Admin\Actions;

use App\DeliveryPartner;
use App\Services\Token\TokenService;
use App\User;
use Encore\Admin\Actions\RowAction;

class DeliveryPartnerDisable extends RowAction
{
    public function handle($model){
        // Toggle the enabled status
        $actionName = $model->is_active ? __('admin.disable') : __('admin.enable');
        $model->is_active = !$model->is_active;
        $model->save();

        return $this->response()->success($actionName . ' Success!')->refresh();
    }
    public function display($value)
    {
        return $value ? "<i class=\"fa fa-check text-green\"></i>" : "<i class=\"fa fa-close text-red\"></i>";
    }

}
