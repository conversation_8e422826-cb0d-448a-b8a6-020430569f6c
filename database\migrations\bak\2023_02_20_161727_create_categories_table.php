<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // bảng <PERSON><PERSON> mục sản phẩm
        Schema::create('categories', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->comment("Tên sản phẩm");
            $table->uuid('parent_id')->comment("Danh mục cha")->nullable();
            $table->boolean('enable')->default(1);
            $table->uuid('created_by')->nullable();
            $table->string('slug')->nullable();
            $table->uuid('shop_id')->nullable();
            $table->integer('index')->default(0);

            $table->index('parent_id');
            $table->index('enable');
            $table->index('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('categories');
    }
}
