<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;
use Carbon\Carbon;

class UserUpdateClientRequest extends BaseRequest
{
    public function rules()
    {
        if(isset($this->id))
        {
            $id = 'required|uuid|exists:users,id';
            $email = 'nullable|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u|unique:users,email,'.$this->id;
            $user_name = 'bail|nullable|unique:users,user_name,'.$this->id.'|string|min:6|max:64|regex:/^([a-zA-Z0-9]+)(\_[a-zA-Z0-9]+)*$/u';
        }
        else{
            $id = 'required|uuid|exists:users,id';
            $email = 'nullable|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u|unique:users,email';
            $user_name = 'bail|nullable|unique:users,user_name|string|min:6|max:64|regex:/^([a-zA-Z0-9]+)(\_[a-zA-Z0-9]+)*$/u';
        }
        $seventeen_years_prior_now = Carbon::now()->subYears(17);
        return [
            'id' => $id,
            'name' => 'required|max:255',
            'email' => $email,
            'gender' => 'nullable|boolean',
            'phone' => 'required|digits_between:10,11',
            'date_of_birth' => 'bail|nullable|date_format:Y-m-d|before:'.$seventeen_years_prior_now,
            'identity_card' => 'nullable|regex:/^\d{9}(\d{3})?$/',
            'province_id' => 'nullable|integer|exists:dvhc2021_tinh,id',
            'district_id' => 'nullable|integer|exists:dvhc2021_huyen,id',
            'ward_id' => 'nullable|integer|exists:dvhc2021_xa,id',
            'address' => 'max:255',
            'description' => 'max:5000',
            'user_name' => $user_name,
            // 'file' => 'nullable|base64image|base64size',
            // 'file_background' => 'nullable|base64image|base64size',
            'custom_path' => 'bail|nullable|unique:users,custom_path,'.$this->id.'|string|min:4|max:50|regex:/^([a-zA-Z0-9]+)(\_[a-zA-Z0-9]+)*$/u'
        ];
    }
    // public function messages()
    // {

    //     return [

    //         'name.required' => 'User_001_E_001',
    //         'name.regex' => 'User_002_E_002',

    //         'email.required' => 'User_001_E_003',
    //         'email.regex' => 'User_002_E_004',
    //         'email.unique' => 'User_005_E_005',

    //         'gender.boolean' => 'User_002_E_013',

    //         'province_id.required' => 'User_001_E_014',
    //         'province_id.integer' => 'User_002_E_015',
    //         'province_id.exists' => 'User_003_E_016',

    //         'district_id.required' => 'User_001_E_017',
    //         'district_id.integer' => 'User_002_E_018',
    //         'district_id.exists' => 'User_003_E_019',

    //         'ward_id.required' => 'User_001_E_020',
    //         'ward_id.integer' => 'User_002_E_021',
    //         'ward_id.exists' => 'User_003_E_022',

    //         'address.required' => 'User_001_E_023',

    //         'phone.required' => 'User_001_E_024',
    //         'phone.digits_between' => 'User_004_E_025',

    //         'date_of_birth.required' => 'User_001_E_026',
    //         'date_of_birth.date_format' => 'User_008_E_027',
    //         'date_of_birth.before' => 'User_004_E_028',

    //         'identity_card.regex' => 'User_002_E_029',

    //         'id.required' => 'User_001_E_030',
    //         'id.uuid' => 'User_002_E_031',
    //         'id.exists' => 'User_003_E_032',

    //         'user_name.required' => 'User_001_E_037',
    //         'user_name.unique' => 'User_005_E_038',
    //         'user_name.string' => 'User_002_E_039',
    //         'user_name.min' => 'User_004_E_040',
    //         'user_name.max' => 'User_004_E_041',
    //         'user_name.regex' => 'User_002_E_042',

    //         'file.base64image' => 'User_002_E_043',
    //         'file.base64size' => 'User_004_E_044',

    //         'file_background.base64image' => 'User_002_E_058',
    //         'file_background.base64size' => 'User_004_E_059',

    //         'name.max'          => 'User_004_E_064',


    //         'email.max'         => 'User_004_E_067',

    //         'address.max'       => 'User_004_E_068',

    //         'description.max'   => 'User_004_E_069',

    //         'custom_path.unique'    => 'User_005_E_072',
    //         'custom_path.string'    => 'User_002_E_073',
    //         'custom_path.min'       => 'User_004_E_074',
    //         'custom_path.max'       => 'User_004_E_075',
    //         'custom_path.regex'     => 'User_002_E_076',
    //     ];
    // }
}
