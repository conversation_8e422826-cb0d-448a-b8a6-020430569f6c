<?php
namespace App\Services\Mqtt;

//use Mqtt;
use <PERSON><PERSON>\Mqtt\MqttClass\Mqtt;

class MqttChatService
{
    public function publish(array $data, $client_id='')
    {
        $mess = isset($data['message']) ? $data['message'] : null;
        $user_id = isset($client_id) ? $client_id : null;
        $time = now();
        $data['message'] = [
            'mess' => $mess,
            'user_id' => $user_id,
            'time'  => $time,
            'topic' => $data['topic']
        ];

        $mqtt = new Mqtt();
        $output = $mqtt->ConnectAndPublish($data['topic'], json_encode($data['message']), $client_id);
        // $a = $this->SubscribetoTopic($data['topic']);
        if ($output === true)
        {
            return true;
        }

        return false;
    }

    //subscribing topic

    public function SubscribetoTopic($topic, $client_id='')
    {
        $b = Mqtt::ConnectAndSubscribe($topic, function($topic, $msg){
            echo "Msg Received: \n";
            echo "Topic: {$topic}\n\n";
            echo "\t$msg\n\n";
        });
        var_dump($b);
        exit;
    }


}
