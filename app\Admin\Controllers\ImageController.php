<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use App\Image;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use App\Admin\Actions\BatchDelete;

class ImageController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Image controller';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Image);

        $grid->column('id', __('ID'));
        $grid->column('object_type', __('admin.image.object_type'))->using(config('constants.image_object_type_admin'));
        $grid->column('parent_id', __('admin.image.parent_id'));
        $grid->column('title', __('admin.image.title'));
        $grid->column('path', __('admin.image.path'))->display(function ($path) {
            return "<img height='150' width='auto' src='".env('S3_HOST_PATH') . "" .  $path . "'>" . "<p>".$path. "</p>";
        });
        $grid->column('enable', __('admin.image.enable'))->icon([
            0 => 'toggle-off',
            1 => 'toggle-on',
        ], $default = '');
        $grid->column('created_at', __('Created at'));
        $grid->column('updated_at', __('Updated at'))->hide();


        if (!request()->has('_sort')) {
            // Apply default sorting by created_at column in descending order
            $grid->model()->orderBy('created_at', 'desc');
        }

        $grid->filter(function($filter){
            $filter->column(1/2, function ($filter) {
                // Remove the default id filter
                // $filter->disableIdFilter();
                
                // Add a column filter
                $filter->where( function ($query) {
                    $query->where('title', 'ilike', "%{$this->input}%");
                
                }, __('title'));
            });
            $filter->column(1/2, function ($filter) {
                $filter->equal('object_type')->checkbox(config('constants.image_object_type_admin'));
                $filter->equal('enable')->radio([
                    1 => 'YES',
                    0 => 'NO',
                ]);
                // $filter->in('shop_id', __('shop'))->multipleSelect(Shop::where('enable',true)->get()->pluck('name','id'));
            });
        });
        // Add batch delete action
        $grid->batchActions(function ($batch) {
            $batch->add(new BatchDelete('Image')); // Ensure 'Shop' is the correct model type
        });
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed   $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Image::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Image);
        Log::info("form->model()->object_type");
        Log::info($form->model()->object_type);
        Log::info($form->model()->title);
        $form->display('id', __('ID'));
        $form->text('title', __('admin.image.title'))->rules(['required']);
        $form->select('object_type', __('admin.image.object_type'))->options(config('constants.image_object_type_admin'))->rules('required');
        $imageFolder = $_REQUEST['object_type'] ?? '';
        if($imageFolder != ''){
            $imageFolder = config('constants.image_object_type_admin')[$imageFolder] . "/";
        }else{
            $imageFolder = 'uploads/public/';
        }
        $form->image('path', __('admin.image.path'))->removable()->downloadable()
        ->move($imageFolder.date("Y")."/".date("m"), Str::slug($_REQUEST['title'] ?? '')."_".uniqid().'.jpg')
        ->rules('mimes:jpeg,png,jpg,gif,svg,webp')
        // ->thumbnail('small', $width = 300, $height = 300)
        ->rules(['required'])
        ;
        // $form->submitted(function (Form $form) {
        //     Log::info("form->model submitted");
        //     Log::info(json_encode($form));
        //     Log::info($_REQUEST);
        //     Log::info($form->model()->object_type);
        //     Log::info($form->model()->title);
        // });
        return $form;
    }
}
