<?php

namespace App\Http\Requests\Brand;

use App\Http\Requests\BaseRequest;

class BrandCheckIdRequest extends BaseRequest
{
   
    public function rules()
    {
        return [
            'id' => 'required|uuid|exists:brands,id'
        ];
    }

    public function messages()
    {
        return [
            'id.uuid' => 'Brand_002_E_003',
            'id.required' => 'Brand_002_E_004',
            'id.exists' => 'Brand_002_E_005',
        ];
    }
}
