<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Shop;
use App\DeliveryPartner;

class ShopDeliveryPartner extends Model
{

    protected $table = 'shop_delivery_partners';

    // Use UUIDs
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'shop_id',
        'delivery_partner_id',
        'is_enabled',
        'is_default',
        'connect_data',
        'token',
        'token_expired'
    ];

    protected $casts = [
        'connect_data' => 'array'
    ];

    public $hidden = [
        'connect_data',
        'token',
        'token_expired'
    ];

    /**
     * Boot function to handle UUID generation.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = $model->id ?? Str::uuid();
            $model->is_enabled = true;
        });
    }

    /**
     * Define the relationship with Shop.
     */
    public function shop()
    {
        return $this->belongsTo(Shop::class, 'shop_id');
    }

    /**
     * Define the relationship with DeliveryPartner.
     */
    public function deliveryPartner()
    {
        return $this->belongsTo(DeliveryPartner::class, 'delivery_partner_id')->select(['id', 'name', 'is_active']);
    }
}
