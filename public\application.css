html, body { min-height: 100vh; }
html {overflow-y:scroll;}
body { font-family: Verdana, sans-serif; font-size: 12px; color:#333; margin: 0; padding: 0; min-width: 900px; }

h1, h2, h3, h4, h5, h6 {font-family: "Trebuchet MS", Verdana, sans-serif;padding: 2px 10px 1px 0px;margin: 0 0 10px 0;}
#content h1, h2, h3, h4, h5, h6 {color: #555;}
h2 {font-size: 20px;}
h3 {font-size: 16px;}
h4 {font-size: 13px; border-bottom: 1px solid #ccc; font-weight:normal;}
pre, code {font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, "Liberation Mono", Courier, monospace;}
.wiki h1 {font-size: 2em;}
.wiki h2 {font-size: 1.8em;}
.wiki h3 {font-size: 1.5em;}
.wiki h4 {font-size: 1.2em; border: none; font-weight: bold;}
.wiki h5 {font-size: 1em;}
.wiki h6 {font-size: 1em; color: #8e8e8e;}

/***** Layout *****/
#wrapper {
  min-height: inherit;
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

#top-menu {background: #3E5B76; color: #fff; height:1.8em; font-size: 0.8em; padding: 2px 10px 0px 12px;}
#top-menu ul {margin: 0;  padding: 0;}
#top-menu li {
  float:left;
  list-style-type:none;
  margin: 0px 0px 0px 0px;
  padding: 0px 0px 0px 0px;
  white-space:nowrap;
}
#top-menu a {color: #fff; margin-right: 8px; font-weight: bold;}
#top-menu #loggedas { float: right; margin-right: 0.5em; color: #fff; }

#account {float:right;}

#header {min-height:5.3em;margin:0;background-color:#628DB6;color:#f8f8f8; padding: 4px 16px 20px 16px; position:relative;}
#header a {color:#f8f8f8;}
#header h1 { overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
#header h1 .breadcrumbs { display:block; font-size: .5em; font-weight: normal; }

#quick-search {float:right;}
#quick-search #q {width:130px; height:24px; box-sizing:border-box; vertical-align:middle; border:1px solid #ccc; border-radius:3px;}
#quick-search form {float:left; margin-right:3px;}
#quick-search form input {margin-top:0; margin-bottom:0;}
#quick-search form label {vertical-align:middle;}
#quick-search #project-jump {float:left;}

#main-menu {position: absolute;  bottom: 0px;  left: 10px; margin-right: -500px; width: 100%;}
#main-menu ul {margin: 0;  padding: 0; width: 100%; white-space: nowrap;}
#main-menu li {
  float:none;
  list-style-type:none;
  margin: 0px 2px 0px 0px;
  padding: 0px 0px 0px 0px;
  white-space:nowrap;
  display:inline-block;
}
#main-menu li a {
  display: block;
  color: #fff;
  text-decoration: none;
  font-weight: bold;
  margin: 0;
  padding: 4px 10px 4px 10px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
#main-menu li a:hover {background: #759FCF; color: #fff; }
#main-menu li:hover ul.menu-children, #main-menu li ul.menu-children.visible {display: block;}
#main-menu li a.selected, #main-menu li a.selected:hover {background: #fff; color: #555; }
#main-menu li a.new-object {background-color: #759FCF; }

#main-menu .menu-children {
  display: none;
  position:absolute;
  width: inherit;
  z-index:45;
  background-color:#fff;
  border-right: 1px solid #759FCF;
  border-bottom: 1px solid #759FCF;
  border-left: 1px solid #759FCF;
}
#main-menu .menu-children li {float:left; clear:both; width:100%;}
#main-menu .menu-children li a {color: #555; background-color:#fff; font-weight:normal; border-radius: 0;}
#main-menu .menu-children li a:hover {color: #fff; background-color: #759FCF;}

#main-menu .tabs-buttons {
  right: 6px;
  background-color: transparent;
  border-bottom-color: transparent;
}

#admin-menu ul {margin: 0;  padding: 0;}
#admin-menu li {margin: 0;  padding: 0 0 6px 0; list-style-type:none;}

#main {flex-grow: 2; display: flex; flex-direction: row-reverse;}

#sidebar{ flex-shrink: 0; padding-left: 20px; padding-right: 8px; background: #EEEEEE; border-left: 1px solid #ddd}
@media screen and (min-width: 0px) and (max-width: 1089px) {#sidebar{width: 22%;}}
@media screen and (min-width: 1090px) and (max-width: 1279px) {#sidebar{width: 240px;}}
@media screen and (min-width: 1280px) and (max-width: 1599px) {#sidebar{width: 280px;}}
@media screen and (min-width: 1600px) and (max-width: 1919px) {#sidebar{width: 320px;}}
@media screen and (min-width: 1920px) and (max-width: 2559px) {#sidebar{width: 360px;}}
@media screen and (min-width: 2560px) {#sidebar{width: 380px;}}
#sidebar h3{ font-size: 14px; margin-top:14px; color: #666;  }
#sidebar hr{ width: 100%; margin: 0 auto; height: 1px; background: #ccc; border: 0; }
* html #sidebar hr{ width: 95%; position: relative; left: -6px; color: #ccc; }
#sidebar .contextual { margin-right: 1em; }
#sidebar ul, ul.flat {margin: 0;  padding: 0;}
#sidebar ul li, ul.flat li {list-style-type:none;margin: 0px 2px 0px 0px; padding: 0px 0px 0px 0px;}
#sidebar div.wiki ul {margin:inherit; padding-left:40px;}
#sidebar div.wiki ul li {list-style-type:inherit;}
#sidebar a.icon-only {opacity: 0.4; margin-left: 5px;}
#sidebar a.icon-only:hover {opacity: 1;}
#sidebar span.icon-warning {margin-left: 5px;}
#sidebar li input[type=checkbox] {height: 20px;}

#content { flex-grow: 1; background-color: #fff; margin: 0px; padding: 10px 16px 10px 16px; overflow-x: auto;}

#main.nosidebar #sidebar{ display: none; }

#footer {clear: both; border-top: 1px solid #bbb; font-size: 0.9em; color: #aaa; padding: 5px; text-align:center; background:#fff;}

#login-form {margin:5em auto 2em auto; padding:20px; width:340px; border:1px solid #FDBF3B; background-color:#FFEBC1; border-radius:4px; box-sizing: border-box;}
#login-form label {display:block; margin-bottom:5px; font-weight:bold;}
#login-form label[for=autologin] {font-weight:normal;}
#login-form input {height: 29px;}
#login-form input[type=text], #login-form input[type=password], #login-form input[type=submit] {display: block; width: 100%;}
#login-form input[type=text], #login-form input[type=password] {margin-bottom: 15px;}
#login-form a.lost_password {float:right; font-weight:normal;}
#login-form h3 {text-align: center;}

div.modal { border-radius:5px; background:#fff; z-index:50; padding:4px;}
div.modal h3.title {display:none;}
div.modal p.buttons {margin-bottom:0;}
div.modal .box p {margin: 0.3em 0;}

.clear:after{ content: "."; display: block; height: 0; clear: both; visibility: hidden; }

.mobile-show {display: none;}
.hidden {display: none;}
.inline-flex {display: inline-flex;}

/***** Links *****/
a, a:link, a:visited{ color: #169; text-decoration: none; }
a:hover, a:active{ color: #c61a1a; text-decoration: underline;}
a img{ border: 0; }

a.issue.closed, a.issue.closed:link, a.issue.closed:visited { color: #999; text-decoration: line-through; }
a.project.closed, a.project.closed:link, a.project.closed:visited { color: #999; }
a.user.locked, a.user.locked:link, a.user.locked:visited {color: #999;}
a.user.user-mention {
  background-color: #DDEEFF;
  padding: 0.1em 0.1em;
  border-radius: 0.1em;
}

#sidebar a.selected {line-height:1.7em; padding:1px 3px 2px 2px; margin-left:-2px; background-color:#9DB9D5; color:#fff; border-radius:2px;}
#sidebar a.selected:hover {text-decoration:none;}
#sidebar .query.default {font-weight: bold;}
#admin-menu a {line-height:1.7em;}
#admin-menu a.selected {padding-left: 20px !important; background-position: 2px 40%;}

a.collapsible {padding-left: 12px; }

a#toggle-completed-versions {color:#999;}

/***** Dropdown *****/
.drdn {position:relative;}
.drdn-trigger {
  box-sizing:border-box;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  cursor:pointer;
  user-select:none;
  -moz-user-select:none;
  -webkit-user-select:none;
}
.drdn-content {
  display:none;
  position:absolute;
  right:0px;
  top:25px;
  min-width:100px;
  background-color:#fff;
  border:1px solid #ccc;
  border-radius:4px;
  color:#555;
  z-index:99;
}
.drdn.expanded .drdn-content {display:block;}

.drdn-content .quick-search {margin:8px;}
.drdn-content .autocomplete {box-sizing: border-box; width:100% !important; height:28px;}
.drdn-content .autocomplete:focus {border-color:#5ad;}
.drdn-items {max-height:400px; overflow:auto;}
div + .drdn-items {border-top:1px solid #ccc;}
.drdn-items>* {
  display:block;
  border:1px solid #fff;
  overflow:hidden;
  text-overflow: ellipsis;
  white-space:nowrap;
  padding:4px 8px;
}
.drdn-items>a:hover {text-decoration:none;}
.drdn-items>*:focus {border:1px dotted #bbb;}

.drdn-items.selection>*:before {
  content:' ';
  display:inline-block;
  line-height:1em;
  width:1em;
  height:1em;
  margin-right:4px;
  font-weight:bold;
}
.drdn-items.selection>*.selected:before {
  content:"\2713 ";
}
.drdn-items.selection:empty {
  border: none;
}
.drdn-items>span {color:#999;}

.contextual .drdn-content {top:18px;}
.contextual .drdn-items {padding:2px; min-width: 160px;}
.contextual .drdn-items>a {padding: 5px 8px;}
.contextual .drdn-items>a.icon {padding-left: 24px; background-position-x: 4px;}
.contextual .drdn-items>a:hover {color:#2A5685; border:1px solid #628db6; background-color:#eef5fd; border-radius:3px;}

#project-jump.drdn {width:200px;display:inline-block;}
#project-jump .drdn-trigger {
  width:100%;
  height:24px;
  display:inline-block;
  padding:3px 18px 3px 6px;
  border-radius:3px;
  border:1px solid #ccc;
  margin:0 !important;
  vertical-align:middle;
  color:#555;
  background:#fff url(../images/arrow_down.png) no-repeat 97% 50%;
}
#project-jump .drdn.expanded .drdn-trigger {background-image:url(../images/arrow_up.png);}
#project-jump .drdn-content {width:280px;}
#project-jump .drdn-items>* {color:#555 !important;}
#project-jump .drdn-items>a:hover {background-color:#759FCF; color:#fff !important;}

/***** Tables *****/
table.list, .table-list { border: 1px solid #e4e4e4; width: 100%; margin-bottom: 4px; border-radius: 3px; border-spacing: 0; overflow: hidden;}
table.list th, .table-list-header { background-color:#EEEEEE; padding: 4px; white-space:nowrap; font-weight:bold; }
table.list th.whitespace-normal {white-space: normal;}
table.list td {text-align:center; vertical-align:middle; padding-right:10px;}
table.list td.id { width: 2%; text-align: center;}
table.list td.name, table.list td.description, table.list td.subject, table.list td.parent-subject, table.list td.comments, table.list td.roles, table.list td.attachments, table.list td.text,  table.list td.short_description {text-align: left;}

table.list td.attachments span {display: block; height: 16px;}
table.list td.attachments span a.icon-download {display: inline-block; visibility: hidden;}
table.list td.attachments span:hover a.icon-download {visibility: visible;}
table.list td.tick {width:15%}
table.list td.checkbox { width: 15px; padding: 2px 0 0 0; }
table.list .checkbox input {padding:0px; height: initial;}
table.list td.buttons, div.buttons { white-space:nowrap; text-align: right; }
table.list td.buttons a, div.buttons a { margin-right: 0.6em; }
table.list td.buttons a:last-child, div.buttons a:last-child { margin-right: 0; }
table.list td.buttons img, div.buttons img {vertical-align:middle;}
table.list td.reorder {width:15%; white-space:nowrap; text-align:center; }
table.list table.progress td {padding-right:0px;}
table.list caption { text-align: left; padding: 0.5em 0.5em 0.5em 0; }
table.list tr.overdue td.due_date  { color: #c22; }
#role-permissions-trackers table.list th {white-space:normal;}

.table-list-cell {display: table-cell; vertical-align: top; padding:2px; }
.table-list div.buttons {width: 15%;}

tr.project td.name a { white-space:nowrap; }
tr.project.closed, tr.project.archived { color: #aaa; }
tr.project.closed a, tr.project.archived a { color: #aaa; }

tr.issue { text-align: center; white-space: nowrap; }
tr.issue td.subject, tr.issue td.parent-subject, tr.issue td.category, td.assigned_to, td.last_updated_by, tr.issue td.string, tr.issue td.text, tr.issue td.list, tr.issue td.relations, tr.issue td.parent { white-space: normal; }
tr.issue td.relations { text-align: left; }
tr.issue td.done_ratio table.progress { margin-left:auto; margin-right: auto;}
tr.issue td.relations span {white-space: nowrap;}
table.issues td.block_column {color:#777; font-size:90%; padding:4px 4px 4px 24px; text-align:left; white-space:normal;}
table.issues td.block_column span {font-weight: bold; display: block; margin-bottom: 4px;}
table.issues td.block_column pre {white-space:normal;}

tr.idnt td.subject, tr.idnt td.name {background: url(../images/arrow_right.png) no-repeat 2px 50%;}
tr.idnt-1 td.subject, tr.idnt-1 td.name {padding-left: 24px; background-position: 8px 50%;}
tr.idnt-2 td.subject, tr.idnt-2 td.name {padding-left: 40px; background-position: 24px 50%;}
tr.idnt-3 td.subject, tr.idnt-3 td.name {padding-left: 56px; background-position: 40px 50%;}
tr.idnt-4 td.subject, tr.idnt-4 td.name {padding-left: 72px; background-position: 56px 50%;}
tr.idnt-5 td.subject, tr.idnt-5 td.name {padding-left: 88px; background-position: 72px 50%;}
tr.idnt-6 td.subject, tr.idnt-6 td.name {padding-left: 104px; background-position: 88px 50%;}
tr.idnt-7 td.subject, tr.idnt-7 td.name {padding-left: 120px; background-position: 104px 50%;}
tr.idnt-8 td.subject, tr.idnt-8 td.name {padding-left: 136px; background-position: 120px 50%;}
tr.idnt-9 td.subject, tr.idnt-9 td.name {padding-left: 152px; background-position: 136px 50%;}

table.issue-report {table-layout:fixed;}
.issue-report-graph {width: 75%; margin: 2em 0;}

tr.entry { border: 1px solid #f8f8f8; }
tr.entry td { white-space: nowrap; }
tr.entry td.filename {width:30%; text-align:left;}
tr.entry td.filename_no_report {width:70%; text-align:left;}
tr.entry td.size { text-align: right; font-size: 90%; }
tr.entry td.revision, tr.entry td.author { text-align: center; }
tr.entry td.age { text-align: right; }
tr.entry.file td.filename a { margin-left: 16px; }
tr.entry.file td.filename_no_report a { margin-left: 16px; }

tr span.expander, .gantt_subjects div > span.expander {background-position: 2px 50%; padding-left: 8px; margin-left: 0; cursor: pointer;}
.gantt_subjects div > span.expander {padding-left: 12px;}
.gantt_subjects div > span .icon-gravatar {float: none;}

tr.changeset { height: 20px }
tr.changeset ul, ol { margin-top: 0px; margin-bottom: 0px; }
tr.changeset td.revision_graph { width: 15%; background-color: #fffffb; }
tr.changeset td.author { text-align: center; width: 15%; white-space:nowrap;}
tr.changeset td.committed_on { text-align: center; width: 15%; white-space:nowrap;}

table.files tbody th {text-align:left;}
table.files tr.file td.filename { text-align: left; padding-left: 24px; }
table.files tr.file td.digest { font-size: 80%; }

table.members td.roles, table.memberships td.roles { width: 45%; }
table.members td.buttons { text-align: left; width: 1px; white-space: nowrap;}

table.messages td.last_message {text-align:left;}
tr.message { height: 2.6em; }
tr.message td.created_on { white-space: nowrap; }
tr.message td.last_message { font-size: 80%; white-space: nowrap; }
tr.message.sticky td.subject { font-weight: bold; }

body.avatars-on #replies .message.reply {padding-left: 32px;}
#replies .reply:target h4.reply-header {background-color:#DDEEFF;}
#replies h4 img.gravatar {margin-left:-32px;}

tr.version.closed, tr.version.closed a { color: #999; }
tr.version td.name { padding-left: 20px; }
tr.version td.date, tr.version td.status, tr.version td.sharing { text-align: center; white-space:nowrap; }

tr.member td.icon-user, #principals_for_new_member .icon-user {background:transparent;}

tr.user td {width:13%;white-space: nowrap;}
td.username, td.firstname, td.lastname, td.email {text-align:left !important;}
tr.user td.email { width:18%; }
tr.user.locked, tr.user.registered { color: #aaa; }
tr.user.locked a, tr.user.registered a { color: #aaa; }

table.permissions td.role {color:#999;font-size:90%;font-weight:normal !important;text-align:center;vertical-align:bottom;}
table.permissions tr.group>td:nth-of-type(1),
table.tracker-summary tr.group>td:nth-of-type(1) {font-weight: bold;}

tr.wiki-page-version td.updated_on, tr.wiki-page-version td.author {text-align:center;}

tr.time-entry { text-align: center; white-space: nowrap; }
tr.time-entry td.issue, tr.time-entry td.comments, tr.time-entry td.subject, tr.time-entry td.activity, tr.time-entry td.project { text-align: left; white-space: normal; }
td.hours { text-align: right; font-weight: bold; padding-right: 0.5em; }
td.hours .hours-dec { font-size: 0.9em; }

table.plugins td { vertical-align: middle; }
table.plugins td.configure { text-align: right; padding-right: 1em; }
table.plugins span.name { font-weight: bold; display: block; margin-bottom: 6px; }
table.plugins span.description { display: block; font-size: 0.9em; }
table.plugins span.url { display: block; font-size: 0.9em; }

table.list.enumerations {table-layout: fixed; margin-bottom: 2em;}

tr.group td { padding: 0.8em 0 0.5em 0.3em; border-bottom: 1px solid #ccc; text-align:left; background-color: #fff;}
tr.group span.count {top:-1px;}
tr.group span.name {font-weight:bold;}
tr.group span.totals {color: #aaa; font-size: 80%;}
tr.group span.totals .value {font-weight:bold; color:#777;}
tr.group a.toggle-all { color: #aaa; font-size: 80%; display:none; float:right; margin-right:4px;}
tr.group:hover a.toggle-all { display:inline;}
a.toggle-all:hover {text-decoration:none;}

table.list tbody tr.group:hover { background-color:inherit; }

table td {padding:2px;}
table p {margin:0;}

table.list:not(.odd-even) tbody tr:nth-child(odd), .odd, #issue-changesets div.changeset:nth-child(odd) { background-color:#f6f7f8; }
table.list:not(.odd-even) tbody tr:nth-child(even), .even, #issue-changesets div.changeset:nth-child(even) { background-color: #fff; }
table.list:not(.odd-even) tbody tr:nth-child(odd):hover, .odd:hover, #issue-changesets div.changeset:nth-child(odd):hover,
table.list:not(.odd-even) tbody tr:nth-child(even):hover, .even:hover, #issue-changesets div.changeset:nth-child(even):hover { background-color:#ffffdd; }

tr.builtin td.name {font-style:italic;}

a.sort { padding-right: 16px; background-position: 100% 50%; background-repeat: no-repeat; }

table.boards a.board { background: url(../images/comment.png) no-repeat 0% 50%; padding-left: 20px; }
table.boards td.last-message {text-align:left;font-size:80%;}

div.table-list.boards .table-list-cell.name {width: 30%;}

#query_form_content {font-size:90%;}
#query_form_with_buttons > p.contextual {font-size:12px; margin:12px 0px;}

.query_sort_criteria_count {
  display: inline-block;
  min-width: 1em;
}

.query-columns>span {
  display:inline-block;
  height:100%;
  vertical-align: middle;
}
.query-columns label {
  display:block;
}
.query-columns .buttons input[type=button] {
  width:35px;
  display:block;
}
.query-columns select {
  min-width:150px;
}

.query-totals {text-align:right; margin-top:-2.3em;}
.query-totals>span:not(:first-child) {margin-left:0.6em;}
.query-totals .value {font-weight:bold;}
body.controller-timelog .query-totals {margin-top:initial;}

td.center {text-align:center;}

#watchers select {width: 95%; display: block;}
#watchers img.gravatar {margin: 0 4px 2px 0;}
#users_for_watcher img.gravatar {padding-bottom: 2px; margin-right: 4px;}

span#watchers_inputs {overflow:auto; display:block;}
span.search_for_watchers {display:block;}
span.search_for_watchers, span.add_attachment {font-size:80%; line-height:2.5em;}
span.add_attachment a {padding-left:16px; background: url(../images/bullet_add.png) no-repeat 0 50%; }

input:disabled, select:disabled, textarea:disabled {
  cursor: not-allowed;
  color: graytext;
  background-color: #ebebe4;
}


.highlight { background-color: #FCFD8D;}
.highlight.token-1 { background-color: #faa;}
.highlight.token-2 { background-color: #afa;}
.highlight.token-3 { background-color: #aaf;}

.box{
  padding:6px;
  margin-bottom: 10px;
  background-color:#f6f6f6;
  color:#505050;
  line-height:1.5em;
  border: 1px solid #e4e4e4;
  word-wrap: break-word;
  border-radius: 3px;
}

div.square {
  border: 1px solid #999;
  float: left;
  margin: .3em .4em 0 .4em;
  overflow: hidden;
  width: .6em; height: .6em;
}
.contextual {float:right; white-space: nowrap; line-height:1.4em;margin:5px 0px; padding-left: 10px; font-size:0.9em;}
.contextual .icon {padding-top: 2px; padding-bottom: 3px;}
.contextual input, .contextual select {font-size:0.9em;}
.message .contextual { margin-top: 0; }

.splitcontent {overflow: auto; display: flex; flex-wrap: wrap;}
.splitcontentleft {flex: 1; margin-right: 5px;}
.splitcontentright {flex: 1; margin-left: 5px;}
.splitcontenttop {flex: 2; flex-basis: 100%;}

form {display: inline;}
input, select, button {vertical-align: middle; margin-top: 1px; margin-bottom: 1px; height: 24px; padding: 0 7px;}
input, select, textarea, button { color: #333; background-color: #fff; border:1px solid #ccc; border-radius:3px; box-sizing: border-box;}
select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  background-color: #fff;
  background-image: url(../images/arrow_down.png);
  background-repeat: no-repeat;
  background-position: calc(100% - 7px) 50%;
  padding-right: 20px;
}
input[type="file"] {border: 0; padding-left: 0; padding-right: 0; height: initial; background-color: initial; }
input[type="submit"], button[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
  background-color: #fff;
  height: 28px;
  -webkit-transition: background-color 100ms linear;
  -moz-transition: background-color 100ms linear;
  -o-transition: background-color 100ms linear;
  transition: background-color 100ms linear;
}
input[type="submit"]:hover, button[type="submit"]:hover {
  background-color: #ddd;
}

input[type="text"]:focus, input[type="text"]:active,
input[type="password"]:focus, input[type="password"]:active,
input[type="date"]:focus, input[type="date"]:active,
input[type="number"]:focus, input[type="number"]:active,
select:focus, select:active,
textarea:focus, textarea:active {
  border: 1px solid #5ad;
  outline: none;
}

input:placeholder-shown {
  text-overflow: ellipsis;
}

select[multiple=multiple] {background: #fff; padding-right: initial; height: auto;}
fieldset {border: 1px solid #e4e4e4; margin:0; min-width: inherit;}
legend {color: #333;}
hr { width: 100%; height: 1px; background: #ccc; border: 0;}
blockquote { font-style: italic; border-left: 3px solid #e0e0e0; padding-left: 0.6em; margin-left: 0;}
blockquote blockquote { margin-left: 0;}
abbr, span.field-description[title] { border-bottom: 1px dotted #aaa; cursor: help; }
textarea.wiki-edit {width:99%; resize:vertical; box-sizing: border-box;}
body.textarea-monospace textarea.wiki-edit {font-family: Consolas, Menlo, "Liberation Mono", Courier, monospace; font-size: 12px;}
body.textarea-proportional textarea.wiki-edit {font-family: Verdana, sans-serif; font-size: 12px;}
li p {margin-top: 0;}
div.issue {background:#ffffdd; padding:6px; margin-bottom:6px; border: 1px solid #d7d7d7; border-radius:3px;}
p.breadcrumb { font-size: 0.9em; margin: 4px 0 4px 0;}
p.subtitle { font-size: 0.9em; margin: -6px 0 12px 0; font-style: italic; }
p.footnote { font-size: 0.9em; margin-top: 0px; margin-bottom: 0px; }
.wiki-class-ltr {direction:ltr !important;}
.wiki-class-rtl {direction:rtl !important;}

div.issue div.subject div div { padding-left: 16px; word-break: break-word; }
div.issue div.subject p {margin: 0; margin-bottom: 0.1em; font-size: 90%; color: #999;}
div.issue div.subject>div>p { margin-top: 0.5em; }
div.issue div.subject h3 {margin: 0; margin-bottom: 0.1em;}
div.issue p.author {margin-top:0.5em;}
div.issue span.private, div.journal span.private {font-size: 60%;}
div.issue .next-prev-links {color:#999;}
div.issue .attributes {margin-top: 2em;}
div.issue .attributes .attribute {padding-left:180px; clear:left; min-height: 1.8em;}
div.issue .attributes .attribute .label {width: 170px; margin-left:-180px; font-weight:bold; float:left;  overflow: clip visible; text-overflow: ellipsis;}
div.issue .attribute .value {overflow:auto; text-overflow: ellipsis;}
div.issue .attribute.string_cf .value .wiki p {margin-top: 0; margin-bottom: 0;}
div.issue .attribute.text_cf .value .wiki p:first-of-type {margin-top: 0;}
div.issue.overdue .due-date .value { color: #c22; }
body.controller-issues h2.inline-flex {padding-right: 0}

#issue_tree table.issues, #relations table.issues { border: 0; }
#issue_tree td.checkbox, #relations td.checkbox {display:none;}
#issue_tree td.buttons, #relations td.buttons {padding:0;}
#issue_tree .issues-stat, #relations .issues-stat {font-size: 80%}
#issue_tree .issues-stat .badge, #relations .issues-stat .badge {bottom: initial;}
#issue_tree .issue > td, #relations .issue > td, #issue_tree .issue .user {
  text-overflow: ellipsis; /* if text exceeds its space, add ... */
  overflow: hidden;
}
#issue_tree .issue > td.subject, #relations .issue > td.subject {
  width: 50%;
  word-break: break-word; /* break word if subject is too long */
  padding-right: 25px; /* this is the spaces that .buttons uses next to subject */
}
#issue_tree .issue > td.assigned_to, #relations .issue > td.assigned_to {
  white-space: nowrap;
}
#trackers_description, #issue_statuses_description {display:none;}
#trackers_description dt, #issue_statuses_description dt {font-weight: bold; text-decoration: underline;}
#trackers_description dd, #issue_statuses_description dd {margin: 0; padding: 0 0 1em 0;}

#issue-form .assign-to-me-link { padding-left: 5px; }

fieldset.collapsible {border-width: 1px 0 0 0;}
fieldset.collapsible>legend { cursor:pointer; padding-left: 18px; background-position: 4px;}

fieldset#date-range p { margin: 2px 0 2px 0; }
fieldset#filters table { border-collapse: collapse; }
fieldset#filters table td { padding: 0; vertical-align: middle; }
fieldset#filters tr.filter { height: 2.1em; }
fieldset#filters td.field { width:230px; }
fieldset#filters td.operator { width:130px; }
fieldset#filters td.operator select {max-width:140px;}
fieldset#filters td.values { white-space:nowrap; }
fieldset#filters td.values select {min-width:130px;}

#filters-table {width:60%; float:left;}
.add-filter {width:35%; float:right; text-align: right; vertical-align: top;}

#issue_is_private_wrap {float:right; margin-right:1em;}
.toggle-multiselect { margin-right:5px; cursor:pointer;}
.buttons { font-size: 0.9em; margin-bottom: 1.4em; margin-top: 1em; }
.buttons .icon {padding-bottom:3px; padding-top:1px;}

div#issue-changesets {float:right; width:45%; margin-left: 1em; margin-bottom: 1em; background: #fff; padding-left: 1em; font-size: 90%;}
div#issue-changesets div.changeset {border-bottom: 1px solid #ddd; padding: 4px;}
div#issue-changesets p { margin-top: 0; margin-bottom: 1em;}
.changeset-comments {margin-bottom:1em;}

div.journal .contextual {margin-top: 0;}
div.journal.private-notes .wiki {border-left:2px solid #d22; padding-left:4px; margin-left:-6px;}
div.journal ul.details, ul.revision-info {color:#959595; margin-bottom: 1.5em;}
div.journal ul.details a, ul.revision-info a {color:#70A7CD;}
div.journal ul.details a:hover, ul.revision-info a:hover {color:#D14848;}
body.avatars-on div.journal {padding-left:32px;}
div.journal h4 img.gravatar {margin-left:-32px;}
div.journal span.update-info {color: #666; font-size: 0.9em;}

#update {margin-bottom: 1.4em;}

#history .tab-content {
  padding: 0 6px;
  margin-bottom: 10px;
  border-right: 1px solid #d7d7d7;
  border-bottom: 1px solid #d7d7d7;
  border-left: 1px solid #d7d7d7;
  border-radius: 0 0 3px 3px / 0 0 3px 3px;
}

#history div:target h4.note-header {background-color:#DDEEFF;}
#history p.nodata {display: none;}

div#activity dl, #search-results { margin-left: 2em; }
div#activity dd, #search-results dd { margin-bottom: 1em; padding-left: 18px; font-size: 0.9em; }
div#activity dt.me .time { border-bottom: 1px solid #999; }
div#activity dt .time { color: #777; font-size: 80%; }
div#activity dd .description, #search-results dd .description { font-style: italic; }
div#activity span.project:after, #search-results span.project:after { content: " -"; }
div#activity dd span.description, #search-results dd span.description { display:block; color: #808080; }
div#activity dt.grouped {margin-left:5em;}
div#activity dd.grouped {margin-left:9em;}
div#activity dt.icon {background-position: 0 10px !important;}
div#activity h3 {
  padding: 5px;
  background-color: #eeeeee;
}
div#activity dt {
  padding-top: 10px;
  border-top: 1px solid #eeeeee;
}
div#activity dl dt:first-child {
  border: 0px;
}

#activity_scope_form select#user_id {
  max-width: 100%;
}

#search-results dd { margin-bottom: 1em; padding-left: 20px; margin-left:0px; }

div#search-results-counts {float:right;}
div#search-results-counts ul { margin-top: 0.5em; }
div#search-results-counts  li { list-style-type:none; float: left; margin-left: 1em; }

div#roadmap .related-issues { margin-bottom: 1em; }
div#roadmap .related-issues td.checkbox { display: none; }
div#roadmap .related-issues td.assigned_to { width:1px; white-space:nowrap; padding: 0; }
div#roadmap .related-issues td.assigned_to img { padding-left: 4px; padding-right: 4px;}
div#roadmap .wiki h1:first-child { display: none; }
div#roadmap .wiki h1 { font-size: 120%; }
div#roadmap .wiki h2 { font-size: 110%; }
div#roadmap h2, div#roadmap h3 {padding-right: 0;}
body.controller-versions.action-show div#roadmap .related-issues {width:70%;}

div#roadmap .version-article {padding-bottom: 12px;}

div#version-summary { float:right; width:28%; margin-left: 16px; margin-bottom: 16px; background-color: #fff; }
div#version-summary fieldset { margin-bottom: 1em; }
div#version-summary fieldset.time-tracking table { width:100%; }
div#version-summary th, div#version-summary td.total-hours { text-align: right; }

table#time-report td.hours, table#time-report th.period, table#time-report th.total { text-align: right; padding-right: 0.5em; }
table#time-report tbody tr.subtotal { font-style: italic; color:#777;}
table#time-report tbody tr.subtotal td.hours { color:#b0b0b0; }
table#time-report tbody tr.total { font-weight: bold; background-color:#EEEEEE; border-top:1px solid #e4e4e4;}
table#time-report .hours-dec { font-size: 0.9em; }

div.wiki-page .contextual a {opacity: 0.4}
div.wiki-page .contextual a:hover {opacity: 1}

div.wiki a:target + h1, div.wiki a:target + h2, div.wiki a:target + h3, div.wiki a:target + h4, div.wiki a:target + h5, div.wiki a:target + h6 {
  background-color:#DDEEFF;
}

.wiki-update-info {text-align:right; color:#666; font-size:90%;}

form .attributes select { width: 60%; }
form .attributes select + a.icon-only { vertical-align: middle; margin-left: 4px; }
input#issue_subject, input#document_title { width: 99%; }
select#issue_done_ratio { width: 95px; }

ul.projects {margin:0; padding-left:1em;}
ul.projects ul.projects {padding-left:1.6em;}
ul.projects.root {margin:0; padding:0;}
ul.projects li.root, ul.projects li.child {list-style-type:none;}
ul.projects li.root div.archived, ul.projects li.child div.archived {color: #aaa;}
ul.projects div.description ul li {list-style-type:initial;}

#projects-index {
  column-count: auto;
  column-width: 400px;
  -webkit-column-count: auto;
  -webkit-column-width: 400px;
  -webkit-column-gap : 0.5rem;
  -moz-column-count: auto;
  -moz-column-width: 400px;
  -moz-column-gap : 0.5rem;
  margin-bottom: 1.2em;
}
#projects-index li.root ul.projects { border-left: 3px solid #e0e0e0; padding-left:1em;}
#projects-index ul.projects li.root {
  margin-bottom: 1em;
  padding: 15px 20px;
  border: 1px solid #d7d7d7;
  border-radius: 3px;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  break-inside: avoid-column;
  -webkit-break-inside: avoid-column;
  -moz-break-inside: avoid-column;
  page-break-inside:avoid;
  -webkit-column-break-inside: avoid;
  -moz-column-break-inside: avoid;
  width: 100%;
}
#projects-index ul.projects li.child {margin-top: 1em;}
#projects-index ul.projects div.root a.project { font-family: "Trebuchet MS", Verdana, sans-serif; font-weight: bold; font-size: 16px; margin: 0 0 10px 0; }
#projects-index ul.projects div.description {
  padding-top: 0.5em;
}
#projects-index a.icon-user, a.icon-bookmarked-project {padding-left:0; padding-right:20px; background-position:98% 50%;}
#projects-index a.icon-user.icon-bookmarked-project {
  background-image: url(../images/tag_blue.png), url(../images/user.png);
  background-position: bottom 0px right 0px, bottom 0px right 20px;
  padding-right: 40px;
  padding-top: 4px;
}

#notified-projects>ul, #tracker_project_ids>ul, #custom_field_project_ids>ul {max-height:250px; overflow-y:auto;}

ul.subprojects {list-style: none; display: inline-block; padding: 0; margin: 0;}
ul.subprojects li {float: left;}
ul.subprojects li:not(:last-child)::after {content: ', '; white-space: pre;}

#related-issues li img {vertical-align:middle;}

ul.properties {padding:0; font-size: 0.9em; color: #777;}
ul.properties li {list-style-type:none;}
ul.properties li span {font-style:italic;}

.total-hours { font-size: 110%; font-weight: bold; }
.total-hours span.hours-int { font-size: 120%; }

.autoscroll {overflow-x: auto; padding:1px; margin-bottom: 1.2em; position: relative;}
#user_login, #user_firstname, #user_lastname, #user_mail, #my_account_form select, #user_form select { width: 90%; }

#workflow_copy_form select { width: 200px; }
table.transitions td.enabled {background: #bfb;}
#workflow_form table select {font-size:90%; max-width:100px;}
table.fields_permissions td.readonly {background:#ddd;}
table.fields_permissions td.required {background:#d88;}

select.expandable {vertical-align:top;}

textarea#custom_field_possible_values {width: 95%; resize:vertical}
textarea#custom_field_default_value {width: 95%; resize:vertical}
.sort-handle {display:inline-block; vertical-align:middle; cursor: move;}

input#content_comments {width: 99%}

span.pagination {margin-left:3px; color:#888; display:block;}
.pagination ul.pages {
  margin: 0 5px 0 0;
  padding: 0;
  display: inline;
}
.pagination ul.pages li {
  display: inline-block;
  padding: 0;
  border: 1px solid #ddd;
  margin-left: -1px;
  line-height: 2em;
  margin-bottom: 1em;
  white-space: nowrap;
  text-align: center;
}
.pagination ul.pages li a,
.pagination ul.pages li span {
  padding: 3px 8px;
}
.pagination ul.pages li:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.pagination ul.pages li:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.pagination ul.pages li.current {
  color: white;
  background-color: #628DB6;
  border-color: #628DB6;
}
.pagination ul.pages li.page:hover {
  background-color: #ddd;
}
.pagination ul.pages li.page a:hover,
.pagination ul.pages li.page a:active {
  color: #169;
  text-decoration: inherit;
}
.pagination .per-page span.selected {
  font-weight: bold;
}
span.pagination>span {white-space:nowrap;}
.controller-attachments.action-show span.pagination, .controller-repositories.action-entry span.pagination {
  display: block;
  margin-top: 1.2em;
}

#search-form fieldset p {margin:0.2em 0;}
#csv-export-options fieldset {padding: 0;}

/***** Tabular forms ******/
.tabular p{
  margin: 0;
  padding: 3px 0 3px 0;
  padding-left: 180px; /* width of left column containing the label elements */
  min-height: 2em;
  clear:left;
}
html>body .tabular p {overflow:hidden;}

.tabular input, .tabular select {max-width:95%}
.tabular textarea {width:95%; resize:vertical;}
input#twofa_code, img#twofa_code { width: 140px; }
ul.twofa_backup_codes { list-style-type: none; padding: 0; display: inline-block; columns: 14em 2;}
ul.twofa_backup_codes code { font-size: 16px; line-height: 2em }

.tabular label{
  font-weight: bold;
  float: left;
  text-align: right;
  /* width of left column */
  margin-left: -180px;
  /* width of labels. Should be smaller than left column to create some right margin */
  width: 175px;
  line-height: 24px;
}

.tabular label.floating{
  font-weight: normal;
  margin-left: 0px;
  text-align: left;
  width: 270px;
}

label.block {
  display: block;
  width: auto !important;
}

.tabular label.block{
  font-weight: normal;
  margin-left: 0px !important;
  text-align: left;
  float: none;
}

.tabular label.inline{
  font-weight: normal;
  float:none;
  margin-left: 5px !important;
  width: auto;
}

.tabular label.error {
  color: #bb0000;
}

.tabular label.error + * {
  border: 1px solid #bb0000;
}

label.no-css {
  font-weight: inherit;
  float:none;
  text-align:left;
  margin-left:0px;
  width:auto;
}
input#time_entry_comments { width: 90%;}
input#months { width: 46px; }

.jstBlock .jstTabs, .jstBlock .wiki-preview { width: 99%; }

.jstBlock .jstTabs { padding-right: 6px; }
.jstBlock .wiki-preview { padding: 2px; }
.jstBlock .wiki-preview p:first-child { padding-top: 0 !important; margin-top: 0 !important;}
.jstBlock .wiki-preview p:last-child { padding-bottom: 0 !important; margin-bottom: 0 !important;}

.tabular .wiki-preview, .tabular .jstTabs {width: 95%;}
.tabular.settings .wiki-preview, .tabular.settings .jstTabs { width: 99%; }
.tabular.settings .wiki-preview p {padding-left: 0 !important}
.tabular .wiki-preview p {
  min-height: initial;
  padding: 0;
  padding-top: 1em !important;
  padding-bottom: 1em !important;
  overflow: initial;
}

.tabular.settings p { padding-left: 300px; }
.tabular.settings label{ margin-left: -300px; width: 295px; }
.tabular.settings textarea, .tabular.settings .wiki-preview, .tabular.settings .jstTabs { width: 99%; }

.settings.enabled_scm table {width:100%}
.settings.enabled_scm td.scm_name{ font-weight: bold; }

fieldset.settings label { display: block; }
fieldset#notified_events .parent { padding-left: 20px; }

span.required {color: #bb0000;}
.summary {font-style: italic;}

.check_box_group {
  display:block;
  width:95%;
  max-height:120px;
  overflow-y:auto;
  padding:2px 4px 4px 2px;
  background:#fff;
  border:1px solid #9EB1C2;
  border-radius:2px
}
.check_box_group label {
  font-weight: normal;
  margin-left: 0px !important;
  text-align: left;
  float: none;
  display: block;
  width: auto;
}
.check_box_group.bool_cf {border:0; background:inherit;}
.check_box_group.bool_cf label {display: inline;}

.attachments_fields input.description, #existing-attachments input.description {margin-left:4px; width:340px;}
.attachments_fields>span, #existing-attachments>span {display:block; white-space:nowrap;}
.attachments_fields input.filename, #existing-attachments .filename {border:0; width:250px; color:#555; background-color:inherit; }
.tabular input.filename {max-width:75% !important;}
.attachments_fields input.filename {height:1.8em;}
.attachments_fields .ajax-waiting input.filename {background:url(../images/hourglass.png) no-repeat 0px 50%;}
.attachments_fields .ajax-loading input.filename {background:url(../images/loading.gif) no-repeat 0px 50%;}
.attachments_fields div.ui-progressbar { width: 100px; height:14px; margin: 2px 0 -5px 8px; display: inline-block; }

a.remove-upload:hover {text-decoration:none !important;}
.existing-attachment.deleted .filename {text-decoration:line-through; color:#999 !important;}

div.fileover, p.custom-field-filedroplistner.fileover { background-color: lavender; }

div.attachments p { margin:4px 0 2px 0; }
div.attachments img { vertical-align: middle; }
div.attachments span.author { font-size: 0.9em; color: #888; }

div.thumbnails {margin:0.6em;}
div.thumbnails div {background:#fff;border:2px solid #ddd;display:inline-block;margin-right:2px;}
div.thumbnails img {margin: 3px; vertical-align: middle;}
#history div.thumbnails {margin-left: 2em;}

p.other-formats { text-align: right; font-size:0.9em; color: #666; }
.other-formats span + span:before { content: "| "; }

a.atom { background: url(../images/feed.png) no-repeat 1px 50%; padding: 2px 0px 3px 16px; }

em.info {font-style:normal;display:block;font-size:90%;color:#888;}
em.info.error {padding-left:20px; background:url(../images/exclamation.png) no-repeat 0 50%;}

textarea.text_cf {width:95%; resize:vertical;}
input.string_cf, input.link_cf {width:95%;}
select.bool_cf {width:auto !important;}

#tab-content-modules fieldset p {margin:3px 0 4px 0;}

#users_for_watcher {height: 200px; overflow:auto;}
#users_for_watcher label {display: block;}

input#principal_search, input#user_search {width:90%}
.roles-selection label {display:inline-block; width:210px;}

input.autocomplete {
  background: #fff url(../images/magnifier.png) no-repeat 2px 50%; padding-left:20px !important;
}
input.autocomplete.ajax-loading {
  background-image: url(../images/loading.gif);
}

.role-visibility {padding-left:2em;}

.objects-selection {
  height: 300px;
  overflow: auto;
  margin-bottom: 1em;
}

.objects-selection label {
  display: block;
}

.objects-selection>div, #user_group_ids {
  column-count: auto;
  column-width: 200px;
  -webkit-column-count: auto;
  -webkit-column-width: 200px;
  -webkit-column-gap : 0.5rem;
  -webkit-column-rule: 1px solid #ccc;
  -moz-column-count: auto;
  -moz-column-width: 200px;
  -moz-column-gap : 0.5rem;
  -moz-column-rule: 1px solid #ccc;
}

/***** Flash & error messages ****/
#errorExplanation, div.flash, .nodata, .warning, .conflict {
  padding: 6px 4px 6px 30px;
  margin-bottom: 12px;
  font-size: 1.1em;
  border: 1px solid;
  border-radius: 3px;
}

div.flash {margin-top: 8px;}

div.flash.error, #errorExplanation {
  background: url(../images/exclamation.png) 8px 50% no-repeat;
  background-color: #ffe3e3;
  border-color: #d88;
  color: #880000;
}

div.flash.notice {
  background: url(../images/true.png) 8px 5px no-repeat;
  background-color: #dfffdf;
  border-color: #9fcf9f;
  color: #005f00;
}

div.flash.warning, .conflict {
  background: url(../images/warning.png) 8px 5px no-repeat;
  background-color: #F3EDD1;
  border-color: #eadbbc;
  color: #A6750C;
  text-align: left;
}

.nodata, .warning {
  text-align: center;
  background-color: #F3EDD1;
  border-color: #eadbbc;
  color: #A6750C;
}

#errorExplanation ul { font-size: 0.9em;}
#errorExplanation h2, #errorExplanation p { display: none; }

.conflict-details {font-size:80%;}

/***** Ajax indicator ******/
#ajax-indicator {
position: absolute; /* fixed not supported by IE */
background-color:#eee;
border: 1px solid #bbb;
top:35%;
left:40%;
width:20%;
font-weight:bold;
text-align:center;
padding:0.6em;
z-index:100;
opacity: 0.5;
}

html>body #ajax-indicator { position: fixed; }

#ajax-indicator span {
background-position: 0% 40%;
background-repeat: no-repeat;
background-image: url(../images/loading.gif);
padding-left: 26px;
vertical-align: bottom;
}

/***** Calendar *****/
ul.cal {
  list-style: none;
  width: 100%;
  padding: 0;
  display: grid;
  grid-template-columns: 2rem repeat(7, 1fr);
  margin: 0;
  border: 1px solid #c0c0c0;
  border-spacing: 0;
  border-radius: 3px;
}

.cal .calhead {
  background-color:#eee;
  text-align: center;
  font-weight: bold;
  padding: 4px
}

.cal .week-number {
  background-color:#eee;
  border:none;
  font-size: 1em;
  padding: 4px;
  text-align: center;
}

.cal .week-number .label-week {
  display: none;
}

.cal .calbody {
  border: 1px solid #d7d7d7;
  vertical-align: top;
  font-size: 0.9em;
  border-bottom: 0;
  border-right: 0;
  line-height: 1.2;
  min-height: calc(1.2em * 6);
  padding: 2px;
}

.cal .calbody p.day-num {font-size: 1.1em; text-align:right;}
.cal .calbody .abbr-day {display:none}
.cal .calbody.odd p.day-num {color: #bbb;}
.cal .calbody.today {background:#ffd;}
.cal .calbody.today p.day-num {font-weight: bold;}

.cal .calbody .icon {padding-top: 2px; padding-bottom: 3px;}
.cal .calbody.nwday:not(.odd) {background-color:#f1f1f1;}
.cal .starting a.issue, p.cal.legend .starting {background: url(../images/bullet_go.png) no-repeat -1px -2px; padding-left:16px;}
.cal .ending a.issue, p.cal.legend .ending {background: url(../images/bullet_end.png) no-repeat -1px -2px; padding-left:16px;}
.cal .starting.ending a.issue, p.cal.legend .starting.ending {background: url(../images/bullet_diamond.png) no-repeat -1px -2px; padding-left:16px;}

p.cal.legend span {display:block;}
.controller-calendars p.buttons {margin-top: unset;}

/***** Tooltips ******/
.tooltip{position:relative;z-index:24;}
.tooltip:hover{z-index:25;color:#000;}
.tooltip span.tip{display: none; text-align:left;}
.tooltip span.tip a { color: #169 !important; }

.tooltip span.tip img.gravatar {
  float: none;
  margin: 0;
}

div.tooltip:hover span.tip{
display:block;
position:absolute;
top:12px; width:270px;
border:1px solid #555;
background-color:#fff;
padding: 4px;
font-size: 0.8em;
color:#505050;
}

table.cal div.tooltip:hover span.tip {
  top: 25px;
}

img.ui-datepicker-trigger {
  cursor: pointer;
  vertical-align: middle;
  margin-left: 4px;
}

/***** Documents *****/

#document-list .document-group {
  margin-bottom: 15px;
}

/***** Progress bar *****/
table.progress {
  border-collapse: collapse;
  border-spacing: 0pt;
  empty-cells: show;
  text-align: center;
  float:left;
  margin: 1px 6px 1px 0px;
  width:80px;
}

table.progress td { height: 1em; }
table.progress td.closed { background: #BAE0BA none repeat scroll 0%; }
table.progress td.done { background: #D3EDD3 none repeat scroll 0%; }
table.progress td.todo { background: #eee none repeat scroll 0%; }
p.percent {font-size: 80%; margin:0;}
p.progress-info {clear: left; font-size: 80%; margin-top:-4px; color:#777;}

.version-overview table.progress {width:40em;}
.version-overview table.progress td { height: 1.2em; }

/***** Tabs *****/
#content .tabs {height: 2.6em; margin-bottom:1.2em; position:relative; overflow:hidden;}
#content .tabs ul {margin:0; position:absolute; bottom:0; padding-left:0.5em; min-width: 2000px; width: 100%;  border-bottom: 1px solid #bbbbbb;}
#content .tabs ul li {
  float:left;
  list-style-type:none;
  white-space:nowrap;
  margin-right:4px;
  position:relative;
  margin-bottom:-1px;
}
#content .tabs ul li a{
  display:block;
  font-size: 0.9em;
  text-decoration:none;
  line-height:1.3em;
  padding:4px 6px 4px 6px;
  border: 1px solid #ccc;
  border-bottom: 1px solid #bbbbbb;
  color:#999;
  font-weight:bold;
  border-top-left-radius:3px;
  border-top-right-radius:3px;
}

#content .tabs ul li a:hover {
  color:#777;
  text-decoration:none;
}

#content .tabs ul li a.selected {
  background-color: #fff;
  border: 1px solid #bbbbbb;
  border-bottom: 1px solid #fff;
  color:#444;
}

#content .tabs ul li a.selected:hover {background-color: #fff;}

div.tabs-buttons { position:absolute; right: 0; width: 54px; height: 24px; background: white; bottom: 0; border-bottom: 1px solid #bbbbbb; }

button.tab-left, button.tab-right {
  font-size: 0.9em;
  cursor: pointer;
  height:24px;
  border: 1px solid #ccc;
  border-bottom: 1px solid #bbbbbb;
  position:absolute;
  padding:4px;
  width: 24px;
  bottom: -1px;
}
button.tab-left:hover, button.tab-right:hover {
  background-color: #f5f5f5;
}
button.tab-left:focus, button.tab-right:focus {
  outline: 0;
}

button.tab-left {
  right: 28px;
  background: #eeeeee url(../images/arrow_left.png) no-repeat 50% 50%;
  border-top-left-radius:3px;
}

button.tab-right {
  right: 4px;
  background: #eeeeee url(../images/arrow_right.png) no-repeat 50% 50%;
  border-top-right-radius:3px;
}

button.tab-left.disabled, button.tab-right.disabled {
  background-color: #ccc;
  cursor: unset;
}

/***** Diff *****/
.diff_out { background: #fcc; }
.diff_out span { background: #faa; }
.diff_in { background: #cfc; }
.diff_in span { background: #afa; }

.text-diff {
  padding: 1em;
  background-color:#f6f6f6;
  color:#505050;
  border: 1px solid #e4e4e4;
  white-space: pre-wrap;
}

/***** Wiki *****/
div.wiki table {
  border-collapse: collapse;
  margin-bottom: 1em;
}

div.wiki table, div.wiki td, div.wiki th {
  border: 1px solid #bbb;
  padding: 4px;
}

div.wiki .wiki-class-noborder, div.wiki .wiki-class-noborder td, div.wiki .wiki-class-noborder th {border:0;}

div.wiki .external {
  background-position: 0% 60%;
  background-repeat: no-repeat;
  padding-left: 12px;
  background-image: url(../images/external.png);
}

div.wiki a {word-wrap: break-word;}
div.wiki a.new {color: #b73535;}

div.wiki ul, div.wiki ol {margin-bottom:1em;}
div.wiki li>ul, div.wiki li>ol {margin-bottom: 0;}

div.wiki pre {
  margin: 1em 1em 1em 1.6em;
  padding: 8px;
  background-color: #fafafa;
  border: 1px solid #e2e2e2;
  border-radius: 3px;
  width:auto;
  overflow-x: auto;
  overflow-y: hidden;
}

div.wiki *:not(pre)>code, div.wiki>code {
  background: rgba(62, 91, 118, 0.08);
  padding: 0.1em 0.1em;
  border-radius: 0.1em;
}

div.wiki ul.toc {
  background-color: #ffffdd;
  border: 1px solid #e4e4e4;
  padding: 4px;
  line-height: 1.2em;
  margin-bottom: 12px;
  margin-right: 12px;
  margin-left: 0;
  display: table
}
* html div.wiki ul.toc { width: 50%; } /* IE6 doesn't autosize div */

div.wiki ul.toc.right { float: right; margin-left: 12px; margin-right: 0; width: auto; }
div.wiki ul.toc.left  { float: left; margin-right: 12px; margin-left: 0; width: auto; }
div.wiki ul.toc ul { margin: 0; padding: 0; }
div.wiki ul.toc li {list-style-type:none; margin: 0; font-size:12px;}
div.wiki ul.toc>li:first-child {margin-bottom: .5em; color: #777;}
div.wiki ul.toc li li {margin-left: 1.5em; font-size:10px;}
div.wiki ul.toc a {
  font-size: 0.9em;
  font-weight: normal;
  text-decoration: none;
  color: #606060;
}
div.wiki ul.toc a:hover { color: #c61a1a; text-decoration: underline;}

a.wiki-anchor { display: none; margin-left: 6px; text-decoration: none; }
a.wiki-anchor:hover { color: #aaa !important; text-decoration: none; }
h1:hover a.wiki-anchor, h2:hover a.wiki-anchor, h3:hover a.wiki-anchor, h4:hover a.wiki-anchor, h5:hover a.wiki-anchor, h6:hover a.wiki-anchor { display: inline; color: #ddd; }

div.wiki img {vertical-align:middle; max-width:100%;}
div.wiki>.task-list {
  padding-left: 0px;
}
div.wiki .task-list {
  list-style-type: none;
}
div.wiki .task-list input.task-list-item-checkbox {
  height: initial;
}

/***** My page layout *****/
.block-receiver {
  border:1px dashed #fff;
  padding: 15px 0 0 0;
}
.dragging .block-receiver {
  border:1px dashed #777;
  margin-bottom: 20px;
}
.mypage-box {
  border:1px solid #ddd;
  padding:8px;
  margin:0 0 20px 0;
  color:#505050;
  line-height:1.5em;
  border-radius: 3px;
}

.mypage-box>.contextual {opacity:0.001; transition: opacity 0.2s;}
.mypage-box:hover>.contextual {opacity:1;}

.handle {cursor: move;}

#my-page .list th.checkbox, #my-page .list td.checkbox {display:none;}
/***** Gantt chart *****/
table.gantt-table {
  width: 100%;
  border-collapse: collapse;
}
table.gantt-table td {
  padding: 0px;
}
.gantt_hdr {
  position:absolute;
  top:0;
  height:16px;
  border-top: 1px solid #c0c0c0;
  border-bottom: 1px solid #c0c0c0;
  border-left: 1px solid #c0c0c0;
  text-align: center;
  overflow: hidden;
}
#gantt_area .gantt_hdr {
  border-left: 0px;
  border-right: 1px solid #c0c0c0;
}
.gantt_subjects_container:not(.draw_selected_columns) .gantt_hdr,
.last_gantt_selected_column .gantt_hdr {
  border-right: 1px solid #c0c0c0;
}
.last_gantt_selected_column .gantt_selected_column_container,
.gantt_subjects_container .gantt_subjects * {
  z-index: 10;
}

.gantt_subjects_column + td {
  padding: 0;
}

.gantt_hdr.nwday {background-color:#f1f1f1; color:#999;}

.gantt_subjects,
.gantt_selected_column_content.gantt_hdr {
  font-size: 0.8em;
  position: relative;
  z-index: 1;
}
.gantt_subjects div,
.gantt_selected_column_content div {
  line-height: 16px;
  height: 16px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
}
.gantt_subjects div.issue-subject:hover { background-color:#ffffdd; }
.gantt_selected_column_content { padding-left: 3px; padding-right: 3px;}
.gantt_subjects .issue-subject img.icon-gravatar {
  margin: 2px 5px 0px 2px;
}
.gantt_hdr_selected_column_name {
  position: absolute;
  top: 50%;
  width:100%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  font-size: 0.8em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

}
td.gantt_selected_column {
  width: 50px;
}
td.gantt_selected_column .gantt_hdr,.gantt_selected_column_container {
  width: 49px;
}

.task {
  position: absolute;
  height:8px;
  font-size:0.8em;
  color:#888;
  padding:0;
  margin:0;
  line-height:16px;
  white-space:nowrap;
}

.task.label {width:100%;}
.task.label.project, .task.label.version { font-weight: bold; }

.task_late { background:#f66 url(../images/task_late.png); border: 1px solid #f66; }
.task_done { background:#00c600 url(../images/task_done.png); border: 1px solid #00c600; }
.task_todo { background:#aaa url(../images/task_todo.png); border: 1px solid #aaa; }

.task_todo.parent { background: #888; border: 1px solid #888; height: 3px;}
.task_late.parent, .task_done.parent { height: 3px;}
.task.parent.marker.starting  { position: absolute; background: url(../images/task_parent_end.png) no-repeat 0 0; width: 8px; height: 16px; margin-left: -4px; left: 0px; top: -1px;}
.task.parent.marker.ending { position: absolute; background: url(../images/task_parent_end.png) no-repeat 0 0; width: 8px; height: 16px; margin-left: -4px; right: 0px; top: -1px;}

.version.task_late { background:#f66 url(../images/milestone_late.png); border: 1px solid #f66; height: 2px; margin-top: 3px;}
.version.task_done { background:#00c600 url(../images/milestone_done.png); border: 1px solid #00c600; height: 2px; margin-top: 3px;}
.version.task_todo { background:#fff url(../images/milestone_todo.png); border: 1px solid #fff; height: 2px; margin-top: 3px;}
.version.marker { background-image:url(../images/version_marker.png); background-repeat: no-repeat; border: 0; margin-left: -4px; margin-top: 1px; }

.project.task_late { background:#f66 url(../images/milestone_late.png); border: 1px solid #f66; height: 2px; margin-top: 3px;}
.project.task_done { background:#00c600 url(../images/milestone_done.png); border: 1px solid #00c600; height: 2px; margin-top: 3px;}
.project.task_todo { background:#fff url(../images/milestone_todo.png); border: 1px solid #fff; height: 2px; margin-top: 3px;}
.project.marker { background-image:url(../images/project_marker.png); background-repeat: no-repeat; border: 0; margin-left: -4px; margin-top: 1px; }

.version-behind-schedule a, .issue-behind-schedule a {color: #f66914;}
.version-overdue a, .issue-overdue a, .project-overdue a {color: #f00;}

/***** Badges *****/
.badge {
  position:relative;
  font-weight:bold;
  font-size: 10px;
  bottom: 2px;
  padding: 1px 3px;
  margin-right: 2px;
  margin-left: 2px;
  border-radius: 2px;
  text-transform: uppercase;
  text-decoration: none;
}
.badge-private {
  background: #d22;
  color: #fff;
  border: 1px solid #d22;
}
.badge-count {
  color: #fff;
  background:#9DB9D5;
}
.badge-status-open {
  color: #205D86;
  border: 1px solid #205D86;
}
.badge-status-locked {
  color: #696969;
  border: 1px solid #696969;
}
.badge-status-closed {
  color: #1D781D;
  border: 1px solid #1D781D;
}
.badge-issues-count {
  background: #EEEEEE;
}

/***** Tooltips *****/
.ui-tooltip {
  background: #000;
  color: #fff;
  border-radius: 3px;
  border: 0;
  box-shadow: none;
  white-space: pre-wrap;
}

/***** Icons *****/
.icon {
  background-position: 0% 50%;
  background-repeat: no-repeat;
  padding-left: 20px;
}
.icon-only {
  background-position: 0% 50%;
  background-repeat: no-repeat;
  padding-left: 16px;
  display: inline-block;
  width: 0;
  height: 16px;
  overflow: hidden;
  padding-top: 0;
  padding-bottom: 0;
  font-size: 8px;
  vertical-align: middle;
}
.icon-only::after {
  content: "\a0";
}

.icon-add { background-image: url(../images/add.png); }
.icon-edit { background-image: url(../images/edit.png); }
.icon-copy { background-image: url(../images/copy.png); }
.icon-duplicate { background-image: url(../images/duplicate.png); }
.icon-del { background-image: url(../images/delete.png); }
.icon-move { background-image: url(../images/move.png); }
.icon-save { background-image: url(../images/save.png); }
.icon-download { background-image: url(../images/download.png); }
.icon-cancel { background-image: url(../images/cancel.png); }
.icon-multiple { background-image: url(../images/table_multiple.png); }
.icon-folder { background-image: url(../images/folder.png); }
.open .icon-folder { background-image: url(../images/folder_open.png); }
.icon-package { background-image: url(../images/package.png); }
.icon-user { background-image: url(../images/user.png); }
.icon-project, .icon-projects { background-image: url(../images/projects.png); }
.icon-help { background-image: url(../images/help.png); }
.icon-attachment  { background-image: url(../images/attachment.png); }
.icon-history  { background-image: url(../images/history.png); }
.icon-time-entry, .icon-time  { background-image: url(../images/time.png); }
.icon-time-add  { background-image: url(../images/time_add.png); }
.icon-stats  { background-image: url(../images/stats.png); }
.icon-warning  { background-image: url(../images/warning.png); }
.icon-error { background-image: url(../images/exclamation.png); }
.icon-fav  { background-image: url(../images/fav.png); }
.icon-fav-off  { background-image: url(../images/fav_off.png); }
.icon-reload  { background-image: url(../images/reload.png); }
.icon-lock, .icon-locked  { background-image: url(../images/locked.png); }
.icon-unlock  { background-image: url(../images/unlock.png); }
.icon-checked  { background-image: url(../images/toggle_check.png); }
.icon-report  { background-image: url(../images/report.png); }
.icon-comment, .icon-comments  { background-image: url(../images/comment.png); }
.icon-summary  { background-image: url(../images/lightning.png); }
.icon-server-authentication { background-image: url(../images/server_key.png); }
.icon-issue { background-image: url(../images/ticket.png); }
.icon-zoom-in { background-image: url(../images/zoom_in.png); }
.icon-zoom-out { background-image: url(../images/zoom_out.png); }
.icon-magnifier { background-image: url(../images/magnifier.png); }
.icon-passwd { background-image: url(../images/textfield_key.png); }
.icon-arrow-right, .icon-test, .icon-sticky { background-image: url(../images/bullet_go.png); }
.icon-email { background-image: url(../images/email.png); }
.icon-email-disabled { background-image: url(../images/email_disabled.png); }
.icon-email-add { background-image: url(../images/email_add.png); }
.icon-ok { background-image: url(../images/true.png); }
.icon-not-ok { background-image: url(../images/false.png); }
.icon-link-break { background-image: url(../images/link_break.png); }
.icon-list { background-image: url(../images/text_list_bullets.png); }
.icon-close { background-image: url(../images/close.png); }
.icon-close:hover { background-image: url(../images/close_hl.png); }
.icon-settings { background-image: url(../images/changeset.png); }
.icon-group, .icon-groupnonmember, .icon-groupanonymous { background-image: url(../images/group.png); }
.icon-roles { background-image: url(../images/database_key.png); }
.icon-issue-edit { background-image: url(../images/ticket_edit.png); }
.icon-workflows { background-image: url(../images/ticket_go.png); }
.icon-custom-fields { background-image: url(../images/textfield.png); }
.icon-plugins { background-image: url(../images/plugin.png); }
.icon-news { background-image: url(../images/news.png); }
.icon-issue-closed { background-image: url(../images/ticket_checked.png); }
.icon-issue-note { background-image: url(../images/ticket_note.png); }
.icon-changeset { background-image: url(../images/changeset.png); }
.icon-message { background-image: url(../images/message.png); }
.icon-reply { background-image: url(../images/comments.png); }
.icon-wiki-page { background-image: url(../images/wiki_edit.png); }
.icon-document { background-image: url(../images/document.png); }
.icon-project { background-image: url(../images/projects.png); }
.icon-add-bullet { background-image: url(../images/bullet_add.png); }
.icon-shared { background-image: url(../images/link.png); }
.icon-actions { background-image: url(../images/3_bullets.png); }
.icon-sort-handle { background-image: url(../images/reorder.png); }
.icon-expanded { background-image: url(../images/arrow_down.png); }
.icon-collapsed { background-image: url(../images/arrow_right.png); }
.icon-bookmark { background-image: url(../images/tag_blue_delete.png); }
.icon-bookmark-off { background-image: url(../images/tag_blue_add.png); }
.icon-bookmarked-project { background-image: url(../images/tag_blue.png); }
.icon-sorted-asc { background-image: url(../images/arrow_down.png); }
.icon-sorted-desc { background-image: url(../images/arrow_up.png); }
.icon-toggle-plus { background-image: url(../images/bullet_toggle_plus.png) }
.icon-toggle-minus { background-image: url(../images/bullet_toggle_minus.png) }
.icon-clear-query { background-image: url(../images/close_hl.png); }
.icon-import { background-image: url(../images/database_go.png); }

.icon-file { background-image: url(../images/files/default.png); }
.icon-file.text-plain { background-image: url(../images/files/text.png); }
.icon-file.text-x-c { background-image: url(../images/files/c.png); }
.icon-file.text-x-csharp { background-image: url(../images/files/csharp.png); }
.icon-file.text-x-java { background-image: url(../images/files/java.png); }
.icon-file.application-javascript { background-image: url(../images/files/js.png); }
.icon-file.text-x-php { background-image: url(../images/files/php.png); }
.icon-file.text-x-ruby { background-image: url(../images/files/ruby.png); }
.icon-file.text-xml { background-image: url(../images/files/xml.png); }
.icon-file.text-css { background-image: url(../images/files/css.png); }
.icon-file.text-html { background-image: url(../images/files/html.png); }
.icon-file.image-gif { background-image: url(../images/files/image.png); }
.icon-file.image-jpeg { background-image: url(../images/files/image.png); }
.icon-file.image-png { background-image: url(../images/files/image.png); }
.icon-file.image-tiff { background-image: url(../images/files/image.png); }
.icon-file.application-pdf { background-image: url(../images/files/pdf.png); }
.icon-file.application-zip { background-image: url(../images/files/zip.png); }
.icon-file.application-gzip { background-image: url(../images/files/zip.png); }
.icon-copy-link { background-image: url(../images/copy_link.png); }

.sort-handle.ajax-loading { background-image: url(../images/loading.gif); }
tr.ui-sortable-helper { border:1px solid #e4e4e4; }

.contextual>*:not(:first-child), .buttons>.icon:not(:first-child), .contextual .journal-actions>*:not(:first-child) { margin-left: 5px; }

img.gravatar {
  vertical-align: middle;
  border-radius: 20%;
}

div.issue img.gravatar {
  float: left;
  margin: 0 12px 6px 0;
}

div.gravatar-with-child {
  position: relative;
}

div.gravatar-with-child > img.gravatar:nth-child(2) {
  position: absolute;
  top: 30px;
  left: 30px;
  border-radius: 20%;
  border: 2px solid rgba(255, 255, 255, 0.9);
}

h2 img.gravatar, h3 img.gravatar {margin-right: 4px;}
h4 img.gravatar {margin: -2px 4px -4px 0;}
td.username img.gravatar {margin: 0 0.5em 0 0; vertical-align: top;}
#activity dt img.gravatar {float: left; margin: 0 1em 1em 0;}
/* Used on 12px Gravatar img tags without the icon background */
.icon-gravatar {float: left; margin-right: 4px;}

#activity dt, .journal {clear: left;}

h2 img { vertical-align:middle; }

.hascontextmenu { cursor: context-menu; }

.sample-data {border:1px solid #ccc; border-collapse:collapse; background-color:#fff; margin:0.5em;}
.sample-data td {border:1px solid #ccc; padding: 2px 4px; font-family: Consolas, Menlo, "Liberation Mono", Courier, monospace;}
.sample-data tr:first-child td {font-weight:bold; text-align:center;}

.ui-progressbar {position: relative;}
#progress-label {
position: absolute; left: 50%; top: 4px;
font-weight: bold;
color: #555; text-shadow: 1px 1px 0 #fff;
}

.repository-graph {width:75%; margin-bottom:2em;}

img.filecontent.image {background-image: url(../images/transparent.png);}

/* Custom JQuery styles */
.ui-autocomplete, .ui-menu {
  border-radius: 2px;
  border: 1px solid #ccc;
}
.ui-autocomplete .ui-menu-item > div, .ui-menu .ui-menu-item > div {
  padding: 4px 8px;
  max-width: 500px;
}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
  border-color: #759FCF;
  background: #759FCF;
}
.ui-widget-overlay {
    background: #000;
    opacity: 70%;
}

/* Custom tribute styles */
.tribute-container ul {
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 2px;
}
.tribute-container li.highlight {background-color: #759FCF; color:#fff;}

/************* Rouge styles *************/
/* generated by: pygmentize -f html -a .syntaxhl -S colorful */
.syntaxhl .hll { background-color: #ffffcc }
.syntaxhl  { background: #fafafa; }
.syntaxhl .c { color: #888888 } /* Comment */
.syntaxhl .err { color: #FF0000; background-color: #FFAAAA } /* Error */
.syntaxhl .k { color: #008800; font-weight: bold } /* Keyword */
.syntaxhl .o { color: #333333 } /* Operator */
.syntaxhl .ch { color: #888888 } /* Comment.Hashbang */
.syntaxhl .cm { color: #888888 } /* Comment.Multiline */
.syntaxhl .cp { color: #557799 } /* Comment.Preproc */
.syntaxhl .cpf { color: #888888 } /* Comment.PreprocFile */
.syntaxhl .c1 { color: #888888 } /* Comment.Single */
.syntaxhl .cs { color: #cc0000; font-weight: bold } /* Comment.Special */
.syntaxhl .gd { color: #A00000 } /* Generic.Deleted */
.syntaxhl .ge { font-style: italic } /* Generic.Emph */
.syntaxhl .gr { color: #FF0000 } /* Generic.Error */
.syntaxhl .gh { color: #000080; font-weight: bold } /* Generic.Heading */
.syntaxhl .gi { color: #00A000 } /* Generic.Inserted */
.syntaxhl .go { color: #888888 } /* Generic.Output */
.syntaxhl .gp { color: #c65d09; font-weight: bold } /* Generic.Prompt */
.syntaxhl .gs { font-weight: bold } /* Generic.Strong */
.syntaxhl .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
.syntaxhl .gt { color: #0044DD } /* Generic.Traceback */
.syntaxhl .kc { color: #008800; font-weight: bold } /* Keyword.Constant */
.syntaxhl .kd { color: #008800; font-weight: bold } /* Keyword.Declaration */
.syntaxhl .kn { color: #008800; font-weight: bold } /* Keyword.Namespace */
.syntaxhl .kp { color: #003388; font-weight: bold } /* Keyword.Pseudo */
.syntaxhl .kr { color: #008800; font-weight: bold } /* Keyword.Reserved */
.syntaxhl .kt { color: #333399; font-weight: bold } /* Keyword.Type */
.syntaxhl .m { color: #6600EE; font-weight: bold } /* Literal.Number */
.syntaxhl .s { background-color: #fff0f0 } /* Literal.String */
.syntaxhl .na { color: #0000CC } /* Name.Attribute */
.syntaxhl .nb { color: #007020 } /* Name.Builtin */
.syntaxhl .nc { color: #BB0066; font-weight: bold } /* Name.Class */
.syntaxhl .no { color: #003366; font-weight: bold } /* Name.Constant */
.syntaxhl .nd { color: #555555; font-weight: bold } /* Name.Decorator */
.syntaxhl .ni { color: #880000; font-weight: bold } /* Name.Entity */
.syntaxhl .ne { color: #FF0000; font-weight: bold } /* Name.Exception */
.syntaxhl .nf { color: #0066BB; font-weight: bold } /* Name.Function */
.syntaxhl .nl { color: #997700; font-weight: bold } /* Name.Label */
.syntaxhl .nn { color: #0e84b5; font-weight: bold } /* Name.Namespace */
.syntaxhl .nt { color: #007700 } /* Name.Tag */
.syntaxhl .nv { color: #996633 } /* Name.Variable */
.syntaxhl .ow { color: #000000; font-weight: bold } /* Operator.Word */
.syntaxhl .w { color: #bbbbbb } /* Text.Whitespace */
.syntaxhl .mb { color: #6600EE; font-weight: bold } /* Literal.Number.Bin */
.syntaxhl .mf { color: #6600EE; font-weight: bold } /* Literal.Number.Float */
.syntaxhl .mh { color: #005588; font-weight: bold } /* Literal.Number.Hex */
.syntaxhl .mi { color: #0000DD; font-weight: bold } /* Literal.Number.Integer */
.syntaxhl .mo { color: #4400EE; font-weight: bold } /* Literal.Number.Oct */
.syntaxhl .sa { background-color: #fff0f0 } /* Literal.String.Affix */
.syntaxhl .sb { background-color: #fff0f0 } /* Literal.String.Backtick */
.syntaxhl .sc { color: #0044DD } /* Literal.String.Char */
.syntaxhl .dl { background-color: #fff0f0 } /* Literal.String.Delimiter */
.syntaxhl .sd { color: #DD4422 } /* Literal.String.Doc */
.syntaxhl .s2 { background-color: #fff0f0 } /* Literal.String.Double */
.syntaxhl .se { color: #666666; font-weight: bold; background-color: #fff0f0 } /* Literal.String.Escape */
.syntaxhl .sh { background-color: #fff0f0 } /* Literal.String.Heredoc */
.syntaxhl .si { background-color: #eeeeee } /* Literal.String.Interpol */
.syntaxhl .sx { color: #DD2200; background-color: #fff0f0 } /* Literal.String.Other */
.syntaxhl .sr { color: #000000; background-color: #fff0ff } /* Literal.String.Regex */
.syntaxhl .s1 { background-color: #fff0f0 } /* Literal.String.Single */
.syntaxhl .ss { color: #AA6600 } /* Literal.String.Symbol */
.syntaxhl .bp { color: #007020 } /* Name.Builtin.Pseudo */
.syntaxhl .fm { color: #0066BB; font-weight: bold } /* Name.Function.Magic */
.syntaxhl .vc { color: #336699 } /* Name.Variable.Class */
.syntaxhl .vg { color: #dd7700; font-weight: bold } /* Name.Variable.Global */
.syntaxhl .vi { color: #3333BB } /* Name.Variable.Instance */
.syntaxhl .vm { color: #996633 } /* Name.Variable.Magic */
.syntaxhl .il { color: #0000DD; font-weight: bold } /* Literal.Number.Integer.Long */

/***** Media print specific styles *****/
@media print {
  #top-menu, #header, #main-menu, #sidebar, #footer, .contextual, .other-formats { display:none; }
  #main { background: #fff; }
  #content { width: 99%; margin: 0; padding: 0; border: 0; background: #fff; overflow: visible !important;}
  #wiki_add_attachment { display:none; }
  .hide-when-print, .pagination ul.pages, .pagination .per-page { display: none !important; }
  .autoscroll {overflow-x: visible;}
  table.list {margin-top:0.5em;}
  table.list th, table.list td {border: 1px solid #aaa;}
}

/* Accessibility specific styles */
.hidden-for-sighted {
  position:absolute;
  left:-10000px;
  top:auto;
  width:1px;
  height:1px;
  overflow:hidden;
}

img {
  image-orientation: from-image;
}

.filecontent-container {
  position: relative;
  margin-bottom: 20px;
  min-height: 200px;
}
.filecontent-container > .filecontent {
  position: absolute;
  max-height: 100%;
  max-width: 100%;
}

.filecontent.wiki {
  position: relative;
  padding: 1em;
  border: 1px solid #e4e4e4;
  border-radius: 3px;
}

/* Fixes for IE 11 */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  select::-ms-expand {
    display: none;
  }
  select[multiple=multiple] {padding-right: 0;}
}

/* tablesort */
th[role=columnheader]:not(.no-sort) {
	cursor: pointer;
}

th[role=columnheader]:not(.no-sort):after {
	content: '';
	float: right;
	margin-top: 7px;
	border-width: 0 4px 4px;
	border-style: solid;
	border-color: #404040 transparent;
	display: none;
	opacity: 0;
	-ms-user-select: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;
}

th[aria-sort=ascending]:not(.no-sort):after {
	border-bottom: none;
	border-width: 4px 4px 0;
}

th[aria-sort]:not(.no-sort):after {
	display: inline;
	opacity: 0.4;
}

th[role=columnheader]:not(.no-sort):hover:after {
	display: inline;
	opacity: 1;
}
/***** General Styles *****/
body {
    font-family: 'Arial', sans-serif; /* Changed to a softer font */
    color: #333; /* Softer text color */
    background-color: #f9f9f9; /* Light background for a flat look */
  }
  
  /***** Links *****/
  a, a:link, a:visited {
    color: #007BFF; /* Flat blue color */
    text-decoration: none;
  }
  a:hover, a:active {
    color: #0056b3; /* Darker shade for hover */
    text-decoration: underline;
  }
  
  /***** Buttons *****/
  button, input[type="button"], input[type="submit"] {
    background-color: #007BFF; /* Flat button color */
    color: #fff; /* White text */
    border: none; /* No border for flat design */
    border-radius: 4px; /* Slightly rounded corners */
    padding: 10px 15px; /* Padding for buttons */
    cursor: pointer; /* Pointer cursor */
  }
  button:hover, input[type="button"]:hover, input[type="submit"]:hover {
    background-color: #0056b3; /* Darker shade on hover */
  }
  
  /***** Tables *****/
  table {
    border-collapse: collapse; /* Remove spacing between cells */
    width: 100%; /* Full width */
    margin-bottom: 1em; /* Space below tables */
  }
  table th, table td {
    padding: 10px; /* Padding for table cells */
    border: 1px solid #ddd; /* Light border for flat look */
  }
  table th {
    background-color: #f1f1f1; /* Light gray for headers */
    color: #333; /* Darker text for headers */
  }
  
  /***** Form Elements *****/
  input, select, textarea {
    border: 1px solid #ccc; /* Light border */
    border-radius: 4px; /* Rounded corners */
    padding: 8px; /* Padding for inputs */
    width: 100%; /* Full width */
    box-sizing: border-box; /* Include padding in width */
  }
  
  /***** Notifications *****/
  .flash {
    border-radius: 4px; /* Rounded corners for notifications */
    padding: 10px; /* Padding for notifications */
    margin-bottom: 1em; /* Space below notifications */
  }
  .flash.error {
    background-color: #f8d7da; /* Soft red for errors */
    color: #721c24; /* Darker text for contrast */
  }
  .flash.notice {
    background-color: #d1ecf1; /* Soft blue for notices */
    color: #0c5460; /* Darker text for contrast */
  }
  
  /***** Footer *****/
  footer {
    background-color: #007BFF; /* Flat footer color */
    color: #fff; /* White text */
    padding: 20px; /* Padding for footer */
    text-align: center; /* Centered text */
  }
  
  /***** Responsive Design *****/
  @media (max-width: 768px) {
    /* Adjustments for smaller screens */
    body {
      font-size: 14px; /* Slightly smaller font */
    }
  }