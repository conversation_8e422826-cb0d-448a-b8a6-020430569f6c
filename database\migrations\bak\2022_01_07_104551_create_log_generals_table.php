<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLogGeneralsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('logs_general', function (Blueprint $table) {
            $table->string('id');
            $table->integer('action');
            $table->integer('object_type');
            $table->string('object_id');
            $table->string('created_by')->nullable();
            $table->text('data_response');
            $table->text('data_request')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('log_generals');
    }
}
