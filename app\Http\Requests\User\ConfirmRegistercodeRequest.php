<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class ConfirmRegistercodeRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'email' => 'bail|required|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u',
            'key' => 'required'
        ];
    }
    // public function messages()
    // {
    //     return [
    //     'email.required' => 'User_001_E_013',
    //     'email.unique' => 'User_005_E_014',
    //     'email.regex' => 'User_002_E_015',
    //     'email.max' => 'User_004_E_016',

    //     'key.required' => 'User_001_E_035',
    //     ];
    // }
}
