<?php

namespace App\Services\Stock;

use App\Product;
use App\StockTracking;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockService
{
    public function __construct()
    {
        // Service only handles business logic, no user context needed
    }

    /**
     * Import stock (Purchase)
     *
     * @param array $data
     * @param string $userId
     * @return array
     */
    public function importStock(array $data, string $userId)
    {
        try {
            DB::beginTransaction();

            $product = Product::find($data['product_id']);
            if (!$product) {
                return ['error' => 'Product not found'];
            }

            $oldStock = $product->stock ?? 0;
            $quantity = $data['quantity'];
            $newStock = $oldStock + $quantity;

            // Update product stock
            $product->stock = $newStock;
            $product->save();

            // Record stock tracking
            $stockTracking = StockTracking::recordImport(
                $product->id,
                $oldStock,
                $newStock,
                $userId,
                $data['unit'] ?? null,
                $data['unit_price'] ?? null,
                $data['note'] ?? null
            );

            // Log the operation for database backup
            $this->logStockOperation('import', $product, $data, $oldStock, $newStock, $userId);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Stock imported successfully',
                'data' => [
                    'product' => $product,
                    'stock_tracking' => $stockTracking,
                    'old_stock' => $oldStock,
                    'new_stock' => $newStock,
                    'change' => $quantity
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Stock import failed: ' . $e->getMessage());
            return ['error' => 'Failed to import stock: ' . $e->getMessage()];
        }
    }

    /**
     * Export stock (Sale)
     *
     * @param array $data
     * @param string $userId
     * @return array
     */
    public function exportStock(array $data, string $userId)
    {
        try {
            DB::beginTransaction();

            $product = Product::find($data['product_id']);
            if (!$product) {
                return ['error' => 'Product not found'];
            }

            $oldStock = $product->stock ?? 0;
            $quantity = $data['quantity'];

            // Check if there's enough stock
            if ($oldStock < $quantity) {
                return ['error' => 'Insufficient stock. Available: ' . $oldStock . ', Requested: ' . $quantity];
            }

            $newStock = $oldStock - $quantity;

            // Update product stock
            $product->stock = $newStock;
            $product->save();

            // Record stock tracking
            $stockTracking = StockTracking::recordExport(
                $product->id,
                $oldStock,
                $newStock,
                $userId,
                $data['order_id'] ?? null,
                $data['unit'] ?? null,
                $data['unit_price'] ?? null,
                $data['note'] ?? null
            );

            // Log the operation for database backup
            $this->logStockOperation('export', $product, $data, $oldStock, $newStock, $userId);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Stock exported successfully',
                'data' => [
                    'product' => $product,
                    'stock_tracking' => $stockTracking,
                    'old_stock' => $oldStock,
                    'new_stock' => $newStock,
                    'change' => -$quantity
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Stock export failed: ' . $e->getMessage());
            return ['error' => 'Failed to export stock: ' . $e->getMessage()];
        }
    }

    /**
     * Waste/Discard stock
     *
     * @param array $data
     * @param string $userId
     * @return array
     */
    public function wasteStock(array $data, string $userId)
    {
        try {
            DB::beginTransaction();

            $product = Product::find($data['product_id']);
            if (!$product) {
                return ['error' => 'Product not found'];
            }

            $oldStock = $product->stock ?? 0;
            $quantity = $data['quantity'];

            // Check if there's enough stock
            if ($oldStock < $quantity) {
                return ['error' => 'Insufficient stock. Available: ' . $oldStock . ', Requested: ' . $quantity];
            }

            $newStock = $oldStock - $quantity;

            // Update product stock
            $product->stock = $newStock;
            $product->save();

            // Record stock tracking
            $stockTracking = StockTracking::recordWaste(
                $product->id,
                $oldStock,
                $newStock,
                $userId,
                $data['unit'] ?? null,
                $data['reason'] ?? 'Waste/Discard'
            );

            // Log the operation for database backup
            $this->logStockOperation('waste', $product, $data, $oldStock, $newStock, $userId);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Stock waste recorded successfully',
                'data' => [
                    'product' => $product,
                    'stock_tracking' => $stockTracking,
                    'old_stock' => $oldStock,
                    'new_stock' => $newStock,
                    'change' => -$quantity
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Stock waste failed: ' . $e->getMessage());
            return ['error' => 'Failed to record stock waste: ' . $e->getMessage()];
        }
    }

    /**
     * Get stock history for a product
     *
     * @param string $productId
     * @return array
     */
    public function getStockHistory($productId)
    {
        $product = Product::find($productId);
        if (!$product) {
            return ['error' => 'Product not found'];
        }

        $history = StockTracking::getStockHistory($productId);

        return [
            'success' => true,
            'data' => [
                'product' => $product,
                'history' => $history
            ]
        ];
    }

    /**
     * Get daily stock summary for a shop
     *
     * @param string $shopId
     * @param string|null $date
     * @return array
     */
    public function getDailyStockSummary($shopId, $date = null)
    {
        $summary = StockTracking::getDailyStockSummary($shopId, $date);

        return [
            'success' => true,
            'data' => [
                'shop_id' => $shopId,
                'date' => $date ?? now()->format('Y-m-d'),
                'summary' => $summary
            ]
        ];
    }

    /**
     * Log stock operation for database backup purposes
     *
     * @param string $operation
     * @param Product $product
     * @param array $data
     * @param int $oldStock
     * @param int $newStock
     * @param string $userId
     */
    private function logStockOperation($operation, $product, $data, $oldStock, $newStock, $userId)
    {
        $logData = [
            'operation' => $operation,
            'product_id' => $product->id,
            'product_name' => $product->name,
            'shop_id' => $product->shop_id,
            'executor_id' => $userId,
            'old_stock' => $oldStock,
            'new_stock' => $newStock,
            'change' => $newStock - $oldStock,
            'quantity' => $data['quantity'] ?? null,
            'unit' => $data['unit'] ?? null,
            'unit_price' => $data['unit_price'] ?? null,
            'note' => $data['note'] ?? $data['reason'] ?? null,
            'timestamp' => now()->toDateTimeString()
        ];

        Log::channel('daily')->info('STOCK_OPERATION: ' . json_encode($logData));
    }
}
