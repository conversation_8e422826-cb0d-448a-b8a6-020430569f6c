<?php

namespace App\Http\Controllers\Api\v2;

use App\Http\Controllers\Controller;
use App\Helpers\S3Utils;
use App\Rating;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use FFMpeg\FFMpeg;
use FFMpeg\FFProbe;
use FFMpeg\Media\Video;
use Illuminate\Support\Str;
use App\Http\Requests\Image\ImageRequest;
use App\Http\Requests\Video\VideoRequest;
use Illuminate\Http\JsonResponse;
use App\File;
use App\Image;
use Illuminate\Support\Facades\Auth;

class VideoController extends Controller
{
    public function upload(VideoRequest $request)
    {

        $videoId = Str::uuid();
        // Save video file temporarily
        $video = file_get_contents($request->file('video'));
        $name = $request->get('name') ?? 'No name';
        $thumbnailTime = $request->get('thumbnail_time') ?? '00:00:03.000';
        $description = $request->get('description') ?? '';
        $localTempPath = public_path('videos/').'video_tmp_'.$videoId.'.mp4';
        file_put_contents($localTempPath, $video);

        $date = date('Ymd');

        $hlsDir = public_path('videos/'  . $videoId);
        $outputImagePath = public_path('videos/'  . $videoId.'_thumbnail.jpg');
        $S3Filedir = '/videos/'. $date ;
        if (!file_exists($hlsDir)) {
            mkdir($hlsDir, 0777, true);
        }

        // Path to save HLS playlist (m3u8 file)
        $hlsFile = $hlsDir . '/output.m3u8';

        try {
            $ffmpeg = FFMpeg::create([
                'ffmpeg.binaries' => '/usr/bin/ffmpeg',   // Adjust path as needed
                'ffprobe.binaries' => '/usr/bin/ffprobe',  // Adjust path as needed
            ]);

            // $ffprobe = FFProbe::create([
            //     'ffmpeg.binaries' => '/usr/bin/ffmpeg',
            //     'ffprobe.binaries' => '/usr/bin/ffprobe',
            // ]);

            // $duration = $ffprobe
            //     ->format($localTempPath) // extracts information about the video file
            //     ->get('duration');
            // $description.=$duration;


            // This command captures a thumbnail from the video at the specified time
            // and saves it as a JPEG image with a quality of 2
            if($thumbnailTime){
                $command = sprintf(
                    'ffmpeg -ss %s -i %s -vframes 1 -q:v 2 %s',
                    escapeshellarg($thumbnailTime),           // Time in HH:MM:SS.sss format
                    escapeshellarg($localTempPath),      // Path to the input video
                    escapeshellarg($outputImagePath) // Path for the output image
                );
                // Execute the command
                exec($command, $output, $returnVar);
                // Get the image contents
                $imageContents = file_get_contents($outputImagePath);
                // Upload the image to S3
                $thumbnailFileName = $S3Filedir .'/'. $videoId.'/thumbnail.jpg';
                $uploaded = S3Utils::upload($imageContents, $thumbnailFileName);

                // Get the public URL of the image
                $s3ImageUrl = S3Utils::url($thumbnailFileName);
                $image = [];
                $image['parent_id'] = $videoId;
                $image['object_type'] = 12;
                $image['title'] = $name;
                $image['path'] = $thumbnailFileName;
                $image['path_thumbnail'] = $thumbnailFileName;
                $profile_picture = $image['path'];
                $result = Image::create($image);

                // Delete the local temporary image file
                unlink($outputImagePath);
            }//end making thumbnail

            // Generate HLS output
            $hlsOutputPath = $hlsDir . '/segment_%03d.ts'; // HLS segment files
            $command = sprintf(
                'ffmpeg -i %s -hls_time 6 -hls_list_size 0 -f hls %s',
                escapeshellarg($localTempPath),
                escapeshellarg($hlsFile)
            );

            // Execute the command
            exec($command, $output, $returnVar);

            // Upload các file HLS lên Minio
            $files = glob($hlsDir . '/*'); // Lấy tất cả các files trong thư mục HLS

            foreach ($files as $file) {
                $fileName = basename($file);
                $filePath = $S3Filedir .'/'. $videoId .'/'. $fileName;
                S3Utils::upload(file_get_contents($file), $filePath);
            }

            // Lấy URL của file playlist .m3u8 trên Minio
            $playlistUrl = S3Utils::url($S3Filedir .'/'. $videoId .'/output.m3u8');

            // Xóa các file tạm sau khi upload
            unlink($localTempPath);
            array_map('unlink', glob("$hlsDir/*.*"));
            rmdir($hlsDir);

            if ($returnVar !== 0) {
                throw new \Exception("FFmpeg failed to execute: " . implode("\n", $output));
            }

        } catch (\Exception $e) {
                $response = [
                    'status' => JsonResponse::HTTP_BAD_REQUEST,
                    'message' => 'Video conversion failed: ' . $e->getMessage()
                ];
            return response()->json($response, JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $parent_id = $request->get('parent_id') ?? null;
        $object_type = $request->get('object_type') ?? null;

        $fileRecord = File::create([
            'id' => $videoId,
            'file_name' => $name,
            'file_type' => 'video',
            'file_path' => $playlistUrl,
            'view_total' => 0,
            'like_total' => 0,
            'description' => $description,
            'user_id' => Auth::user()->id,
            'parent_id' => $parent_id,
            'object_type' => $object_type,
        ]);


        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $fileRecord
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
    public function list()
    {
        $limit = request()->input('limit', 10);
        $offset = request()->input('offset', 0);
        $searchText = request()->input('search_text', '');

        $videos = File::where('file_type', 'tiktok')->with(['user'])
            ->when($searchText, function ($query) use ($searchText) {
                $query->where('file_name', 'like', '%' . $searchText . '%');
            })
            ->offset($offset)
            ->limit($limit)
            ->orderBy('created_at','desc')
            ->get();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $videos
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // Danh sách video đánh giá của sản phẩm
    public function listProductReviewVideos(Request $request){
        $data = $request->validate([
            'product_id' => 'required|string',
            'limit' => 'nullable|integer',
            'offset' => 'nullable|integer'
        ]);
        $limit = request()->input('limit', 10);
        $offset = request()->input('offset', 0);
        $productId = $request->get('product_id');

        $videos = Rating::on('pgsqlReplica')
            ->where('object_type', 2)
            ->where('object_id', $productId)
            ->with(['video'])
            ->offset($offset)
            ->limit($limit)
            ->get()
            ->pluck('video')
            ->flatten();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => ($videos)
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
