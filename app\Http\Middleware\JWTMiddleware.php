<?php

namespace App\Http\Middleware;

use Closure;
use <PERSON><PERSON><PERSON><PERSON>;
use Exception;
use <PERSON><PERSON>\JWTAuth\Http\Middleware\BaseMiddleware;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Exceptions\HttpResponseException;
use App\Token;
use Illuminate\Support\Facades\Redis;
use App\Helpers\Helper;
use Illuminate\Support\Facades\Auth;

class JWTMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        try {
            $authHeader = $request->header('Authorization');
            if (!$authHeader) {
                return response()->json(['status' => JsonResponse::HTTP_UNAUTHORIZED, 'body' => ['C_E_001']], JsonResponse::HTTP_UNAUTHORIZED);
            }

            $token = explode(' ', $authHeader);
            if (count($token) < 2) {
                return response()->json(['status' => JsonResponse::HTTP_UNAUTHORIZED, 'body' => ['C_E_002']], JsonResponse::HTTP_UNAUTHORIZED);
            }
            // Uncomment if you want to check if the user has a token
            // if(empty($user->token))
            // {
            //     return response()->json(['status' => JsonResponse::HTTP_UNAUTHORIZED, 'body' => ['C_E_003']], JsonResponse::HTTP_UNAUTHORIZED);
            // }
            // Kiểm tra token trong Redis trước
            $payload = JWTAuth::parseToken()->getPayload();
            $userId  = $payload->get('sub');
            $jti     = $payload->get('jti');
            # Check if token is blacklisted  dùng blacklist mặc định của JWT thì ko có expired time, và bị clear mất khi chạy artisan o:c
            $blacklistKey = env('APP_ENV').":".'token_blacklist:'.$jti;
            if (Redis::exists($blacklistKey)) {
                return response()->json(['status' => JsonResponse::HTTP_UNAUTHORIZED, 'body' => ['C_E_007']], JsonResponse::HTTP_UNAUTHORIZED);
            }
            $cacheKey = env('APP_ENV').":".'user_token:'.$userId;
            $cachedUser = Redis::get($cacheKey);
            if ($cachedUser) {
                // Nếu có trong cache, trả về người dùng từ cache
                $user = unserialize($cachedUser);
                Auth::setUser($user);
            } else {
                // Nếu không có trong cache, kiểm tra trong bảng token
                // $check = Token::on('pgsqlReplica')->where('token', $token[1])->first();
                // if (!$check) {
                //     return response()->json(['status' => JsonResponse::HTTP_UNAUTHORIZED, 'body' => ['C_E_003']], JsonResponse::HTTP_UNAUTHORIZED);
                // }
                $user = JWTAuth::parseToken()->authenticate();
                // Lưu người dùng vào cache
                Redis::setex($cacheKey, 3600, serialize($user)); // Lưu trong cache với thời gian sống 1 giờ
            }
        } catch (Exception $e) {
            // check token invalid
            if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
                return response()->json(['status' => JsonResponse::HTTP_UNAUTHORIZED, 'body' => ['C_E_005']], JsonResponse::HTTP_UNAUTHORIZED);
            }
            // check token expired
            else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
                return response()->json(['status' => JsonResponse::HTTP_UNAUTHORIZED, 'body' => ['C_E_004']], JsonResponse::HTTP_UNAUTHORIZED);
            }
            // check token exist
            else {
                return response()->json(['status' => JsonResponse::HTTP_UNAUTHORIZED, 'body' => ['C_E_006']], JsonResponse::HTTP_UNAUTHORIZED);
            }
        }

        return $next($request);
    }
}
