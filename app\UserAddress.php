<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class UserAddress extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'phone',
        'address',
        'ward_id',
        'district_id',
        'province_id',
        'latitude',
        'longitude',
        'title',
        'is_default',
        'note'
    ];
    protected $primaryKey = 'id';
    protected $table = 'addresses';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
    protected $hidden = ['updated_at', 'created_at'];

    public function users()
    {
        return $this->belongsTo(User::class,'user_id');
    }

    public function provinces()
    {
        return $this->belongsTo(Province::class,'province_id');
    }


    public function districts()
    {
        return $this->belongsTo(District::class,'district_id');
    }

    public function wards()
    {
        return $this->belongsTo(Ward::class,'ward_id');
    }

    public function images()
    {
        return $this->hasMany(Image::class, 'parent_id')->orderBy('index','desc');
    }
}
