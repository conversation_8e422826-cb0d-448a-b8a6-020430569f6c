<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\User;
use App\District;
use App\Ward;
use App\Shop;
use DB;

class AdminController extends Controller
{
    // php artisan admin:make OrderController --model='App\Order'
    public $breadcrumbs_admin = [
        [
            'name' => 'Admin',
            'href' => '/admin',
            'active' => true,
        ],
    ];

    public function index()
    {
        $breadcrumbs_admin = $this->breadcrumbs_admin;
        return view('admin.layout.app', ['breadcrumbs' => $breadcrumbs_admin]);
    }

    public function shops(){
        return Shop::where('enable',true)->select('id', DB::raw('name as text'))->get()->toArray();
    }
    public function shop_by_id(Request $request){
        $shopId = $request->input('q');
        $shop = Shop::find($shopId);
        if ($shop) {
            return response()->json(['results' => [$shop->latitude]]);
        } else {
            return response()->json(['results' => []]);
        }
    }
    public function districtByProvinceId(Request $request){
        return District::where('province_id',$request->q)->select('id', DB::raw('name as text'))->get()->toArray();
    }
    public function wardByDistrictId(Request $request){
        return Ward::where('district_id',$request->q)->select('id', DB::raw('name as text'))->get()->toArray();
    }
    public function users(){
        return User::where('enable',true)->select('id', DB::raw('name as text'))->get()->toArray();
    }
    public function agents(){
        return User::where('enable',true)->where('role_id', 4)->select('id', DB::raw('name as text'))->get()->toArray();
    }
}

