<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Setting extends Model
{
    protected $fillable = [
    	'key','value','description','reference_value',
        'object_type','show_on_site'
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model){
            $model->id = Str::uuid();
        });

        static::saving(function ($model) {
            $model->value = preg_replace('([\s]+)', ' ', $model->value);
            $model->key = preg_replace('([\s]+)', ' ', $model->key);
        });
    }


    public $incrementing = false;
    protected $primaryKey = 'id';
    protected $table = 'settings';
    protected $hidden = ['created_at', 'updated_at', 'pivot'];
}
