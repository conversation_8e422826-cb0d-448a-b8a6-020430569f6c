<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use App\Ward;

class CheckWard implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    protected $ward_id;
    public function __construct($ward_id)
    {
        //
        $this->ward_id = $ward_id;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        //
        $ward = Ward::find($value);
        return $ward;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Ward_001_E_001';
    }
}
