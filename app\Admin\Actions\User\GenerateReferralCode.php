<?php

namespace App\Admin\Actions\User;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class GenerateReferralCode extends RowAction
{
    public $name = 'Generate Referral Code';

    public function handle(Model $model, Request $request)
    {
        if ($model->referral_code) {
            return $this->response()->error('User already has a referral code: ' . $model->referral_code);
        }

        $code = $model->generateReferralCode();
        
        return $this->response()->success('Referral code generated successfully: ' . $code)->refresh();
    }

    public function dialog()
    {
        $this->confirm('Are you sure you want to generate a referral code for this user?');
    }
}
