<?php

namespace App\Http\Requests\Role;

use App\Http\Requests\BaseRequest;

class RoleUpdateRequest extends BaseRequest
{

    public function rules()
    {
        if(isset($this->id))
        {
            $id = 'required|integer|exists:roles,id';
            $name = 'required|max:255|unique:roles,name,'.$this->id;
        }
        else{
            $id = 'required|integer|exists:roles,id';
            $name = 'required|max:255|unique:roles,name';
        }

        return [
            'id' => $id,
            'name' => $name,
            'description' => 'nullable|max:255'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'name.required' => 'Role_001_E_001',
    //         'name.unique' => 'Role_005_E_002',
    //         'id.required' => 'Role_001_E_003',
    //         'id.integer' => 'Role_002_E_004',
    //         'id.exists' => 'Role_003_E_005',

    //         'name.max' => 'Role_004_E_006',
    //         'description.max' => 'Role_004_E_007',
    //     ];
    // }
}
