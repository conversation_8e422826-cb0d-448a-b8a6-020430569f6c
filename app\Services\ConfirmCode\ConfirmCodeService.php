<?php
namespace App\Services\ConfirmCode;

use App\ConfirmCode;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ConfirmCodeService
{
    public static function insert(array $data)
    {
        $data['code'] = password_hash($data['code'], PASSWORD_DEFAULT);
        $result = ConfirmCode::create($data);

        return $result;
    }

    public static function checkCode(array $data)
    {
        $query = ConfirmCode::where('action_type', $data['action_type'])
            ->where('expire_time', '>', Carbon::now());
        
        if(isset($data['user_id']) && !empty($data['user_id'])){
            $query->where('user_id', $data['user_id']);
            // $query->orderBy('created_at', 'desc');
        }
        $queryResult = $query->latest('created_at')->first();
        if($query){
            $storedHashedCode = $queryResult->code;
        }else{
            return false;
        }
        $userProvidedCode = $data['code'];
        // Log::info($userProvidedCode ." || ". $storedHashedCode);
        $result = password_verify($userProvidedCode, $storedHashedCode);
        return $result ? $queryResult : false;
    }

}
