<?php
namespace App\Services\Auth;

use App\Property;
use App\Http\Requests\PropertyRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\User;
use App\User2;
use App\Role;
use App\Category;
use App\District;
use App\Mail\SendMail;
use App\Mail\SendForgetPasswordMail;
use Mail;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use App\Services\Image\ImageService;
use App\Jobs\ActionMark;
use App\Setting;
use App\Services\Log\LogSendMailService;
use Illuminate\Support\Facades\Log;
use App\Notifications\NotificationUser;
use App\Services\Token\TokenService;
use App\HistoryMark;
use App\LogGeneral;
use App\Services\Log\LogGeneralService;
use App\UserCrawl;

class AuthService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId)
        {
            $this->_userId = $userId;
        }
        else
        {
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    public function getUserInfor()
    {
        $result = User::with(['roles','provinces','districts','wards'])->find($this->_userId);
        $general = new GeneralService($this->_userId);
        $result['image'] = $general->avatar($result->profile_picture, config('constants.image_type.thumbnail'));
        $result['background'] = $general->avatar($result->background_picture, config('constants.image_type.medium'));
        return $result;

    }

        // public function getRoleID(){
        //     $role_id = Auth::user()->role_id;
        //     return $role_id;
        // }

    //------logout--------
    public function logout()
    {
        $user = User::find(Auth::user()->id);

        $user->token = NULL;
        $user->save();
        Auth::logout();

        return true;
    }

    //---process list user-----
    public function list($user)
    {
        $checkU = User::find($this->_userId);
        $data = Role::find($checkU->role_id);
        $offset = isset($user['offset']) ? $user['offset'] : 0;
        $limit = isset($user['limit']) ? $user['limit'] : 20;
        // $setting = Setting::where('key',$key)->first();
        if($checkU)
        {
            $data =[
                'count'  => User::count(),
                'result' => User::orderBy('updated_at','desc')->with(['roles','districts','provinces','wards', 'image'])->where('enable',true)->offset($offset)->limit($limit)->get()
            ];
            return $data;
        }
        else
        {
            return [];
        }

    }
    //------general update-------------
    public function generalUpdate(array $user)
    {
        $result = User::find($user['id']);

        $result->update($user);
        // $general = new GeneralService($this->_userId);
        // $general->addLog(Config::get('constants.log_action.update', 2),$result->id,Config::get('constants.object_type.user', 5),$result, json_encode($user));
        // $dataAddLogGeneral = [
        //     'action' => Config::get('constants.log_action.update', 2),
        //     'object_id' => $result->id,
        //     'object_type' => Config::get('constants.object_type.user', 5),
        //     'data_response' => $result,
        //     'data_request' => json_encode($user)
        // ];

        // $service = new LogGeneralService($this->_userId);
        // $dataLog = $service->add($dataAddLogGeneral);
        return $result;

    }

    //----process update user----
    public function update(array $user)
    {
        $checkU = User::find($this->_userId);
        $result = User::find($user['id']);

        if(!$result)
        {
            return false;
        }
        if($checkU && $checkU->role_id == 2 && $checkU->id == $result->id)
        return false;

        $con = new Controller;
        $con->authorize('update',$result);


        $this->generalUpdate($user);

        $result = $this->detail($result->id);
        return $result;
    }

    //---------handle image user --------------
    public function handleImage(array $user, $image, $orientation,  $id = null)
    {

        $service = new ImageService($this->_userId);

        if($id)
        {
            $data = [
                'id' => $id
            ];
            $service->delete($data);
        }

        $data = [
            'path' => $image,
            'parent_id' => $user['id'],
            'object_type' => config('constants.properties.user_image'),
            'orientation' => $orientation
        ];

        $image = $service->addBase64NoWaterMark($data);
        $result = $image['images'];
        return $result['id'];
    }

    //---process detail user-----------

    public function detail($id)
    {
        $result = User::with(['roles','districts','provinces','wards'])->find($id);

        if(!$result)
        {
            return false;
        }

        $general = new GeneralService($this->_userId);
        // $result['image'] = $general->avatar($result->profile_picture, config('constants.image_type.thumbnail'));
        // $result['background'] = $general->avatar($result->background_picture, config('constants.image_type.medium'));
        return $result;
    }

    public function detailInfoOnly($id)
    {
        $result = User::find($id);

        if(!$result)
        {
            return false;
        }

        $general = new GeneralService($this->_userId);
        $result['image'] = $general->avatar($result->profile_picture, config('constants.image_type.thumbnail'));
        $result['background'] = $general->avatar($result->background_picture, config('constants.image_type.medium'));
        return $result;
    }

    //----process update password-----
    public function updatePassword(array $user)
    {
        $result = User::find($this->_userId);

        $con = new Controller;
        $con->authorize('update',$result);

        $user['password'] = bcrypt($user['password']);
        $user['id'] = $result->id;

        $this->generalUpdate($user);

        return $result;
    }

    //--------reset password------------
    public function reset(array $user)
    {

        $result = User::where('email',$user['email'])->first();

        if(!$result)
        {
            return false;
        }

        $con = new Controller;
        $con->authorize('update',$result);

        //  $password = substr(str_shuffle("0123456789"), 0, 8);
        $password = rand((pow(10, 7)), (pow(10, 8) - 1));

        $user['id'] = $result->id;
        $user['password'] = bcrypt($password);

        $this->generalUpdate($user);

        $log = new LogSendMailService();
        try {
            Mail::to($user['email'])->send(new SendMail($result->name,$password));
            $mess ='Sent Successfully';
            $log->logSendMail($result, $mess);
        } catch (\Exception $e) {
            $log->logSendMail($result,$e);
        }


        //store log
        // $log = new LogSendMailService();
        // if(count(Mail::failures()) > 0 ) {
        //     $mess = 'Sent Failures';
        //     $log->logSendMail($result,$mess);
        // }
        // else
        // {
        //     $mess ='Sent Successfully';
        //     $log->logSendMail($result, $mess);
        // }

        return $result;
    }

    //---------delete user----------------
    public function delete(array $user)
    {
        $result = User::find($user['id']);

        if(!$result)
        {
            return false;
        }

        $con = new Controller;
        $con->authorize('delete',$result);

        //-------delete all token---------------------
        TokenService::deleteList(['id' => $result->id]);

        
        //-----------delete user---------------------
        $result->delete();

        //---add into log-----------
        $general = new GeneralService($this->_userId);
        $general->addLog(Config::get('constants.log_action.delete', 3),$user['id'],Config::get('constants.object_type.user', 5),$result, json_encode($user));

        return $result;
    }

    //---------forget passwork----------------
    public function forget(array $user)
    {
        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        $result = User::where('email',$user['email'])->first();
        if(!$result){
            return false;
        }

        if($result->enable == false)
        {
            return 'lock';
        }

        if($result->email_verified_at == null)
        {
            return 'verify';
        }

        $key = rand( pow(10,5) , (pow(10,6) - 1));
        // check $key exisit in Cache

        if(Cache::has($result['id']))
        {
            Cache::forget($result['id']);
            $expiresAt = Carbon::now()->addSeconds(300);
            Cache::put($result['id'], $key, $expiresAt);
        }
        else{

            $expiresAt = Carbon::now()->addSeconds(300);
            Cache::put($result['id'], $key, $expiresAt);
        }

        $log = new LogSendMailService();
        try {
            Mail::to($user['email'])->send(new SendForgetPasswordMail($result->name,$key));
            $mess ='Sent Successfully';
            $log->logSendMail($result, $mess);
        } catch (\Exception $e) {
            $log->logSendMail($result,$e);
        }

        // Mail::to($user['email'])->send(new SendForgetPasswordMail($result->name,$key));
        //store log
        // $log = new LogSendMailService();
        // if(count(Mail::failures()) > 0 ) {
        //     $mess = 'Sent Failures';
        //     $log->logSendMail($result,$mess);
        // }
        // else
        // {
        //     $mess ='Sent Successfully';
        //     $log->logSendMail($result, $mess);
        // }
        return $result;
    }
    //------------------------
    public function confirmCode(array $user)
    {
        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        $result = User::find($user['id']);
        if(!$result){
            return false;
        }

        if($result->enable == false)
        {
            return 'lock';
        }

        if($result->email_verified_at == null)
        {
            return 'verify';
        }

        if(!Cache::has($result['id']))
        {
            return false;
        }
        if(Cache::get($result['id']) != $user['key'])
        {
            return false;
        }

        return $result;
    }
    //----process update forget password-----
    public function updateForgetPassword(array $user)
    {
        $redis = GeneralService::redisConnection();
        try {
            $redis->set("connect redis",'success','EX', 1);
        } catch (\Throwable $th) {
            return 'redis';
        }

        $result = User::find($user['id']);

        if(!$result){
            return false;
        }

        if($result->enable == false)
        {
            return 'lock';
        }

        if($result->email_verified_at == null)
        {
            return 'verify';
        }

        $user['password'] = bcrypt($user['password']);
        if(Cache::has($result['id']))
        {
            if(Cache::get($result['id']) != $user['key']){
                return false;
            }
        }
        else{
            return false;
        }
        if(Cache::has($result['id']))
        {
            Cache::forget($result['id']);
        }

        $user['is_new'] = false;

        $this->generalUpdate($user);

        return $result;
    }
    public function filter(array $user)
    {
        $checkU = User::find($this->_userId);
        $result = User::query();
        $numCompare = config('constants.num_compare');
        $strCompare = config('constants.str_compare');
        $idCompare = config('constants.compare');

        if(isset($user['name']) && !empty($user['name']))
        {
            $result->where('name','ILIKE','%'.$user['name'].'%');
        }

        if(isset($user['email']) && !empty($user['email']))
        {
            $result->where('email','ILIKE','%'.$user['email'].'%');
        }

        if(isset($user['role']) && !empty($user['role']))
        {
            $result->where('role_id',$user['role']);
        }

        if(isset($user['gender']) && !empty($user['gender']))
        {
            $result->where('gender',$user['gender']);
        }

        if(isset($user['phone_op']) && !empty($user['phone_op']) && $user['phone_op'] == config('constants.compare.null'))
        {
            $result->where('phone',null);
        }
        elseif(isset($user['phone']) && !empty($user['phone']))
        {
            if(isset($user['phone_op']) && !empty($user['phone_op']) && $user['phone_op'] == config('constants.compare.is'))
            {
                $result->where('phone',$user['phone']);
            }
        }
        $result = $result->with(['roles','districts','provinces','wards','image'])->orderBy('updated_at','desc')->where('enable',true);

        $count = $result->count();
        $data = [
            'count' => $count,
            'result' => $result->offset($user['offset'])->limit($user['limit'])->get()
        ];
        return $data;
    }
    public function checkUnique(array $user)
    {
        switch ($user['status']) {
            case 1:
                if(isset($user['email']) && !empty($user['email']))
                {
                    $result = User::where('email','ILIKE',$user['email'])->first();
                }
                break;
            case 2:
                if(isset($user['user_name']) && !empty($user['user_name']))
                {
                    $result = User::where('user_name','ILIKE',$user['user_name'])->first();
                }
                break;
            default:
                break;
        }
        if(!$result)
        {
            return true;
        }
        return false;
    }
    public function updateImage(array $user)
    {
        $result = User::find($user['id']);

        if(!$result)
        {
            return false;
        }

        //------profile_picture-------
        if(isset($user['file']))
        {
            $countAv = HistoryMark::where([
                ['object_type', config('constants.action_mark_object_type.user')],
                ['active_mark_id', 13],
                ['user_id', $result->id]
            ])->count();

            if($countAv == 1)

            {
                $user['profile_picture'] = $this->handleImage($user, $user['file'], isset($user["orientation_avatar"])?$user["orientation_avatar"]:null, $result->profile_picture);
            }
            else
            {
                $user['profile_picture'] = $this->handleImage($user, $user['file'], isset($user["orientation_avatar"])?$user["orientation_avatar"]:null);

                $actionMark = new ActionMark(config('constants.action_mark_object_type.user'),$result->id,13,$result->id);
                Log::info("Run Queue Action Mark");
                dispatch($actionMark);
            }
        }
        $this->generalUpdate($user);

        $result = $this->detail($result->id);
        return $result;
    }
    public function remove(array $user)
    {
        $checkU = User::find($this->_userId);
        $result = User::where('id',$user['id'])->first();
        if(!$result){
            return false;
        }
        $data = [
            'enable' => false,
            'id' => $user['id']
        ];
        $result->update($data);

        //-------delete all token---------------------
        TokenService::deleteList(['id' => $result->id]);

        $general = new GeneralService($this->_userId);
        $general->addLog(Config::get('constants.log_action.remove', 4),$result->id,Config::get('constants.object_type.user', 5),$result, json_encode($user));

        $dataAddLogGeneral = [
            'action' => Config::get('constants.log_action.remove', 4),
            'object_id' => $result->id,
            'object_type' => Config::get('constants.object_type.user', 5),
            'data_response' => $result,
            'data_request' => json_encode($user)
        ];

        $service = new LogGeneralService($this->_userId);
        $dataLog = $service->add($dataAddLogGeneral);

        return $result;
    }
    public function removeList(array $user)
    {
        if(isset($user['id']))
        {
        foreach ($user['id'] as $key => $value) {
            $data= [
                'id' => $value
            ];
            $this->remove($data);
        }}
        return [];
    }
    public function listRemoved(array $user)
    {
        $limit  = isset($user['limit']) ? $user['limit'] : 20;
        $offset = isset($user['offset']) ? $user['offset'] : 0;
        $name   = isset($user['name']) ? $user['name'] : 'updated_at';
        $sort   = isset($user['sort']) ? $user['sort'] : 'desc';

        $checkU = User2::find($this->_userId);

        $result = User2::where('enable',false);
        
        $data = [
            'count'  => $result->count(),
            'result' => $result->orderBy($name,$sort)->limit($limit)->offset($offset)->get()
        ];
        return $data;
    }
    public function restore(array $user)
    {
        $result = User::where('id',$user['id'])->first();
        if(!$result){
            return false;
        }
        $data = [
            'enable' => true,
            'id' => $user['id']
        ];
        $result->update($data);

        $general = new GeneralService($this->_userId);
        $general->addLog(
            Config::get('constants.log_action.restore', 5),
            $result->id,
            Config::get('constants.object_type.user', 5),
            $result,
            json_encode($data)
        );

        return $result;
    }
    public function restoreList(array $user)
    {
        foreach ($user['id'] as $key => $value) {
            $data = [
                'id'    => $value,
                'enable'=> true
            ];
            $this->restore($data);
        }
        return true;
    }
    public function deleteList(array $user)
    {
        if(isset($user['id']))
        {
            foreach ($user['id'] as $key) {
                $data = [
                    'id' => $key
                ];
                $this->delete($data);
            }
        }

        return [];
    }
    public function listUserCrawl(array $user)
    {
        $limit = isset($user['limit']) ? $user['limit'] : 10;
        $offset = isset($user['offset']) ? $user['offset'] : 0;
        $result = UserCrawl::query();

        $data = [
            'count'  => $result->count(),
            'result' => $result->with(['properties:id,link_source,user_id'])->orderBy('updated_at','desc')->limit($limit)->offset($offset)->get()
        ];
        return $data;
    }
    public function updateUserCrawl(array $user)
    {
        $result = UserCrawl::find($user['id']);
        $result->phone = $user['phone'];
        $result->save();
        return $result;
    }
    public function detailUserCrawl($id)
    {
        $result = UserCrawl::with(['properties:id,link_source,user_id'])->find($id);
        return $result;
    }

    public function areaManager()
    {
        $checkU = User::find($this->_userId);

        if($checkU)
        {
            switch ($checkU->role_id) {
                case 1:
                case 3:
                    $result = District::with('provinces:id,name')->get(['id', 'name', 'province_id']);
                    break;
                case 2:
                    $area = SellArea::where('user_id', $checkU->id)->get("district_id");
                    if($area)
                    {
                        $collection = collect($area);

                        $idArea = explode(',',$collection->implode('district_id', ','));
                    }

                    $result = District::with('provinces:id,name')->whereIn('id',$idArea)->get(['id', 'name', 'province_id']);
                    break;
                default:
                    $result = [];
                    break;
            }
            return $result;
        }
        else
        {
            return [];
        }
    }
}


