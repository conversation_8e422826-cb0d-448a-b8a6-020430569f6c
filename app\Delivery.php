<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Order;

class Delivery extends Model
{
    //
    protected $fillable = [
    	'id',
        'order_id',
        'driver_id',
        'status',
        'status_time',
        'duration',
        'pickup_time',
        'latitude_from',
        'longitude_from',
        'latitude_to',
        'longitude_to',
        'name_from',
        'address_from',
        'phone_from',
        'name_to',
        'address_to',
        'phone_to',
        'package_info',
        'special_require',
        'total_amount',
        'discount_id',
        'discount_amount',
        'grand_total',
        'distance',
        'notes',
        'short_code',
        'cod_price',
        'payment_method',
        'pending_time',
        'delivery_partner_id'
    ];


    protected $casts = [
        'cod_price' => 'float',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model){
            $model->id = Str::uuid();
            do {
                $datePrefix = date('ymd'); // Add current year, month, and day as prefix
                $randomAlphabet = strtoupper(Str::random(2)); // Generate two random uppercase letters
                $uniqueIdentifier = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT); // Generates a unique 4-digit numeric identifier

                // Combine the date prefix, random uppercase alphabet characters, and the unique identifier
                $deliveryID = $datePrefix . '-' . $randomAlphabet . $uniqueIdentifier;

                $model->short_code = $deliveryID;
            } while (self::where('short_code', $model->short_code)->exists());
        });
    }

    public $incrementing = false;
    protected $primaryKey = 'id';

    protected $table = 'deliveries';
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id');
    }
}
