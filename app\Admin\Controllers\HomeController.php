<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\Dashboard;
use Encore\Admin\Layout\Column;
use Encore\Admin\Layout\Content;
use Encore\Admin\Layout\Row;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Widgets\Tab;
use Encore\Admin\Widgets\Table;
use Encore\Admin\Widgets\InfoBox;
use App\Product;
use App\Shop;
use App\User;
use App\Order;

class HomeController extends Controller
{
    public function baseIndex(Content $content)
    {
        return $content
            ->title('Dashboard')
            ->description('Description...')
            ->row(Dashboard::title())
            ->row(function (Row $row) {

                $row->column(4, function (Column $column) {
                    $column->append(Dashboard::environment());
                });

                $row->column(4, function (Column $column) {
                    $column->append(Dashboard::extensions());
                });

                $row->column(4, function (Column $column) {
                    $column->append(Dashboard::dependencies());
                });
            });
    }
    public function index(Content $content)
    {
        return $content
            ->title('Dashboard')
            ->description('Rẻ mà Gần Admin site')
            ->row(function (Row $row) {

                $row->column(4, function (Column $column) {
                    $infoBox = new InfoBox('Users', 'users', 'aqua', '/admin/users', number_format(User::All()->count(), 0));
                    $column->append($infoBox);
                });
                $row->column(4, function (Column $column) {
                    $infoBox = new InfoBox('User Products', 'product-hunt', 'aqua', '/admin/products', number_format(Product::select('id')->where('type', '<>',1)->where('enable', true)->get()->count(), 0));
                    $column->append($infoBox);
                });
                $row->column(4, function (Column $column) {
                    $infoBox = new InfoBox('Shops', 'fort-awesome', 'aqua', '/admin/shops', number_format(Shop::get()->count(), 0));
                    $column->append($infoBox);
                    
                    
                    // $productManage = "<a href='" . route('admin.products.index') . "'>" . __('product') . "</a>: " . number_format(Product::All()->count(), 0) . " items";
                    // $box = new Box(__('product'), $productManage);
                    // $box->collapsable()->style('info')->solid();

                    // $column->append($box);

                    // $shopManage = "<a href='" . route('admin.shops.index') . "'>" . __('shop') . "</a>: " . number_format(Shop::All()->count(), 0) . " items";
                    // $box = new Box(__('shop'), $shopManage);
                    // $box->collapsable()->style('info')->solid();

                    // $column->append($box);
                });
                // $row->column(4, function (Column $column) {
                //     $infoBox = new InfoBox(__('admin.order.title'). " mới đang chờ xác nhận", 'product-hunt', 'aqua', '/admin/orders', number_format(Order::select('id')->where('status', 1)->count(), 0));
                //     $column->append($infoBox);
                // });
                $row->column(8, function (Column $column) {
                    $infoBox = new InfoBox(__('admin.order.title'). " mới đang chờ xác nhận", 'product-hunt', 'aqua', '/admin/orders?_selector%5Bstatus%5D=1', number_format(Order::select('id')->where('status', 1)->count(), 0));
                    $column->append($infoBox);
                    $shops = route('admin.shops.index');
                    $tab = new Tab();

                    // $tab->add(__('shop'), $shops);
                    // table 1
                    $headers = [__('admin.order.short_code'), __('admin.order.notes'), __('admin.order.address'),__('admin.order.customer_phone'), __('admin.order.customer_name'),__('admin.order.grand_total')];
                    $newOrder = Order::select('id',
                        'short_code',
                        'notes',
                        'address',
                        'customer_name',
                        'customer_phone',
                        'grand_total'
                    )->where('status', 1)->orderBy('created_at', 'desc')->limit(10)->get()->toArray();
                    foreach ($newOrder as $key => $order) {
                        $newOrder[$key]['short_code'] = '<a href="admin/orders/'.$order['id'].'/edit"\>'.$order['short_code'].'</a>';
                        unset($newOrder[$key]['id']);
                        $newOrder[$key]['grand_total'] = number_format($order['grand_total'], 0).'đ';
                    }
                    
                    $table = new Table($headers, $newOrder);
                    $tab->add('Đơn hàng mới đang chờ xác nhận', $table);
                    // $tab->add('Text', 'blablablabla....');

                    $column->append($tab->render());
                });
            });
    }
}
