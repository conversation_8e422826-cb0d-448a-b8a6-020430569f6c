<?php

namespace App\Admin\Actions\User;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class SetAgent extends RowAction
{
    public function name()
    {
        return __('admin.agent.set');
    }

    public function handle (Model $model)
    {
        $model->role_id = 4;
        $model->save();

        return $this->response()->success('SetAgent')->refresh();
    }

    public function dialog()
    {
        $this->confirm(__('admin.agent.set').'?');
    }
}