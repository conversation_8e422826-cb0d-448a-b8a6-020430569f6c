{"version": 3, "sources": ["webpack://ShortcutButtonsPlugin/webpack/universalModuleDefinition", "webpack://ShortcutButtonsPlugin/webpack/bootstrap", "webpack://ShortcutButtonsPlugin/./src/index.ts"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "r", "value", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "defaultConfig", "theme", "ShortcutButtonsPlugin", "config", "cfg", "assign", "fp", "wrapper", "onClick", "event", "stopPropagation", "preventDefault", "target", "tagName", "toLowerCase", "index", "parseInt", "dataset", "callbacks", "Array", "isArray", "callback", "onReady", "document", "createElement", "classList", "add", "label", "length", "textContent", "append<PERSON><PERSON><PERSON>", "buttons", "button", "for<PERSON>ach", "b", "String", "calendarContainer", "addEventListener", "onDestroy", "removeEventListener", "undefined"], "mappings": "CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,UAAAH,GACA,iBAAAC,QACAA,QAAA,sBAAAD,IAEAD,EAAA,sBAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA2CA,OAtCAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GACAK,cAAA,EACAC,YAAA,EACAC,IAAAN,KAMAV,EAAAiB,EAAA,SAAAvB,GACAkB,OAAAC,eAAAnB,EAAA,cAAiDwB,OAAA,KAIjDlB,EAAAmB,EAAA,SAAAxB,GACA,IAAAe,EAAAf,KAAAyB,WACA,WAA2B,OAAAzB,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAU,EAAAC,GAAsD,OAAAV,OAAAW,UAAAC,eAAAnB,KAAAgB,EAAAC,IAGtDtB,EAAAyB,EAAA,GAIAzB,IAAA0B,EAAA,mFC7CA,MAAMC,GACFC,MAAO,SAgCXlC,EAAAmC,sBAAA,SAAsCC,GAClC,MAAMC,EAAGnB,OAAAoB,UAAQL,EAAkBG,GAEnC,OAAQG,IAIJ,IAAIC,EAKJ,SAAAC,EAAiBC,GACbA,EAAMC,kBACND,EAAME,iBAEN,MAAMC,EAASH,EAAMG,OACrB,GAAqC,WAAjCA,EAAOC,QAAQC,oBAAqD,IAAhBV,EAAII,QACxD,OAGJ,MAAMO,EAAQC,SAASJ,EAAOK,QAAQF,MAAO,IAEvCG,EAAyDC,MAAMC,QAAQhB,EAAII,SAC7EJ,EAAII,SACHJ,EAAII,SAET,IAAK,MAAMa,KAAYH,EACK,mBAAbG,GACPA,EAASN,EAAOT,GAK5B,OAIIgB,QAAS,KAIL,IAHAf,EAAUgB,SAASC,cAAc,QACzBC,UAAUC,IAAI,qCAAsCtB,EAAIH,YAEvC,IAAdG,EAAIuB,OAAyBvB,EAAIuB,MAAMC,OAAQ,CACtD,MAAMD,EAAQJ,SAASC,cAAc,OACrCG,EAAMF,UAAUC,IAAI,oCACpBC,EAAME,YAAczB,EAAIuB,MAExBpB,EAAQuB,YAAYH,GAGxB,MAAMI,EAAUR,SAASC,cAAc,OACvCO,EAAQN,UAAUC,IAAI,uCAErBP,MAAMC,QAAQhB,EAAI4B,QAAU5B,EAAI4B,QAAU5B,EAAI4B,SAASC,QAAQ,CAACC,EAAGnB,KAChE,MAAMiB,EAAST,SAASC,cAAc,UACtCQ,EAAOP,UAAUC,IAAI,qCACrBM,EAAOH,YAAcK,EAAEP,MACvBK,EAAOf,QAAQF,MAAQoB,OAAOpB,GAE9BgB,EAAQD,YAAYE,KAGxBzB,EAAQuB,YAAYC,GAEpBzB,EAAG8B,kBAAkBN,YAAYvB,GAEjCA,EAAQ8B,iBAAiB,QAAS7B,IAMtC8B,UAAW,KACP/B,EAAQgC,oBAAoB,QAAS/B,GACrCD,OAAUiC", "file": "shortcut-buttons-flatpickr.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ShortcutButtonsPlugin\"] = factory();\n\telse\n\t\troot[\"ShortcutButtonsPlugin\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "/*\n * Copyright (c) 2018 <PERSON> under the MIT license.\n * https://github.com/jcsmorais/shortcut-buttons-flatpickr/\n */\n\nimport { Instance as Flatpickr } from 'flatpickr/dist/types/instance.d';\n\nexport namespace ShortcutButtonsFlatpickr {\n    export type OnClickSignature = (index: number, fp: Flatpickr) => void;\n\n    export type Button = {\n        label: string,\n    };\n\n    export type Config = {\n        button: Button | Button[],\n        label?: string,\n        onClick?: OnClickSignature | OnClickSignature[],\n        theme?: string,\n    };\n}\n\nconst defaultConfig: Partial<ShortcutButtonsFlatpickr.Config> = {\n    theme: 'light',\n};\n\n/**\n * Adds shortcut buttons to flatpickr providing users an alternative way to interact with the datetime picker.\n *\n * Example usage:\n *\n * ```ts\n * flatpickr('.target-input-element', {\n *     // ...\n *     plugins: [ShortcutButtonsPlugin({\n *         button: {\n *             label: 'The Beginning Of Time',\n *         },\n *         onClick: (index: number, fp: Flatpickr) => {\n *             // Do something when a button is clicked\n *         },\n *         theme: 'light',\n *     })],\n * })\n * ```\n *\n * @param config Configuration options.\n *\n * Supported options are:\n *    `button`: button(s).\n *    `button.label`: button's label.\n *    `label`: label including a word/sentence stating that the user can use the calendar or one of the buttons.\n *    `onClick`: callback(s) invoked when plugin's buttons are clicked.\n *    `theme`: flatpickr's theme.\n */\nexport function ShortcutButtonsPlugin(config: ShortcutButtonsFlatpickr.Config) {\n    const cfg = { ...defaultConfig, ...config };\n\n    return (fp: Flatpickr) => {\n        /**\n         * Element that wraps this plugin's dependent elements.\n         */\n        let wrapper: HTMLElement;\n\n        /**\n         * Handles click events on plugin's button.\n         */\n        function onClick(event: Event) {\n            event.stopPropagation();\n            event.preventDefault();\n\n            const target = event.target as HTMLButtonElement;\n            if (target.tagName.toLowerCase() !== 'button' || typeof cfg.onClick === 'undefined') {\n                return;\n            }\n\n            const index = parseInt(target.dataset.index, 10);\n\n            const callbacks: ShortcutButtonsFlatpickr.OnClickSignature[] = Array.isArray(cfg.onClick) ?\n                cfg.onClick :\n                [cfg.onClick];\n\n            for (const callback of callbacks) {\n                if (typeof callback === 'function') {\n                    callback(index, fp);\n                }\n            }\n        }\n\n        return {\n            /**\n             * Initialize plugin.\n             */\n            onReady: () => {\n                wrapper = document.createElement('div');\n                wrapper.classList.add('shortcut-buttons-flatpickr-wrapper', cfg.theme);\n\n                if (typeof cfg.label !== 'undefined' && cfg.label.length) {\n                    const label = document.createElement('div');\n                    label.classList.add('shortcut-buttons-flatpickr-label');\n                    label.textContent = cfg.label;\n\n                    wrapper.appendChild(label);\n                }\n\n                const buttons = document.createElement('div');\n                buttons.classList.add('shortcut-buttons-flatpickr-buttons');\n\n                (Array.isArray(cfg.button) ? cfg.button : [cfg.button]).forEach((b, index) => {\n                    const button = document.createElement('button');\n                    button.classList.add('shortcut-buttons-flatpickr-button');\n                    button.textContent = b.label;\n                    button.dataset.index = String(index);\n\n                    buttons.appendChild(button);\n                });\n\n                wrapper.appendChild(buttons);\n\n                fp.calendarContainer.appendChild(wrapper);\n\n                wrapper.addEventListener('click', onClick);\n            },\n\n            /**\n             * Clean up before flatpickr is destroyed.\n             */\n            onDestroy: () => {\n                wrapper.removeEventListener('click', onClick);\n                wrapper = undefined;\n            },\n        };\n    };\n}\n"], "sourceRoot": ""}