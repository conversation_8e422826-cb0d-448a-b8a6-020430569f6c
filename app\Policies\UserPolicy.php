<?php

namespace App\Policies;

use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use App\Policies\BasePolicy;

class UserPolicy extends BasePolicy
{
    use HandlesAuthorization;

    public function __construct()
    {
        //
    }

    public function show(User $user)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting =  $this->setting('user_view');

        return in_array($user->role_id, json_decode($setting['value'],true));
    }

    public function create(User $user)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting = $this->setting('user_create');

        return in_array($user->role_id, json_decode($setting['value'],true));
    }

    public function update(User $user, User $user1)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting = $this->setting('user_update');

        if(in_array($user->role_id,json_decode($setting['value'],true)) && $user->id == $user1->created_by)
            return true;
    }

    public function delete(User $user)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting = $this->setting('user_delete');

        return in_array($user->role_id, json_decode($setting['value'],true));
    }
    public function login(User $user,User $user1)
    {
        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting = $this->setting('login_admin');

        return in_array($user->role_id, json_decode($setting['value'],true));
    }

    public function updateClient(User $user, User $user1)
    {
        // $admin = $this->setting('root_admin');
        // if (in_array($user->role_id, json_decode($admin['value'],true))) {
        //     return true;
        // }

        return $user->id == $user1->id;
    }


    //-------------handle Data--------------------
    public function handleData(User $user)
    {

        $admin = $this->setting('root_admin');
        if (in_array($user->role_id, json_decode($admin['value'],true))) {
            return true;
        }

        $setting = $this->setting('handle_data');

        return in_array($user->role_id, json_decode($setting['value'],true));

    }

    public function admin(User $user)
    {
        $admin = $this->setting('root_admin');

        return in_array($user->role_id, json_decode($admin['value'],true));

    }

}
