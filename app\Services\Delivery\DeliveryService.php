<?php
namespace App\Services\Delivery;

use App\Delivery;
use App\Notifications\NotificationUser;
use App\Services\DeliveryPartner\DeliveryPartnerService;
use App\Services\Mqtt\MqttChatService;
use App\Setting;
use App\Shop;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Order;
use Illuminate\Support\Facades\Cache;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use App\Helpers\Helper;
use PHPUnit\Util\Json;
use Illuminate\Support\Facades\DB;
use App\Services\Token\TokenService;
use App\Services\Notification\NotificationService;
use App\Services\Interaction\InteractionService;

class DeliveryService
{
    //------process create Delivery----------
    public function create(array $data)
    {
        try {
            DB::beginTransaction();

            $delivery = new Delivery();
            $delivery->fill($data);
            $delivery->id = Str::uuid();
            $delivery->status = $data['status'] ?? 1; // Default to pending if not provided
            $delivery->status_time = json_encode([
                ["status" => $delivery->status, "time" => now()->format('Y-m-d H:i:s'), "notes" => ""]
            ]);
            $delivery->save();

            DB::commit();
            // send notify to driver
            // $this->sendNotificationToDriver($delivery->id, $data['driver_id']);

            return $this->detail($delivery->id);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating delivery: ' . $e->getMessage());
            return false;
        }
    }
    public function sendNotificationToDriver($deliveryID, $driverID)
    {
        $mqtt = new MqttChatService();
        $messToNoti = [
            'body'  => "Bạn có vận đơn mới",
            'title' => "Bạn có vận đơn mới",
            // 'image' => $image,
            'url'   => $deliveryID,
            'target_url' => '/driver-tools/delivery-history/'. $deliveryID,
            'type'  => 'delivery',
        ];

        $tokenService = new TokenService();
        $tokens = $tokenService->listByUser($driverID, 2);
        $notiService = new NotificationService;
        if(count($tokens) > 0){
            $notiService->sendBatchNotification($tokens, $messToNoti);
        }
        $mqtt->publish(['topic' => "driver_$deliveryID" , 'message' => $messToNoti]);

        # nhận thông báo và lưu vào table notifications
        $driver_obj = User::find($driverID);
        if($driver_obj){
            $delivery_obj = Delivery::find($deliveryID);
            $$messToNoti['body'] = $delivery_obj->short_code ?? $$messToNoti['body'];
            $driver_obj->notify(new NotificationUser($driverID, $messToNoti));
        }

        return true;
    }

    //---- Update location driver
    function updateDriverLocation($driver, $longitude, $latitude)
    {
        $driver->update(['longitude' => $longitude, 'latitude' => $latitude]);
    }



    //-------process update Delivery--------
    public function update(array $data)
    {
        try {
            DB::beginTransaction();

            $delivery = Delivery::find($data['id']);
            if (!$delivery) {
                return false;
            }
            $oldStatus = $delivery->status;

            $delivery->fill($data);

            if (isset($data['status']) && $data['status'] != $oldStatus) {
                $statusTime = json_decode($delivery->status_time, true) ?? [];
                $statusTime[] = [
                    "status" => $data['status'],
                    "time" => now()->format('Y-m-d H:i:s'),
                    "notes" => $data['notes'] ?? ""
                ];
                $delivery->status_time = json_encode($statusTime);
            }

            $delivery->save();

            DB::commit();
            switch ($data['status']) {
                case config('constants.delivery.pending'):
                    $this->sendNotificationToDriver($delivery->id, $data['driver_id']);
                    break;
                case config('constants.delivery.delivered'):
                    $order = Order::find($delivery->order_id);
                    if ($order) {
                        $order->status = config('constants.order.done');
                        $order->save();
                    }
                    break;
                case config('constants.delivery.failed'):

                    break;


                default:
                    # code...
                    break;
            }



            return $this->detail($delivery->id);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating delivery: ' . $e->getMessage());
            return false;
        }
    }
    //------process detail Delivery----------
    public function detail($id)
    {
        $result = Delivery::where('id',$id)
            ->with('order', 'driver')
            ->first();

        if(!$result)
        {
            return false;
        }

        return $result;
    }
    //------process detail Delivery by Order id----------
    public function detail_by_order_id($id)
    {
        $result = Delivery::where('order_id',$id)
            ->with('order', 'driver')
            ->first();

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    public function updateStatus(array $data)
    {
        $delivery = Delivery::find($data['delivery_id']);
        $order = Order::find($delivery->order_id);
        $shop = Shop::find($order->shop_id);

        if (!$delivery || !$order) {
            return false;
        }

        $mqtt = new MqttChatService();

        // Prepare notification message data
        $msgShop = [
            'title' => "Trạng thái vận đơn đã được cập nhật",
            'body'  => "Đơn hàng ". $order->short_code,
            'url'   => $order->id,
            'extra' => json_encode([
                'shop_id'   => $order->shop_id
            ]),
            'target_url' => '/my-shop/orders/' . $order->id,
            'type'  => "delivery_update",
        ];
        $msgAgent = [
            'title' => "Trạng thái vận đơn đã được cập nhật",
            'body'  => "Đơn hàng ". $order->short_code . " của shop " . $shop->name,
            'url'   => $order->id,
            'extra' => json_encode([
                'shop_id'   => $order->shop_id
            ]),
            'target_url' => '/agent/order/' . $order->id,
            'type'  => "delivery_update",
        ];

        // Update delivery status
        $delivery->status = $data['status'];
        $service = new DeliveryPartnerService();
        $service->autoUpdateStatusOrder($data['status'], $order);

        // Get status message based on delivery status
        $statusMessages = [
            config('constants.delivery.pending') => " đang chờ xử lý",
            config('constants.delivery.confirmed') => " đã được xác nhận và đang chờ lấy hàng",
            config('constants.delivery.prepared') => " đã được đóng gói và sẵn sàng giao",
            config('constants.delivery.picked_up') => " đã được tài xế lấy hàng",
            config('constants.delivery.in_transit') => " đang trên đường giao đến khách hàng",
            config('constants.delivery.delivered') => " đã giao hàng thành công",
            config('constants.delivery.cancelled') => " đã bị hủy vận chuyển",
            config('constants.delivery.failed') => " giao hàng không thành công"
        ];

        $msg = $statusMessages[$data['status']] ?? "";

        $msgAgent['body'] .= $msg;
        $msgShop['body'] .= $msg;

        $statusTime = $delivery->status_time ? json_decode($delivery->status_time, true) : [];
        $statusTime[] = [
            "status" => $data['status'],
            "time" => now()->format('Y-m-d H:i:s'),
            "notes" => $data['notes'] ?? ""
        ];

        $delivery->status_time = json_encode($statusTime);

        if ($delivery->save()) {
            // Initialize services
            $tokenService = new TokenService();
            $notiService = new NotificationService;

            // Get shop and agent tokens
            $shopTokens = $tokenService->listByUser($shop->user_id, 2);
            $agentTokens = $tokenService->listByUser($shop->agent_id, 2);


            // Send notifications if tokens exist
            if(count($shopTokens) > 0){
                $notiService->sendBatchNotification($shopTokens, $msgShop);
            }
            if(count($agentTokens) > 0){
                $notiService->sendBatchNotification($agentTokens, $msgAgent);
            }
            // Gửi tới shop
            $mqtt->publish(['topic' => 'remagan_shop/'. $shop->id , 'message' => $msgShop]);
            // Gửi tới order
            $mqtt->publish(['topic' => 'remagan_order/'. $order->id , 'message' => $msgShop]);

            # nhận thông báo và lưu vào table notifications
            $shop_obj = User::find($shop->user_id);
            if($shop_obj){
                $shop_obj->notify(new NotificationUser($shop->user_id, $msgShop));
            }
            $shop_agent_obj = User::find($shop->agent_id);
            if($shop_agent_obj){
                $shop_agent_obj->notify(new NotificationUser($shop->agent_id, $msgAgent));
            }

            return $this->detail($delivery->id);
        }

        return false;
    }

    public static function checkPrice(array $data)
    {
        $distance = round(floatval($data['distance']), 1);
        if ($distance < 0) {
            return false;
        }

        $settings = Setting::whereIn('key', [
            'delivery_min_fee_distance',
            'delivery_min_fee',
            'delivery_normal_fee'
        ])->pluck('value', 'key');

        $minFeeDistance = floatval($settings['delivery_min_fee_distance']) ?? 13000;
        $minFee = floatval($settings['delivery_min_fee']) ?? 25000;
        $normalFeePerKm = floatval($settings['delivery_normal_fee']) ?? 4000;
        $maxDistance = floatval($settings['delivery_fast_mode_max_distance'] ?? 50);
        $maxDuration = floatval($settings['delivery_fast_mode_max_duration'] ?? 1440);


        if ((isset($data['duration']) && $data['duration'] > $maxDuration) || $distance > $maxDistance) {
            return null;
        }

        $finalPrice = $minFee;

        if ($distance > $minFeeDistance) {
            $finalPrice += ($distance - $minFeeDistance) * $normalFeePerKm;
        }

        return $finalPrice;
    }
    public function getBoundByRadius($latitude, $longitude, $radius = 7){
        // 111.18957696 = 60 * 1.1515 * 1.609344: để quy đổi km ra độ lệch của tọa độ map
        $r = $radius / 111.18957696 ;
        $data = [];
        $data['latitude_s']  = $latitude - $r;
        $data['latitude_b']  = $latitude + $r;
        $data['longitude_s'] = $longitude - $r;
        $data['longitude_b'] = $longitude + $r;
        return $data;
    }


    public function findDriver(array $filter, $searchLevel = 1)
    {
        $modelUser = new User();
        $modelUser->setConnection('pgsqlReplica');
        $filter = array_merge([
            'latitude' => null,
            'longitude' => null,
            'radius' => 45,
            'latitude_s' => null,
            'latitude_b' => null,
            'longitude_s' => null,
            'longitude_b' => null
        ], $filter);

        if(!$filter['latitude'] || !$filter['longitude']){
            return false;
        }

        if ($searchLevel === 2) {
            $filter['radius'] = 100;
        }

        // Calculate bounds if not provided
        if (!$filter['latitude_s'] || !$filter['longitude_s'] || !$filter['latitude_b'] || !$filter['longitude_b']) {
            $bounds = $this->getBoundByRadius($filter['latitude'], $filter['longitude'], $filter['radius']);
            $filter['latitude_s'] = $bounds['latitude_s'];
            $filter['latitude_b'] = $bounds['latitude_b'];
            $filter['longitude_s'] = $bounds['longitude_s'];
            $filter['longitude_b'] = $bounds['longitude_b'];
        }

        $result = $modelUser->select('id','name', 'email','role_id', 'gender', 'address', 'phone', 'date_of_birth', 'province_id', 'district_id', 'ward_id', 'profile_picture', 'description','enable', 'custom_path', 'notes','latitude','longitude','total_rating', 'user_status', 'last_action_at')
            ->with('rating')
            ->where([
                'enable' => true,
                'role_id' => config('constants.role_id.driver')
            ]);

        $isShopFilter = isset($filter['shop_id']);
        if ($isShopFilter) {
            $shopId = $filter['shop_id'];
            $shop = Shop::find($shopId);
            // Join the interactions table to fetch 'liked' status
            $result = $result->leftJoin('interactions', function ($join) use ($shopId) {
                $join->on('interactions.object_id_b', '=', 'users.id')
                    ->where([
                        'interactions.object_id_a' => $shopId,
                        'interactions.interaction_type' => config("constants.interaction.like")
                    ]);
            });

            // Compute 'liked' status as a raw field
            $result->selectRaw('COALESCE(interactions.object_id_b IS NOT NULL, false) AS liked');
            $result->selectRaw(
                "(2 * 6371 * asin(sqrt((sin((radians(?) - radians(latitude)) / 2) ^ 2) +
            cos(radians(?)) * cos(radians(latitude)) *
            (sin((radians(?) - radians(longitude)) / 2) ^ 2)))) AS distance_to_shop", [
                   (float)$shop->latitude,
                   (float)$shop->latitude,
                   (float)$shop->longitude,]);
        } else {
            // If no shop filtering, add a default false liked column
            $result->selectRaw('false AS liked');
        }

        // Calculate distance based on latitude and longitude
        $result->selectRaw(
            "(1000*6371 * acos(
                LEAST(1, GREATEST(-1,
                    cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) +
                    sin(radians(?)) * sin(radians(latitude))
                ))
            )) AS distance", [
                $filter['latitude'],
                $filter['longitude'],
                $filter['latitude']
            ]
        );

        // Filter with conditional bounds
        $result->where(function ($query) use ($filter) {
            // Liked drivers - include regardless of location
            $query->whereNotNull('interactions.object_id_b')
            // Drivers within bounds
            ->orWhere(function ($subQuery) use ($filter) {
                $subQuery->whereBetween('latitude', [$filter['latitude_s'], $filter['latitude_b']])
                    ->whereBetween('longitude', [$filter['longitude_s'], $filter['longitude_b']]);
            });
        });

        // Create a CASE expression for sorting order (liked > online > offline)
        // Fix: Reference the actual join table column, not the alias
        $result->selectRaw("
            CASE 
                WHEN interactions.object_id_b IS NOT NULL THEN 1
                WHEN user_status >= 1 THEN 2
                ELSE 3
            END AS sort_priority
        ");

        // First order by the custom sort priority
        $result->orderBy('sort_priority', 'asc');
        
        // Then, for online drivers, order by distance (nearest first)
        $result->orderBy('distance', 'asc');

        // Additional sorting options if specified
        $sortBy = $filter['sortBy'] ?? null;
        if ($sortBy) {
            $sortOptions = [
                1 => ['name', 'asc'],
                3 => ['created_at', 'desc']
            ];
            if (isset($sortOptions[$sortBy])) {
                $result->orderBy($sortOptions[$sortBy][0], $sortOptions[$sortBy][1]);
            }
        }

        $count = $result->count();

        if($count == 0 && $searchLevel <= 2){
            return $this->findDriver($filter, $searchLevel + 1);
        }

        $result = $result
            ->offset(0)
            ->limit(25)
            ->get()->toArray();
            
        return [
            'count' => $count,
            'result' => $result
        ];
    }

    public function updateDriverId(array $data){
        $result = Delivery::find($data['id']);
        if(!$result)
        {
            return false;
        }
        $body = [
            'id' => $data['id'],
            'driver_id' => $data['driver_id']
        ];
        $this->update($body);

        return $this->detail($result->id);
    }
    public function getDeliveriesByDriverId($driverId, $data)
    {
        $result = Delivery::where('driver_id', $driverId)
            ->with('order');

        if ($data['start_date']) {
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $data['start_date'])) {
                $data['start_date'] = $data['start_date'] . ' 00:00:00';
            }
            $result = $result->whereRaw("(status_time->0->>'time')::timestamp >= ?", [$data['start_date']]);
        }

        if ($data['end_date']) {
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $data['end_date'])) {
                $data['end_date'] = $data['end_date'] . ' 23:59:59';
            }
            $result = $result->whereRaw("(status_time->json_array_length(status_time)-1->>'time')::timestamp <= ?", [$data['end_date']]);
        }

        if($data['status']){
            $result = $result->where('status', $data['status']);
        }

        $result= $result->orderBy('created_at', 'desc');

        $count = $result->count();

        $deliveries = $result->offset($data['offset'])
            ->limit($data['limit'])
            ->get();

        return [
            'count' => $count,
            'result' => $deliveries
        ];
    }

    public static function checkDiscountBySettingShop($price, $order , $shop = null)
    {
        if(!$price) return null;
        $order['delivery_price'] = $price;
        $maxDiscount = 0;
        if ($shop && !empty($shop->settings['order']['delivery']) && $order) {
            $discounts = $shop->settings['order']['delivery'];
            foreach ($discounts as $discount) {
                // Skip if not enabled
                if (!$discount['enabled']) continue;
                switch ($discount['type']) {
                    case "percent":
                        $discountPrice = $price * ($discount['value'] / 100); break;
                    case "money":
                        $discountPrice = $discount['value']; break;
                    default:
                        $discountPrice = 0;
                }
                if(isset($discount['max_value']) && $discount['max_value'] < $discountPrice){
                    $discountPrice = (float)$discount['max_value'];
                }
                if($discountPrice <= $maxDiscount) continue;

                $flagCondition = false;
                // Evaluate conditions using a switch case
                switch ($discount['relationship_condition']) {
                    case "AND":
                        $flagCondition = true;
                        foreach ($discount['conditions'] as $condition) {
                            if (!self::checkCondition($condition, $order)) {
                                $flagCondition = false;
                                break;
                            }
                        }
                        break;
                    case "OR":
                        foreach ($discount['conditions'] as $condition) {
                            if (self::checkCondition($condition, $order)) {
                                $flagCondition = true;
                                break;
                            }
                        }
                        break;
                }

                // Calculate discount if conditions are met
                if ($flagCondition) {
                    $maxDiscount = $discountPrice;
                }
            }
        }

        return abs($maxDiscount) * -1;
    }

    public static function checkCondition($condition, $order) {
        
        if (!isset($order[config('constants.condition_setting_param.'.$condition['param'])])) return false;
        // Map operators to their respective checks
        $value = $order[config('constants.condition_setting_param.'.$condition['param'])];
        $conditionValue = $condition['value'];

        switch ($condition['operator']) {
            case 'greater_than_or_equal_to':
                return $value >= $conditionValue;
            case 'less_than_or_equal_to':
                return $value <= $conditionValue;
            case 'equal_to':
                return $value == $conditionValue;
            case 'greater_than':
                return $value > $conditionValue;
            case 'less_than':
                return $value < $conditionValue;
            case 'not_equal_to':
                return $value != $conditionValue;
            case 'in':{
                if(gettype($conditionValue) != "array"){
                    $conditionValue = explode(',', $conditionValue);
                }
                $conditionValue =  array_map("trim", $conditionValue);
        //                die(json_encode([$value, $conditionValue]));
                return in_array(trim($value), $conditionValue);
            }
            case 'not_in': {
                if(gettype($conditionValue) != "array"){
                    $conditionValue = explode(',', $conditionValue);
                }
                $conditionValue =  array_map("trim", $conditionValue);
                return !in_array(trim($value), $conditionValue);
            }

            default:
                return false;
        }
    }
}
