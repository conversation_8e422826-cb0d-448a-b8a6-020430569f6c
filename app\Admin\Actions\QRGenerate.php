<?php

namespace App\Admin\Actions;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Image;

class QRGenerate extends RowAction
{
    public function name()
    {
        return __('admin.qr_maker');
    }

    public function handle (Model $model)
    {

        // $model->save();
   
        $logo_path = '';
        if($model->logo_id != null && $model->logo_id != ''){
            $logo = Image::findOrFail($model->logo_id);
            if($logo){
                $logo_path = env('S3_HOST_PATH').$logo['path'];
            }
        }
        $imageUrl = GeneralService::generateQRCodeAndPlaceInFrame(env('APP_URL')."shop/".$model->slug, $logo_path, $model->name);
        $model->qr_code = $imageUrl;
        $model->save();
        return $this->response()->success('QRGenerate')->refresh();
    }

    // public function dialog()
    // {
    //     $this->confirm('Đặt người dùng này là Đại lý môi giới?');
    // }
}