<?php
namespace App\Services\Notification;

require __DIR__ . '/../../../vendor/autoload.php';

use App\Jobs\SendNotification;
use App\Organize;
use Illuminate\Support\Facades\Auth;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Response;
use App\Setting;
use Illuminate\Support\Facades\Log;
use Google\Client as GoogleClient;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use App\Token;

class NotificationService
{
    private $serverKey = null;
    private $url = null;
    private $projectId=null;
    public function __construct()
    {
        $setting = Setting::where('key', 'firebase_cloud_messaging_options')->first();
        $options = json_decode($setting->value ?? '{}');
        $this->url = $options->url ?? 'https://fcm.googleapis.com/fcm/send';
        $this->serverKey = $options && $options->serverKey ? $options->serverKey : null;
        $this->projectId = $options && $options->projectId ? $options->projectId : null;
    }
    /**
     * @param $deviceTokens
     * @param $data
     * @throws GuzzleException
     */
    public function sendBatchNotification($deviceTokens, $data = [])
    {

        $information  = compact('deviceTokens', "data");
        SendNotification::dispatch($information);
/*        $this->sendNotification($data, $deviceTokens);*/
    }

    /**
     * @param $data
     * @param $tokens
     * @throws GuzzleException
     */
    public function sendNotification($data, $tokens = null)
    {
        $credentialsFilePath = __DIR__ .'/remagan-account-service.json';
        $client = new GoogleClient();
        $client->setAuthConfig($credentialsFilePath);
        $client->addScope('https://www.googleapis.com/auth/firebase.messaging');
        $client->refreshTokenWithAssertion();
        $token = $client->getAccessToken();

        $access_token = $token['access_token'];

        $headers = [
            "Authorization: Bearer $access_token",
            'Content-Type: application/json'
        ];
        Log::channel('notify')->info('Ready to send: '. count($tokens) .' notifications.');
        foreach($tokens as $token){
            $dataPost = [
                "message" => [
                    'token'    => $token,
                    // 'notification' => [
                    //     'body' => $data['body'] ?? 'Something',
                    //     'title' => $data['title'] ?? 'Something',
                    //     'image' => $data['image'] ?? null,
                    //     'icon'  => env('S3_HOST_PATH').'favicon-noti.png',
                    //     'collapseKey' => 'new_order'
                    // ],
                    'data' => [
                        'url' => $data['url'] ?? null,
                        'target_url' => $data['target_url'] ?? '/',
                        'type' => $data['type'] ?? null,
                        'redirect_to' => $data['redirect_to'] ?? null,
                        'body' => $data['body'] ?? 'Something',
                        'title' => $data['title'] ?? 'Something',
                        'image' => $data['image'] ?? null,
                        'extra' => $data['extra'] ?? null,
                        'icon'  => $data['icon'] ?? env('S3_HOST_PATH').'favicon-noti.png',
                        'collapseKey' => 'new_order'
                    ],
                    'apns' => [
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $data['title'] ?? 'Something',
                                    'body' => $data['title'] ?? 'Something',
                                ],
                                'content_available' => true,
                            ],
                        ],
                    ],
                    'android' => [
                        'priority' => 'high',
                    ],
                    'webpush' => [
                        'headers' => [
                        'image' => $data['image'] ?? null,
                        'icon'  => $data['icon'] ??  env('S3_HOST_PATH').'favicon-noti.png',
                        ]
                    ],
                ]
            ];

            $payload = json_encode($dataPost);
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send");
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
            curl_setopt($ch, CURLOPT_VERBOSE, true); // Enable verbose output for debugging
            $result = curl_exec($ch);
            $result = json_decode($result);
            // dd($result->error->status);
            if(isset($result->error->status) && $result->error->status == 'NOT_FOUND'){
                Log::channel('notify')->info('Not found token '." :: " . $token.' on FIREBASE --> DELETE!');
                Token::where('token', $token)->delete();
                continue;
            }

            Log::channel('notify')->info("Success send to: " .$token);
            $err = curl_error($ch);
            curl_close($ch);
        }


        // $this->execute($data);
    }

    /**
     * @param $url
     * @param array $dataPost
     * @param string $method
     * @return bool
     * @throws GuzzleException
     */
    private function execute($dataPost = [], $method = 'POST')
    {
        $result = false;
        try {
            $client = new Client();
            $result = $client->request($method, $this->url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'key=' . $this->serverKey,
                ],
                'json' => $dataPost,
                'timeout' => 300,
            ]);

            Log::channel('notify')->info($this->url);
            Log::channel('notify')->info(json_encode($result->getReasonPhrase())."|".json_encode($result->getStatusCode()));
            $result = $result->getStatusCode() == Response::HTTP_OK;

        } catch (Exception $e) {
            Log::debug($e);
        }

        return $result;
    }
}
