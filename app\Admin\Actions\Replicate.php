<?php

namespace App\Admin\Actions;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class Replicate extends RowAction
{
    public function name()
    {
        return __('admin.copy');
    }

    public function handle(Model $model)
    {
        $name = __('admin.copy');
        // Here the model's `replicate` method is called to copy the data, then call the `save` method to save it.
        $model->replicate()->save();
        // $model ...

        return $this->response()->success('Copy success.')->refresh();
    }

}