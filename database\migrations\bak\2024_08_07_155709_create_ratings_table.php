<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRatingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ratings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->integer('object_type')->comment('user => 1,product=> 2,shop=> 3,category=> 4, order=> 9');
            $table->uuid('object_id');
            $table->uuid('user_id');
            $table->integer('rating')->unsigned();
            $table->text('review')->nullable();
            $table->timestamps();
            $table->index(['object_id','rating']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ratings');
    }
}
