<?php

namespace App\Services\Request;

use App\Request;
use Carbon\Carbon;

class RequestService
{
    public static function createRequest($userId, $type, $objectId = null, $reason = null)
    {
        $data = [
            'user_id' => $userId,
            'type' => $type,
            'object_id' => $objectId,
            'reason' => $reason,
            'status' => 'pending'
        ];

        if ($type === 'delete_account') {
            $data['scheduled_at'] = Carbon::now()->addDays(30);
        }

        return Request::create($data);
    }

    public function processRequest($requestId)
    {
        $request = Request::findOrFail($requestId);
        $request->status = 'processed';
        $request->save();

        // Thêm logic xử lý tùy thuộc vào loại request
        switch ($request->type) {
            case 'report_product':
                // Xử lý report product
                break;
            case 'report_shop':
                // Xử lý report shop
                break;
            case 'report_user':
                // Xử lý report user
                break;
            case 'delete_account':
                // Xử lý xóa tài khoản
                break;
        }
    }

    public function getPendingRequests()
    {
        return Request::where('status', 'pending')->get();
    }

    public function getScheduledDeletions()
    {
        return Request::where('type', 'delete_account')
            ->where('status', 'pending')
            ->where('scheduled_at', '<=', Carbon::now())
            ->get();
    }
}