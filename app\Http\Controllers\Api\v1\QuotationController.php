<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\MaterialAndQuotation\MaterialAndQuotationService;
use App\Services\GeneralService;
use App\Helpers\S3Utils;
use App\Material;
use App\User;
use Illuminate\Support\Facades\Log;

class QuotationController extends Controller
{
    private $_userId;
    public function detail(Request $request){
        $data = $request->only([
            'quotation_id'
        ]);
        $data['quotation_id'] = isset($data['quotation_id']) ? $data['quotation_id'] : '';

        $service = new MaterialAndQuotationService();
        $result = $service->detail($data['quotation_id']);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }    
    
    public function list(Request $request){
        $data = $request->only([
            'shop_id', 'limit', 'offset', 'from', 'to'
        ]);
        $data['shop_id'] = isset($data['shop_id']) ? $data['shop_id'] : '';
        $data['limit'] = isset($data['limit']) ? $data['limit'] : 50;
        $data['offset'] = isset($data['offset']) ? $data['offset'] : 0;
        $data['from'] = isset($data['from']) ? $data['from'] : '';
        $data['to'] = isset($data['to']) ? $data['to'] : '';

        $service = new MaterialAndQuotationService();
        $result = $service->list($data['shop_id'], $data['limit'], $data['offset'], $data['from'], $data['to']);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
    // -------create quotation---------------
    public function add(Request $request)
    {
        $data = $request->only([
            'supplier_id', 'name', 'from', 'to', 'notes', 'file', "material_quotations"
        ]);
        $service = new MaterialAndQuotationService();
        $result = NULL;
        $responseStatus = JsonResponse::HTTP_OK;
        
        if(!isset($data['supplier_id']) || !isset($data['from']) || !isset($data['to']) || $data['supplier_id'] == '' || $data['from'] == '' || $data['to'] == '' ){
            $responseStatus = JsonResponse::HTTP_BAD_REQUEST;
            $result = "Supplier, From & To Date or quotation was invailid!";
        }else{
            $result = $service->addQuotation($data);
        }

        $response = [
            'status' => $responseStatus,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function update(Request $request)
    {
        $data = $request->only([
            'supplier_id', 'name', 'from', 'to', 'notes', "material_quotations", "id", "status"
        ]);
        $service = new MaterialAndQuotationService();
        $result = NULL;
        $responseStatus = JsonResponse::HTTP_OK;
        
        if(!isset($data['supplier_id']) || !isset($data['from']) || !isset($data['to']) || $data['supplier_id'] == '' || $data['from'] == '' || $data['to'] == '' ){
            $responseStatus = JsonResponse::HTTP_BAD_REQUEST;
            $result = "Supplier, From & To Date or quotation was invailid!";
        }else{
            $result = $service->updateQuotation($data);
        }

        $response = [
            'status' => $responseStatus,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------create and request quotation---------------
    public function request(Request $request)
    {
        $data = $request->only([
            'supplier_ids', 'name', 'from', 'to', 'notes', 'file', "material_quotations"
        ]);
        $service = new MaterialAndQuotationService();
        $result = NULL;
        $responseStatus = JsonResponse::HTTP_OK;
        
        if(!isset($data['supplier_ids']) || !isset($data['from']) || !isset($data['to']) || empty($data['supplier_ids']) || $data['from'] == '' || $data['to'] == '' ){
            $responseStatus = JsonResponse::HTTP_BAD_REQUEST;
            $result = "Supplier, From & To Date or quotation was invailid!";
        }else{
            $result = $service->requestQuotation($data);
        }

        $response = [
            'status' => $responseStatus,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
    
    public function readExcel(Request $request){
        if ($request->hasFile('file')) {
            set_time_limit(300);
            $file = $request->file('file');
            // $filePath = $file->store('excel-files', 'local'); // 'local' disk could be replaced with your desired disk
           
            $data = $request->only([
                'shop_id','row_column','row_start_data','column_start', 'column_finish'
            ]);
            $shopId = isset($data['shop_id']) ? $data['shop_id'] : '';
            $data['row_column'] = isset($data['row_column']) ? $data['row_column'] : 5;
            $data['row_start_data'] = isset($data['row_start_data']) ? $data['row_start_data'] : 6;
            $data['column_start'] = isset($data['column_start']) ? $data['column_start'] : 'A';
            $data['column_finish'] = isset($data['column_finish']) ? $data['column_finish'] : 'M';
            if($shopId == ''){
                $this->_userId = Auth::check() ? Auth::user()->id : null;
                $user = User::where('id',$this->_userId)->with('shop')->first();
                if($user){
                    if(isset($user->shop[0])){
                        $shopId = $user->shop[0]->id;
                    };
                };
            }
            $generalService = new GeneralService();
            // $result = $generalService->readExcel(storage_path('app/' .$filePath), 5, 6, 'A', 'M');
            $result = $generalService->readExcel($file, $data['row_column'], $data['row_start_data'], $data['column_start'], $data['column_finish']);
            
            $MaterialAndQuotationService = new MaterialAndQuotationService();
            
            $ShopMaterials = Material::select('name','id')->where('shop_id', $shopId)->get();
            
            // echo count($ShopMaterials);die;
            foreach ($result['sheet_data'] as $keySheet => $SheetValue) {
                foreach ($SheetValue['row_data'] as $keyRow => $RowValue) {
                    // $Name4Search = isset($RowValue["Tên mặt hàng"]) ? $RowValue["Tên mặt hàng"] : (isset($RowValue["PRODUCT NAME VN"]) ? $RowValue["PRODUCT NAME VN"] : (isset($RowValue["Product name"]) ? $RowValue["Product name"] : ''));
                    $Name4Search = $RowValue["Tên mặt hàng"] ?? $RowValue["PRODUCT NAME VN"] ?? $RowValue["Product name"] ?? '';
                    
                    
                    if($Name4Search != ''){
                        if($shopId){
                            // Filter the collection based on the search term
                            $search = $MaterialAndQuotationService->searchMaterial($Name4Search, $ShopMaterials);
                            if(($search)) {
                                $result['sheet_data'][$keySheet]['row_data'][$keyRow]['material_id'] = $search->id;
                                // break;
                            }else{
                                $result['sheet_data'][$keySheet]['row_data'][$keyRow]['material_id'] = '';
                            }
                        }else{}
                    }else
                    {
                        $result['sheet_data'][$keySheet]['row_data'][$keyRow]['material_id'] = '';
                    }
                    // Log::info($keySheet . ":".$keyRow . "::".$Name4Search);
                }
                // break;
                // Log::info("End foreach 1");
            }
            
            
            
            $fileName = date('YmdHis')."_".uniqid().".xlsx";
            $filePathUploaded = '/quotations/file_upload/';
            S3Utils::uploadFile($file, $filePathUploaded, $fileName);
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result,
                    'file' => $filePathUploaded.$fileName,
                    ]
            ];
            return response()->json($response);
        }

        return response()->json(['error' => 'No file uploaded.'], 400);
    }

    public function filter(Request $request){
        $data = $request->only([
            'material_ids', "from", "to", "order", "sort"
        ]);
        $shopId = '';
        if($shopId == ''){
            $this->_userId = Auth::check() ? Auth::user()->id : null;
            $user = User::where('id',$this->_userId)->with('shop')->first();
            if($user){
                if(isset($user->shop[0])){
                    $shopId = $user->shop[0]->id;
                };
            };
        }
        $data['material_ids'] = isset($data['material_ids']) ? $data['material_ids'] : [];
        $data['from'] = isset($data['from']) ? $data['from'] : '';
        $data['to'] = isset($data['to']) ? $data['to'] : '';
        $data['order'] = isset($data['order']) ? $data['order'] : 'price';
        $data['sort'] = isset($data['sort']) ? $data['sort'] : 'asc';
        $responseStatus = JsonResponse::HTTP_OK;
        $result = NULL;

        $MaterialAndQuotationService = new MaterialAndQuotationService();
        $result = $MaterialAndQuotationService->filter($data['material_ids'], $shopId, $data['order'], $data['sort'], $data['from'], $data['to']);
        $response = [
            'status' => $responseStatus,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
    
}