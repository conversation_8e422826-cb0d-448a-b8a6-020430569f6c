<?php

namespace App\Services\DeliveryPartner;

use App\Order;
use App\Services\Delivery\DeliveryService;
use App\Services\DeliveryPartner\DeliveryPartner;
use App\DeliveryPartner as DeliveryPartnerModel;
use App\Delivery;
use App\Services\Mqtt\MqttChatService;
use App\Services\Notification\NotificationService;
use App\Services\Token\TokenService;
use App\Shop;
use App\ShopDeliveryPartner;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Class AhamoveDeliveryService
 * Handles integration with Ahamove delivery service
 */
class Ahamove extends DeliveryPartner
{
    /** @var string Authentication token */
    private $token;

    /** @var DeliveryPartnerModel Ahamove partner model */

    /** @var string Partner mobile number */
    private $mobile;

    /** @var string Partner name */
    private $name;

    /**
     * Initialize Ahamove service
     */

    public function __construct($shopId = null)
    {
        // Initialize shop ID and partner credentials
        $this->shopId = $shopId;
        $this->name = env('AHAMOVE_PARTNER_NAME');
        $this->mobile = env('AHAMOVE_PARTNER_MOBILE');

        // Get Ahamove delivery partner model
        $this->partner = DeliveryPartnerModel::where('name', 'Ahamove')->first();

        // Handle shop-specific delivery partner connection
        if ($shopId) {
            $shopDeliveryPartner = $this->getShopDeliveryPartner();

            if ($shopDeliveryPartner) {
                if ($shopDeliveryPartner->token_expired <= now()) {
                    $this->token = $this->getToken($shopDeliveryPartner->connect_data);
                    $this->updateConnect($shopDeliveryPartner->connect_data, $shopDeliveryPartner ,$shopDeliveryPartner->token);
                } else {
                    $this->token = $shopDeliveryPartner->token;
                }
            }
        }
    }

    /**
     * Get estimated delivery prices
     *
     * @param array $data Delivery data
     * @return array Price estimates for available services
     */
    public function checkPrice($data)
    {
        $originPoint = $data['path'][0];
        $availableServices = $this->getListService($originPoint['lat'], $originPoint['lng']);

        $requestPayload = [
            'order_time' => 0,
            'path' => $data['path'],
            'services' => $this->getEnabledServices($availableServices),
            'payment_method' => 'CASH',
        ];

        $response = $this->makeApiRequest(
            $this->partner->api_host . '/v3/orders/estimates',
            'POST',
            $requestPayload,
            [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->token,
            ]
        );
        $filterAll = ($data['filter'] ?? '') === 'all';
        return $this->formatPriceResponse($response ?: [], $requestPayload['services'], $data['order'] ?? [], $filterAll);
    }

    /**
     * Get list of available services for location
     */
    private function getListService($lat, $lng)
    {
        $url = $this->partner->api_host . "/v3/services?lat=". $lat ."&lng=".$lng;

        $response = $this->makeApiRequest($url, 'GET', null,  [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->token
        ]);
        return $response ?: [];
    }

    /**
     * Get authentication token
     */
    private function getToken(array $data = [])
    {
        if (empty($data)) {
            $data = $this->getShopConnectData($this->shopId);
        }

        $request = [
            'api_key' => $this->partner->api_key,
            'mobile' => $data['phone'] ?? $this->mobile,
            'name' => $data['name'] ?? $this->name,
            'address' => $data['address'] ?? $this->name,
        ];

        $response = $this->makeApiRequest(
            $this->partner->api_host . '/v3/accounts/token',
            'POST',
            $request,
            ['Content-Type: application/json']
        );

        return isset($response['token']) ? $response['token'] : false;
    }

    /**
     * Create new delivery order
     */
    public function createDelivery($data, $orderId)
    {
        $errorService = new DeliveryErrorService();

        try {
            $order = $this->getOrder($orderId);
            $shop = Shop::find($this->shopId);

            // Log the delivery creation attempt
            $errorService->logApiCall('ahamove', $orderId, 'createDelivery', $data);

        if($order->delivery_id) {
            $deliveryPartner =  DeliveryPartnerModel::where('id', $order->delivery_partner_id)->first();
            $deliveryService = new DeliveryPartnerService();
            $deliveryService = $deliveryService->setDeliveryPartner(strtolower($deliveryPartner->name), $this->shopId);
            $deliveryDetail = $deliveryService->detailDelivery($orderId);

            if(isset($deliveryDetail['message'])) {
                return [
                    'message' => 'Delivery has created',
                    'data'    =>  null,
                ];
            }

            if($deliveryDetail['status'] != config('constants.delivery.cancelled')) {
                return [
                    'message' => 'Delivery has created',
                    'data'    =>  $deliveryDetail,
                ];
            }

        };

        $request = [
            'service_id' => $data['delivery_service'],
            'payment_method' => strtoupper($data['payment_method']),
            'promo_code' => $data['promo_code'] ?? "",
            'remarks' => $data['note'] ?? "",
            'order_time' => isset($data['pickup_time']) && strtotime($data['pickup_time']) ? strtotime($data['pickup_time']) : 0,
            'path' => $data['path'],
        ];

        if($request['order_time'] > 0){
            $request['idle_until'] = $request['order_time'];
        }


        $response = $this->makeApiRequest(
            $this->partner->api_host . '/v3/orders',
            'POST',
            $request,
            [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->token,
            ]
        );

        if ($response) {
            // Lưu thông tin vận đơn trong order trước khi lấy chi tiết vận đơn
            $order->delivery_partner_id = $this->partner->id;
            $order->delivery_id = $response['order_id'];
            $order->save();
            // Chi tiết vận đơn đã được thành công
            $detail =  $this->detailDelivery($orderId);
            $order->delivery_price = $detail['grand_total'];
            $order->delivery_discount = DeliveryService::checkDiscountBySettingShop($detail['grand_total'], $order, $shop);
            $order->save();
            $msgToCustomer = [
                'title' => "Đơn hàng đã được vận đơn",
                'body'  => 'Đơn hàng '.$order->short_code .' từ cửa hàng '. $shop->name .'đã được vận đơn',
                'url'   => $order->id,
                'target_url' => '/my-orders/'. $order->short_code,
                'type'  => 'delivery_new',
            ];
            $mqtt = new MqttChatService();

            $mqtt->publish(['topic' => 'remagan_order/'. $order->id , 'message' => $msgToCustomer]);

            // Log successful delivery creation
            $errorService->logSuccess('ahamove', $orderId, $response['order_id'], [
                'delivery_price' => $detail['grand_total'],
                'delivery_discount' => $order->delivery_discount,
                'service_id' => $data['delivery_service']
            ]);

            return $detail;
        }

        // Log API failure
        $errorService->logAndNotify(
            'API_RESPONSE_FAILED',
            'Ahamove API returned empty or invalid response',
            'ahamove',
            $orderId,
            [
                'request_data' => $request,
                'response' => $response,
                'api_endpoint' => $this->partner->api_host . '/v3/orders'
            ]
        );

        return [
            'message' => 'Create order delivery failed'
        ];

        } catch (\Exception $e) {
            // Log the error with full context
            $errorService->logAndNotify(
                'DELIVERY_CREATION_EXCEPTION',
                'Exception occurred during Ahamove delivery creation: ' . $e->getMessage(),
                'ahamove',
                $orderId,
                [
                    'request_data' => $data ?? [],
                    'shop_id' => $this->shopId,
                    'order_details' => isset($order) ? [
                        'id' => $order->id,
                        'short_code' => $order->short_code ?? null,
                        'existing_delivery_id' => $order->delivery_id ?? null
                    ] : null,
                    'api_endpoint' => $this->partner->api_host . '/v3/orders'
                ],
                $e
            );

            return [
                'message' => 'Create order delivery failed due to system error'
            ];
        }
    }

    /**
     * Get delivery order details
     */
    public function detailDelivery($orderId)
    {
        $order =$this->getOrder($orderId);

        if (!$order->delivery_id) {
            return ['message' => 'Delivery not created'];
        }

        $url = $this->partner->api_host."/v3/orders/".$order->delivery_id;
        $result = $this->makeApiRequest($url, 'GET', null, [
            'Authorization: Bearer ' . $this->token,
        ]);

        if(!$result){
            return ['message' => 'Delivery not created in system'];
        }

        $path = $result['path'];
        $startPoint = $path[0];
        $endPoint = $path[count($path) - 1];

        $availableServices = $this->getListService($startPoint['lat'], $startPoint['lng']);
        $services = $this->getEnabledServices($availableServices);
        $serviceId = $result['service_id'];

        $selectedService = array_filter($services, function ($service) use ($serviceId) {
            return $service['_id'] == $serviceId;
        });

        // `array_filter` trả về mảng. Để lấy phần tử đầu tiên:
        $selectedService = empty($selectedService) ? null : reset($selectedService);


        $response = [
          'id' => null,
          'order_id' => $orderId,
          'driver_id'=> null,
          'status' => $this->getStatus($result['status']),
          'duration' => ceil($result['duration'] / 60),
          "pickup_time" => Carbon::createFromTimestamp($result['order_time'])->format('Y-m-d H:i:s'),
          "notes" => $result['remarks'] ?? "",
          "latitude_from" => $startPoint['lat'],
          "longitude_from"=> $startPoint['lng'],
          "latitude_to"   => $endPoint['lat'],
          "longitude_to"  => $endPoint['lng'],
          "distance"      => $result['distance'],
          "address_from"  => $startPoint['address'],
          "address_to"    => $endPoint['address'] ?? "",
          "name_from"     => $startPoint['name'] ?? "",
          "phone_from"    => $startPoint['mobile'] ?? "",
          "name_to"       => $endPoint['name'] ?? "",
          "phone_to"      => $endPoint['mobile'] ?? "",
          "status_time" => null,
          "package_info"=> null,
          "special_require" => [],
          "total_amount" => $result['total_price'] ?? 0,// Total fee = Distance Fee + Request Fee + Stop Fee - Discount
          "discount_id" => null,
          "discount_amount" => $result['discount'] ?? 0,
          "grand_total" => $result['total_price'],
          "created_at" => Carbon::parse($result['order_time'])->format('Y-m-d H:i:s'),
          "updated_at" => null,
          "short_code" => $result['_id'],
          "order" => $order,
          "driver" => null,
          "cod_price" => $endPoint['cod'] ?? "",
          "service" => $selectedService ? [
              'id' => $serviceId,
              'name' => $selectedService['name'] ?? "",
              'name_vi_vn' => $selectedService['name_vi_vn'] ?? "",
              'name_en_us' => $selectedService['name_en_us'] ?? "",
          ] : null,
            "shared_link" => $this->getShareLink($order->delivery_id)
        ];
        return $response;
    }

    /**
     * Cancel delivery order
     */
    public function cancelDelivery($data, $orderId)
    {
        $order = $this->getOrder($orderId);

        if (!$order->delivery_id) {
            return ['message' => 'Delivery not created'];
        }

        $delivery = $this->detailDelivery($orderId);

        if(isset($delivery['message'])){
            return $delivery;
        }
        $url = $this->partner->api_host. "/v3/orders/".$order->delivery_id;
        $result = $this->makeApiRequest($url, 'DELETE', [
            'comment'  => $data['note'] ?? ""
        ], [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->token,
        ]);

        if($result === [] || $delivery['status'] == config('constants.delivery.cancelled')){
            $order->update([
                'delivery_id' => null,
            ]);
            return true;
        }
        return ['message' => "Failed to cancel delivery"];
    }

    /**
     * Connect shop with delivery partner
     */
    public function connectDeliveryPartner($data)
    {
        // Get existing delivery partner connection for this shop
        $connection = $this->getShopDeliveryPartner();

        // Prepare connection data
        $connectData = $data;

        // Format phone number to include country code if needed
        if(isset($connectData['phone'])) {
            $phoneNumber = $connectData['phone'];
            $connectData['phone'] = str_starts_with($phoneNumber, '0') ? '84' . substr($phoneNumber, 1) : $phoneNumber;
        }

        // Register new account with delivery partner
        if (!$this->register($connectData)) {
            return ['error' => 'Connection failed'];
        }

        // Get authentication token
        $token = $this->getToken($connectData);
        if (!$token) {
            return ['error' => 'Get token connect failed'];
        }

        // Update existing connection or create new one
        return $connection ?
            $this->updateConnect($data, $connection, $token) :
            $this->createConnect($data, $token);
    }

    /**
     * Create new delivery partner connection
     */
    private function createConnect($data, $token)
    {
        $payload = $this->decodeJwtPayload($token);

        return ShopDeliveryPartner::create([
            'shop_id' => $this->shopId,
            'delivery_partner_id' => $this->partner->id,
            'connect_data' => $data,
            'token' => $token,
            'token_expired' => $payload ? Carbon::createFromTimestamp($payload['exp']) : Carbon::now()->addYear(),
            'is_default' => !$this->hasConnectedToDeliveryPartner()
        ]);
    }

    /**
     * Update existing delivery partner connection
     */
    private function updateConnect($data, $connection, $token)
    {
        $payload = $this->decodeJwtPayload($token);

        $connection->update([
            'connect_data' => $data,
            'token' => $token,
            'token_expired' => $payload ? Carbon::createFromTimestamp($payload['exp']) : Carbon::now()->addYear(),
        ]);

        return $this->getShopDeliveryPartner();
    }

    /**
     * Register new Ahamove account
     */
    private function register($data)
    {
        $request = [
            'api_key' => $this->partner->api_key,
            'mobile' => $data['phone'],
            'name' => $data['name'],
            'address' => $data['address'],
        ];

        $response = $this->makeApiRequest(
            $this->partner->api_host . '/v3/accounts',
            'POST',
            $request,
            ['Content-Type: application/json']
        );
        return $response !== false;
    }

    /**
     * Make HTTP API request
     */
    private function makeApiRequest($url, $method, $data = null, $headers = [])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, is_string($data) ? $data : json_encode($data));
        }
        if ($method === 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            curl_setopt($ch, CURLOPT_POSTFIELDS, is_string($data) ? $data : json_encode($data));
        }

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // Log::info(json_decode($result, true));
//        die($result);
        return ($httpCode == 200 || $httpCode == 409) ? json_decode($result, true) : false;
    }

    /**
     * Helper methods
     */
    private function getEnabledServices($services)
    {
        $enabledServices = [];
        foreach ($services as $service) {
            if ($service['enable']) {
                $enabledServices[] = [
                    '_id' => $service['_id'],
                    'name' => $service['name'],
                    'name_vi_vn' => $service['name_vi_vn'],
                    'name_en_us' => $service['name_en_us'] ?? "",
                ];
            }
        }
        return $enabledServices;
    }

    private function formatPriceResponse($response, $services, $order, $getAllService = false)
    {
        $serviceMap = array_column($services, null, '_id');
        $validResponses = array_filter($response, fn($item) => !isset($item['http_code']));
        $mergeResponse = array_map(function($item) use ($serviceMap) {
            return array_merge($item, $serviceMap[$item['service_id']] ?? []);
        }, array_values($validResponses));
        $result = [];

        $shop =  Shop::find($this->shopId);

        $order['grand_total'] = $order['grand_total'] ?? $order['price_off'] ?? 0;
        # BIKE : Siêu Tốc
        # ECO : Siêu Tốc - Tiết Kiệm
        # POOL : Siêu Rẻ
        $serviceLang = 'BIKE';
        $RusLang = [
            'BIKE' => 'Супер Скорость - Экономия',
            'ECO' => 'Супер Скорость - Экономия',
            'POOL' => 'Супер Дешево',
        ];
        $KoreaLang = [
            'BIKE' => '초고속도 - 절약',
            'ECO' => '초고속도 - 절약',
            'POOL' => '초저가',
        ];
        foreach ($mergeResponse as $item) {
            $itemService = [];
            if(isset($item['error'])){
                if(isset($shop->settings['order']['delivery_default_message'])){
                    $itemService['error'] = [
                        "message" => $shop->settings['order']['delivery_default_message']
                    ];
                }else{
                    $itemService['error'] = [
                        "message" => [
                            'vi' => "Thông báo phí vận chuyển sau khi đặt hàng",
                            'en' => "Shipping fee provided after order placement"
                        ]
                    ];
                }
            }else{
                $price = $item['data']['total_price'] ?? null;

                $order['delivery_distance'] = $item['data']['distance'] ?? null;
                $serviceLang = 'BIKE';
                if (strpos($item['service_id'], 'ECO') !== false) {
                    $serviceLang = 'ECO';
                }elseif(strpos($item['service_id'], 'POOL') !== false){
                    $serviceLang = 'POOL';
                }
                
                $itemService = [
                    'service_id' => $item['service_id'],
                    'name' => $item['name'],
                    'name_vi_vn' => $item['name_vi_vn'],
                    'name_en_us' => $item['name_en_us'],
                    'name_ru_ru' => $RusLang[$serviceLang] ?? '',
                    'name_ko_kr' => $KoreaLang[$serviceLang] ?? '',
                    'price' =>  $price,
                    'discount' => DeliveryService::checkDiscountBySettingShop($price, $order, $shop),
                    'distance' => $item['data']['distance'] ?? null,
                    'duration' => isset($item['data']['duration']) ? ceil($item['data']['duration'] / 60) : null,
                ];
            }
            # chỉ trả về loại siêu tốc nếu có(Mr.K yêu cầu 2025/04/07)
            if($serviceLang == 'BIKE' && $getAllService == false){
                return [ $itemService ];
            }
            $result[] = $itemService;
        }
        return $result;
    }

    private function decodeJwtPayload($token)
    {
        $result = json_decode(base64_decode(explode(".", $token)[1]), true);
        return $result;
    }

    private function getShopConnectData($shopId)
    {
        return ShopDeliveryPartner::on('pgsqlReplica')
            ->where([
                'shop_id' => $shopId,
                'delivery_partner_id' => $this->partner->id
            ])->first()->connect_data;
    }

    function notification($data, $orderId)
    {
        $status = $data["status"];
        $pathLength = isset($data["path"]) ? count($data["path"]) : 0;
        if($status == "COMPLETED" || $status == "IN PROCESS") {
            if($pathLength > 0) {
                $status = $data['path'][$pathLength - 1]['status'];
            }
        }

        $service = new DeliveryPartnerService();
        $mappedStatus = $this->getStatus($status);
        if($mappedStatus) {
            $service->notification($mappedStatus, $orderId);
        }
    }

    private function getStatus($status){
        $statusFormat = [
            'IDLE'    => config('constants.delivery.pending'),
            'PENDING' => config('constants.delivery.pending'),
            'ASSIGNING' => config('constants.delivery.pending'),
            'ACCEPTED' => config('constants.delivery.confirmed'),
            'IN_PROCESS' => config('constants.delivery.picked_up'),
            'IN PROCESS' => config('constants.delivery.picked_up'),
            'COMPLETED' => config('constants.delivery.delivered'),
            'FAILED' => config('constants.delivery.failed'),
            'CANCELLED' => config('constants.delivery.cancelled'),
        ];
        return $statusFormat[$status] ?? null;
    }

    private function getShareLink($deliveryId)
    {
        $url = $this->partner->api_host. "/v3/orders/".$deliveryId."/shared-link";
        $result = $this->makeApiRequest($url, 'GET', null, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->token,
        ]);
        return $result['shared_link'] ?? null;
    }
}
