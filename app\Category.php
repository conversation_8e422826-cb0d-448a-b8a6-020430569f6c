<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Product;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Helpers\Helper;

class Category extends Model
{
    protected $fillable = [
        'name',
        'created_by',
        'enable',
        'slug',
        'profile_picture',
        'parent_id',
        'shop_id',
        'index',
        'notes'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
            $myModel = new Category;
            $model->slug = (!$myModel->where('slug', Str::slug($model->name))->exists()) ? Str::slug($model->name) : Str::slug($model->name)."_".uniqid();
        });

        static::saving(function($model){
            $model->name = preg_replace('([\s]+)', ' ', $model->name);
            if($model->id){
                Helper::flushCacheByTag(env('APP_ENV').":"."category_list_system");
            }
        });
        static::deleting(function ($model) {
            $model->products()->detach();
            Helper::flushCacheByTag(env('APP_ENV').":".'category_list_system');
        });
    }

    public $incrementing = false;
    protected $primaryKey = 'id';
    protected $table = 'categories';
    protected $hidden = ['created_at', 'updated_at'];
    protected $keyType = 'string';

    public function created_by()
    {
        return $this->belongsTo(User::class, 'created_by')->select('id','name','role_id');
    }

    public function categories()
    {
        return $this->hasMany(Category::class, 'parent_id')->where('enable', true)->orderBy('name');
    }


    public function parent_category()
    {
        return $this->belongsTo(Category::class, 'parent_id')->orderBy('name');
    }

    public function sub_categories()
    {
        return $this->hasMany(Category::class, 'parent_id')->where('enable', true)->orderBy('name');
    }

    public function translation()
    {
        return $this->hasMany(Translation::class,'object_id')->select('id','object_id','object_type','language_code','name','description');
    }
    
    public function products(): BelongsToMany
    {
        $pivotTable = 'product_categories';

        $relatedModel = Product::class;

        return $this->belongsToMany($relatedModel, $pivotTable,'category_id', 'product_id')->distinct();
    }

    public function products_super(): BelongsToMany
    {
        $pivotTable = 'product_categories';

        $relatedModel = Product::class;

        return $this->belongsToMany($relatedModel, $pivotTable,'category_id', 'product_id')->distinct();//->orderBy('index','asc');
    }


    public function materials()
    {
        return $this->belongsToMany(Material::class, 'material_categories', 'category_id', 'material_id')->distinct();
    }
}
