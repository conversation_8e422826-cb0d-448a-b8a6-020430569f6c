<?php
namespace App\Services\Log;

use App\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\User;

class LogService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId)
        {
            $this->_userId = $userId;
        }
        else
        {
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

    //---process insert log --------
    public function add(array $log)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $log['created_by'] = $user->id;
        }

        $result = Log::create($log);

        return $result;
    }

    // //-----process list log---------

    // public function list()
    // {
    //     $result = Log::with('created_by:id,name')->orderBy('created_at', 'desc')->get();

    //     return $result;
    // }

    // // -----process delete-----------

    // public function delete($id)
    // {
    //     $result = Log::find($id);

    //     $result->delete();

    //     return $result;
    // }
    public function update(array $log)
    {
        $result = Log::find($log['id']);
        $result->update($log);
        return $result;
    }
}
