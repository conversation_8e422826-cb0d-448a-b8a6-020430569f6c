<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Category;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\SearchIndexConfigurator;
use App\IndexElasticsearch;
use ScoutElastic\Searchable;
use Staudenmeir\EloquentEagerLimit\HasEagerLimit;
use App\Helpers\Helper;
use Illuminate\Support\Facades\Log;
use App\User;
use App\Shop;

class Product extends Model
{
    use  HasEagerLimit;
    protected $indexConfigurator = SearchIndexConfigurator::class;    protected $fillable = [
        'name',
        'is_main',
        'type',
        'brand_id',
        'profile_picture',
        'latitude',
        'longitude',
        'notes',
        'shop_id',
        'price',
        'price_root',
        'price_off',
        'enable',
        'created_by',
        'parent_id',
        'extra_id',
        'unaccent_name',
        'is_feature',
        'lowercase_name',
        'stock',
        'sold_count',
        'slug',
        'is_suggest',
        'views',
        'likes',
        'follows',
        'ratings',
        'extra_code',
        'commission_percent',
    ];
    protected $primaryKey = 'id';
    protected $table = 'products';
    public $incrementing = false;
    // public $timestamps = true;
    protected $dateFormat = 'Y-m-d H:i:s';
    protected $keyType = 'string';
    protected $casts = [
        'price' => 'float',
        'price_root' => 'float',
        'price_off' => 'float',
        'ratings' => 'float',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'commission_percent' => 'float',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = $model->id ?? Str::uuid();
            $model->unaccent_name = Helper::unaccentVietnamese($model->name, true);
            $model->lowercase_name = Str::lower($model->name);
            $randomAlphabet = strtolower(Str::random(2)); // Generate two random lowercase letters
            $uniqueIdentifier = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT); // Generates a unique 4-digit numeric identifier

            // Combine the date prefix, random uppercase alphabet characters, and the unique identifier
            $customSlug = Str::slug($model->name) . '-' . $randomAlphabet . $uniqueIdentifier;

            $model->slug = $customSlug;
        });

        static::created(function ($model) {
            // Record initial stock if set
            if ($model->stock > 0) {
                StockTracking::recordStockChange(
                    $model->id,
                    0,
                    $model->stock,
                    'initial_stock',
                    null
                );
            }
            if ($model->shop_id) {
                Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $model->shop_id);
            }
        });

        static::updating(function ($model) {
            // $model->updated_at = now();
        });

        static::saving(function ($model) {
            $model->unaccent_name = Helper::unaccentVietnamese($model->name, true);
            $model->lowercase_name = Str::lower($model->name);

            if ($model->shop_id) {
                Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $model->shop_id);
            }
        });

        static::deleting(function ($model) {
            $model->categories()->detach();
            if ($model->shop_id) {
                Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $model->shop_id);
                Helper::flushCacheByTag(env('APP_ENV').":".'dashboard');
            }
        });
    }
    protected $hidden = ['updated_at', 'created_at'];

    public function shop()
    {
        return $this->belongsTo(Shop::class,'shop_id')->with('logo');
    }

    public function categories2()
    {
        // return $this->belongsToMany(Category::class);
        return $this->belongsToMany(Category::class, 'product_categories','product_id','category_id')->distinct();
    }

    /**
     * A product has and belongs to many categories.
     *
     * @return BelongsToMany
     */
    public function categories(): BelongsToMany
    {
        $pivotTable = 'product_categories';

        $relatedModel = Category::class;

        return $this->belongsToMany($relatedModel, $pivotTable, 'product_id','category_id')->distinct()->with(['translation']);
    }

    public function children_products()
    {
        return $this->hasMany(Product::class, 'parent_id')->where('enable', true)->orderBy('name')->distinct();
    }

    public function parent_product()
    {
        return $this->belongsTo(Product::class, 'parent_id')->orderBy('name');
    }
    // public function brands()
    // {
    //     // return $this->belongsTo(Brand::class,'brand_id');
    // }

    // public function profile_pictures()
    // {
    //     return $this->belongsTo(Image::class,'profile_picture');
    // }
    public function order_items()
    {
        return $this->belongsToMany(Order::class, 'order_items', 'order_id', 'product_id')->distinct();
    }

    public function translation()
    {
        return $this->hasMany(Translation::class,'object_id')->select('id','object_id','object_type','language_code','name','description');
    }
    public function images()
    {
        return $this->hasMany(Image::class, 'parent_id')->orderBy('index','desc');
    }

    /**
     * Check if a user can view the price_root field
     * Only shop owners and agents can view price_root
     */
    public function canViewPriceRoot($userId)
    {
        if (!$userId) {
            return false;
        }

        $user = User::find($userId);
        if (!$user) {
            return false;
        }

        // Admin can view all
        if ($user->role_id == User::ROLE_ADMIN) {
            return true;
        }

        // Check if user is shop owner
        if ($this->isShopOwner($userId)) {
            return true;
        }

        // Check if user is agent of the shop
        if ($this->isShopAgent($userId)) {
            return true;
        }

        return false;
    }

    /**
     * Check if a user can edit the price_root field
     * Only shop owners and agents can edit price_root
     */
    public function canEditPriceRoot($userId)
    {
        return $this->canViewPriceRoot($userId);
    }

    /**
     * Check if user is the owner of the shop that owns this product
     */
    public function isShopOwner($userId)
    {
        $shop = Shop::find($this->shop_id);
        return $shop && $shop->user_id == $userId;
    }

    /**
     * Check if user is an agent of the shop that owns this product
     */
    public function isShopAgent($userId)
    {
        $shop = Shop::find($this->shop_id);
        return $shop && $shop->agent_id == $userId;
    }
}
