<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class OpenTime extends Model
{
    protected $fillable = [
        'object_id',
        'object_type',
        'from',
        'to'
    ];
    protected $primaryKey = 'id';
    protected $table = 'open_times';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
    protected $hidden = ['updated_at', 'created_at'];

    public function created_by()
    {
        return $this->belongsTo(User::class,'created_by');
    }

    public function objects()
    {
        if($this->object_type == 1){
            return $this->belongsTo(Shop::class,'object_id');
        }
        else return $this->belongsTo(Product::class,'object_id');
        
    }
}
