<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Log;
use App\Services\Mqtt\MqttChatService;
use App\User;
use App\Services\HistoryMail\HistoryMailService;

class NotificationReport extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    private $_user;
    private $_report;
    private $text;
    private $_person;
    private $_object_type;
    private $_confirm;
    public function __construct($user, $report, $person, $object_type)
    {
        $this->_person = $person;
        $this->_user = $user;
        $this->_report = $report;
        $this->_object_type = $object_type;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
        // return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $mqtt = new MqttChatService();
        $result = User::find($this->_user->id);
        $_notify = $result->notifications();
        $countUnread = $_notify->whereNull('read_at')->count();
        $confirm = $this->_person == config('constants.report.annunciator') ? "Bài báo cáo của bạn: ".$this->_report->name. " đã được duyệt." : "Bài đăng của bạn bị báo cáo: ".$this->_report->name;
        $path = null;
        $mqtt->publish(['topic' => $this->_user->id, 'message' => $countUnread]);
        return [
            'content' => $confirm,
            'image' => $path
        ];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // Log::info("person: ".$this->_person);
        $historyMail = null;
        try {
            if($this->_person == config('constants.report.annunciator'))
            {
                if($this->_object_type == config('constants.object_type_report.post'))
                {
                    $this->text = "Báo cáo về bài viết của quý khách: ";
                    $this->_confirm = 2;

                    $historyMail = HistoryMailService::insert([
                        'title' => 'Thông Báo Bài Báo Cáo NOMNOMLAND',
                        'content' => json_encode(['content' => $this->text, 'object_name' => $this->_report->name, 'confirm' => $this->_confirm]),
                        'status' => config('constants.status_history_mail.success')
                    ]);

                    return (new MailMessage)->subject('Thông Báo Bài Báo Cáo NOMNOMLAND')->view('emails.notification_report',['content' => $this->text, 'object_name' => $this->_report->name, 'confirm' => $this->_confirm]);
                }
                else
                {
                    // Log::info("nguoi bao cao");
                    $this->text = "Báo cáo về bài bất động sản của quý khách ";
                    $textEng = "Your real estate report ";
                    $this->_confirm = 2;

                    $historyMail = HistoryMailService::insert([
                        'title' => 'Thông Báo Bài Báo Cáo NOMNOMLAND',
                        'content' => json_encode(['content' => $this->text, 'object_name' => $this->_report->name, 'confirm' => $this->_confirm]),
                        'status' => config('constants.status_history_mail.success')
                    ]);

                    return (new MailMessage)->subject('Thông Báo Bài Báo Cáo NOMNOMLAND')->view('emails.notification_report',['content' => $this->text, 'object_name' => $this->_report->name, 'confirm' => $this->_confirm, 'user_name' => $this->_user->name, 'slug' => $this->_report->slug,'content_eng' => $textEng]);
                }

            }
            else
            {
                if($this->_object_type == config('constants.object_type_report.post'))
                {
                    $this->text = "Bài viết của quý khách bị báo cáo: ";
                    $this->_confirm = 4;

                    $historyMail = HistoryMailService::insert([
                        'title' => 'Thông Báo Bài Báo Cáo NOMNOMLAND',
                        'content' => json_encode(['content' => $this->text, 'object_name' => $this->_report->name, 'confirm' => $this->_confirm]),
                        'status' => config('constants.status_history_mail.success')
                    ]);

                    return (new MailMessage)->subject('Thông Báo Bài Báo Cáo NOMNOMLAND')->view('emails.notification_report',['content' => $this->text, 'object_name' => $this->_report->name, 'confirm' => $this->_confirm]);
                }
                else
                {
                    // Log::info("nguoi bi bao cao");
                    $this->text = "Bài bất động sản của quý khách bị báo cáo: ";
                    $textEng = "Your real estate post ";
                    $this->_confirm = 4;

                    $historyMail = HistoryMailService::insert([
                        'title' => 'Thông Báo Bài Báo Cáo NOMNOMLAND',
                        'content' => json_encode(['content' => $this->text, 'object_name' => $this->_report->name, 'confirm' => $this->_confirm]),
                        'status' => config('constants.status_history_mail.success')
                    ]);

                    return (new MailMessage)->subject('Thông Báo Bài Báo Cáo NOMNOMLAND')->view('emails.notification_report',['content' => $this->text, 'object_name' => $this->_report->name, 'confirm' => $this->_confirm, 'user_name' => $this->_user->name, 'slug' => $this->_report->slug,'content_eng' => $textEng]);
                }
            }
        } catch (\Exception $e) {
            Log::info("Mail Error: ".$e);

            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

    }


}
