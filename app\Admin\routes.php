<?php

use Illuminate\Routing\Router;

Admin::routes();

Route::group([
    'prefix'        => config('admin.route.prefix'),
    'namespace'     => config('admin.route.namespace'),
    'middleware'    => config('admin.route.middleware'),
    'as'            => config('admin.route.prefix') . '.',
], function (Router $router) {

    $router->get('/', 'HomeController@index')->name('home');
    $router->get('/base', 'HomeController@baseIndex')->name('home');
    $router->resource('users', UserController::class);
    $router->resource('products', ProductController::class);
    $router->resource('shops', ShopController::class);
    $router->resource('categories', CategoryController::class);
    $router->resource('orders', OrderController::class);
    $router->resource('images', ImageController::class);
    $router->resource('requests', RequestController::class);
    $router->resource('queues', QueueController::class);
    $router->resource('delivery_partners', DeliveryPartnerController::class);
    $router->resource('shop_delivery_partners', ShopDeliveryPartnerController::class);
    $router->resource('delivery', DeliveryController::class);
    // $router->resource('order_item', OrderItemController::class);
    $router->get('order_item/{order_id}/{product_id}', 'OrderItemController@detail')->name('admin.order_item.show');
    $router->get('order_item/{order_id}/{product_id}/edit', 'OrderItemController@edit')->name('admin.order_item.edit');
    $router->post('order_item/{order_id}/{product_id}/edit', 'OrderItemController@update')->name('admin.order_item.update');

    $router->resource('suppliers', SupplierController::class);
    $router->resource('business_type', BusinessTypeController::class);
});
