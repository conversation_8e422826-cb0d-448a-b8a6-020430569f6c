use App\User;
use App\Order;
echo "Testing referral system..." . PHP_EOL;

// Test 1: Check first user
$user = User::first();
if ($user) {
    echo "Found user: " . $user->name . " (ID: " . $user->id . ")" . PHP_EOL;
    
    // Test referral code generation
    if (!$user->referral_code) {
        $code = $user->generateReferralCode();
        echo "Generated referral code: " . $code . PHP_EOL;
    } else {
        echo "User already has referral code: " . $user->referral_code . PHP_EOL;
    }
    
    // Test findByReferralCode
    $foundUser = User::findByReferralCode($user->referral_code);
    if ($foundUser && $foundUser->id === $user->id) {
        echo "✓ findByReferralCode works correctly" . PHP_EOL;
    } else {
        echo "✗ findByReferralCode failed" . PHP_EOL;
    }
    
    // Test referral stats
    $stats = $user->getReferralStats();
    echo "Referral stats:" . PHP_EOL;
    echo "- Code: " . $stats['referral_code'] . PHP_EOL;
    echo "- Total referrals: " . $stats['total_referrals'] . PHP_EOL;
    echo "- Total referred orders: " . $stats['total_referred_orders'] . PHP_EOL;
    echo "- Successful referrals: " . $stats['successful_referrals'] . PHP_EOL;
    echo "- Total order value: " . $stats['total_referred_order_value'] . PHP_EOL;
} else {
    echo "No users found" . PHP_EOL;
}

echo "Test complete." . PHP_EOL;
