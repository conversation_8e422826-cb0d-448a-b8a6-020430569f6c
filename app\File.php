<?php

namespace App;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class File extends Model
{

    protected $table = 'files';
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $fillable = [
        'id',
        'file_name',
        'file_type',
        'file_path',
        'view_total',
        'like_total',
        'user_id',
        'description',
        'parent_id',
        'object_type',
        'extra_data',
    ];
    protected $casts = [
        'view_total' => 'integer',
        'like_total' => 'integer',
        'extra_data' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = $model->id ?? Str::uuid();
        });

        static::addGlobalScope('thumbnail', function (Builder $builder) {
            $builder->with('thumbnail');
        });
    }
    public function user()
    {
        return $this->belongsTo(User::class)->select('id','name','profile_picture');
    }

    public function thumbnail()
    {
        return $this->hasOne(Image::class, 'parent_id', 'id')->where('object_type', 12);
    }

    // ... additional methods or relationships can be added here ...
}
