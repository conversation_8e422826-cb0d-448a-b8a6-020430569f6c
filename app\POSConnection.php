<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class POSConnection extends Model
{
    protected $table = 'pos_connections';
    protected $primaryKey = 'id';
    protected $keyType = 'string';
    protected $fillable = [
        'shop_id',
        'token',
        'data',
        'token_expired',
        'extra_data',
        'pos_type',
        'enabled',
        'branch_id'
    ];

    protected $casts = [
        'data' => 'array',
        'extra_data' => 'array',
    ];
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }

}
