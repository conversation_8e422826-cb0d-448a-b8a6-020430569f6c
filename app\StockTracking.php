<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class StockTracking extends Model
{
    protected $table = 'stock_tracking';
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = true;

    protected $fillable = [
        'product_id',
        'old_stock',
        'new_stock',
        'change',
        'reason',
        'order_id',
        'executor_id',
        'unit',
        'unit_price',
        'note',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = $model->id ?? Str::uuid();
        });
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function executor()
    {
        return $this->belongsTo(User::class, 'executor_id');
    }

    public static function recordStockChange($productId, $oldStock, $newStock, $reason, $orderId = null, $executorId = null, $unit = null, $unitPrice = null, $note = null)
    {
        return self::create([
            'product_id' => $productId,
            'old_stock' => $oldStock ?? 0,
            'new_stock' => $newStock,
            'change' => $newStock - $oldStock,
            'reason' => $reason,
            'order_id' => $orderId,
            'executor_id' => $executorId,
            'unit' => $unit,
            'unit_price' => $unitPrice,
            'note' => $note,
        ]);
    }

    public static function getStockHistory($productId)
    {
        return self::where('product_id', $productId)
                   ->with(['executor:id,name', 'order:id,short_code'])
                   ->orderBy('created_at', 'desc')
                   ->get();
    }

    /**
     * Record stock import (purchase)
     */
    public static function recordImport($productId, $oldStock, $newStock, $executorId, $unit = null, $unitPrice = null, $note = null)
    {
        return self::recordStockChange(
            $productId,
            $oldStock,
            $newStock,
            'import',
            null,
            $executorId,
            $unit,
            $unitPrice,
            $note
        );
    }

    /**
     * Record stock export (sale)
     */
    public static function recordExport($productId, $oldStock, $newStock, $executorId, $orderId = null, $unit = null, $unitPrice = null, $note = null)
    {
        return self::recordStockChange(
            $productId,
            $oldStock,
            $newStock,
            'export',
            $orderId,
            $executorId,
            $unit,
            $unitPrice,
            $note
        );
    }

    /**
     * Record stock waste/discard
     */
    public static function recordWaste($productId, $oldStock, $newStock, $executorId, $unit = null, $reason = null)
    {
        return self::recordStockChange(
            $productId,
            $oldStock,
            $newStock,
            'waste',
            null,
            $executorId,
            $unit,
            null,
            $reason
        );
    }

    /**
     * Get daily stock summary for a shop
     */
    public static function getDailyStockSummary($shopId, $date = null)
    {
        $date = $date ?? now()->format('Y-m-d');

        return self::join('products', 'stock_tracking.product_id', '=', 'products.id')
                   ->where('products.shop_id', $shopId)
                   ->whereDate('stock_tracking.created_at', $date)
                   ->selectRaw('
                       stock_tracking.reason,
                       COUNT(*) as transaction_count,
                       SUM(ABS(stock_tracking.change)) as total_quantity,
                       SUM(CASE WHEN stock_tracking.unit_price IS NOT NULL THEN ABS(stock_tracking.change) * stock_tracking.unit_price ELSE 0 END) as total_value
                   ')
                   ->groupBy('stock_tracking.reason')
                   ->get();
    }
}
