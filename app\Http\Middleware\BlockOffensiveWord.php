<?php

namespace App\Http\Middleware;

use Closure;
use App\Setting;

class BlockOffensiveWord
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        // $url = str_replace($request->url(), "", $request->fullUrl());
        $input = $request->except(['id', 'object_id']);

        // $input = array_except($input,['id', 'object_id']);
        
        array_walk_recursive($input, function (&$input) {
            if($input !== null && $input !== false && $input !== true && !is_numeric($input))
            {
                $input = $this->blockOffensive($input);
                // $input = htmlspecialchars($input);
            }
            // var_dump($input);
        });

        // if (preg_match('/[\'^£$%&*()}{@#~><>|_+¬-]/', $url))
        //     return redirect($request->url() . "/" . preg_replace('/[\'^£$%&*()}{@#~><>|_+¬-]/',"",strip_tags($url)));
        $request->merge($input);
        return $next($request);
    }

    public function blockOffensive($text){
        $arrayParams = Setting::where('key','offsensive_words')->first();
        $arrayParams = str_replace("'","",$arrayParams["value"]);
        $arrayParams = explode(",", $arrayParams);
        $textLower = mb_strtolower($text);
        // var_dump($text, $textLower);
        foreach($arrayParams as $key => $value){
            // var_dump(str_contains($textLower,$value));
            while(str_contains($textLower,$value)){
                $a = str_repeat("*",strlen($value));
                $text = substr_replace($text, $a, strpos($textLower, $value), strlen($value));
                $textLower = mb_strtolower($text);
            }
            
            // $text = str_ireplace($value, $a, $text);

            // $text = str_ireplace($value, $a, $text);
            // // $text = str_ireplace(ucfirst($value), $a, $text);
            // $text = str_replace(str_replace("đ","Đ",$value), $a, $text);
            // $text = str_replace(strtoupper($value), $a, $text);
        }
        return $text;
    }
}
