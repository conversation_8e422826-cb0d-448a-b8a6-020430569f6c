<?php

namespace App\Jobs;

use App\Interaction;
use App\Notifications\NotificationUser;
use App\Services\Mqtt\MqttChatService;
use App\Services\Notification\NotificationService;
use App\Services\Token\TokenService;
use App\Shop;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;

class NotifyNewMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $members;
    protected $sender;
    protected $channelId;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($members, $sender, $channelId)
    {
        $this->members = $members;
        $this->sender = $sender;
        $this->channelId = $channelId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $mqtt = new MqttChatService();

        $title = "Tin nhắn mới";
        $topic = "remagan_chat/".$this->channelId;
        $tokenService = new TokenService();

        $interactionModel = new Interaction();
        $interactionModel->on('pgsqlReplica');

        if($this->members && count($this->members) > 0){
            foreach ($this->members as $key => $member) {
                // Lấy tương tác chưa xem (interaction_type = 3) giữa channel và member
                $unread = $interactionModel->where([
                    'object_id_a' => $this->channelId,
                    'object_id_b' => $member['member']['id'],
                    'interaction_type' => config('constants.interaction.unread')
                ])->first();
                if($member['member_type'] == 'shop'){
                    $shop = Shop::find($member['member']['id']);
                    if (!$shop) {
                        return "Not found shop";
                    }

                    $messToShop = [
                        'title' => $title,
                        'body'  => "Có tin nhắn mới từ ". $this->sender['name'],
                        'icon'  => $this->sender['avatar'],
                        'url'   => $this->channelId,
                        'extra' => json_encode([
                                'shop_id'   => $shop['id']
                        ]),
                        'target_url' => '/chat?object=my_shop&shop_id='. $shop['id'] . '&channel_id=' . $this->channelId,
                        'type'  => "shop_message_new",
                    ];

                    $messToShopLV1 = array_merge($messToShop, ['target_url' => '/chat?object=my_shop&shop_id='. $shop['id']]);

                    $messToAgent = [
                        'title' => $title,
                        'body'  => "Có tin nhắn mới từ ". $this->sender['name'],
                        'icon'  => $this->sender['avatar'],
                        'url'   => $this->channelId,
                        'extra'     => json_encode([
                            'shop_id'   => $shop['id']
                        ]),
                        'target_url' =>  '/chat?object=agent_shop&shop_id='. $shop['id'] . '&channel_id=' . $this->channelId,
                        'type'  => "agent_message_new",
                    ];

                    $messToAgentLV1 = array_merge($messToAgent, ['target_url' => '/chat?object=agent_shop&shop_id='. $shop['id']]);

                    $shop_owner = $shop->user_id;
                    $shop_agent = $shop->user_id != $shop->agent_id ? $shop->agent_id : "";
                    # tự nhận thông báo
                    $tokens = $tokenService->listByUser($shop_owner, 2);
                    $agentTokens = $tokenService->listByUser($shop_agent, 2);
                    $notiService = new NotificationService;
                    if(count($tokens) > 0){
                        $notiService->sendBatchNotification($tokens, $messToShop);
                    }
                    if(count($agentTokens) > 0){
                        $notiService->sendBatchNotification($agentTokens, $messToAgent);
                    }
                    $mqtt->publish(['topic' => $topic, 'message' => $messToShop]);
                    $mqtt->publish(['topic' => $shop_owner, 'message' => $messToShopLV1]);
                    if(!$unread){
                        Interaction::create([
                            'object_id_a' => $this->channelId,
                            'object_type_a' => config('constants.object_type.channel'),
                            'object_type_b' => config('constants.object_type.shop'),
                            'object_id_b' => $member['member']['id'],
                            'interaction_type' => config('constants.interaction.unread')
                        ]);
                    }

                    if($shop_agent){
                        $mqtt->publish(['topic' => $topic, 'message' => $messToAgent]);
                        $mqtt->publish(['topic' => $shop_agent, 'message' => $messToAgentLV1]);
                    }

                    # nhận thông báo và lưu vào table notifications
                    $shop_owner_obj = User::find($shop_owner);
                    if($shop_owner_obj){
                        $messToOwnerNoti = array_merge($messToShop, ['image'=> $messToShop['icon']]);
                        $shop_owner_obj->notify(new NotificationUser($shop_owner, $messToOwnerNoti));
                    }
                    $shop_agent_obj = User::find($shop_agent);
                    if($shop_agent_obj){
                        $messToAgentNoti = array_merge($messToAgent, ['image'=> $messToAgent['icon']]);
                        $shop_agent_obj->notify(new NotificationUser($shop_agent, $messToAgentNoti));
                    }
                    // die(json_encode($channelMembers));
                }else{
                    //member is user
                    $messToUser = [
                        'title' => $title,
                        'body'  => $this->sender['name'] . " gửi tin nhắn cho bạn.",
                        'icon'  => $this->sender['avatar'],
                        'url'   => $this->channelId,
                        'target_url' => '/chat?object=user',
                        'type'  => "message_new",
                    ];

                    $messToUserLV1 = array_merge($messToUser, ['target_url' => '/chat?object=user&channel_id='. $this->channelId]);

                    $tokens = $tokenService->listByUser($member['member']['id'], 2);
                    $notiService = new NotificationService;
                    if(count($tokens) > 0){
                        $notiService->sendBatchNotification($tokens, $messToUser);
                    }
                    if(!$unread){
                        Interaction::create([
                            'object_id_a' => $this->channelId,
                            'object_type_a' => config('constants.object_type.channel'),
                            'object_type_b' => config('constants.object_type.user'),
                            'object_id_b' => $member['member']['id'],
                            'interaction_type' => config('constants.interaction.unread')
                        ]);
                    }

                    $mqtt->publish(['topic' => $topic , 'message' => $messToUser]);
                    $mqtt->publish(['topic' => $member['member']['id'], 'message' => $messToUserLV1]);

                    # nhận thông báo và lưu vào table notifications
                    $user_obj = User::find($member['member']['id']);
                    if($user_obj){
                        $messToUserNoti = array_merge($messToUser, ['image'=> $messToUser['icon']]);
                        $user_obj->notify(new NotificationUser($member['member']['id'], $messToUserNoti));
                    }
                }
                //
                # code...
            }
        }
    }
}
