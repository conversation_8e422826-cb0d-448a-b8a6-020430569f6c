<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\View\ViewRequest;
use App\Services\View\ViewService;

class ViewController extends Controller
{
    //--------------insert--------------------
    public function insert(ViewRequest $request, ViewService $service)
    {
        $data = $request->only([
            'object_type',
            'object_id'
        ]);

        $result = $service->insert($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // public function increase($id = null, ViewService $service)
    // {
    //     $result = $service->increase($id);

    //     if($result == false)
    //     {
    //         $response = [
    //             'status' => JsonResponse::HTTP_BAD_REQUEST,
    //             'body'  => [

    //             ]
    //         ];

    //         return response()->json($response, JsonResponse::HTTP_OK);
    //     }

    //     $response = [
    //         'status' => JsonResponse::HTTP_OK,
    //         'body'  => [
    //             'data' => $result
    //         ]
    //     ];

    //     return response()->json($response, JsonResponse::HTTP_OK);
    // }

    // public function listHotSearch(ViewService $service)
    // {
    //     $result = $service->listHotSearch();

    //     $response = [
    //         'status' => JsonResponse::HTTP_OK,
    //         'body' => [
    //             'data' => $result
    //         ]
    //     ];

    //     return response()->json($response, JsonResponse::HTTP_OK);
    // }
}
