<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class SendMailApprovedShop extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    private $user_name;
    private $object_name;
    private $confirm;
    private $reason;
    public function __construct($user_name, $object_name, $confirm, $reason)
    {
        $this->user_name = $user_name;
        $this->object_name = $object_name;
        $this->confirm = $confirm;
        $this->reason = $reason;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        Log::info("SEND MAIL");

        $subject = "Thông báo: C<PERSON>a hàng [". $this->object_name."]";
        switch ($this->confirm) {
            case config('constants.shop.status.accepted') : {
                $subject.=" đã được duyệt";
                break;
            }
            case config('constants.shop.status.rejected') : {
                $subject.=" đã bị từ chối duyệt";
                break;
            }
        }

        return $this->view('emails.notification_approve_shop')
            ->with(
                [
                    'user_name' => $this->user_name,
                    'object_name' => $this->object_name,
                    'confirm' => $this->confirm,
                    'reason' => $this->reason
                ])
            ->subject($subject);
    }
}
