<?php

namespace App\Services\Reel;

use App\File;
use App\Image;
use App\Reel;

class ReelService
{

    function deleteReelData($reelId, $reelType)
    {
        if($reelType == 'video'){
            File::where('parent_id', $reelId)
                ->where('object_type', 11)
                ->delete();
        }
        if($reelType == 'image'){
            Image::where('parent_id', $reelId)
                ->where('object_type', 11)
                ->delete();
        }
    }
    // Thêm video hoặc thay đổi video cho reel
    function insertOrUpdateReelVideo($videoId, $reelId, $reelType)
    {
        $this->deleteReelData($reelId, $reelType);
        $result = File::where('id', $videoId)
            ->where("file_type", "video")
            ->update(['object_type' => 11, 'parent_id' => $reelId]);

        return $result;
    }

    // Thêm ảnh hoặc thay đổi ảnh cho reel
    function insertOrUpdateReelImages($imageIds, $reelId, $reelType)
    {
        $this->deleteReelData($reelId, $reelType);
        $result = Image::whereIn('id', $imageIds)
            ->update(['object_type' => 11, 'parent_id' => $reelId]);
        return $result;
    }

    function createReel($data){
        return Reel::create($data);
    }
}
