<?php
namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class MaterialQuotation extends Model
{
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $table = 'material_quotations';

    protected $fillable = [
        'material_id', 'quotation_id', 'price', 'price_vat', 'notes'
        // Add other fillable columns
    ];
    protected $hidden = ['updated_at', 'created_at'];
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
    // Define any relationships or additional methods here
    public function quotation()
    {
        return $this->belongsTo(Quotation::class, 'quotation_id')->with('supplier');
    }

    public function material()
    {
        return $this->belongsTo(Material::class);
    }

    public function importHistories()
    {
        return $this->hasMany(ImportHistory::class, 'material_quotation_id', 'id');
    }
}
