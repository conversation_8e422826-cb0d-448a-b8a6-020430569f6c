<?php
namespace App\Http\Response;

use App\Http\Response\ApiResponse;

class PropertyResponse extends ApiResponse
{
    /**
     * Define error status
     */
    protected $_errors = [
        "Login_Err_001"     => [400, 'Property_001_E_010'],
    ];


    /**
     * Create success response
     * When success, return the empty response
     *
     * @return array Response data
     */
    protected function makeSuccessResponse()
    {
        $result = [];
        $result['id']                           = $this->data['id'];
        $result['username']                     = $this->data['username'];
        $result['token']                        = $this->data['token'];
        $result['refresh_token']                = $this->data['refresh_token'];
        $result['role_id']                      = $this->data['role_id'];
        $result['email']                        = $this->data['email'];
        $result['birthday']                     = $this->data['birthday'];
        $result['gender']                       = $this->data['gender'];
        $result['avatar']                       = S3_HOST_PATH. $this->data['avatar'];
        $result['first_name']                   = $this->data['first_name'];
        $result['last_name']                    = $this->data['last_name'];
        $result['nick_name']                    = $this->data['nick_name'];
        $result['address']                      = $this->data['address'];
        $result['health_flag']                  = $this->data['health_flag'];
        $result['profile_flag']                 = $this->data['profile_flag'];
        $result['email_marketing_setting']      = $this->data['email_marketing_setting'];
        $result['latitude']                     = $this->data['latitude'];
        $result['longitude']                    = $this->data['longitude'];
        $result['health_source']                = $this->data['health_source'];
        $result['token_expire']                 = $this->data['token_expire'];
        $result['refresh_token_expire']         = $this->data['refresh_token_expire'];
        return $result;
    }
}
