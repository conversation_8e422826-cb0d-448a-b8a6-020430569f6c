<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UserOrderByRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'name'      => 'required|max:255',
            'phone'     => 'required|digits_between:10,11',
            'email'     => 'required|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u',
            'topic'     => 'required|max:255',
            'id'        => 'required|uuid|exists:places,id',
            'content'   => 'nullable|max:5000'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'name.required'     => 'User_001_E_001',

    //         'email.required'    => 'User_001_E_003',
    //         'email.regex'       => 'User_002_E_004',

    //         'id.required'       => 'User_001_E_009',
    //         'id.uuid'           => 'User_002_E_010',
    //         'id.exists'         => 'User_003_E_011',

    //         'phone.required'    => 'User_001_E_024',
    //         'phone.digits_between' => 'User_004_E_025',

    //         'name.max'          => 'User_004_E_070',
    //         'email.max'         => 'User_004_E_071',

    //         'topic.required'    => 'User_001_E_073',
    //         'topic.max'         => 'User_004_E_074',

    //         'content'           => 'User_004_E_075'
    //     ];
    // }
}
