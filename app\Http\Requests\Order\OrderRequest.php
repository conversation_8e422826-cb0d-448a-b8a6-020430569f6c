<?php

namespace App\Http\Requests\Order;

use App\Http\Requests\BaseRequest;

class OrderRequest extends BaseRequest
{

    public function rules()
    {        return [
            'address'           => 'required|max:5000',
            'province_id'       => 'nullable|exists:dvhc2021_tinh,id',
            'district_id'       => 'nullable|exists:dvhc2021_huyen,id',
            'ward_id'           => 'nullable|exists:dvhc2021_xa,id',
            'customer_id'       => 'nullable',
            'customer_name'     => 'required|max:255',
            'customer_phone'    => 'required|digits_between:10,11',
            // 'price'             => 'required|numeric|digits_between:0,15|min:0',
            // 'price_off'         => 'nullable|numeric|digits_between:0,15',
            'delivery_type'     => 'required',
            'delivery_price'    => 'nullable|numeric|digits_between:0,15',
            'delivery_price_estimate' => 'nullable|numeric',
            'delivery_discount' => 'nullable|numeric|digits_between:0,15',
            'payment_method'    => 'required',
            'notes'             => 'nullable|max:5000',
            'delivery_partner_id' => 'nullable|uuid|exists:delivery_partners,id',
            'extra_data'        => 'nullable',
            'referral_code'     => 'nullable|string|max:20'
        ];
    }    
    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Normalize referral code to uppercase if provided
        if ($this->has('referral_code') && !empty($this->input('referral_code'))) {
            $this->merge([
                'referral_code' => strtoupper(trim($this->input('referral_code')))
            ]);
        }
    }

    // public function messages()
    // {
    //     return [
    //         'name.required'             => 'Order_001_E_001',
    //         'name.max'                  => 'Order_001_E_002',
    //         'address.max'               => 'Order_002_E_003',
    //         'province_id.required'      => 'Order_003_E_004',
    //         'province_id.exists'        => 'Order_003_E_005',
    //         'district_id.required'      => 'Order_004_E_006',
    //         'district_id.exists'        => 'Order_004_E_007',
    //         'ward_id.required'          => 'Order_005_E_008',
    //         'ward_id.exists'            => 'Order_005_E_009',
    //         'latitude.required'         => 'Order_006_E_010',
    //         'longitude.required'        => 'Order_007_E_011',
    //         'user_id.required'          => 'Order_008_E_012',
    //         'user_id.exists'            => 'Order_008_E_013',
    //         'slug.max'                  => 'Order_009_E_014',
    //         'business_type_id.required' => 'Order_011_E_015',
    //         'business_type_id.exists'   => 'Order_011_E_016',
    //     ];
    // }
}
