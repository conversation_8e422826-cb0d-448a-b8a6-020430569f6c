<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class LogGeneral extends Model
{
    protected $fillable = [
        'action',
        'object_id',
        'object_type',
        'created_by',
        'data_response',
        'data_request'
    ];

    public $incrementing = false;

    protected $primaryKey = 'id';

    protected $table = 'logs_general';
    protected $hidden = ['pivot'];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model){
            $model->id = Str::uuid();
            // $model->created_by = Auth::id();
        });

    }
    public function created_by()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
