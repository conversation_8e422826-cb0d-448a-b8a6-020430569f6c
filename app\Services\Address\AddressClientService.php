<?php
namespace App\Services\Address;
use App\Province;
use App\Ward;
use App\District;
use App\Http\Requests\PropertyRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\User;
use Illuminate\Support\Facades\Cache;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Redis;

class AddressClientService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }
    // -----------------------
    public function list()
    {
        $result = Province::get();
        foreach($result as $key => $value)
        {
            $arr[$key] = $value;
            $value['district'] = District::where('province_id',$value->id)->orderBy('name','desc')->with('wards')->get();
        }
        return $arr;
    }
    // public function getAddress(array $address)
    // {
    //     $province_id = $address['province_id'];
    //     $district_id = $address['district_id'];
    //     $ward_id = $address['ward_id'];
    //     $result = Province::where('id',$province_id)->first();
    //     if(!$result)
    //     {
    //         return false;
    //     }
    //     $arr[] = $result;
    //     $result['district'] = District::where('id',$district_id)
    //     ->where('province_id',$province_id)->first();
    //     if(!$result['district'])
    //     {
    //         return false;
    //     }
    //     $a = $result['district'];
    //     $a['ward'] = Ward::where('id',$ward_id)->where('district_id',$district_id)->first();
    //     if(!$a['ward'])
    //     {
    //         return false;
    //     }
    //    return $arr;
    // }
    public function getProvince($id)
    {
        $check = Province::find($id);

        if(!$check)
        {
            return false;
        }

        $result = Province::where('id',$id)->with('districts:id,name,province_id,slug')->first();
        return $result;
    }
    public function getDistrict($id)
    {
        $check = District::find($id);
        if(!$check)
        {
            return false;
        }
        $result = District::where('id',$id)->with('wards:id,name,district_id,slug')->first();
        return $result;
    }

    public function getWard($id)
    {
        $check = Ward::find($id);
        if(!$check)
        {
            return false;
        }
        $result = Ward::where('id',$id)->with('villages:id,postcode,name,ward_id')->first();
        return $result;
    }

    public function getProvinceList(){

        try {
            $result = Cache::remember('list-client-province', now()->addHours(24), function () {
                return Province::orderBy('slug','asc')->get();
            });
        } catch (\Throwable $th) {

            $result = Province::orderBy('slug','asc')->get();
        }

        return $result;
    }

    public function provinceListDictrict()
    {
        try {
            $result = Cache::remember('list-client-province-district', now()->addHours(24), function () {
                $province = $this->handleProvince();
                // $district = $this->handleDistrict();
                // $data = [];
                // $data = array_merge($data,$province,$district);
                $collection = collect($province);
                $arrayA = $collection->where('is_hot_search',true)->sortBy('slug')->values()->all();
                $arrayB = $collection->where('is_hot_search',false)->sortBy('slug')->values()->all();

                return array_merge($arrayA, $arrayB);
            });
        } catch (\Throwable $th) {
            $province = $this->handleProvince();
            // $district = $this->handleDistrict();
            // $data = [];
            // $data = array_merge($data,$province,$district);
            $collection = collect($province);
            $arrayA = $collection->where('is_hot_search',true)->sortBy('slug')->values()->all();
            $arrayB = $collection->where('is_hot_search',false)->sortBy('slug')->values()->all();
            $result = array_merge($arrayA, $arrayB);
        }

        return $result;
    }

    public function handleProvince()
    {
        $result = Province::with('districts')->get();
        // $result = Province::get();
        $data = [];
        foreach ($result as $key => $value) {
            $is_hot_search = false;
            if(in_array($value->id,[208,261,258,201,202,204,205,206,203]))
            {
                $is_hot_search = true;
            }
            $value['is_hot_search'] = $is_hot_search;
            $this->handleSlug($value);
            // $data[] = [
            //     'id'        => $value->id,
            //     'name'      => $value->name,
            //     'slug'      => $value->slug,
            //     'latitude'  => $value->latitude,
            //     'longitude' => $value->longitude,
            //     'bound'     => $value->bound,
            //     'is_hot_search' =>$is_hot_search
            // ];
        }
        return $result;
    }

    public function handleSlug($data)
    {
        $province_slug = $data['slug'];
        foreach ($data['districts'] as $key => $value) {
            $value['slug'] = $province_slug.'_'.$value['slug'];
        }
        return [];
    }

    public function handleDistrict()
    {
        $result = District::with('provinces')->has('provinces','>',0)->get();
        $data = [];
        foreach ($result as $key => $value) {
            $slug = $value->provinces['slug'].'_'.$value['slug'];
            // $is_hot_search = false;

            // if(in_array($value->id,[1548]))
            // {
            //     $is_hot_search = true;
            // }

            $data[] = [
                'id'        => $value->id,
                'name'      => $value->name,
                'slug'      => $slug,
                'latitude'  => $value->latitude,
                'longitude' => $value->longitude,
                'bound'     => $value->bound,
                'is_hot_search' => $is_hot_search
            ];
        }

        return $data;
    }

    public function updateCoordinate($id)
    {
        $baseURL = 'https://maps.google.com/maps/api/geocode/json?address=';
        switch ($id) {
            case 1:
                $result = Province::get();
                foreach ($result as $key => $value) {
                    $check = Province::find($value->id);
                    $url = $baseURL.urlencode($value->name).'&key=' . env('GOOGLE_KEY');
                    $curl = curl_init($url);
                    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                    $geocode = json_decode(curl_exec($curl));
                    $latitude = $geocode->results[0]->geometry->location->lat;
                    $longitude = $geocode->results[0]->geometry->location->lng;
                    $bounds = $geocode->results[0]->geometry->bounds;
                    $provicne = ['latitude' => $latitude, 'longitude' => $longitude, 'province_bound' => json_encode($bounds)];
                    $check->update($provicne);
                }
                break;
            case 2:
                $result = District::with('provinces')->get();
                // die(json_encode($result));
                foreach ($result as $key => $value) {
                    $check = District::find($value->id);
                    $url = $baseURL.urlencode($value->name.', '. $value->provinces->name).'&key=' . env('GOOGLE_KEY');
                    $curl = curl_init($url);
                    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                    $geocode = json_decode(curl_exec($curl));
                    $latitude = $geocode->results[0]->geometry->location->lat;
                    $longitude = $geocode->results[0]->geometry->location->lng;
                    $bounds = $geocode->results[0]->geometry->bounds;
                    $district = ['latitude' => $latitude, 'longitude' => $longitude, 'district_bound' => json_encode($bounds)];
                    $check->update($district);
                }
                break;

            default:
                # code...
                break;
        }
    }

    //--------------detail area-------------------
    public function detailArea($slug)
    {
        $result = null;
        if($slug == 'toan-quoc')
        {
            $result['bound'] = '{"northeast":{"lat":6.402648405963896,"lng":89.56054687500001},"southwest":{"lat":26.31311263768267,"lng":122.56347656250001}}';
            $result['slug'] = $slug;
            $result['latitude'] = 21.02921083954497;
            $result['longitude'] = 105.8520356695735;

        }
        else {
            $data = explode('_', $slug);

            switch (count($data)) {
                case 1:
                    $result = Province::where('slug',$data[0])->first();
                    if(isset($result['id']))
                    {
                        $result['address'] = $result['name'];
                    }
                    break;
                case 2:
                    $province = Province::where('slug',$data[0])->first(['id','name']);
                    if(isset($province['id']))
                    {
                        $result = District::where([['slug',$data[1]],
                                                ['province_id', $province['id']]
                                                ])->first();
                    }
                    if(isset($result['id'], $province['id']))
                    {
                        $result['address'] = $result['name'].', '.$province['name'];
                    }
                    break;
                case 3:
                    $province = Province::where('slug',$data[0])->first(['id','name']);
                    if(isset($province['id']))
                    {
                        $district = District::where([['slug',$data[1]],
                                                ['province_id', $province['id']]
                                                ])->first(['id','name','satellite','streetmap']);
                    }
                    if(isset($district['id']))
                    {
                        $result = Ward::where([['slug',$data[2]],
                                            ['district_id', $district['id']]
                                            ])->first();
                    }
                    if(isset($result->id, $province->id, $district->id))
                    {
                        $result['address'] = $result['name'].', '.$district['name'].', '.$province['name'];
                        $result['satellite'] = $result['satellite'] != null ? $result['satellite'] : $district['satellite'];
                        $result['streetmap'] = $result['streetmap'] != null ? $result['streetmap'] : $district['streetmap'];
                    }
                    break;
                default:

                    break;
            }
        }


        return $result;
    }

}
