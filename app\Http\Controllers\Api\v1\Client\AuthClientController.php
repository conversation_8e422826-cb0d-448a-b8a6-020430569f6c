<?php

namespace App\Http\Controllers\Api\v1\Client;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Role;
use Hash;
use App\Mail\SendMail;
use Mail;
use App\Helpers\Helper;
use App\Notifications\NotificationUser;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\RoutesNotifications;
use App\Http\Requests\User\UserDeleteRequest;
use App\Http\Requests\User\UserResetRequest;
use App\Http\Requests\User\ConfirmEmailRequest;
use App\Services\Auth\AuthService;
use App\Services\Auth\AuthClientService;
use App\Services\Auth\RegisterService;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use App\Http\Requests\User\RegisterUserRequest;
use App\Http\Requests\User\RegisterUserClientRequest;
use App\Http\Requests\User\FollowClientRequest;
use App\Http\Requests\User\UserUpdateRequest;
use App\Http\Requests\User\UserUpdateClientRequest;
use App\Http\Requests\User\UserUpdatePasswordRequest;
use App\Http\Requests\User\UserUpdatePasswordClientRequest;
use App\Http\Requests\User\ForgetPasswordRequest;
use App\Http\Requests\User\UpdateForgetPasswordRequest;
use App\Http\Requests\User\UpdateForgetPasswordClientRequest;
use App\Http\Requests\User\ConfirmCodeRequest;
use App\Http\Requests\User\ConfirmCodeClientRequest;
use App\Http\Requests\User\ConfirmRegistercodeRequest;
use App\Http\Requests\User\CreatePasswordClientRequest;
use App\Http\Requests\User\UserLikeRequest;
use App\Http\Requests\User\UserClientUpdateImageRequest;
use App\Http\Requests\User\NotifyListRequest;
use App\Http\Requests\User\LogoutDeviceRequest;
use App\Http\Requests\User\FollowListRequest;
use App\Http\Requests\User\OTPRequest;
use App\Http\Requests\User\OTPConfirmRequest;
use App\Http\Requests\User\ChangeEmailClientRequest;

use Illuminate\Http\JsonResponse;
// use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Services\Token\TokenService;
use App\Services\GeneralService;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Socialite;
use App\Http\Requests\User\CustomerLoginRequest;
use App\Http\Requests\User\UpdateImageDriver;
use App\Http\Requests\User\LoginZalo;

class AuthClientController extends Controller
{
    //--------------------------- login---------------------------
    public function login() {
        $data = request(['email','phone','user_name', 'password']);
        $phone = $data['phone'] ?? null;
        $email = $data['email'] ?? null;
        $user_name = $data['user_name'] ?? null;
        $password = Helper::decrypt($data['password']) ?? null;
        $device = request()->header('User-Agent');
        $deviceId = request()->header('X-Device-Id');
        $general = new GeneralService();
        $address_ip = $general->getUserIpAddr();

        $token = '';
        if($phone){
            $token = JWTAuth::attempt(['phone' => $phone,'password' => $password]);
        }elseif($email){
            $email = Str::lower($data['email']);
            $token = JWTAuth::attempt(['email' => $email,'password' => $password]);
            if(!$token)
            {
                $token = JWTAuth::attempt(['user_name' => $email,'password' => $password]);
            }
        }elseif($user_name){
            $token = JWTAuth::attempt(['user_name' => $user_name,'password' => $password]);
        }

        if(!$token)
        {
            return response()->json([
                'status' => JsonResponse::HTTP_FORBIDDEN,
                'error' => 'login_000_E_001',
            ], JsonResponse::HTTP_OK);
        }

        if(Auth::user()->enable == false)
        {
            return response()->json([
                'status'        => JsonResponse::HTTP_FORBIDDEN,
                'message'       => 'User has been disabled',
                'message_code'  => 'C_E_008',
            ], JsonResponse::HTTP_OK);
        }
        // $currentUser  = Auth::user();
        // $currentUser->token = $token;
        // $currentUser->save();
        TokenService::insert(['token' => $token, 'user_id' => Auth::user()->id, 'device' => $device,'device_id' => $deviceId, 'address_ip' => $address_ip, 'token_type' => 1]);
        $response = [
                'status' => JsonResponse::HTTP_OK,
                'token' => $token,
                'type' => 'bearer',
                'expires' => auth('api')->factory()->getTTL(),
        ];
        return response()->json($response,JsonResponse::HTTP_OK);

    }
    public function loginDriver(CustomerLoginRequest $request, RegisterService $service)
    {
        $request = (array)$request->only([
            'email',
            'name',
            'provider_name',
            'provider_id',
            'file',
            'picture'
        ]);
        $result = $service->loginDriver($request);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_001"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'lock')
        {
            return response()->json([
                'status'        => JsonResponse::HTTP_FORBIDDEN,
                'message'       => 'User has been disabled',
                'message_code'  => 'C_E_008',
            ], JsonResponse::HTTP_OK);
        }


        // var_dump($request['picture']['data']['url']);
        $result->path = $request['picture']['data']['url'] ? $request['picture']['data']['url'] : null;
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'token' => $result->token,
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function redirectToProvider($driver)
    {

        return Socialite::driver($driver)->redirect();
    }

    public function handleProviderCallback($driver, RegisterService $service)
    {
        try {
            $user = Socialite::driver($driver)->user();
        } catch (\Exception $e) {

            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Login_002_E_001'
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $result = User::where('email', $user->getEmail())->first();

        if ($result) {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ]
            ];
            return response()->json($response, JsonResponse::HTTP_OK);

        } else {

            $data = [
                'provider_name' => $driver,
                'provider_id' => $user->getId(),
                'name' => $user->getName(),
                'email' => $user->getEmail(),
                'email_verified_at' => now(),
                'token' => $user->token
            ];

            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $data
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        return [];
    }

    //-------------update image avatar user----------
    public function updateImage(UpdateImageDriver $request)
    {
        $request = (array)$request->only([
            'file',
            'title',
            'description',
            'parent_id',
            'object_type',
            'orientation',
            'style',
            'image_type',
            'index',
        ]);
        $service = new RegisterService(Auth::user()->id);
        $result = $service->updateImage($request);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "User_003_E_011"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => []
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //--------------------------- register---------------------------
    public function register(RegisterUserClientRequest $request, AuthClientService $service)
    {
        $data = (array)$request->only([
            'name','email','password',
            'gender','phone',
            'date_of_birth', 'identity_card',
            'province_id', 'district_id',
            'ward_id', 'address','user_name',
            'description','otp', 'agent'
        ]);

        $emailOrPhone = isset($data['phone']) ? $data['phone'] : $data['email'];
        $isPhone = isset($data['phone']) ? true : false;
        $otp = $data['otp'] ?? null;
        $isPhone = isset($data['phone']) ? true : false;
        $data['password'] = Helper::decrypt($data['password']);
        $otpAgent = $data['agent'] ?? null;
        if($isPhone && $otpAgent != 'zalo') $otpAgent = 'phone'; elseif(isset($data['email'])) $otpAgent = 'email';
        $checkOTP = $service->confirmOTP($emailOrPhone, $otp, $otpAgent);
        if(!$checkOTP){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => "confirm OTP failed.",
                'errors' => [
                    [
                    'error_message' => "confirm OTP failed.",
                    'field'    => "otp",
                    ]
                ],
            ];
            return response()->json($response, JsonResponse::HTTP_OK);

        }
        $registerService = new RegisterService;
        $result = $registerService->registerUser($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response,JsonResponse::HTTP_OK);
    }

    //--------------------------- user infor---------------------------
    public function userInfor()
    {
        $service = new AuthClientService(Auth::user()->id);
        $result = $service->getUserInfor();
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'data' => $result,
        ]);
    }

    //--------------------------- update---------------------------
    public function update(UserUpdateClientRequest $request, AuthClientService $service)
    {
        $data = (array)$request->only([
            'name','email',
            'gender','phone','id',
            'date_of_birth', 'identity_card',
            'province_id', 'district_id',
            'ward_id', 'address','user_name',
            'description', 'custom_path',
            'latitude', 'longitude','referral_code'
        ]);

        $result = $service->update($data);
        if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => 'User_003_E_011'
           ];
           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => [
               'data' => $result,
           ],
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }



    //--------------------------- update password---------------------------
    public function updatePassword(Request $request)
    {

        $data = (array)$request->only([
            'password'
        ]);

        $data['password'] = Helper::decrypt($data['password']);
        // dd($data['password']); die;
        // Validate password using regex
        $passwordRegex = '/^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};:\\|,.\<\>\/?\-]).{6,20}$/';

        if (!preg_match($passwordRegex, $data['password'])) {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => 'Password must be at least 8 characters long and contain at least one letter, one number, and one special character.',
                'errors' => [
                    'message' => 'Password must be at least 8 characters long and contain at least one letter, one number, and one special character.',
                    'field' => 'password',
                ],
            ];
            return response()->json($response, JsonResponse::HTTP_BAD_REQUEST);
        }
        $service = new AuthClientService(Auth::user()->id);
        $result = $service->updatePassword($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //--------------------- forget passwword-----------------------------
    public function forgetPassword(ForgetPasswordRequest $request, AuthClientService $service)
    {

        $email = (array)$request->only(['email']);
        $result = $service->forget($email);
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'Email không tồn tại',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'lock')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_003"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'verify')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_002"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => true,
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //----------------- confirm code forget password--------------------
    public function confirmCode(ConfirmCodeClientRequest $request,AuthClientService $service)
    {
        $data = (array)$request->only(['id']);
        $result = $service->confirmCode($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_011',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'lock')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_003"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'verify')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_002"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------------------- change forget password---------------------
    public function changeForgetPassword(UpdateForgetPasswordClientRequest $request, AuthClientService $service)
    {
        $data = (array)$request->only(['email','phone','otp','password','agent']);
        $phone = $data['phone'] ?? null;
        $email = $data['email'] ?? null;
        $password = Helper::decrypt($data['password']) ?? null;
        $emailOrPhone = $email??$phone;
        $otp = $data['otp'] ?? null;
        $isPhone = isset($data['phone']) ? true : false;
        $otpAgent = $data['agent'] ?? null;
        if($isPhone && $otpAgent != 'zalo') $otpAgent = 'phone'; elseif(isset($data['email'])) $otpAgent = 'email';
        $checkOTP = $service->confirmOTP($emailOrPhone, $otp, $otpAgent);
        if(!$checkOTP){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => "confirm OTP failed.",
                'errors' => [
                    [
                        'error_message' => "confirm OTP failed.",
                        'field' => "otp",
                    ]
                ],
            ];
            return response()->json($response, JsonResponse::HTTP_OK);

        }

        $user = null;
        if($phone){
            $user = User::where('phone',$phone)->first()->toArray();
        }else{
            $email = Str::lower($data['email']);
            $user = User::where('email',$email)->first()->toArray();
        }
        $result = false;

        if($user != null && $password != null){
            $user['password'] = $password;
            $result = $service->updateForgetPassword($user);
        }

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_032',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'lock')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_003"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'verify')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => "login_000_E_002"
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' =>[
                    'result' => $result['result'],
                    // 'token'  => $result['token']
                ]
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //-------------------- change email---------------------
    public function changeEmail(ChangeEmailClientRequest $request, AuthClientService $service)
    {
        $data = (array)$request->only(['email', 'otp']);
        $email = Str::lower($data['email'] ?? '');
        $otp = $data['otp'] ?? null;

        // Check OTP
        if (!$service->confirmOTP($email, $otp, 'email')) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => "confirm OTP failed.",
                'errors' => [['error_message' => "confirm OTP failed.", 'field' => "otp"]],
            ], JsonResponse::HTTP_OK);
        }
        // Check if email already exists
        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => "Email already exists.",
                'errors' => [['error_message' => "Email already exists.", 'field' => "email"]],
            ], JsonResponse::HTTP_OK);
        }

        // Update user email
        $user = User::find(Auth::user()->id);
        if ($user) {
            $user->email = $email;
            $user->save();
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => ['data' => $user],
        ], JsonResponse::HTTP_OK);
    }

    //-------------------- send code register---------------------
    public function registerCode(ConfirmEmailRequest $request,AuthClientService $service)
    {
        $data = (array)$request->only(['email']);
        $result = $service->registerCode($data);

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' =>$result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //--------------------  confirm code register---------------------
    public function confirmRegisterCode(ConfirmRegistercodeRequest $request,AuthClientService $service)
    {
        $data = (array)$request->only(['email','key']);
        $result = $service->confirmRegisterCode($data);
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => 'User_003_E_016',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        if($result === 'redis')
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'error' => 'Redis disconnect',
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' =>$result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // check exists user by email | phone | username
    public function checkExist(Request $request, AuthClientService $service)
    {
        $data = (array)$request->only(['key','type']);
        $result = $service->checkExist($data);
        $status = $result ? JsonResponse::HTTP_OK : JsonResponse::HTTP_BAD_REQUEST;
        $response = [
            'status' => $status,
            'body' => [
                'data' =>$result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function checkExistPath(Request $request,AuthClientService $service)
    {
        $data = (array)$request->only(['key']);
        $result = $service->checkExistPath($data);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' =>$result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }




    //----------list device user --------------
    public function listDevice()
    {
        $service = new AuthClientService(Auth::user()->id);
        $result = $service->listDevice();

        if ($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => ['User_003_E_032'],
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }


    //-----------logout device---------------
    public function logoutDevice(LogoutDeviceRequest $request)
    {
        $data = $request->only([
            'id',
            'device_id'
        ]);

        $service = new AuthClientService(Auth::user()->id);
        $result = $service->logoutDevice($data);

        if ($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => ['User_003_E_032'],
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function get_otp(OTPRequest $request) {
        # NẾU LỖI "error_name":"Invalid oauthorized code" thì chỉ cần chạy link này + cấp quyền là xong: https://oauth.zaloapp.com/v4/oa/permission?app_id=1735716509130650710&redirect_uri=https%3A%2F%2Fapi.remagan.com%2Fapi%2Fcallback%2Fzalo_auth_code
        $data = $request->all();
        $emailOrPhone = isset($data['phone']) ? $data['phone'] : $data['email'];
        $sessionID = $data['session_id'] ?? null;
        $otpType = $data['type'] ?? null; //reset_password | default
        $otpAgent = $data['agent'] ?? null;

        $isPhone = isset($data['phone']) ? true : false;
        if($isPhone && $otpAgent != 'zalo') $otpAgent = 'phone'; elseif(isset($data['email'])) $otpAgent = 'email';
        $otpAgentArr = ['phone','email','zalo'];

        $result = [];
        if(!$emailOrPhone || !in_array($otpAgent, $otpAgentArr)){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'error_code' => 'otp_err_002',
                    'error_message' => 'wrong otp agent or input phone|email',
                ],
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $service = new AuthClientService();
        $result = $service->get_otp($emailOrPhone, $otpType, $otpAgent);

        if(!$result['status']){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'message' => $result['message'] ?? '',
                'errors' => $result['errors'] ?? [],
                'otp_cooldown' => $result['otp_cooldown'] ?? '',

            ];
            return response()->json($response, JsonResponse::HTTP_OK);

        }
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function confirm_otp(OTPConfirmRequest $request) {
        $data = $request->all();
        $emailOrPhone = isset($data['phone']) ? $data['phone'] : $data['email'];
        $isPhone = isset($data['phone']) ? true : false;
        $otpAgent = $data['agent'] ?? null;
        if($isPhone && $otpAgent != 'zalo') $otpAgent = 'phone'; elseif(isset($data['email'])) $otpAgent = 'email';
        $otp = $data['otp'] ?? null;
        $result = [];
        if(!$emailOrPhone || !$otp){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'data' => $result,
                ],
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $service = new AuthClientService();
        $result = $service->confirmOTP($emailOrPhone, $otp, $otpAgent);
        if(!$result){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'data' => $result,
                ],
            ];
            return response()->json($response, JsonResponse::HTTP_OK);

        }else{
            $user = null;
            if($isPhone)
                $user = User::where('phone', $emailOrPhone)->first();
            else
                $user = User::where('email', $emailOrPhone)->first();
            $userID = $user->id ?? null;
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result,
                    'user_id' => $userID,
                ],
            ];
        }
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function zaloGetAuthCode(Request $request){
        $authCode = $request->get('code');
        $service = new AuthClientService();
        $result = $service->setZaloAuthCode($authCode);
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //--------------------------- request delete in 30 days---------------------------
    public function delete_account()
    {
        if(!Auth::user())
        return response()->json([
            'status' => JsonResponse::HTTP_BAD_REQUEST,
            'data' => [],
        ]);
        $service = new AuthClientService(Auth::user()->id);
        $result = $service->delete_account();
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'message' => 'Account was disabled and scheduled to remove in 30 days.',
        ]);
    }

    //--------------------------- switchLang---------------------------
    public function switchLang(Request $request)
    {
        $lang = $request->get('language');
        $id = Auth::user()->id;
        $user = User::where('id', $id)->update(['language' => json_encode([$lang])]);
        if($user){
            return response()->json(
                [
                    "status" => JsonResponse::HTTP_NO_CONTENT,
                ]
                , JsonResponse::HTTP_OK);
        }
        return response()->json(
            [
                "status" => JsonResponse::HTTP_FORBIDDEN,
            ]
            , JsonResponse::HTTP_OK);
    }

    //---------------notify---------------
    public function notify(NotifyListRequest $request)
    {
        $data = $request->only([
            'offset',
            'limit'
        ]);

        $service = new AuthClientService(Auth::user()->id);
        $result = $service->notify($data);

        if ($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [],
            ];
            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'count' => $result['count'],
                'data' => $result['data'],
                'unread' => $result['unread']
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //------------detail notify----------------------
    public function detailNotify(Request $request, $id = null)
    {
        $service = new AuthClientService(Auth::user()->id);
        $result = $service->detailNotify($id, $request);

        // if ($result == false)
        // {        //     $response = [
        //         'status' => JsonResponse::HTTP_BAD_REQUEST,
        //         'body' => [],
        //     ];
        //     return response()->json($response, JsonResponse::HTTP_OK);
        // }
        
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ],
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------------------------- referral code---------------------------
    public function getReferralCode()
    {
        $user = Auth::user();
        
        // Generate referral code if user doesn't have one
        if (!$user->referral_code) {
            $user->generateReferralCode();
            $user->refresh();
        }

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'referral_code' => $user->referral_code,
            ],
        ]);
    }

    //--------------------------- referral statistics---------------------------
    public function getReferralStats()
    {
        $user = Auth::user();
        $stats = $user->getReferralStats();

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $stats,
            ],
        ]);
    }
}
