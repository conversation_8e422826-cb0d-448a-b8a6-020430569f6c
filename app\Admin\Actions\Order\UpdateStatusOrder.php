<?php

namespace App\Admin\Actions\Order;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class UpdateStatusOrder extends RowAction
{
    public function name()
    {
        return __("admin.update_status");
    }

    public function handle (Model $model)
    {
        if($model->status >= 4) return $this->response()->success(__('Update status'));
        $model->status = (int)$model->status + 1;

        $model->notes = " Cập nhật đơn từ Admin.";
        $model->save();
        return $this->response()->success(__('admin.update_status'))->refresh();
    }
}
