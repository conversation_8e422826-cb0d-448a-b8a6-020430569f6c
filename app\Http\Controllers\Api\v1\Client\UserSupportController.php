<?php

namespace App\Http\Controllers\Api\v1\Client;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\UserSupport\UserSupportService;
use App\Http\Requests\User\UserSupportRequest;
use App\Http\Requests\User\UserContactRequest;
use App\Http\Requests\User\UserOrderByRequest;

class UserSupportController extends Controller
{
    public function userRequest(UserSupportRequest $request, UserSupportService $service)
    {
        $data = $request->only([
            'name',
            'email',
            'phone',
            'request',
            'content'
        ]);

        $result = $service->userRequest($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function listRequest(UserSupportService $service)
    {
        $result = $service->listRequest();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function contact(UserContactRequest $request, UserSupportService $service)
    {
        $data = $request->only([
            'name',
            'phone',
            'content',
            'id_property',
            'name_property',
            'url_property',
            'created_by',
            'knb_id',
            'email'
        ]);

        $result = $service->contact($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function orderBuy(UserOrderByRequest $request, UserSupportService $service)
    {
        $data = $request->only([
            'name',
            'phone',
            'content',
            'email',
            'topic',
            'id'

        ]);

        $result = $service->orderBuy($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'User_003_E_011'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
