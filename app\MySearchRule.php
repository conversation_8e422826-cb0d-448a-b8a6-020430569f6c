<?php

namespace App;

use ScoutElastic\SearchRule;

class MySearchRule extends SearchRule
{
    /**
     * @inheritdoc
     */
    // This method returns an array, describes how to highlight the results.
    // If null is returned, no highlighting will be used
    public function buildHighlightPayload()
    {
        return [
            'fields' => [
                'name' => [
                    'type' => 'plain'
                ]
            ]
        ];
    }

    /**
     * @inheritdoc
     */
    // This method returns an array, that represents bool query.
    public function buildQueryPayload()
    {
        $query = $this->builder->query;
        return [
            'must'  => [
                'multi_match'   => [
                    'query'     => $query,
                    'fields'    => ['name'],
                    'type'      => 'phrase_prefix',
                ],
            ],
        ];
        /*return [
            'must' => [
                'match' => [
                    'name' => $this->builder->query
                ]
            ]
        ];*/
    }
}
