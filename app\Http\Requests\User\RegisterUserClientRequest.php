<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class RegisterUserClientRequest extends BaseRequest
{
    public function rules()
    {
        $seventeen_years_prior_now = Carbon::now()->subYears(17);
        return [
            'name' => 'required|max:255',
            'email' => 'required_without:phone|unique:users,email|max:255|regex:/^([a-zA-Z0-9\_\-\/]+)(\.[a-zA-Z0-9\_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u',
            'password' => 'bail|required|min:6|max:255',//|regex:/^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};:\\|,.<>\/?]).{6,20}$/',
            'gender' => 'nullable|boolean',
            'phone' => 'required_without:email|unique:users,phone|digits_between:10,11',
            'date_of_birth' => 'bail|nullable|date_format:Y-m-d|before:'.$seventeen_years_prior_now,
            'identity_card' => 'nullable|regex:/^\d{9}(\d{3})?$/',
            'province_id' => 'nullable|integer|exists:dvhc2021_tinh,id',
            'district_id' => 'nullable|integer|exists:dvhc2021_huyen,id',
            'ward_id' => 'nullable|integer|exists:dvhc2021_xa,id',
            'address' => 'nullable|max:255',
            'description' => 'nullable|max:5000',
            'otp' => 'required|min:6',
            'agent' => 'required|in:phone,zalo,email',
            // 'role_id' => 'bail|required|integer|exists:roles,id|in:8',
            // 'user_name' => 'nullable|unique:users,user_name|string|min:8|max:64|regex:/^([a-zA-Z0-9]+)(\_[a-zA-Z0-9]+)*$/u'
        ];
    }
    // public function messages()
    // {

    //     return [
    //         'name.required' => 'User_001_E_001',
    //         'name.regex' => 'User_002_E_002',

    //         'email.required' => 'User_001_E_003',
    //         'email.regex' => 'User_002_E_004',
    //         'email.unique' => 'User_005_E_005',

    //         'password.required' => 'User_001_E_006',
    //         'password.confirmed' => 'User_010_E_007',
    //         'password.min' => 'User_004_E_008',

    //         'role_id.required' => 'User_001_E_009',
    //         'role_id.integer' => 'User_002_E_010',
    //         'role_id.exists' => 'User_003_E_011',
    //         'role_id.in' => 'User_002_E_012',

    //         'gender.boolean' => 'User_002_E_013',

    //         'province_id.required' => 'User_001_E_014',
    //         'province_id.integer' => 'User_002_E_015',
    //         'province_id.exists' => 'User_003_E_016',

    //         'district_id.required' => 'User_001_E_017',
    //         'district_id.integer' => 'User_002_E_018',
    //         'district_id.exists' => 'User_003_E_019',

    //         'ward_id.required' => 'User_001_E_020',
    //         'ward_id.integer' => 'User_002_E_021',
    //         'ward_id.exists' => 'User_003_E_022',

    //         'address.required' => 'User_001_E_023',

    //         'phone.required' => 'User_001_E_024',
    //         'phone.digits_between' => 'User_004_E_025',

    //         'date_of_birth.required' => 'User_001_E_026',
    //         'date_of_birth.date_format' => 'User_008_E_027',
    //         'date_of_birth.before' => 'User_004_E_028',

    //         'identity_card.regex' => 'User_002_E_029',

    //         'user_name.required' => 'User_001_E_037',
    //         'user_name.unique' => 'User_005_E_038',
    //         'user_name.string' => 'User_002_E_039',
    //         'user_name.min' => 'User_004_E_040',
    //         'user_name.max' => 'User_004_E_041',
    //         'user_name.regex' => 'User_002_E_042',

    //         'name.max'          => 'User_004_E_064',

    //         'password.max'      => 'User_004_E_065',
    //         'password.regex'      => 'User_002_E_066',

    //         'email.max'         => 'User_004_E_067',

    //         'address.max'       => 'User_004_E_068',

    //         'description.max'   => 'User_004_E_069',

    //     ];
    // }
}
