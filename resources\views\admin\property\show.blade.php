@extends('admin.layout.app')
@section('content')
<script type="text/javascript">
    var token1= "<?php echo Auth::user()->token; ?>";
</script>
<script src="{{ asset('/assets/js/admin/show_datatable.js') }}"></script>
<script src="{{ asset('/assets/js/admin/update_property.js') }}"></script>
<script src="{{ asset('/assets/js/admin/showAddress.js') }}"></script>

<h1 ><PERSON>h sách Bất Động Sản</h1>
<table class="table table-striped" id="showproperty" style="width:100%">
    <thead>
        <tr id="list-header" >
            <th scope="col"></th>
            <th scope="col"></th>
            <th scope="col">ID</th>
            <th scope="col">land_block_code</th>
            <th scope="col">province_id</th>
            <th scope="col">district_id</th>
            <th scope="col">ward_id</th>
            <th scope="col">address</th>
            <th scope="col">building_permitted</th>
            <th scope="col">remaining_area</th>
            <th scope="col">under_plan</th>
            <th scope="col">taken_plan_area</th>
            <th scope="col">block_number</th>
            <th scope="col">map_number</th>
            <th scope="col">expire_time</th>
            <th scope="col">legal_changes</th>
            <th scope="col">longitude</th>
            <th scope="col">latitude</th>
            <th scope="col">notes</th>
            <th scope="col">size_id</th>
            <th scope="col">remaining_area_facility_id</th>
            <th scope="col">red_book</th>
            <th scope="col">pink_book</th>
            <th scope="col">hand_paper</th>
            <th scope="col">construction_legal_paper</th>
            <th scope="col">created_at</th>
            <th scope="col">updated_at</th>
    </thead>
</table>
<!-- Modal -->
<div id="myModal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="container-fluid">
                <form id="update_property" class="form-horizontal" role="form">
                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.land_block_code') }}</label>
                        <div class="col-10">
                            <input type="text" id="land_block_code" class="form-control">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.province_id') }}</label>
                        <div class="col-3">
                            <select class="form-control" id="province_id" name="province_id">
                                <option value='0'>-- Select Province --</option>
                            </select>
                        </div>

                        <label class="col-form-label col-1">{{ __('property.district_id') }}</label>
                        <div class="col-3">
                            <select class="form-control" id="district_id" name="district_id">
                                <option value='0'>-- Select District --</option>
                            </select>
                        </div>

                        <label class="col-form-label col-1">{{ __('property.ward_id') }}</label>
                        <div class="col-2">
                            <select class="form-control" id="ward_id" name="ward_id">
                                <option value='0'>-- Select Wards --</option>                            
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.address') }}</label>
                        <div class="col-10">
                            <input type="text" id="address" class="form-control">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.building_permitted') }}</label>
                        <div class="col-4">
                            <input type="text" id="building_permitted" class="form-control">
                        </div>
                        <label class="col-2 col-form-label">{{ __('property.remaining_area') }}</label>
                        <div class="col-4">
                            <input type="text" id="remaining_area" class="form-control">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.under_plan') }}</label>
                        <div class="col-2">
                            <select class="form-control" id="under_plan" name="under_plan">
                                <option value="0">Không</option>
                                <option value="1">Có</option>
                            </select>
                        </div>

                        <label class="col-2 col-form-label">{{ __('property.red_book') }}</label>
                        <div class="col-2">
                            <select class="form-control" id="red_book" name="red_book">
                                <option value="0">Không</option>
                                <option value="1">Có</option>
                            </select>
                        </div>

                        <label class="col-2 col-form-label">{{ __('property.pink_book') }}</label>
                        <div class="col-2">
                            <select class="form-control" id="pink_book" name="pink_book">
                                <option value="0">Không</option>
                                <option value="1">Có</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.taken_plan_area') }}</label>
                        <div class="col-4">
                            <input type="text" id="taken_plan_area" class="form-control">
                        </div>
                        <label class="col-2 col-form-label">{{ __('property.block_number') }}</label>
                        <div class="col-4">
                            <input type="text" id="block_number" class="form-control">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.map_number') }}</label>
                        <div class="col-10">
                            <input type="text" id="map_number" class="form-control">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.expire_time') }}</label>
                        <div class="col-10">
                            <input class="form-control" type="date" id="expire_time">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.legal_changes') }}</label>
                        <div class="col-10">
                            <input type="text" id="legal_changes" class="form-control">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.longitude') }}</label>
                        <div class="col-4">
                            <input type="text" id="longitude" class="form-control">
                        </div>

                        <label class="col-2 col-form-label">{{ __('property.longitude') }}</label>
                        <div class="col-4">
                            <input type="text" id="latitude" class="form-control">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.hand_paper') }}</label>
                        <div class="col-2">
                            <select class="form-control" id="hand_paper" name="hand_paper">
                                <option value="0">Không</option>
                                <option value="1">Có</option>
                            </select>
                        </div>

                        <label class="col-2 col-form-label">{{ __('property.construction_legal_paper') }}</label>
                        <div class="col-2">
                            <select class="form-control" id="construction_legal_paper" id="construction_legal_paper">
                                <option value="0">Không</option>
                                <option value="1">Có</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-2 col-form-label">{{ __('property.notes') }}</label>
                        <div class="col-10">
                            <textarea class="form-control" type="text" id="notes" rows="5"></textarea>
                        </div>
                    </div>

                    <div class="form-group row justify-content-end">
                        <button class="btn btn-success" type="submit" id="update">Update</button>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>
@endsection
