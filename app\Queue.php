<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;

class Queue extends Model
{
    protected $table = 'jobs';
    protected $fillable = [
        'id', 'queue', 'payload', 'attempts', 'reserved_at', 'available_at', 'created_at'
    ];

//    public function description()
//    {
//        $key = 'admin.queue.' . $this->queue;
//
//        if (Lang::has($key)) {
//            return __($key);
//        } else {
//            return 'Mặc định';
//        }
//    }
}
