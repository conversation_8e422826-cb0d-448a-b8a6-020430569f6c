<?php

namespace App\Admin\Controllers;

use App\Request;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Controllers\AdminController;

class RequestController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Request';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */

    protected function grid(){
        $grid = new Grid(new Request());
        $grid->column('id', __('Id'));
        $grid->column('user', __('admin.user.title'))->display(function ($user)
        {
            if(isset($user))
                return "<a href='/admin/users/{$user['id']}'>{$user['name']}</a>";
            else return $this->user_id;
        });
        $grid->column('type', __('Type'));
        $grid->column('object_id', __('Object_Id'));
        $grid->column('reason', __('admin.reason.title'));
        $grid->column('status', __('admin.status'));
        $grid->column('scheduled_at', __('Scheduled_at'));
        $grid->column('created_at', __('Created at'));
        $grid->column('updated_at', __('Updated at'));
//        $grid->disableActions();
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id){
        $show = new Show(Request::findOrFail($id));
        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */

    protected function form()
    {
        $form = new Form(new Request());
        $form->text('id', __('Id'))->disable();
        $form->text('user_id', __('UserID'))->disable();
        $form->select('type', __('Type'))
        ->options([
            "delete_account" => "Delete Account"
        ])->default($form->model()->type);
        $form->text('object_id', __('Object Id'))->disable();
        $form->textarea('reason', __('admin.reason.title'))->placeholder(__('admin.reason.placeholder'));
        $form->select('status', __('admin.status'))
            ->options([
                'pending' => 'Pending',
                'success' => 'Success',
                'failed' => 'Failed'
            ])
            ->default($form->model()->status); // Trạng thái hiện tại

        $form->datetime('scheduled_at', __('Scheduled_at'));
        return $form;
    }
}
