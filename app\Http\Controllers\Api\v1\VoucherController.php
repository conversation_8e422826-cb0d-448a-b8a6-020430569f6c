<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Voucher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use App\Http\Requests\Voucher\ShopCreateVoucherRequest;
use App\Http\Requests\Voucher\ShopUpdateVoucherRequest;

class VoucherController extends Controller
{
    public function create(ShopCreateVoucherRequest $request)
    {
        $data = $request->all();

        
        $voucher = Voucher::create($data);
        
        if (!$voucher) {
            return response()->json([
                'status' => JsonResponse::HTTP_OK,
                'message' => "error."],
                JsonResponse::HTTP_OK);
        }
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $voucher
            ]
        ];
    
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function update(ShopUpdateVoucherRequest $request)
    {
        $data = $request->all();
        $voucher = Voucher::find($data['id']);

        $voucher->update();
        if (!$voucher) {
            return response()->json([
                'status' => JsonResponse::HTTP_OK,
                'message' => "error."],
                JsonResponse::HTTP_OK);
        }
        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $voucher
            ]
        ];
    
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function list(Request $request)
    {
        $data = $request->only([
            'shop_id', 'limit', 'offset', 'from', 'to'
        ]);
        $data['shop_id'] = $data['shop_id'] ?? '';
        $data['limit'] = $data['limit'] ?? 50;
        $data['offset'] = $data['offset'] ?? 0;
        $data['from'] = $data['from'] ?? '';
        $data['to'] = $data['to'] ?? '';

        $vouchers = Voucher::where('shop_id', $data['shop_id'])
            ->when($data['from'], function ($query) use ($data) {
                return $query->where('created_at', '>=', $data['from']);
            })
            ->when($data['to'], function ($query) use ($data) {
                return $query->where('created_at', '<=', $data['to']);
            })
            ->offset($data['offset'])
            ->limit($data['limit'])
            ->get();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $vouchers,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function detail($id)
    {
        $voucher = Voucher::find($id);
        if (!$voucher) {
            return response()->json([
                'status' => JsonResponse::HTTP_NOT_FOUND,
                'message' => "Voucher not found."
            ], JsonResponse::HTTP_NOT_FOUND);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $voucher
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
