<?php

namespace App\Http\Controllers\Api\v2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Token\RemoveTokenRequest;
use App\Services\Device\DeviceService;
use App\Services\Token\TokenService;
use App\Token;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DeviceController extends Controller
{
    function getDevicesByUser(Request $request, DeviceService $service)
    {
        $data = $request->only([
            'token_type',
        ]);
        $result = $service->listByUser($data['token_type']);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
}
