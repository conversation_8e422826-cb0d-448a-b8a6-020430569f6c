<?php

namespace App\Http\Requests\Brand;

use App\Http\Requests\BaseRequest;

class BrandRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'name' => 'required|max:255',
        ];
    }

    public function messages()
    {
        return [
            // 'name.required' => 'Brand_001_E_001',
            // 'name.max' => 'Brand_001_E_002',
        ];
    }
}
