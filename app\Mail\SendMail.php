<?php

namespace App\Mail;

use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Services\HistoryMail\HistoryMailService;

class SendMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $name;
    protected $password;

    public function __construct($name, $password)
    {
        $this->name = $name;
        $this->password = $password;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $historyMail = null;
        try {
            $historyMail = HistoryMailService::insert([
                'title' => 'Thông báo đặt lại mật khẩu mới',
                'content' => json_encode(['name' => $this->name, 'pass' => $this->password]),
                'status' => config('constants.status_history_mail.success')
            ]);

            return $this->subject('Thông báo đặt lại mật khẩu mới')
                    ->view('emails.mail')->with(['name' => $this->name, 'pass' => $this->password]);


        }catch (\Exception $e) {
            Log::info("Mail Error: ".$e);

            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

    }
}
