<?php

namespace App\Admin\Selectable;

use App\Product;
use App\OrderItem;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Grid\Selectable;
use Illuminate\Support\Facades\Log;

class Products extends Selectable
{
    public $model = Product::class;

    public function make()
    {
        // $this->column('id');
        $this->column('name');
        $this->column('profile_picture','profile Picture')->image();

        $this->column('price')->display(function($price) {
            return number_format($price, 2).'đ';
        })->color('#0061a7')->limit(30);

        $this->column('price_root')->display(function($price_root) {
            return $price_root ? number_format($price_root, 2).'đ' : 'N/A';
        })->color('#28a745')->limit(30);

        $this->column('price_off')->display(function($price) {
            return number_format($price, 2).'đ';
        })->color('#0061a7')->limit(30);

        $this->filter(function (Filter $filter) {
            $filter->like('name');
        });
        $this->column('quantity')->display(function($quantity) {
            return $quantity; // Display the quantity directly
        })->label('Quantity'); // Optional: Add a label for clarity
    }
}