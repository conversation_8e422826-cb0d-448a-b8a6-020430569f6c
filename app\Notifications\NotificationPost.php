<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use App\Services\Mqtt\MqttChatService;
use App\User;
use App\Services\HistoryMail\HistoryMailService;
use Illuminate\Support\Facades\Log;

class NotificationPost extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    private $_user;
    private $_post;
    private $text;
    public function __construct($user, $post)
    {
        $this->_user = $user;
        $this->_post = $post;
        $this->text = "Bài viết của quý khách:";
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        // return ['database', 'mail'];
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $mqtt = new MqttChatService();
        $result = User::find($this->_user->id);
        $_notify = $result->notifications();
        $countUnread = $_notify->whereNull('read_at')->count();
        $title = $this->_post->name;
        $confirm = $this->_post->confirmed == 2 ? "Đã được duyệt" : "Đã bị từ chối.";
        $image = isset($this->_post->image['path']) ? $this->_post->image['path'] : null;
        $mqtt->publish(['topic' => $this->_user->id, 'message' => $countUnread]);
        return [
            'title' => $title,
            'content' => "Bài viết tin tức ".$confirm,
            'image' => $image,
            'link'  => null
        ];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // var_dump($this->_user->name,$this->_user->email);
        // exit;
        $historyMail = null;
        try {
            $historyMail = HistoryMailService::insert([
                'title' => 'Thông Báo Bài Viết NOMNOMLAND',
                'content' => json_encode(['content' => $this->text, 'object_name' => $this->_post->name, 'confirm' => $this->_post->confirmed]),
                'status' => config('constants.status_history_mail.success')
            ]);
            return (new MailMessage)->subject('Thông Báo Bài Viết NOMNOMLAND')->view('emails.notification_post',['content' => $this->text, 'object_name' => $this->_post->name, 'confirm' => $this->_post->confirmed, 'user_name' => $this->_user->name]);
        } catch (\Exception $e) {
            Log::info("Mail Error: ".$e);

            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

        // ->greeting('Hello!')
        // ->line('Your order status has been updated')
        // ->action('Check it out', url('/'))
        // ->line('Best regards!')
        // ;
    }


}
