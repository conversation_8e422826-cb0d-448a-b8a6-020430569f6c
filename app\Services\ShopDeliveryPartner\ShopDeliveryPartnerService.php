<?php

namespace App\Services\ShopDeliveryPartner;

use App\Services\DeliveryPartner\DeliveryPartnerService;
use App\ShopDeliveryPartner;

class ShopDeliveryPartnerService
{
    private $shopId;
    private $deliveryId;

    function __construct($shopId, $deliveryId){
        $this->shopId = $shopId;
        $this->deliveryId = $deliveryId;
    }

    /**
     * Update shop delivery partner settings and connection data
     *
     * @param array $data Data to update including is_enable and data_connect
     * @return
     */
    function update($data)
    {
        // Fetch shop delivery partner with its delivery partner info
        $shopDeliveryPartner = ShopDeliveryPartner::with('deliveryPartner')
            ->where([
                'shop_id' => $this->shopId,
                'delivery_partner_id' => $this->deliveryId,
            ])->first();

        if (!$shopDeliveryPartner) {
            return false;
        }

        // Update enable/disable status if provided
        if (isset($data['is_enabled'])) {
            $shopDeliveryPartner['is_enabled'] = $data['is_enabled'];
            $shopDeliveryPartner->save();
        }

        // Update default status if provided
        if (isset($data['is_default'])) {
            $shopDeliveryPartner['is_default'] = $data['is_default'];
            $shopDeliveryPartner->save();

            // Update other delivery partners for the shop
            ShopDeliveryPartner::where('shop_id', $this->shopId)
                ->where('delivery_partner_id', '!=', $this->deliveryId)
                ->update(['is_default' => !$data['is_default']]);
        }

        // Merge existing and new connection data
        $newConnectData = [];
        if (isset($data['connect_data'])) {
            $newConnectData =  array_merge($shopDeliveryPartner->connection_data ?? [], $data['connect_data']);
        }
        // If connection data changed, update via delivery partner service
        if ($newConnectData != $shopDeliveryPartner->connect_data && $newConnectData) {
            $partner = strtolower($shopDeliveryPartner->deliveryPartner->name);
            $service = (new DeliveryPartnerService())
                ->setDeliveryPartner($partner, $this->shopId);
            $result = $service->connectDeliveryPartner($newConnectData);

            return isset($result['error']) ? false : $result;
        }

        // Get updated partner data from replica DB
        return $shopDeliveryPartner;
    }
}
