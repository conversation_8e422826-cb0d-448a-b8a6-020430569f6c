<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Product;
use App\User;
use App\Shop;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PriceRootTest extends TestCase
{
    /**
     * Test that price_root is included in fillable array
     */
    public function test_price_root_is_fillable()
    {
        $product = new Product();
        $this->assertContains('price_root', $product->getFillable());
    }

    /**
     * Test that price_root is cast to float
     */
    public function test_price_root_is_cast_to_float()
    {
        $product = new Product();
        $casts = $product->getCasts();
        $this->assertEquals('float', $casts['price_root']);
    }

    /**
     * Test permission methods exist
     */
    public function test_permission_methods_exist()
    {
        $product = new Product();
        $this->assertTrue(method_exists($product, 'canViewPriceRoot'));
        $this->assertTrue(method_exists($product, 'canEditPriceRoot'));
        $this->assertTrue(method_exists($product, 'isShopOwner'));
        $this->assertTrue(method_exists($product, 'isShopAgent'));
    }

    /**
     * Test that admin can view price_root
     */
    public function test_admin_can_view_price_root()
    {
        // Create a mock admin user
        $admin = new User();
        $admin->id = 'test-admin-id';
        $admin->role_id = User::ROLE_ADMIN;

        // Create a mock product
        $product = new Product();
        $product->shop_id = 'test-shop-id';

        // Mock the User::find method to return our admin
        $this->app->bind(User::class, function () use ($admin) {
            $mock = \Mockery::mock(User::class);
            $mock->shouldReceive('find')->with('test-admin-id')->andReturn($admin);
            return $mock;
        });

        $this->assertTrue($product->canViewPriceRoot('test-admin-id'));
    }
}
