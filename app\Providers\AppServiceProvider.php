<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use <PERSON>mon\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\UploadedFile;
use Hash;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Relations\Relation;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app['request']->server->set('HTTPS', true);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        DB::listen(function ($query) {
            if(env('DB_QUERY_LOG') == true){
                // Get the SQL statement and convert to uppercase for checking
                $sql = strtoupper(trim($query->sql));

                // Only log CREATE (INSERT), UPDATE, and DELETE queries
                if (preg_match('/^(INSERT|UPDATE|DELETE|CREATE|ALTER|DROP|TRUNCATE)\s/', $sql)) {

                    // Create executable SQL query
                    $executableSql = self::formatSqlWithBindings($query->sql, $query->bindings);

                    // Parse query for restoration data
                    $logData = [
                        'operation_type' => self::getOperationType($query->sql),
                        'table_name' => self::extractTableName($query->sql),
                        'timestamp' => now()->format('Y-m-d H:i:s'),
                        'user_id' => self::getUserIdFromToken() ?? 'guest',
                        'user_email' => self::getUserEmailFromToken() ?? 'guest',
                        'request_id' => request()->header('X-Request-ID') ?? uniqid(),
                        'ip_address' => request()->ip(),
                        'execution_time' => $query->time . 'ms',
                        'connection' => $query->connectionName,
                        'EXECUTABLE_SQL' => $executableSql,
                        // 'raw_sql_with_placeholders' => $query->sql,
                        // 'bindings' => $query->bindings,
                    ];

                    // Log in a format that's easy to extract and run
                    Log::channel('database_queries')->info(json_encode($logData));
                }
            }
        });
        Schema::defaultStringLength(191);
        // \URL::forceScheme('https');
        // current password validation rule
        Validator::extend('current_password', function ($attribute, $value, $parameters, $validator) {
            return Hash::check($value, Auth::user()->password);
        });

        //---check size image-----------------
        Validator::extend('base64size', function ($attribute, $value, $parameters, $validator) {

            $size = (string)(strlen(rtrim($value, '=')) * 0.75);

            $validator->addReplacer('base64size', function (
                $message,
                $attribute,
                $rule,
                $parameters
            ) use ($size) {
                return \str_replace(':values', $size, $message);
            });

            if((int)$size > 10000000)
            {
                return false;
            }
            return true;
        });

        //---check size image panorama-----------------
        Validator::extend('panoramasize', function ($attribute, $value, $parameters, $validator) {

            $size = (string)(strlen(rtrim($value, '=')) * 0.75);

            $validator->addReplacer('panoramasize', function (
                $message,
                $attribute,
                $rule,
                $parameters
            ) use ($size) {
                return \str_replace(':values', $size, $message);
            });

            if((int)$size > 18000000)
            {
                return false;
            }
            return true;
        });

        //----check image-------------------
        Validator::extend('base64image', function ($attribute, $value, $parameters, $validator) {
            $explode = explode(',', $value);
            $allow = ['jpeg', 'png', 'bmp', 'gif', 'svg', 'webp'];
            // $allow = ['jpeg', 'png', 'jpg'];
            $format = str_replace(
                [
                    'data:image/',
                    ';',
                    'base64',
                ],
                [
                    '', '', '',
                ],
                $explode[0]
            );

            $extensions = implode(',', $allow);

            $validator->addReplacer('base64image', function (
                $message,
                $attribute,
                $rule,
                $parameters
            ) use ($extensions) {
                return \str_replace(':values', $extensions, $message);
            });

            // check file format
            if (!in_array($format, $allow)) {
                return false;
            }

            // check base64 format
            if (!preg_match('%^[a-zA-Z0-9/+]*={0,2}$%', $explode[1])) {
                return false;
            }

            return true;
        });

        Validator::extend('file_extension', function ($attribute, $value, $parameters, $validator) {
            if (!$value instanceof UploadedFile) {
                return false;
            }

            $extensions = implode(',', $parameters);

            $validator->addReplacer('file_extension', function (
                $message,
                $attribute,
                $rule,
                $parameters
            ) use ($extensions) {
                return \str_replace(':values', $extensions, $message);
            });

            $extension = strtolower($value->getClientOriginalExtension());

            return $extension !== '' && in_array($extension, $parameters);
        });

        Relation::morphMap([
            'user' => 'App\User',
            'shop' => 'App\Shop',
        ]);

    }

    /**
     * Extract operation type from SQL query
     */
    private static function getOperationType($sql)
    {
        $sql = strtoupper(trim($sql));
        if (preg_match('/^(INSERT|UPDATE|DELETE|CREATE|ALTER|DROP|TRUNCATE)\s/', $sql, $matches)) {
            return $matches[1];
        }
        return 'UNKNOWN';
    }

    /**
     * Extract table name from SQL query
     */
    private static function extractTableName($sql)
    {
        $sql = strtoupper(trim($sql));

        // For INSERT INTO table_name
        if (preg_match('/INSERT\s+INTO\s+[`"]?([a-zA-Z0-9_]+)[`"]?/i', $sql, $matches)) {
            return $matches[1];
        }

        // For UPDATE table_name SET
        if (preg_match('/UPDATE\s+[`"]?([a-zA-Z0-9_]+)[`"]?\s+SET/i', $sql, $matches)) {
            return $matches[1];
        }

        // For DELETE FROM table_name
        if (preg_match('/DELETE\s+FROM\s+[`"]?([a-zA-Z0-9_]+)[`"]?/i', $sql, $matches)) {
            return $matches[1];
        }

        // For CREATE/ALTER/DROP TABLE table_name
        if (preg_match('/(CREATE|ALTER|DROP)\s+TABLE\s+[`"]?([a-zA-Z0-9_]+)[`"]?/i', $sql, $matches)) {
            return $matches[2];
        }

        return 'unknown';
    }

    /**
     * Format SQL with actual binding values for easier reading
     */
    private static function formatSqlWithBindings($sql, $bindings)
    {
        if (empty($bindings)) {
            return $sql;
        }

        $formattedSql = $sql;
        foreach ($bindings as $binding) {
            if (is_string($binding)) {
                $binding = "'" . addslashes($binding) . "'";
            } elseif (is_null($binding)) {
                $binding = 'NULL';
            } elseif (is_bool($binding)) {
                $binding = $binding ? 'TRUE' : 'FALSE';
            }

            $formattedSql = preg_replace('/\?/', $binding, $formattedSql, 1);
        }

        return $formattedSql;
    }

    /**
     * Extract affected data information for restoration purposes
     */
    private static function extractAffectedData($sql, $bindings)
    {
        $sql = strtoupper(trim($sql));
        $data = [];

        // For UPDATE queries, try to extract WHERE conditions
        if (strpos($sql, 'UPDATE') === 0) {
            $data['operation'] = 'UPDATE';
            $data['note'] = 'To restore: Use the WHERE condition to identify affected records and revert changes';

            // Try to extract WHERE clause
            if (preg_match('/WHERE\s+(.+?)(?:\s+ORDER\s+BY|\s+LIMIT|$)/i', $sql, $matches)) {
                $data['where_clause'] = trim($matches[1]);
            }
        }

        // For DELETE queries
        elseif (strpos($sql, 'DELETE') === 0) {
            $data['operation'] = 'DELETE';
            $data['note'] = 'To restore: Re-insert the deleted records using backup data';
            $data['warning'] = 'CRITICAL: Data permanently removed - ensure backup exists';

            // Try to extract WHERE clause
            if (preg_match('/WHERE\s+(.+?)(?:\s+ORDER\s+BY|\s+LIMIT|$)/i', $sql, $matches)) {
                $data['where_clause'] = trim($matches[1]);
            }
        }

        // For INSERT queries
        elseif (strpos($sql, 'INSERT') === 0) {
            $data['operation'] = 'INSERT';
            $data['note'] = 'To restore: DELETE the inserted records using the same conditions';

            // Try to extract column names
            if (preg_match('/INSERT\s+INTO\s+[`"]?[a-zA-Z0-9_]+[`"]?\s*\(([^)]+)\)/i', $sql, $matches)) {
                $data['columns'] = array_map('trim', explode(',', $matches[1]));
            }
        }

        return $data;
    }

    /**
     * Get user ID from JWT token in Authorization header
     */
    private static function getUserIdFromToken()
    {
        try {
            $token = request()->bearerToken();
            if (!$token) {
                return null;
            }
            
            $user = JWTAuth::setToken($token)->authenticate();
            return $user ? $user->id : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get user email from JWT token in Authorization header
     */
    private static function getUserEmailFromToken()
    {
        try {
            $token = request()->bearerToken();
            if (!$token) {
                return null;
            }
            
            $user = JWTAuth::setToken($token)->authenticate();
            return $user ? $user->email : null;
        } catch (\Exception $e) {
            return null;
        }
    }

}
