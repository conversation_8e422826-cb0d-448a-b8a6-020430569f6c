<?php

namespace App\Http\Requests\Role;

use App\Http\Requests\BaseRequest;

class RoleRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'name' => 'required|max:255|unique:roles,name',
            'description' => 'nullable|max:255'
        ];
    }

    // public function messages()
    // {
    //     return [
    //         'name.required' => 'Role_001_E_001',
    //         'name.unique' => 'Role_005_E_002',

    //         'name.max' => 'Role_004_E_006',
    //         'description.max' => 'Role_004_E_007',
    //     ];
    // }
}
