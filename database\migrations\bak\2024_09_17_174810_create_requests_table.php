<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRequestsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('requests', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id')->nullable();
            $table->string('type')->comment('report_product,report_shop, report_user, delete_account'); // 'report_product', 'report_shop', 'report_user', 'delete_account'
            $table->uuid('object_id'); // UUID của product, shop hoặc user bị report
            $table->text('reason')->nullable();
            $table->string('status')->default('pending'); // 'pending', 'processed', 'rejected'
            $table->timestamp('scheduled_at')->nullable(); // Cho yêu cầu xóa tài khoản
            $table->timestamps();

        });
    }

    public function down()
    {
        Schema::dropIfExists('requests');
    }
}
