<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use App\Place;
use App\Setting;
use App\Policies\PlacePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Model'     => 'App\Policies\ModelPolicy',
        'App\User'      => 'App\Policies\UserPolicy',
        'App\Role'      => 'App\Policies\RolePolicy',
        // 'App\ProblemReport'  => 'App\Policies\ProblemReportPolicy',
        'App\Token'     => 'App\Policies\TokenPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        // Gate::before(function ($user, $ability) {

        //     $setting = Setting::where('key','root_admin')->first();

        //     if (in_array($user->role_id, json_decode($setting['value'],true))) {
        //         return true;
        //     }

        // });

    }
}
