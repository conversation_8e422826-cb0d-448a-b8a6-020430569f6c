<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddConnectDataToShopDeliveryPartnersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shop_delivery_partners', function (Blueprint $table) {
            $table->json('connect_data')->nullable()->comment("Các thông tin kết nối tới đơn vị vận chuyển");
            $table->text('token')->comment("Token kết nối")->nullable();
            $table->timestamp('token_expired')->comment("Thời gian token hết hạn")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shop_delivery_partners', function (Blueprint $table) {
            $table->dropColumn('connect_data');
            $table->dropColumn('token');
            $table->dropColumn('token_expired');
        });
    }
}
