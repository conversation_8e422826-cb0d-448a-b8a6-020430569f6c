<?php
namespace  App\Admin\Actions;

use App\Services\Token\TokenService;
use App\User;
use Encore\Admin\Actions\RowAction;

class SuggestDisable extends RowAction
{
    public function name()
    {
        return $this->row->is_suggest ? 'Disable' : 'Enable';
    }

    public function handle($model)
    {
        // Toggle the enabled status
        $actionName = $model->is_suggest ? __('admin.disable') : __('admin.enable');
        $model->is_suggest = !$model->is_suggest;

        if ($model instanceof User) { // Check if the model is User
            if ($actionName == 'Disable') {
                $model->token = null;
                // Disable user's token in the Token table by user_id
                // Token::where('user_id', $model->id)->update(['token' => null]);
                TokenService::deleteList(['id' => $model->id]);
            }
        }
        $model->save();

        return $this->response()->success($actionName . ' Success!')->refresh();
    }
    public function display($star)
    {
        // return $star ? "<i class=\"fa fa-star-o\"></i>" : "<i class=\"fa fa-star\"></i>";
        return $star ? "<i class=\"fa fa-regular fa-check-circle text-green\"></i>" : "<i class=\"fa fa-solid fa-ban text-red\"></i>";
    }
}
