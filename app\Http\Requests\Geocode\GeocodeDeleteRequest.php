<?php

namespace App\Http\Requests\Geocode;

use App\Http\Requests\BaseRequest;

class GeocodeDeleteRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'id'        => 'required|uuid|exists:geocodes,id'
        ];
    }

    public function messages()
    {
        return [
            'id.required'       => 'Geocode_001_E_007',
            'id.uuid'           => 'Geocode_002_E_008',
            'id.exists'         => 'Geocode_003_E_009',

        ];
    }
}
