<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use App\Services\Mqtt\MqttChatService;
use App\User;
use App\Services\HistoryMail\HistoryMailService;
use App\Services\Log\LogSendMailService;
use Illuminate\Support\Facades\Log;
use App\Services\Notification\NotificationService;
use App\Services\Token\TokenService;

class NotificationProperty extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    private $_user;
    private $_property;
    private $text;
    public function __construct($user, $property)
    {
        $this->_user = $user;
        $this->_property = $property;
        $this->text = "Bất động sản của quý khách:";
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        if($this->_user->role_id == config('constants.role_id.admin'))
        {
            return ['database'];
        }
        else {
            return ['database', 'mail'];
        }
        // return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $mqtt = new MqttChatService();
        $result = User::find($this->_user->id);
        $_notify = $result->notifications();
        $countUnread = $_notify->whereNull('read_at')->count();
        $confirm = $this->_property->confirmed == 2 ? "Bài đăng đã được duyệt" : "Bài đăng đã bị từ chối.";

        $imagePath = isset($this->_property->image['path_thumbnail']) ? $this->_property->image['path_thumbnail'] 
        : (isset($this->_property->image['path']) ? $this->_property->image['path'] 
            : (isset($this->_property->images[0]['path']) ? $this->_property->images[0]['path'] : null));

        $image = $imagePath ? env('MINIO_ENDPOINT').$imagePath : null;
        $mqtt->publish(['topic' => $this->_user->id, 'message' => $countUnread]);

        $data = [
            'body'  => $confirm,
            'title' => $this->_property->name,
            'image' => $image,
            'url'   => $this->_property->slug,
            'type'  => 'property'
        ];
        $tokenService = new TokenService();
        $tokens = $tokenService->listByUser($this->_user->id, 2);

        $notiService = new NotificationService;
        $notiService->sendBatchNotification($tokens, $data);

        return [
            'content' => $confirm,
            'title' => $this->_property->name,
            'image' => $image,
            'link'  => $this->_property->slug,
            'type'  => 'property'
        ];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $historyMail = null;
        try {
            $historyMail = HistoryMailService::insert([
                'title' => 'Thông Báo Bất Động Sản NOMNOMLAND',
                'content' => json_encode(['content' => $this->text, 'object_name' => $this->_property->name, 'confirm' => $this->_property->confirmed]),
                'status' => config('constants.status_history_mail.success')
            ]);
            return (new MailMessage)->subject('Thông Báo Bất Động Sản NOMNOMLAND')->view('emails.notification',['object_name' => $this->_property->name, 'confirm' => $this->_property->confirmed, 'user_name' => $this->_user->name, 'slug' => $this->_property->slug]);
        } catch (\Exception $e) {
            Log::info("Mail Error: ".$e);
            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

        // ->greeting('Hello!')
        // ->line('Your order status has been updated')
        // ->action('Check it out', url('/'))
        // ->line('Best regards!')
        // ;
    }


}
