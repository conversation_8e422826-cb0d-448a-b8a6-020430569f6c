<?php
namespace App\Services\Address;
use App\Province;
use App\ProvinceKML;
use App\Ward;
use App\District;
use App\Village;
use App\Place;
use App\Setting;
use App\Http\Requests\PropertyRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\User;
use Illuminate\Support\Str;
use App\Jobs\UpdateLatLngBoundAddress;
use App\Tag;

class AddressService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }
    // -----------------------
    public function list()
    {
        $result = Province::orderBy('name','asc')->get();
        foreach($result as $key => $value)
        {
            $arr[$key] = $value;
            $value['district'] = District::where('province_id',$value->id)->orderBy('name','asc')->with('wards')->get();
        }
        return $arr;
    }
    // public function getAddress(array $address)
    // {
    //     $province_id = $address['province_id'];
    //     $district_id = $address['district_id'];
    //     $ward_id = $address['ward_id'];
    //     $result = Province::where('id',$province_id)->first();
    //     if(!$result)
    //     {
    //         return false;
    //     }
    //     $arr[] = $result;
    //     $result['district'] = District::where('id',$district_id)
    //     ->where('province_id',$province_id)->first();
    //     if(!$result['district'])
    //     {
    //         return false;
    //     }
    //     $a = $result['district'];
    //     $a['ward'] = Ward::where('id',$ward_id)->where('district_id',$district_id)->first();
    //     if(!$a['ward'])
    //     {
    //         return false;
    //     }
    //    return $arr;
    // }
    public function getProvince($id)
    {
        $check = Province::find($id);

        if(!$check)
        {
            return false;
        }
        $result = Province::where('id',$id)->orderBy('name','asc')->with('districts')->get();
        return $result;
    }
    public function getDistrict($id)
    {
        $check = District::find($id);
        if(!$check)
        {
            return false;
        }
        $result = District::where('id',$id)->orderBy('name','asc')->with('wards')->first();
        return $result;
    }

    public function getWard($id)
    {
        $check = Ward::find($id);
        if(!$check)
        {
            return false;
        }
        $result = Ward::where('id',$id)->orderBy('name','asc')->with('villages')->first();
        return $result;
    }

    public function getProvinceList(){
        $result = Province::orderBy('name','asc')->with('districts')->get();
        return $result;
    }
    public function wardDetail($id)
    {
        $result = Ward::with(['districts','districts.provinces'])->find($id);
        if(!$result)
        {
            return false;
        }
        return $result;
    }
    public function districtByArrId(array $arrId)
    {
        $result = District::whereIn('province_id',$arrId['id'])->orderBy('slug','asc')->get();

        return $result;
    }
    public function wardByArrId(array $arrId)
    {
        $result = Ward::whereIn('district_id',$arrId['id'])->orderBy('slug','asc')->get();

        return $result;
    }
    public function villageByArrId(array $arrId)
    {
        $result = Village::whereIn('ward_id',$arrId['id'])->orderBy('slug','asc')->get();

        return $result;
    }
    //------------------parse address-------------
    public function parseAddress($address)
    {

        $address = str_replace(['-Khánh Hòa', '- Huyện', '-Xã'], [', Khánh Hòa',', Huyện', ',Xã'], $address);
        $data = array_reverse(explode(',' , $address));
        $count = count($data);

        for ($i=0; $i < $count ; $i++) {

            $p = Str::slug(str_replace(['Thành phố', 'tỉnh', 'Tỉnh', 'Province', 'TP.', 'Hanoi','City', 'Tp.','HCM', 'TP', 'tp', 'Haiphong', 'thành phố', 'Thành Phố', 'Tp'],['', '', '','', '','Ha noi','','','ho-chi-minh','','','hai-phong', '', '', ''],preg_replace("/^[0-9]/","",$data[$i])));
            // var_dump($p);
            if(isset($p))
            {
                //--------province-------------------------
                $province_id = Province::where('slug',$p)->orWhere('slug', 'like', "%".$p)->first(['id']);

                //----------district----------------------
                if(isset($province_id) && isset($data[$i +1]))
                {
                    $d = Str::slug(str_replace(['Tp.', 'TP', 'District', 'Q.', 'TX.'],['','','', '', ''],$data[$i +1]));
                    // var_dump($d);
                    $updateDis = str_replace(['quan-2', 'quan-9'], ['thu-duc', 'thu-duc'], $d);
                    $district_id = District::where([['slug', $updateDis],['province_id', $province_id['id']]])->orWhere([['slug', 'like', "%".$updateDis],['province_id', $province_id['id']]])->first(['id']);

                    //-------------ward--------------------
                    if(isset($district_id) && isset($data[$i +2]))
                    {
                        // var_dump($data[$i +2]);
                        $data[$i +2] = preg_replace("/quận [\d] cũ|(Quận Thủ Đức cũ)/","",($data[$i +2]));
                        // var_dump($data[$i +2]); 
                        $w = Str::slug(str_replace(['TT.'],[''],$data[$i +2]));
                        // var_dump($w);
                        $ward_id =Ward::where([['slug', $w],['district_id', $district_id['id']]])->orWhere([['slug', 'like', "%".$w],['district_id', $district_id['id']]])->first(['id']);
                        break;
                    }
                }
            }

            if(isset($province_id))
            {
                break;
            }
        }

        $result = [
            'province_id'   => isset($province_id['id']) ?  $province_id['id'] : null,
            'district_id'   => isset($district_id['id']) ?  $district_id['id'] : null,
            'ward_id'       => isset($ward_id['id']) ?  $ward_id['id'] : null,
        ];

        return $result;

    }

    //-------------update latitude longitude------------------
    public function updateLatLng()
    {
        $result = new UpdateLatLngBoundAddress();
        dispatch($result);

        return [];
    }

    //--------------update province tag---------------
    public function provinceTag()
    {
        $province = Province::get();
        // $district = District::get();
        // $ward = Ward::get();
        $string = ['quan',  'huyen'];
        $string1 = ['thanh-pho','thi-xa'];
        $string2 = ['da-nang','ha-noi','can-tho','hai-phong'];
        $string3 = 'thanh-pho-ho-chi-minh';
        $string4 = ['phuong', 'xa'];
        $string5 = 'thi-tran';

        foreach ($province as $key => $value) {
            // $slug = explode('-',$value->slug);

            if($value->slug == $string3)
            {
                Tag::create(['title' => $value->name,
                                'slug' => $value->slug,
                                'object_type' => 1,
                                'object_id' => $value->id
                ]);
                //địa chỉ thiếu địa chính
                Tag::create(['title' => $value->name,
                                'slug' => Str::slug('ho chi minh'),
                                'object_type' => 1,
                                'object_id' => $value->id
                ]);
            }
            elseif(in_array($value->slug,$string2))
            {
                Tag::create(['title' => $value->name,
                                'slug' => $value->slug,
                                'object_type' => 1,
                                'object_id' => $value->id
                ]);

                //slug địa chỉ đầy đủ
                Tag::create(['title' => $value->name,
                                'slug' => Str::slug('thanh pho-'.$value->slug),
                                'object_type' => 1,
                                'object_id' => $value->id
                ]);
            }
            else {
                Tag::create(['title' => $value->name,
                                'slug' => $value->slug,
                                'object_type' => 1,
                                'object_id' => $value->id
                ]);

                //slug địa chỉ đầy đủ
                Tag::create(['title' => $value->name,
                                'slug' => Str::slug('tinh-'.$value->slug),
                                'object_type' => 1,
                                'object_id' => $value->id
                ]);
            }
        }
    }

    //-----------------update district tag------------------
    public function districtTag()
    {
        // $province = Province::get();
        $district = District::get();
        // $ward = Ward::get();
        $string = ['quan',  'huyen'];
        $string1 = ['thanh-pho','thi-xa'];
        $string2 = ['da-nang','ha-noi','can-tho','hai-phong'];
        $string3 = 'thanh-pho-ho-chi-minh';
        $string4 = ['phuong', 'xa'];
        $string5 = 'thi-tran';

        foreach ($district as $key => $value) {
            $slug = explode('-',$value->slug);

            if(in_array($slug[0], $string))
            {
                Tag::create(['title' => $value->name,
                                'slug' => $value->slug,
                                'object_type' => 2,
                                'object_id' => $value->id
                ]);
                unset($slug[0]);

                $address = explode(',',$value->address);
                $check = is_numeric(implode('-',$slug));

                if($check == false)
                {
                    Tag::create(['title' => $value->name,
                                'slug' => implode('-',$slug),
                                'object_type' => 2,
                                'object_id' => $value->id
                    ]);
                }

                //địa chỉ thiếu địa chính
                $slugPro = explode('-',Str::slug($address[1]));

                if(Str::slug($address[1]) == $string3)
                {
                    $tam = $slugPro;
                    unset($slugPro[0],$slugPro[1]);
                    // $checkSlugDis = is_numeric(implode('-',$slugDis));
                    if($check == false)
                    {

                            Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$slug).'-ho chi minh'),
                                'object_type' => 2,
                                'object_id' => $value->id
                            ]);

                    }
                    else {

                            Tag::create(['title' => $value->name,
                                'slug' => Str::slug($value->slug.'-ho chi minh'),
                                'object_type' => 2,
                                'object_id' => $value->id
                            ]);

                    }
                }
                else {
                    if($check == false)
                    {

                            Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$slug).' '.$address[1]),
                                'object_type' => 2,
                                'object_id' => $value->id
                            ]);
                    }
                    else {

                            Tag::create(['title' => $value->name,
                                'slug' => Str::slug($value->slug.' '.$address[2]),
                                'object_type' => 2,
                                'object_id' => $value->id
                            ]);

                    }
                }

                //slug địa chỉ đầy đủ
                if(in_array(Str::slug($address[1]),$string2))
                {
                    $address[1] = Str::slug('thanh-pho '.$address[1]);
                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 2,
                                'object_id' => $value->id
                    ]);
                }
                elseif(Str::slug($address[1]) == $string3)
                {
                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 2,
                                'object_id' => $value->id
                    ]);
                }
                else {
                    $address[1] = Str::slug('tinh '.$address[1]);

                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 2,
                                'object_id' => $value->id
                    ]);
                }

            }
            elseif(in_array(($slug[0].'-'.$slug[1]), $string1))
            {
                Tag::create(['title' => $value->name,
                                'slug' => $value->slug,
                                'object_type' => 2,
                                'object_id' => $value->id
                ]);
                unset($slug[0],$slug[1]);

                $address = explode(',',$value->address);
                $check = is_numeric(implode('-',$slug));

                if($check == false)
                {
                    Tag::create(['title' => $value->name,
                                'slug' => implode('-',$slug),
                                'object_type' => 2,
                                'object_id' => $value->id
                    ]);
                }

                //địa chỉ thiếu địa chính
                $slugPro = explode('-',Str::slug($address[1]));

                if(Str::slug($address[1]) == $string3)
                {
                    $tam = $slugPro;
                    unset($slugPro[0],$slugPro[1]);
                    // $checkSlugDis = is_numeric(implode('-',$slugDis));
                    if($check == false)
                    {

                            Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$slug).'-ho chi minh'),
                                'object_type' => 2,
                                'object_id' => $value->id
                            ]);

                    }
                    else {

                            Tag::create(['title' => $value->name,
                                'slug' => Str::slug($value->slug.'-ho chi minh'),
                                'object_type' => 2,
                                'object_id' => $value->id
                            ]);

                    }
                }
                else {
                    if($check == false)
                    {

                            Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$slug).' '.$address[1]),
                                'object_type' => 2,
                                'object_id' => $value->id
                            ]);
                    }
                    else {

                            Tag::create(['title' => $value->name,
                                'slug' => Str::slug($value->slug.' '.$address[2]),
                                'object_type' => 2,
                                'object_id' => $value->id
                            ]);

                    }
                }

                //slug địa chỉ đầy đủ
                if(in_array(Str::slug($address[1]),$string2))
                {
                    $address[1] = Str::slug('thanh-pho '.$address[1]);
                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 2,
                                'object_id' => $value->id
                    ]);
                }
                elseif(Str::slug($address[1]) == $string3)
                {
                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 2,
                                'object_id' => $value->id
                    ]);
                }
                else {
                    $address[1] = Str::slug('tinh '.$address[1]);

                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 2,
                                'object_id' => $value->id
                    ]);
                }

            }
        }
        return true;
    }

    //------------------update ward tag------------
    public function wardTag()
    {
        // $province = Province::get();
        // $district = District::get();
        $ward = Ward::get();
        $string = ['quan',  'huyen'];
        $string1 = ['thanh-pho','thi-xa'];
        $string2 = ['da-nang','ha-noi','can-tho','hai-phong'];
        $string3 = 'thanh-pho-ho-chi-minh';
        $string4 = ['phuong', 'xa'];
        $string5 = 'thi-tran';


        foreach ($ward as $key => $value) {
            $slug = explode('-',$value->slug);

            if(in_array($slug[0], $string4))
            {
                Tag::create(['title' => $value->name,
                                'slug' => $value->slug,
                                'object_type' => 3,
                                'object_id' => $value->id
                ]);
                unset($slug[0]);

                $address = explode(',',$value->address);
                $check = is_numeric(implode('-',$slug));

                if($check == false)
                {
                    Tag::create(['title' => $value->name,
                                'slug' => implode('-',$slug),
                                'object_type' => 3,
                                'object_id' => $value->id
                    ]);
                }

                //địa chỉ thiếu địa chính
                $slugDis = explode('-',Str::slug($address[1]));
                if(in_array($slugDis[0], $string))
                {
                    $tam = $slugDis;
                    unset($slugDis[0]);
                    $checkSlugDis = is_numeric(implode('-',$slugDis));

                    if(Str::slug($address[2]) == $string3)
                    {
                        if($check == false)
                        {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$slugDis).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$tam).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }

                        }
                        else {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$slugDis).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$tam).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                    }
                    else {
                        if($check == false)
                        {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$slugDis).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$tam).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }

                        }
                        else {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$slugDis).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$tam).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                    }




                }
                elseif(in_array($slugDis[0].'-'.$slugDis[1], $string1)) {

                    $tam = $slugDis;
                    unset($slugDis[0],$slugDis[1]);
                    $checkSlugDis = is_numeric(implode('-',$slugDis));

                    if(Str::slug($address[2]) == $string3)
                    {
                        if($check == false)
                        {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$slugDis).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$tam).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                        else {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$slugDis).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$tam).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                    }
                    else {
                        if($check == false)
                        {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$slugDis).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$tam).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                        else {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$slugDis).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$tam).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                    }

                }

                //slug địa chỉ đầy đủ
                if(in_array(Str::slug($address[2]),$string2))
                {
                    $address[2] = Str::slug('thanh-pho '.$address[2]);
                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 3,
                                'object_id' => $value->id
                    ]);
                }
                elseif(Str::slug($address[2]) == $string3)
                {
                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 3,
                                'object_id' => $value->id
                    ]);
                }
                else {
                    $address[2] = Str::slug('tinh '.$address[2]);

                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 3,
                                'object_id' => $value->id
                    ]);
                }

            }

            elseif(($slug[0].'-'.$slug[1]) == $string5)
            {
                Tag::create(['title' => $value->name,
                                'slug' => $value->slug,
                                'object_type' => 3,
                                'object_id' => $value->id
                ]);
                unset($slug[0],$slug[1]);

                $address = explode(',',$value->address);
                $check = is_numeric(implode('-',$slug));

                if($check == false)
                {
                    Tag::create(['title' => $value->name,
                                'slug' => implode('-',$slug),
                                'object_type' => 3,
                                'object_id' => $value->id
                    ]);
                }

                //địa chỉ thiếu địa chính
                $slugDis = explode('-',Str::slug($address[1]));
                if(in_array($slugDis[0], $string))
                {
                    $tam = $slugDis;
                    unset($slugDis[0]);
                    $checkSlugDis = is_numeric(implode('-',$slugDis));

                    if(Str::slug($address[2]) == $string3)
                    {
                        if($check == false)
                        {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$slugDis).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$tam).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }

                        }
                        else {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$slugDis).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$tam).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                    }
                    else {
                        if($check == false)
                        {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$slugDis).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$tam).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }

                        }
                        else {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$slugDis).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$tam).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                    }




                }
                elseif(in_array($slugDis[0].'-'.$slugDis[1], $string1)) {

                    $tam = $slugDis;
                    unset($slugDis[0],$slugDis[1]);
                    $checkSlugDis = is_numeric(implode('-',$slugDis));

                    if(Str::slug($address[2]) == $string3)
                    {
                        if($check == false)
                        {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$slugDis).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$tam).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                        else {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$slugDis).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$tam).'-ho chi minh'),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                    }
                    else {
                        if($check == false)
                        {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$slugDis).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug(implode('-',$slug).'-'.implode('-',$tam).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                        else {
                            if($checkSlugDis == false)
                            {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$slugDis).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                            else {
                                Tag::create(['title' => $value->name,
                                    'slug' => Str::slug($value->slug.'-'.implode('-',$tam).' '.$address[2]),
                                    'object_type' => 3,
                                    'object_id' => $value->id
                                ]);
                            }
                        }
                    }

                }

                //slug địa chỉ đầy đủ
                if(in_array(Str::slug($address[2]),$string2))
                {
                    $address[2] = Str::slug('thanh-pho '.$address[2]);
                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 3,
                                'object_id' => $value->id
                    ]);
                }
                elseif(Str::slug($address[2]) == $string3)
                {
                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 3,
                                'object_id' => $value->id
                    ]);
                }
                else {
                    $address[2] = Str::slug('tinh '.$address[2]);

                    Tag::create(['title' => $value->name,
                                'slug' => Str::slug(implode('-',$address)),
                                'object_type' => 3,
                                'object_id' => $value->id
                    ]);
                }
            }

        }

        return true;
    }

    //-----------------update place tag-------------
    // public function placeTag()
    // {

    //     $place = Place::whereNotNull('address')->where('object_type',config('constants.place_object_type.address'))->get(['id','name']);
    //     // Log::info('start');
    //     foreach ($place as $key => $value) {
    //         Tag::create(['title' => $value->name,
    //                             'slug' => Str::slug(str_replace("/","-",$value->name)),
    //                             'object_type' => 4,
    //                             'object_id' => $value->id
    //                 ]);

    //         $placeArr = explode(',', $value->name);

    //         if(count($placeArr) >= 2 )
    //         {
    //             $tag1 = Tag::create(['title' => $value->name,
    //                             'slug' => Str::slug(str_replace("/","-",$placeArr[0])),
    //                             'object_type' => 4,
    //                             'object_id' => $value->id
    //                 ]);

    //                 $tag1->searchable();

    //             $arrSlug = explode('-', Str::slug(str_replace("/","-",$placeArr[0])));

    //             if($arrSlug[0] == 'duong')
    //             {
    //                 unset($arrSlug[0]);

    //                 $tag2 = Tag::create(['title' => $value->name,
    //                                 'slug' => Str::slug(implode('-',$arrSlug)),
    //                                 'object_type' => 4,
    //                                 'object_id' => $value->id
    //                     ]);
    //                 $tag2->searchable();
    //             }
    //         }

    //     }
    // }


    public function provinceConvertVN2000ToWGS84($Parr,$dataProvince)
    {
        $arrlatLong = [];
        foreach ($Parr as $key => $Po) {
            $zDefault = Setting::where('key','vn2000_z_default')->first();
            $zDefault->value = json_decode($zDefault->value);

            $Po['X'] = str_replace(",",".",$Po['X']);
            $Po['Y'] = str_replace(",",".",$Po['Y']);
            $Po['Z'] = isset($Po['Z']) ? str_replace(",",".",$Po['Z']) : intval($zDefault->value[0]);
            $data = [
                'lat' => $this->NBT_to_WGS84_Lat($Po['X'],$Po['Y'],$Po['Z'],$dataProvince),
                'long' => $this->NBT_to_WGS84_Long($Po['X'],$Po['Y'],$Po['Z'],$dataProvince)
            ];
            $arrlatLong[] = $data;
        }
        return $arrlatLong;

    }

    public function updateProvince(array $province){
        
        $check = Province::find($province['id']);
        if(!$check){
            return false;
        }
        $check->update($province);
        return true;
    }


    #region lat long processing area
    function Helmert_X($X, $Y, $Z, $DX, $Y_rot, $Z_rot, $S){

        $Pi = 3.14159265358979;
        $sfactor = $S * 0.000001;
        $RadY_Rot = ($Y_rot / 3600) * ($Pi / 180);
        $RadZ_Rot = ($Z_rot / 3600) * ($Pi / 180);

        return  $X + ($X * $sfactor) - ($Y * $RadZ_Rot) + ($Z * $RadY_Rot) + $DX;
    }
    function Helmert_Y($X, $Y, $Z, $DY, $X_rot, $Z_rot, $S){

        $Pi = 3.14159265358979;
        $sfactor = $S * 0.000001;
        $RadX_Rot = ($X_rot / 3600) * ($Pi / 180);
        $RadZ_Rot = ($Z_rot / 3600) * ($Pi / 180);

        return ($X * $RadZ_Rot) + $Y + ($Y * $sfactor) - ($Z * $RadX_Rot) + $DY;
    }
    function Helmert_Z($X, $Y, $Z, $DZ, $X_rot, $Y_rot, $S){

        $Pi = 3.14159265358979;
        $sfactor = $S * 0.000001;
        $RadX_Rot = ($X_rot / 3600) * ($Pi / 180);
        $RadY_Rot = ($Y_rot / 3600) * ($Pi / 180);

        return (-1 * $X * $RadY_Rot) + ($Y * $RadX_Rot) + $Z + ($Z * $sfactor) + $DZ;
    }
    function XYZ_to_Lat($X, $Y, $Z, $a, $b){

        $RootXYSqr = sqrt(pow($X, 2) + pow($Y, 2));
        $e2 = (pow($a, 2) - pow($b, 2)) / pow($a, 2);
        $PHI1 = atan($Z / ($RootXYSqr * (1 - $e2)));

        $PHI = $this->Iterate_XYZ_to_Lat($a, $e2, $PHI1, $Z, $RootXYSqr);

        $Pi = 3.14159265358979;

        return $PHI * (180 / $Pi);
    }
    function Iterate_XYZ_to_Lat($a, $e2, $PHI1, $Z, $RootXYSqr){

        $V = $a / (sqrt(1 - ($e2 * pow((Sin($PHI1)), 2))));
        $PHI2 = atan(($Z + ($e2 * $V * (Sin($PHI1)))) / $RootXYSqr);

        While(Abs($PHI1 - $PHI2) > 0.000000001){
            $PHI1 = $PHI2;
            $V = $a / (sqrt(1 - ($e2 * pow((Sin($PHI1)), 2))));
            $PHI2 = atan(($Z + ($e2 * $V * (Sin($PHI1)))) / $RootXYSqr);
        }
        return $PHI2;
    }
    function XYZ_to_Long($X, $Y){

        $Pi = 3.14159265358979;

        if( $X >= 0 ){
            return (atan($Y / $X)) * (180 / $Pi);
        }

        if( $X < 0 And $Y >= 0 ){
            return ((atan($Y / $X)) * (180 / $Pi)) + 180;
        }

        if( $X < 0 And $Y < 0 ){
            return ((atan($Y / $X)) * (180 / $Pi)) - 180;
        }

    }
    function XYZ_to_H($X, $Y, $Z, $a, $b){

        $PHI = $this->XYZ_to_Lat($X, $Y, $Z, $a, $b);

        $Pi = 3.14159265358979;
        $RadPHI = $PHI * ($Pi / 180);

        $RootXYSqr = sqrt(pow($X, 2) + pow($Y, 2));
        $e2 = (pow($a, 2) - pow($b, 2)) / pow($a, 2);
        $V = $a / (sqrt(1 - ($e2 * pow((Sin($RadPHI)), 2))));
        $H = ($RootXYSqr / Cos($RadPHI)) - $V;

        return $H;

    }
    function Lat_Long_H_to_X($PHI, $LAM, $H, $a, $b){

        $Pi = 3.14159265358979;
        $RadPHI = $PHI * ($Pi / 180);
        $RadLAM = $LAM * ($Pi / 180);

        $e2 = (pow($a, 2) - pow($b, 2)) / pow($a, 2);
        $V = $a / (sqrt(1 - ($e2 * pow((Sin($RadPHI)), 2))));

        return ($V + $H) * (Cos($RadPHI)) * (Cos($RadLAM));
    }
    function Lat_Long_H_to_Y($PHI, $LAM, $H, $a, $b){

        $Pi = 3.14159265358979;
        $RadPHI = $PHI * ($Pi / 180);
        $RadLAM = $LAM * ($Pi / 180);

        $e2 = (pow($a, 2) - pow($b, 2)) / pow($a, 2);
        $V = $a / (sqrt(1 - ($e2 * pow((Sin($RadPHI)), 2))));

        return ($V + $H) * (Cos($RadPHI)) * (Sin($RadLAM));
    }
    function Lat_H_to_Z($PHI, $H, $a, $b){

        $Pi = 3.14159265358979;
        $RadPHI = $PHI * ($Pi / 180);

        $e2 = (pow($a, 2) - pow($b, 2)) / pow($a, 2);
        $V = $a / (sqrt(1 - ($e2 * pow((Sin($RadPHI)), 2))));

        return (($V * (1 - $e2)) + $H) * (Sin($RadPHI));
    }
    function E_N_to_Lat($East, $North, $a, $b, $E0, $N0, $f0, $PHI0, $LAM0){

        $Pi = 3.14159265358979;
        $RadPHI0 = $PHI0 * ($Pi / 180);
        $RadLAM0 = $LAM0 * ($Pi / 180);

        $af0 = $a * $f0;
        $bf0 = number_format($b * $f0, 8, '.', '');

        $e2 = (pow($af0, 2) - pow($bf0, 2)) / pow($af0, 2);
        $N = ($af0 - $bf0) / ($af0 + $bf0);
        $Et = $East - $E0;


        $PHId = $this->InitialLat($North, $N0, $af0, $RadPHI0, $N, $bf0);

        $nu = $af0 / (sqrt(1 - ($e2 * (pow(Sin($PHId),2)))));
        $rho = ($nu * (1 - $e2)) / (1 - ($e2 * pow(Sin($PHId),2 )));
        $eta2 = ($nu / $rho) - 1;


        $VII = (Tan($PHId)) / (2 * $rho * $nu);
        $VIII = ((Tan($PHId)) / (24 * $rho * pow($nu, 3))) * (5 + (3 * (pow(Tan($PHId), 2))) + $eta2 - (9 * $eta2 * (pow(Tan($PHId), 2))));
        $IX = ((Tan($PHId)) / (720 * $rho * pow($nu, 5))) * (61 + (90 * (pow(Tan($PHId), 2))) + (45 * (pow(Tan($PHId), 4))));

        return (180 / $Pi) * ($PHId - (pow($Et, 2) * $VII) + (pow($Et, 4) * $VIII) - (pow($Et, 6) * $IX));
    }
    function E_N_to_Long($East, $North, $a, $b, $E0, $N0, $f0, $PHI0, $LAM0){


        $Pi = 3.14159265358979;
        $RadPHI0 = $PHI0 * ($Pi / 180);
        $RadLAM0 = $LAM0 * ($Pi / 180);

        $af0 = $a * $f0;
        $bf0 = $b * $f0;
        $e2 = (pow($af0, 2) - pow($bf0, 2)) / pow($af0, 2);
        $N = ($af0 - $bf0) / ($af0 + $bf0);
        $Et = $East - $E0;

        $PHId = $this->InitialLat($North, $N0, $af0, $RadPHI0, $N, $bf0);
        $nu = $af0 / (sqrt(1 - ($e2 * (pow(Sin($PHId), 2)))));

        $rho = ($nu * (1 - $e2)) / (1 - ($e2 * pow(Sin($PHId), 2)));
        $eta2 = ($nu / $rho) - 1;


        $X = (pow(Cos($PHId), -1)) / $nu;
        $XI = ((pow(Cos($PHId), -1)) / (6 * (pow($nu, 3)))) * (($nu / $rho) + (2 * (pow(Tan($PHId), 2))));
        $XII = ((pow(Cos($PHId), -1)) / (120 * (pow($nu, 5)))) * (5 + (28 * (pow(Tan($PHId), 2))) + (24 * (pow(Tan($PHId), 4))));
        $XIIA = ((pow(Cos($PHId), -1)) / (5040 * (pow($nu, 7)))) * (61 + (662 * (pow(Tan($PHId), 2))) + (1320 * (pow(Tan($PHId), 4))) + (720 * (pow(Tan($PHId), 6))));
        return (180 / $Pi) * ($RadLAM0 + ($Et * $X) - ((pow($Et, 3)) * $XI) + ((pow($Et, 5)) * $XII) - ((pow($Et, 7)) * $XIIA));
    }
    function InitialLat($North, $N0, $afo, $PHI0, $N, $bfo){


        $PHI1 = number_format((($North - $N0) / $afo), 20, '.','') + $PHI0;



        $m = $this->Marc($bfo, $N, $PHI0, $PHI1);

        $PHI2 = (($North - $N0 - $m) / $afo) + $PHI1;


        while(Abs($North - $N0 - $m) > 0.00001){
            $PHI2 = (($North - $N0 - $m) / $afo) + $PHI1;
            $m = $this->Marc($bfo, $N, $PHI0, $PHI2);

            $PHI1 = $PHI2;
        }
        return $PHI2;

    }
    function Marc($bf0, $N, $PHI0, $PHI){


        return number_format($bf0 * (((1 + $N + ((5 / 4) * pow($N, 2)) + ((5 / 4) * pow($N, 3))) * ($PHI - $PHI0))
        - (((3 *$N) + (3 * pow($N, 2)) + ((21 / 8) * pow($N, 3))) * (Sin($PHI - $PHI0)) * (Cos($PHI + $PHI0)))
        + ((((15 / 8) * pow($N, 2)) + ((15 / 8) * pow($N, 3))) * (Sin(2 * ($PHI - $PHI0))) * (Cos(2 * ($PHI + $PHI0))))
        - (((35 / 24) * pow($N, 3)) * (Sin(3 * ($PHI - $PHI0))) * (Cos(3 * ($PHI + $PHI0))))), 20, '.', '');
    }

    function NBT_to_WGS84_Lat($North, $East, $Height, $dataProvince){
        $a = number_format($dataProvince['a'], 1, '.', '');
        $b = number_format($dataProvince['b'], 8, '.', '');
        $F = number_format($dataProvince['f'], 18, '.', '');
        if ($b == 0 ) $b = $a * (1 - $F);
        $KTtruc = number_format($dataProvince['LAM0'], 6, '.', '');
        $vttruc = number_format($dataProvince['PHI0'], 6, '.', '');
        $E0 = number_format($dataProvince['E0'], 0, '.', '');
        $N0 = number_format($dataProvince['N0'], 0, '.', '');
        $muichieu = number_format($dataProvince['F0'], 4, '.', '');
        $DX = number_format($dataProvince['DX'], 9, '.', '');
        $DY = number_format($dataProvince['DY'], 9, '.', '');
        $DZ = number_format($dataProvince['DZ'], 9, '.', '');
        $X_rot = number_format($dataProvince['X_Rotation'], 9,'.', '');
        $Y_rot = number_format($dataProvince['Y_Rotation'], 9, '.', '');
        $Z_rot = number_format($dataProvince['Z_Rotation'], 9, '.', '');
        $S = number_format($dataProvince['Scale'], 9, '.', '');
        # Lay cac thong so co ban cua VN2000
        $slieu;
        $B1 = $this->E_N_to_Lat($East, $North, $a, $b, $E0, $N0, $muichieu, $vttruc, $KTtruc);
        $L1 = $this->E_N_to_Long($East, $North, $a, $b, $E0, $N0, $muichieu, $vttruc, $KTtruc);
        $H1 = $Height;
        # Lat long $H to $XYZ VN2000
        $X1 = $this->Lat_Long_H_to_X($B1,$L1, $Height, $a, $b);
        $Y1 = $this->Lat_Long_H_to_Y($B1,$L1, $Height, $a, $b);
        $Z1 = $this->Lat_H_to_Z($B1,$L1, $a, $b);
        #X1Y1Z1 to $XYZ WGS84
        $X = $this->Helmert_X($X1, $Y1, $Z1, $DX, $Y_rot, $Z_rot, $S);
        $Y = $this->Helmert_Y($X1, $Y1, $Z1, $DY, $X_rot, $Z_rot, $S);
        $Z = $this->Helmert_Z($X1, $Y1, $Z1, $DX, $X_rot, $Y_rot, $S);
        #XYZ to Lat Long $H WGS84
        $WGS84Lat = $this->XYZ_to_Lat($X, $Y, $Z, $a, $b);
        $WGS84Long = $this->XYZ_to_Long($X, $Y);
        $WGS84H = $this->XYZ_to_H($X, $Y, $Z, $a, $b);
        return $WGS84Lat - 2.90417781181418E-03 - 0.00006;
    }
    function NBT_to_WGS84_Long($North, $East, $Height, $dataProvince){

        $a = number_format($dataProvince['a'], 1, '.', '');
        $b = number_format($dataProvince['b'], 8, '.', '');
        $F = number_format($dataProvince['f'], 18, '.', '');
        if ($b == 0 ) $b = $a * (1 - $F);
        $KTtruc = number_format($dataProvince['LAM0'], 6, '.', '');
        $vttruc = number_format($dataProvince['PHI0'], 6, '.', '');
        $E0 = number_format($dataProvince['E0'], 0, '.', '');
        $N0 = number_format($dataProvince['N0'], 0, '.', '');
        $muichieu = number_format($dataProvince['F0'], 4, '.', '');
        $DX = number_format($dataProvince['DX'], 9, '.', '');
        $DY = number_format($dataProvince['DY'], 9, '.', '');
        $DZ = number_format($dataProvince['DZ'], 9, '.', '');
        $X_rot = number_format($dataProvince['X_Rotation'], 9,'.', '');
        $Y_rot = number_format($dataProvince['Y_Rotation'], 9, '.', '');
        $Z_rot = number_format($dataProvince['Z_Rotation'], 9, '.', '');
        $S = number_format($dataProvince['Scale'], 9, '.', '');
        # Lay cac thong so co ban cua VN2000
        $slieu;#; As Worksheet
        #Set $slieu = ActiveWorkbook.Worksheets("So lieu");
        # E $N to Lat Long VN2000
        $B1 = $this->E_N_to_Lat($East, $North, $a, $b, $E0, $N0, $muichieu, $vttruc, $KTtruc);
        $L1 = $this->E_N_to_Long($East, $North, $a, $b, $E0, $N0, $muichieu, $vttruc, $KTtruc);
        $H1 = $Height;
        # Lat long $H to $XYZ VN2000
        $X1 = $this->Lat_Long_H_to_X($B1,$L1, $Height, $a, $b);
        $Y1 = $this->Lat_Long_H_to_Y($B1,$L1, $Height, $a, $b);
        $Z1 = $this->Lat_H_to_Z($B1,$L1, $a, $b);
        #X1Y1Z1 to $XYZ WGS84
        $X = $this->Helmert_X($X1, $Y1, $Z1, $DX, $Y_rot, $Z_rot, $S);
        $Y = $this->Helmert_Y($X1, $Y1, $Z1, $DY, $X_rot, $Z_rot, $S);
        $Z = $this->Helmert_Z($X1, $Y1, $Z1, $DX, $X_rot, $Y_rot, $S);
        #XYZ to Lat Long $H WGS84
        $WGS84Lat = $this->XYZ_to_Lat($X, $Y, $Z, $a, $b);
        $WGS84Long = $this->XYZ_to_Long($X, $Y);
        $WGS84H = $this->XYZ_to_H($X, $Y, $Z, $a, $b);
        return $WGS84Long + 3.76088254250817E-03 - 0.00013;
    }
    function NBT_to_WGS84_H($North, $East, $Height){
        global $a, $b, $F, $KTtruc, $VTruc, $muichieu, $E0, $N0, $DX, $DY, $DZ,
        $X_rot, $Y_rot, $Z_rot,
        $S,
        $X, $Y, $Z,
        $X1, $Y1, $Z1,
        $B1,$L1, $H1,
        $WGS84Lat, $WGS84Long, $WGS84H, $vttruc, $KTtruc;
        # E $N to Lat Long VN2000
        $B1 = $this->E_N_to_Lat($East, $North, $a, $b, $E0, $N0, $muichieu, $vttruc, $KTtruc);
        $L1 = $this->E_N_to_Long($East, $North, $a, $b, $E0, $N0, $muichieu, $vttruc, $KTtruc);
        $H1 = $Height;
        # Lat long $H to $XYZ VN2000
        $X1 = $this->Lat_Long_H_to_X($B1,$L1, $Height, $a, $b);
        $Y1 = $this->Lat_Long_H_to_Y($B1,$L1, $Height, $a, $b);
        $Z1 = $this->Lat_H_to_Z($B1,$L1, $a, $b);
        #X1Y1Z1 to $XYZ WGS84
        $X = $this->Helmert_X($X1, $Y1, $Z1, $DX, $Y_rot, $Z_rot, $S);
        $Y = $this->Helmert_Y($X1, $Y1, $Z1, $DY, $X_rot, $Z_rot, $S);
        $Z = $this->Helmert_Z($X1, $Y1, $Z1, $DX, $X_rot, $Y_rot, $S);
        #XYZ to Lat Long $H WGS84
        $WGS84Lat = $this->XYZ_to_Lat($X, $Y, $Z, $a, $b);
        $WGS84Long = $this->XYZ_to_Long($X, $Y);
        $WGS84H = $this->XYZ_to_H($X, $Y, $Z, $a, $b);
        return  $WGS84H - 91.4520812714472 - 4.2632564145606E-14 + 47;
    }
    #endregion
}
