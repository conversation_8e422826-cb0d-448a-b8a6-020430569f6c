<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Requests\Rating\CreateRatingRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Services\Rating\RatingService;

class RatingController extends Controller
{
    // -------create rating---------------
    public function create(CreateRatingRequest $request)
    {
        $data = $request->only([
            'object_id',
            'object_type',
            'rating',
            'review',
        ]);

        $service = new RatingService(Auth::user()->id);

        if($service->hasRatingByUser($data['object_id'], $data['object_type'])) {
            return response()->json([
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body'   => [
                   'message' => 'You have already rated this object'
               ]
            ]);
        }

        $result = $service->create($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------update rating---------------
    public function update(Request $request)
    {
        $data = $request->only([
            'id',
            'rating',
            'review',
            'images',
            'image_delete'
        ]);

        $service = new RatingService(Auth::user()->id);
        $result = $service->update($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------delete product--------------
    public function delete(Request $request, RatingService $service)
    {
       $data = $request->only(['id']);

       $result = $service->delete($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }


    // -------list by object id rating---------------
    public function listByObjectId(Request $request, RatingService $service)
    {
        $search = $request->only([
            'object_id',
            'limit',
            'offset'
        ]);
        $limit = isset($search['limit'])? $search['limit'] : 25;
        $offset = isset($search['offset'])? $search['offset'] : 0;
        $object_id = isset($search['object_id'])? $search['object_id'] : '';

        $result = $service->listByObjectId($object_id, $limit, $offset);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------list by object id rating---------------
    public function list(RatingService $service)
    {

        $result = $service->list();

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function detail($id="aaa", RatingService $service)
    {
        $result = $service->detail($id);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------process calculating average rating of object--------
    public function calcAverageRating($object_id="aaa", RatingService $service)
    {
        $result = $service->calcAverageRating($object_id);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function detailRatingObjectByUser($object_id)
    {

        if(!$object_id){
            return response()->json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'  => [
                    'message' => 'Object id is required'
                ]
            ]);
        }
        $userId = Auth::user()->id;
        $service = new RatingService($userId);

        $result = $service->detailByUserAndObject($object_id);

        if(isset($result['error'])){
            return response()-> json([
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body'   => [
                    'message' => $result['error']
                ]
            ]);
        }
        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => $result
        ]);
    }
}
