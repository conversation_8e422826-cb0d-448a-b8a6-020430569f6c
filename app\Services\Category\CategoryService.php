<?php
namespace App\Services\Category;

use App\ProductCategory;
use App\Category;
use App\Image;
use App\PlaceType;
use Illuminate\Support\Str;
use App\Services\Image\ImageService;
use App\Services\GeneralService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Translation;
use App\Helpers\Helper;

class CategoryService
{
    private $_userId;
    public function __construct($userId = null)
    {
        if($userId){
            $this->_userId = $userId;
        }else{
            $this->_userId = Auth::check() ? Auth::user()->id : null;
        }
    }

     //-------process create category--------
    public function create(array $category)
    {
        $user = User::find($this->_userId);
        if($user)
        {
            $category['created_by'] = $user->id;
        }

        $result = Category::create($category);

        if($result && isset($category['translation'])){
            $this->excuteTranslation($category['translation'], config('constants.object_type.category'), $result->id);
        }

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.create'),
        //                 $result->id,
        //                 config('constants.object_type.category'),
        //                 $result,
        //                 json_encode($category));

        return $result;
    }

    //-------process listing Category--------------------
    public function list($offset, $limit, $all)
    {
        // $general = new GeneralService($this->_userId);
        if($all == true){
            $result = Category::orderBy('created_at','asc')
                ->with('parent_category','sub_categories','created_by', 'translation')
                ->offset($offset)->limit($limit)
                ->get();
        }
        else {
            $result = Category::with('sub_categories','created_by', 'translation')
                ->where('enable', true)
                ->where('shop_id', null)
                ->where('parent_id', null)
                ->orderBy('index','asc')
                ->offset($offset)->limit($limit)
                // ->select('name','slug','profile_picture','parent_id','shop_id','index')
                ->get();
        }

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    public function listLevel($offset, $limit, $all)
    {
        // $general = new GeneralService($this->_userId);
        if($all == true){
            $result = Category::where('parent_id', NULL)->orderBy('created_at','asc')
                ->with('parent_category','sub_categories','created_by')
                ->offset($offset)->limit($limit)
                ->get();
        }
        else {
            $result = Category::with('parent_category','sub_categories','created_by')
                ->where('enable', true)
                ->where('parent_id', NULL)
                ->orderBy('index','asc')
                ->offset($offset)->limit($limit)
                ->select('name','slug','profile_picture','parent_id','shop_id','index','created_by')
                ->get();
        }

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //-------process listing Category--------------------
    public function listByShopId($shopId)
    {
        $result = Category::with('products', 'translation')
                ->where('enable', true)
                ->where('shop_id', $shopId)
                ->orderBy('index','asc')
                ->select('id', 'name', 'slug', 'profile_picture', 'parent_id', 'shop_id', 'index')
                ->get();

        // $result['images'] = $general->listImage($result['images'], config('constants.image_type.medium'));

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //------process detail Category----------
    public function detail($id)
    {
        $result = Category::where('id',$id)
            ->with('parent_category','sub_categories','created_by', 'products', 'translation')
            ->where('enable', true)
            ->first();

        if(!$result)
        {
            return false;
        }

        return $result;
    }

    //-------process update category--------
    public function update(array $category)
    {
        $result = Category::find($category['id']);

        if(!$result)
        {
            return false;
        }

        $result->update($category);
        Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $result->shop_id);

        if($result && isset($category['translation'])){
            $this->excuteTranslation($category['translation'], config('constants.object_type.category'), $result->id);
        }

        $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.category'),
        //                 $result,
        //                 json_encode($category));

        return $result;

    }

    //-----process remove category----------
    public function remove(array $category)
    {
        $result = Category::where([
            ['id', $category['id']],
            ['enable', true]
        ])->first();

        if(!$result)
        {
            return false;
        }

        $category['enable'] = false;
        $this->update($category);

        // $result = $this->detail($result->id);

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.update'),
        //                 $result->id,
        //                 config('constants.object_type.product'),
        //                 $result,
        //                 json_encode($product));

        return true;
    }

    //-----process delete category----------
    public function delete(array $category)
    {
        $result = Category::find($category['id']);

        if(!$result)
        {
            return false;
        }

        //----------- set 'parent_id = NULL' to sub-categories--------------

        $sub_parent = Category::where('parent_id', $category['id'])->get();

        foreach ($sub_parent as $key => $value) {
            $data = [
                'id' => $value->id,
                'parent_id' => null,
            ];
            $this->update($data);
        }

        //----------- delete action ---------------
        $result->delete();

        // $general = new GeneralService($this->_userId);
        // $general->addLog(config('constants.log_action.delete'),
        //                 $result->id,
        //                 config('constants.object_type.category'),
        //                 $result,
        //                 json_encode($category));

        return true;
    }

    //-----process update category's products----------
    public function updateListProduct(array $data)
    {
        $result = Category::find($data['category_id']);

        if(!$result)
        {
            return false;
        }
        Helper::flushCacheByTag(env('APP_ENV').":".'shop:' . $result->shop_id);
        $listCategoryIdCurrent = ProductCategory::where('category_id', $data['category_id'])->delete();
        foreach($data['product_ids'] as $key=>$value){
            $product_category = [
                'category_id' => $data['category_id'],
                'product_id' => $value,
            ];

            ProductCategory::create($product_category);
        };

        return true;
    }
    //-----process update category's Index----------
    public function updateIndex(array $data, $shopId)
    {
        foreach($data as $key => $value){
            $category = Category::where('id', $value['id'])->where('shop_id', $shopId)->first();
            if($category)
            {
                $category['index'] = $value['index'];
                $category->save();
            }else{
                return false;
            }

        };

        return true;
    }

    public function excuteTranslation(array $transArr, $objectType, $objectId){
        foreach ($transArr as $key => $langValue) {
            Translation::updateOrCreate(
                [
                    'object_type' => $objectType,
                    'object_id' => $objectId,
                    'language_code' => $langValue['language_code'] ?? "vi"
                ],
                [
                    'name' => $langValue['name'] ?? "",
                    'description' => $langValue['description'] ?? ""
                ]
            );
        }
    }
}
