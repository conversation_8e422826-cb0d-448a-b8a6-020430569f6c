<?php

// Test referral code case sensitivity using artisan tinker
echo "Testing referral code case sensitivity...\n\n";

// Create test user
$user = App\User::create([
    'name' => 'Test Referrer Case',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'phone' => '9876543210',
    'role_id' => 6
]);

echo "Created user: {$user->name}\n";

// Generate referral code
$code = $user->generateReferralCode();
echo "Generated code: {$code}\n";

// Test different cases
$testCodes = [$code, strtolower($code), 'ref123abc', '  ' . $code . '  '];

foreach ($testCodes as $testCode) {
    $found = App\User::findByReferralCode($testCode);
    $result = $found ? "FOUND" : "NOT FOUND";
    echo "Testing '{$testCode}' -> {$result}\n";
}

// Get stats
$stats = $user->getReferralStats();
echo "\nStats:\n";
echo "Code: {$stats['referral_code']}\n";
echo "Referrals: {$stats['total_referrals']}\n";

// Cleanup
$user->delete();
echo "\nTest user deleted.\n";
