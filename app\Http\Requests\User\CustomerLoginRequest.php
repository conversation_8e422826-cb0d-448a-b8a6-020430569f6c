<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class CustomerLoginRequest extends BaseRequest
{

    public function rules()
    {
        return [
            'email'             => 'required_if:provider_name,GOOGLE|max:255',
            'name'              => 'nullable|max:255',
            'provider_id'       => 'required',
            'provider_name'     => 'required|in:GOOGLE,FACEBOOK,ZALO,APPLE',
            // 'file'              => 'nullable|base64image|base64size'
        ];
    }
    // public function messages()
    // {
    //     return [
    //         'email.required'                => "Login_001_E_001",
    //         'provider_id.required'          => "Login_001_E_002",
    //         'provider_name.required'        => "Login_001_E_003",

    //         'file.base64image'              => 'User_002_E_043',
    //         'file.base64size'               => 'User_004_E_044',
    //         'name.max'                      => 'User_004_E_045',
    //         'email.max'                     => 'User_004_E_046',
    //         ];
    // }
}
