<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Mpociot\HasCompositeKey\HasCompositeKey;

class ChannelConnect extends Model
{
    use HasCompositeKey;

    public $incrementing = false;
    protected $table = 'channel_connects';

    protected $primaryKey = ['channel_id', 'member_id'];
    protected $fillable = ['channel_id', 'member_id', 'member_type'];


    // <PERSON>hai báo mối quan hệ với Channel
    public function channel()
    {
        return $this->belongsTo(Channel::class, 'channel_id')
            ->select('id', 'name', 'type');
    }

    // <PERSON>hai báo mối quan hệ với Object (users, shops)
    public function member()
    {
        return $this->morphTo();
    }

    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

}
