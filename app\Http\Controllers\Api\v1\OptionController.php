<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Http\Request;

class OptionController extends Controller
{
    
    public function list($all = false)
    {
        $service = new OpstionService;
        $result = $service->list($all);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result,
            ]
        ];
        return response()->json($response, JsonResponse::HTTP_OK);
    }

    public function create(OptionRequest $request, OptionService $service)
    {
        $data = (array)$request->only([
            'name',
        ]);

        $result = $service->create($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function update(OptionUpdateRequest $request, OptionService $service)
    {
        $data = (array)$request->only([
            'id',
            'name',
        ]);

        $result = $service->update($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response,JsonResponse::HTTP_OK);
    }
    
    public function remove(OptionDeleteRequest $request, OptionService $service)
    {
        $data = (array)$request->only([
            'id'
        ]);

        $result = $service->remove($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function delete(OptionDeleteRequest $request, OptionService $service)
    {
        $data = (array)$request->only([
            'id'
        ]);

        $result = $service->delete($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response,JsonResponse::HTTP_OK);
    }

    public function detail($id = 'abc')
    {
        $service = new OptionService;

        $result = $service->detail($id);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response,JsonResponse::HTTP_OK);
    }
}
