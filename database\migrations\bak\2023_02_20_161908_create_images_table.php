<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateImagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // bảng H<PERSON>nh <PERSON>nh
        Schema::create('images', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('parent_id');
            $table->tinyInteger('object_type');
            $table->text('path');
            $table->text('title');
            $table->boolean('enable')->default(1);
            $table->tinyInteger('orientation')->nullable();
            $table->uuid('created_by')->nullable();

            $table->index('parent_id');
            $table->index('object_type');
            $table->index('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('images');
    }
}
