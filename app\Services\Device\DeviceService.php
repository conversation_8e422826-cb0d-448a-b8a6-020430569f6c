<?php

namespace App\Services\Device;

use App\Token;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeviceService
{
    static function listByUser($tokenType = 1)
    {
        $userId = Auth::user()->id;
        $result = Token::select(['id', 'device'])
            ->where('token_type', $tokenType)
            ->where('user_id', $userId)
            ->get();
        return $result;
    }

}
