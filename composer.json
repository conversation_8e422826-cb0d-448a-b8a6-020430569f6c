{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.4", "babenkoivan/scout-elasticsearch-driver": "^4.3", "coraxster/flysystem-aws-s3-v3-minio": "^1.0", "crazybooot/base64-validation": "^1.0", "doctrine/dbal": "^2.0", "encore/laravel-admin": "^1.8", "fideloper/proxy": "^4.0", "google/apiclient": "2.10.1", "intervention/image": "^2.5", "laravel-admin-ext/api-tester": "^1.0", "laravel/framework": "5.8.*", "laravel/socialite": "^4.1.0", "laravel/tinker": "^1.0", "maatwebsite/excel": "^3.1", "matthewbdaly/laravel-etag-middleware": "^1.3", "mpociot/laravel-composite-key": "^1.0", "overtrue/chinese-calendar": "^1.0", "php-ffmpeg/php-ffmpeg": "^0.19.0", "predis/predis": "~1.0", "salmanzafar/laravel-mqtt": "v1.0.9", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-permission": "^3.13", "staudenmeir/eloquent-eager-limit": "^1.0", "tymon/jwt-auth": "1.0.*", "wapnen/google-cloud-vision-php": "dev-master"}, "require-dev": {"beyondcode/laravel-dump-server": "^1.0", "filp/whoops": "^2.0", "fzaninotto/faker": "^1.4", "mockery/mockery": "^1.0", "nunomaduro/collision": "^2.0", "phpunit/phpunit": "^7.5"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"], "files": ["app/Helpers/Helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}