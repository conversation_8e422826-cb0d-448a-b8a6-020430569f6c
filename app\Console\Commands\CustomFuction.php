<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Token\TokenService;
use App\Services\GeneralService;
use App\Services\Notification\NotificationService;
use App\Helpers\S3Utils;
use Illuminate\Support\Facades\Storage;
use Mail;
use App\Order;
use App\Shop;
use App\Services\Image\ImageService;
use App\Mail\SendForgetPasswordMail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Services\Geocode\GeocodeService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Carbon;
use App\Translation;
use App\Helpers\Helper;
use Illuminate\Support\Facades\Redis;
use App\Services\Translate\TranslateService;

class CustomFuction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:custom';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // $im = new ImageService();    
        // $im->delete('85faccae-52b9-48b3-a9a6-e56d2895dfab');die;
        // $this->createCategoryTranslations();die;
        $this->testCache();die;
        $this->testRedis();die;
        $this->flushCache(env('APP_ENV')."_".'dashboard');die;
        $this->extractAndSaveOpenHours();die;
        $this->publishMessage();die;
        $this->clearTestOrder();die;
        $this->search_by_address();die;

        echo Str::lower('Đùi gÀ TỏI nHậP kHẩU HÀN QUỐC ĂN KÈm cÓ BiỂu DiỄn VăN NGHỆ');die;
        $this->testQR();die;
        echo json_encode(config('logging.channels'));
        Log::channel('single')->info("aaaaaaaaaa");
        die;
        Mail::to('<EMAIL>')->send(new SendForgetPasswordMail("abc","abc"));die;
        //
        // $this->testNoti();
        // $s3 = Storage::disk('minio');
        // echo public_path('robots.txt');
        // $uploadS3 = $s3->put('uploads/robots.txt',file_get_contents(public_path('robots.txt')), 'public');

        $result = Storage::disk('minio')->delete('pro.remagan.uploads/uploads/soup.jpg');
        $result = Storage::disk('minio')->delete('/pro.remagan.uploads/uploads/soup.jpg');
        var_dump($result);
        $result = Storage::disk('minio')->delete('uploads/soup.jpg');
        $result = Storage::disk('minio')->delete('/uploads/soup.jpg');
        var_dump($result);
        // $result = Storage::disk('minio')->delete('/remagan.uploads/uploads/ocr.png');
        // var_dump($result);
        // $result = Storage::disk('minio')->delete("/uploads/ocr.png");
        // var_dump($result);
        // $result = Storage::disk("minio")->delete("uploads/ocr.png");
        // var_dump($result);
    }
    public function flushCache($key){
        Helper::flushCacheByTag($key);
        return true;
    }
    public function testCache()
    {
        $key = 'test_cache_key';
        $value = 'This is a test cache value';

        // Set a cache value
        Cache::put($key, $value, now()->addMinutes(10));

        // Retrieve the cache value
        $cachedValue = Cache::get($key);

        if ($cachedValue === $value) {
            $this->info('Cache test successful. Value retrieved matches the set value.');
        } else {
            $this->error('Cache test failed. Value retrieved does not match the set value.');
        }

        // Clear the cache value
        // Cache::forget($key);

        // Attempt to retrieve the cache value after clearing
        $cachedValueAfterClear = Cache::get($key);

        if ($cachedValueAfterClear === null) {
            $this->info('Cache clear test successful. Value is no longer present after clearing.');
        } else {
            $this->error('Cache clear test failed. Value is still present after clearing.');
        }
    }
    public function testRedis($key = "client_dev_product_banh_cha_bao_minh_goi_230g_3db6eadb"){
        // Redis::executeRaw('JSON.SET', 'client_dev_product_banh_cha_bao_minh_goi_230g_3db6eadb', '.', json_encode(["redis_data"=>"123321"]));
        Redis::setex($key, 600, 'redis_dataaa');
        var_dump(Redis::get($key));die;
    }
    //------process extract and save open hours---------- 
    public function extractAndSaveOpenHours()
    {
        $shops = Shop::where('open_hours',null)->where('description','like','%h%-%h%')
        // ->limit(5)->offset(55)
        ->get();
        foreach ($shops as $key => $shop) {
            echo "\n".$shop->description."\n";
            // Extract open hours from description
            // preg_match('/(\d{1,2})h\s*-\s*(\d{1,2})h/', $shop->description, $matches);// case normal time "Mở cửa: 6h - 10h"

            //case multi section time "Mở cửa: 6h - 10h, 14h - 19h30"
            // preg_match_all('/(\d{1,2})h(?:\s*(\d{2}))?\s*-\s*(\d{1,2})h(?:\s*(\d{2}))?/', $shop->description, $matches, PREG_SET_ORDER);
            // preg_match('/(\d{1,2})h\s*-\s*(\d{1,2})h(?:\s*(\d{2}))?(?:,\s*(\d{1,2})h\s*-\s*(\d{1,2})h(?:\s*(\d{2}))?)/', $shop->description, $matches);
            preg_match_all('/\s*(\d{1,2})h(\d{0,2})\s*-\s*(\d{1,2})h(\d{0,2})(?:,\s*(\d{1,2})h(\d{0,2})\s*-\s*(\d{1,2})h(\d{0,2}))*?/', $shop->description, $matches, PREG_SET_ORDER);

            $formattedHours = [];
            
            if (!empty($matches)) {
                $formattedTmp = [];
                
                
                foreach ($matches as $match) {
                    // var_dump($match);//continue;
                    // Process first time section
                    $formattedTmp[] = [
                        str_pad($match[1], 2, '0', STR_PAD_LEFT) . ":" . str_pad($match[2] ?: '00', 2, '0', STR_PAD_LEFT), // Open time
                        str_pad($match[3], 2, '0', STR_PAD_LEFT) . ":" . str_pad($match[4] ?: '00', 2, '0', STR_PAD_LEFT)  // Close time
                    ];

                    // // Process second time section if it exists
                    // if (isset($match[5])) {
                    //     $formattedTmp[] = [
                    //         str_pad($match[5], 2, '0', STR_PAD_LEFT) . ":" . str_pad($match[6] ?: '00', 2, '0', STR_PAD_LEFT), // Open time
                    //         str_pad($match[7], 2, '0', STR_PAD_LEFT) . ":" . str_pad($match[8] ?: '00', 2, '0', STR_PAD_LEFT)  // Close time
                    //     ];
                    // }
                    // Apply time from monday to sunday
                }
                foreach (['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day) {
                    $formattedHours[$day] = $formattedTmp;
                }
            }
            
            
            echo $shop->open_hours = json_encode($formattedHours);
            // Save formatted hours back to the shop
            $shop->save();
        }
        return true;
    }


    public function publishMessage() {
        Helper::publishMessage();
    }
    public function search_by_address() {
        $geocodeService = new GeocodeService();
        var_dump($geocodeService->search_by_address(['search'=>'tra tan binh thuan']));
    }

    public function clearTestOrder(){
        $orders = Order::where('customer_name','like','%HOHOHO%')->orWhere('customer_name','like','%TESTER01%')->where('status',1)->get()->pluck('id');
        echo count($orders);
        Order::destroy($orders);
    }

    /**
     * Create translations for all shop categories
     * Ensures each category has at least Vietnamese and English translations
     * Uses TranslateService to translate missing languages
     */
    public function createCategoryTranslations()
    {
        // Get all shops with their categories
        $shops = Shop::with('categories')
        ->where('id','!=' ,'61a7e997-aed6-4e99-a14b-2c4a1442a773')
        ->where('enable', 1)
        // ->take(1)
        ->get();
        $translateService = new TranslateService();
        $requiredLanguages = ['vi', 'en'];
        $counter = 0;

        foreach ($shops as $shop) {
            echo "\n----------------" . $shop->name . "----------------\n";
            
            // Get shop's languages
            $shopLanguages = json_decode($shop->language ?? '["vi"]', true);
            if (!is_array($shopLanguages)) {
                $shopLanguages = ['vi'];
            }
            
            // Merge shop languages with required languages
            $targetLanguages = array_unique(array_merge($shopLanguages, $requiredLanguages));
            
            // Process each category
            foreach ($shop->categories as $category) {
                echo "+ Category: " . $category->name . "\n";
                
                // Check existing translations
                $existingTranslations = Translation::where('object_type', 4)
                    ->where('object_id', $category->id)
                    ->where('name', '!=', '')
                    ->pluck('language_code')
                    ->toArray();
                
                // Find missing languages
                $missingLanguages = array_diff($targetLanguages, $existingTranslations);
                
                if (empty($missingLanguages)) {
                    echo "  All required translations exist\n";
                    continue;
                }

                // Get the source language and text (prefer Vietnamese if available)
                $sourceText = $category->name;
                $sourceLanguage = 'vi'; // Default source language
                
                if (in_array('vi', $existingTranslations)) {
                    // Use Vietnamese as source if available
                    $viTranslation = Translation::where('object_type', 4)
                        ->where('object_id', $category->id)
                        ->where('language_code', 'vi')
                        ->first();
                    if ($viTranslation) {
                        $sourceText = $viTranslation->name;
                    }
                }
                
                // Create missing translations
                foreach ($missingLanguages as $langCode) {
                    // try {
                        if ($langCode == $sourceLanguage) {
                            // If target is the same as source, no need to translate
                            $translatedText = $sourceText;
                            $translatedDescription = $category->notes ?? $sourceText;
                        } else {
                            // Translate the text
                            $translatedText = $translateService->translate($sourceText, $langCode)['content'];  
                            $translatedDescription = $category->notes ? 
                                $translateService->translate($category->notes, $langCode)['content'] : 
                                $translatedText;
                        }
                        
                        // Create or update translation
                        $translation = Translation::updateOrCreate(
                            [
                                'object_type' => 4, // Category type
                                'object_id' => $category->id,
                                'language_code' => $langCode
                            ],
                            [
                                'name' => $translatedText,
                                'description' => $translatedDescription
                            ]
                        );
                        
                        echo "  Created/updated translation for {$langCode}: {$translatedText}\n";
                        $counter++;
                        
                    // } catch (\Exception $e) {
                    //     echo "  Error creating translation for {$langCode}: " . $e->getMessage() . "\n";
                    // }
                }
            }
        }
        
        echo "\nTotal translations created/updated: {$counter}\n";
        return true;
    }

    function createProductTranslations()
    {
        // Get all shops with products, excluding the shop with ID 123456789
        $shops = Shop::with('all_products')->where('id', '!=', '9fbe5e90-b443-45d7-934a-b9daf3727a98')->get();

        foreach ($shops as $shop) {
            // Get the first language code from the shop's languages array
            $languageCode = json_decode($shop->language);
            $languageCode = $languageCode[0];
            echo "----------------".$shop->name."----------------\n";
            // Iterate over the products of the shop
            foreach ($shop->all_products as $product) {
                echo "+ Product: ".$product->name."\n";
                if(Translation::where('object_type',2)->where('object_id' , $product->id)->where('language_code',$languageCode)->count() > 0) continue;
                $result = Translation::updateOrCreate(
                    [
                        'object_type' => 2,
                        'object_id' => $product->id,
                        'language_code' => $languageCode
                    ],
                    [
                        'name' => $product->name,
                        'description' => $product->notes ?? $product->name
                    ]
                );
                echo $result."\n";
            }
        }
    }
    public function checkCache() {
        if(Cache::has("otp_access_token"))
            {
                echo "1\n";
                $token = Cache::get("otp_access_token");
                echo $token;
            }else{
                echo "2\n";
                $expiresAt = Carbon::now()->addSeconds(3600);
                echo $newToken =  Str::uuid();
                Cache::put("otp_access_token",$newToken, $expiresAt);

            }
    }
    public function deleteFromS3($filePath)
    {
        Storage::disk('s3')->delete($filePath);

        return response()->json(['message' => 'File deleted from S3.']);
    }
    public function testNoti(){
        $tokens = ["ex-buNanTAG8A1WbvcBN6z:APA91bFGoJazDPMGSF7Kz9JaU-YJsvta4y6zKaBYb48UpPylfTQL6GcRn0SGob9E_hDPdmyH-EpNa2t-akL_ZmsKVQoitH8_rCmwYg9DGwFC7QK8A18AEr-n8nFmcOQZDKSOhypfc_do"];
        // $tokenService = new TokenService();
        // $tokens = $tokenService->listByUser($shop_owner, 2);
        // Log::info(json_encode($tokens));
        $data = [
            'body'  => "Test notify ".now(),
            'title' => "Bạn có đơn hàng mới",
            // 'image' => $image,
            'url'   => "",
            'type'  => 'order',
        ];
        {
            $notiService = new NotificationService;
            $notiService->sendBatchNotification($tokens, $data);
        }
    }

    function testQR() {
        $shop = Shop::where('slug','tesst-dup_8d0068')->with('logo')->get()->first();
        // dd($shop);
        GeneralService::generateQRCodeAndPlaceInFrame(env('APP_URL')."shop/".$shop->slug, env('S3_HOST_PATH').$shop->logo->path, $shop->name);

    }

}
