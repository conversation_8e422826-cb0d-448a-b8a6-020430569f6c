<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\Shop\ShopService;
use App\Shop;
use Illuminate\Support\Facades\Log;

class CheckShopHolidayJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The shop ID to check.
     *
     * @var string|null
     */
    protected $shopId;

    /**
     * Create a new job instance.
     *
     * @param string|null $shopId
     * @return void
     */
    public function __construct($shopId = null)
    {
        $this->shopId = $shopId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            if ($this->shopId) {
                // Check a specific shop
                $this->checkShop($this->shopId);
            } else {
                // Check all active shops
                $this->checkAllShops();
            }
        } catch (\Exception $e) {
            Log::error('Error checking shop holidays: ' . $e->getMessage(), [
                'shop_id' => $this->shopId,
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * Check a specific shop's holiday status.
     *
     * @param string $shopId
     * @return void
     */
    private function checkShop($shopId)
    {
        $shopService = new ShopService();
        $holidayStatus = $shopService->CheckShopHoliday($shopId);
        
        $shop = Shop::find($shopId);
        if ($shop) {
            $this->updateShopHolidayStatus($shop, $holidayStatus);
        }
    }

    /**
     * Check all active shops for holiday status.
     *
     * @return void
     */
    private function checkAllShops()
    {
        $shops = Shop::whereRaw("settings::jsonb->'general'->'holiday_settings'->'holiday_mode'->>'enabled' = 'true'")
        ->where('enable', true)->get();
        $shopService = new ShopService();
        
        foreach ($shops as $shop) {
            $holidayStatus = $shopService->CheckShopHoliday($shop->id);
            $this->updateShopHolidayStatus($shop, $holidayStatus);
        }
    }

    /**
     * Update the shop with its holiday status.
     *
     * @param Shop $shop
     * @param array $holidayStatus
     * @return void
     */
    private function updateShopHolidayStatus($shop, $holidayStatus)
    {
        $settings = $shop->settings ?? [];
        
        // Update the holiday status in settings
        if (!isset($settings['holiday_status'])) {
            $settings['holiday_status'] = [];
        }
        
        $settings['general']['is_open']['value'] = !$holidayStatus['isClosed'];
        $settings['general']['is_open']['reason'] = $holidayStatus['reason'];
        $settings['general']['is_open']['last_checked'] = now()->toDateTimeString();
        
        // Update the shop settings
        $shop->settings = $settings;
        $shop->save();
        
        // Log::info('Shop holiday status updated', [
        //     'shop_id' => $shop->id,
        //     'shop_name' => $shop->name,
        //     'is_closed' => $holidayStatus['isClosed'],
        //     'reason' => $holidayStatus['reason']
        // ]);
    }
}
