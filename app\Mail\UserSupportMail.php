<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\Services\HistoryMail\HistoryMailService;

class UserSupportMail extends Mailable
{
    use Queueable, SerializesModels;

    private $_data;
    public function __construct($data)
    {
        $this->_data = $data;
    }

    public function build()
    {
        $historyMail = null;
        try {
            $historyMail = HistoryMailService::insert([
                'title' => 'Thông tin liên hệ của khách hàng',
                'content' => json_encode($this->_data),
                'status' => config('constants.status_history_mail.success')
            ]);
            return $this->subject('Thông tin liên hệ của khách hàng: '.$this->_data['name'])
            ->view('emails.request_user_mail')->with([
                                                    'name' => $this->_data['name'],
                                                    'email' => $this->_data['email'],
                                                    'phone' => $this->_data['phone'],
                                                    'request' => $this->_data['request'],
                                                    'content' => $this->_data['content'],
                                                ]);

        } catch (\Exception $e) {
            Log::info("Mail Error: ".$e);

            $historyMail->update([
                'status' => config('constants.status_history_mail.fail'),
                'description' => $e
            ]);
        }

    }
}
