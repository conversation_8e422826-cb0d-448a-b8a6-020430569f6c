<?php

namespace App\Rules;
use App\District;

use Illuminate\Contracts\Validation\Rule;

class CheckDistrict implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    protected $district_id;
    public function __construct($district_id)
    {
        //
        $this->district_id = $district_id;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        //
        $district = District::find($value);
        return $district;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'District_001_E_001';
    }
}
