<?php

namespace App\Jobs;

use App\Services\Delivery\DeliveryService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CancelDelivery implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $delivery;
    private $note;
    public function __construct($delivery, $note = "")
    {
        $this->delivery = $delivery;
        $this->note = $note;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $service = new DeliveryService();
        $this->delivery->update(['status' => config('constants.delivery.cancelled')]);
        $data = [
            'delivery_id' => $this->delivery->id,
            'notes' => $this->note,
            'status' => config('constants.delivery.cancelled')
        ];
        $service->updateStatus($data);
    }
}
