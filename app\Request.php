<?php

namespace App;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\User;

class Request extends Model
{
    protected $primaryKey = 'id';
    protected $table = 'requests';
    public $incrementing = false;

    protected $fillable = [
        'id', 'user_id', 'type', 'object_id', 'reason', 'status', 'scheduled_at'
    ];

    protected $dates = [
        'scheduled_at'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = (string) Str::uuid();
        });
    }

    public function user()
    {

        return $this->belongsTo(User::class,'user_id')->select(['id','name','phone','email','profile_picture','address']);
    }
}
