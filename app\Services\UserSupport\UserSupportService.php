<?php
namespace App\Services\UserSupport;

use Mail;
use App\Setting;
use App\User;
use App\Place;
use App\PlaceType;
use App\Property;
use App\Mail\UserSupportMail;
use App\Mail\UserOrderBuyMail;
use App\Mail\UserContactMail;
use App\Services\Log\LogSendMailService;

class UserSupportService
{
    public function userRequest(array $data)
    {
        $data['name']       = isset($data['name']) ? $data['name'] : null;
        $data['email']      = isset($data['email']) ? $data['email'] : null;
        $data['phone']      = isset($data['phone']) ? $data['phone'] : null;
        $data['request']    = isset($data['request']) ? $data['request'] : null;
        $data['content']    = isset($data['content']) ? $data['content'] : null;

        $log = new LogSendMailService();
        try {
            Mail::to('<EMAIL>')->send(new UserSupportMail($data));
            $mess ='Sent Successfully';
            $log->logSendMail($data, $mess);
        } catch (\Exception $e) {
            $log->logSendMail($data,$e);
        }

        //store log

        // if(count(Mail::failures()) > 0 ) {
        //     $mess = 'Sent Failures';
        //     $log->logSendMail($data,$mess);
        // }
        // else
        // {
        //     $mess ='Sent Successfully';
        //     $log->logSendMail($data, $mess);
        // }
        // Mail::to('<EMAIL>')->send(new UserSupportMail($data));

        return [];
    }

    public function listRequest()
    {
        $setting = Setting::where('key','request_user')->first();

        $result = json_decode($setting['value'],true);

        return $result;
    }

    public function contact(array $data)
    {

        $check = User::find($data['created_by']);

        if(!$check)
        {
            return false;
        }

        $result = $check->organize()->first();
        $email = isset($result->email) && !empty($result->email) ? $result->email : '<EMAIL>';
        $Property = Property::find($data['id_property']);
        $knbId = '';
        $localUrl = '';
        if($Property){
            $knbId = $Property->knb_id;
            if(isset($Property->link_source) && $Property->link_source != '' && isset($Property->origin_poster_phone) && $Property->origin_poster_phone != ''){
                $localUrl = 'http://192.168.1.44:4300/home/'.$Property->slug;
            }
        }
        $data['name']               = isset($data['name']) ? $data['name'] : null;
        $data['phone']              = isset($data['phone']) ? $data['phone'] : null;
        $data['content']            = isset($data['content']) ? $data['content'] : null;
        $data['id_property']        = isset($data['id_property']) ? $data['id_property'] : null;
        $data['name_property']      = isset($data['name_property']) ? $data['name_property'] : null;
        $data['url_property']       = isset($data['url_property']) ? $data['url_property'] : null;
        $data['knb_id']             = $knbId;
        $data['local_url']             = $localUrl;
        $data['email']              = isset($data['email']) ? $data['email'] : null;

        $log = new LogSendMailService();
        try {
            Mail::to($email)->send(new UserContactMail($data));
            $mess ='Sent Successfully';
            $log->logSendMail($data, $mess);
        } catch (\Exception $e) {
            $log->logSendMail($data,$e);
        }

        // Mail::to('<EMAIL>')->send(new UserContactMail($data));
        //store log
        // $log = new LogSendMailService();
        // if(count(Mail::failures()) > 0 ) {
        //     $mess = 'Sent Failures';
        //     $log->logSendMail($data,$mess);
        // }
        // else
        // {
        //     $mess ='Sent Successfully';
        //     $log->logSendMail($data, $mess);
        // }

        return true;
    }

    public function orderBuy(array $data)
    {
        $set1 = Setting::where('key','place_type_project')->first();
        $set2 = Setting::where('key','post_type_planning')->first();

        if(isset($set1->value, $set2->value))
        {
            //-------place---------------
            $result = Place::where(
                [
                    ['object_type', config('constants.place_object_type.address')],
                    ['confirmed' , config('constants.confirm_status.confirmed')],
                    ['enable', true],
                    ['id', $data['id']]
                ]
            )
            ->whereIn('place_type_id', json_decode($set1->value,true))
            //-----------------post---------------------
            ->orwhere([
                ['object_type', config('constants.place_object_type.post')],
                ['confirmed' , config('constants.confirm_status.confirmed')],
                ['enable', true],
                ['id', $data['id']]
            ])
            ->whereIn('category_id', json_decode($set2->value,true))
            ->first();
        }
        else {
            return false;
        }

        if(!$result)
        {
            return false;
        }

        switch ($result->object_type) {
            case config('constants.place_object_type.post'):
                $place_type = PlaceType::find($result->category_id);
                break;

            case config('constants.place_object_type.address'):
                $place_type = PlaceType::find($result->place_type_id);
                break;
            default:
                $place_type = null;
                break;
        }

        $email = '<EMAIL>';

        $data['name']       = isset($data['name']) ? $data['name'] : null;
        $data['phone']      = isset($data['phone']) ? $data['phone'] : null;
        $data['content']    = isset($data['content']) ? $data['content'] : null;
        $data['topic']      = isset($data['topic']) ? $data['topic'] : null;
        $data['email']      = isset($data['email']) ? $data['email'] : null;
        $data['title']      = isset($result->name) ? $result->name : null;
        $data['placeType']  = isset($place_type) ? $place_type : null;
        $data['coordinate'] = isset($result->latitude, $result->longitude) ? $result->latitude.','.$result->longitude : null;
        $log = new LogSendMailService();
        try {
            Mail::to($email)->send(new UserOrderBuyMail($data));
            $mess ='Sent Successfully';
            $log->logSendMail(json_encode($data), $mess);
        } catch (\Exception $e) {
            $log->logSendMail(json_encode($data),$e);
        }

        return true;
    }
}
