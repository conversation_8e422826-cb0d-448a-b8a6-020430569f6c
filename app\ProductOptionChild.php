<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ProductOptionChild extends Model
{
    protected $fillable = [
        'product_id',
        'product_option_id',
        'enable',
        'created_by',
    ];
    protected $primaryKey = 'id';
    protected $table = 'product_options_childs';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
    protected $hidden = ['updated_at', 'created_at'];

    public function created_by()
    {
        return $this->belongsTo(User::class,'created_by');
    }

    public function products()
    {
        return $this->belongsTo(Product::class,'product_id');
    }

    public function product_options()
    {
        return $this->belongsTo(ProductOption::class,'product_option_id');
    }
}
