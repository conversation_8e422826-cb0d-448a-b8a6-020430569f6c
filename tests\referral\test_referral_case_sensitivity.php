<?php

// Bootstrap <PERSON>vel
require_once __DIR__.'/../../vendor/autoload.php';
$app = require_once __DIR__.'/../../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\User;
use App\Order;
use Illuminate\Support\Facades\DB;

echo "=== Referral Code Case Sensitivity Test ===\n\n";

try {
    // Create a test user
    $testUser = User::create([
        'name' => 'Test Referrer',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => '1234567890',
        'role_id' => 6
    ]);
    
    echo "1. Created test user: {$testUser->name} (ID: {$testUser->id})\n";
    
    // Generate referral code
    $code = $testUser->generateReferralCode();
    echo "2. Generated referral code: {$code}\n";
    
    // Test case-insensitive lookups
    $testCodes = [
        $code,              // Original uppercase
        strtolower($code),  // Lowercase
        ucfirst(strtolower($code)), // Mixed case
        '  ' . $code . '  ' // With whitespace
    ];
    
    echo "\n3. Testing case-insensitive lookups:\n";
    foreach ($testCodes as $testCode) {
        $foundUser = User::findByReferralCode($testCode);
        $status = $foundUser ? "✓ FOUND" : "✗ NOT FOUND";
        echo "   Testing '{$testCode}' -> {$status}\n";
    }
    
    // Test with invalid codes
    echo "\n4. Testing invalid codes:\n";
    $invalidCodes = ['INVALID123', 'ref123456', ''];
    foreach ($invalidCodes as $invalidCode) {
        $foundUser = User::findByReferralCode($invalidCode);
        $status = $foundUser ? "✗ INCORRECTLY FOUND" : "✓ CORRECTLY NOT FOUND";
        echo "   Testing '{$invalidCode}' -> {$status}\n";
    }
    
    // Test referral stats
    echo "\n5. Testing referral statistics:\n";
    $stats = $testUser->getReferralStats();
    echo "   Referral code: {$stats['referral_code']}\n";
    echo "   Total referrals: {$stats['total_referrals']}\n";
    echo "   Total referred orders: {$stats['total_referred_orders']}\n";
    echo "   Successful referrals: {$stats['successful_referrals']}\n";
    echo "   Total referred order value: {$stats['total_referred_order_value']}\n";
    
    // Clean up
    echo "\n6. Cleaning up test data...\n";
    $testUser->delete();
    echo "   Test user deleted.\n";
    
    echo "\n=== Test completed successfully! ===\n";
    
} catch (Exception $e) {
    echo "\n!!! ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    
    // Clean up on error
    if (isset($testUser) && $testUser->exists) {
        $testUser->delete();
        echo "Cleaned up test user.\n";
    }
}
