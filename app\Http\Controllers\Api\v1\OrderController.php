<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Requests\Order\OrderListRequest;
use App\Services\POS\POSService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\Order\OrderService;
use App\Services\GeneralService;
use App\User;
use App\Order;
use App\Shop;
use App\Http\Requests\Order\OrderRequest;
use Elasticsearch\Endpoints\Indices\Exists;

class OrderController extends Controller
{
    // -------create order---------------
    public function create(OrderRequest $request)
    {        $data = $request->only([
            'address' ,
            'province_id' ,
            'district_id' ,
            'ward_id' ,
            'customer_id' ,
            'customer_name',
            'customer_phone' ,
            'total_amount',
            'discount_amount',
            'grand_total',
            'delivery_type',
            'delivery_price',
            'delivery_price_estimate',
            'delivery_discount',
            'delivery_time' ,
            'delivery',
            'delivery_distance',
            'delivery_service',
            'delivery_partner',
            'customer_latitude',
            'customer_longitude',
            'payment_method' ,
            'notes',
            'items',
            'shop_id',
            'delivery_partner_id',
            'extra_data',
            'referral_code'
        ]);
        $service = new OrderService();
        $result = $service->create($data);
        if(isset($result['error']) || !$result){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => $result['error'] ?? ''
                ]
            ];
        } else {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ]
            ];
        }

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list order-----------------
    public function list(Request $request, OrderService $service)
    {
        $arrays = $request->all();

        $result = $service->list($arrays['offset'], $arrays['limit'], $arrays['all']);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------detail order-------------
    public function detail($id = null, OrderService $service)
    {
        $userId = Auth::user()->id;
        if(!GeneralService::checkShopOwnerOrder($userId, $id) && !GeneralService::checkAgentShopRelate($userId, $id) && Auth::user()->role != User::ROLE_ADMIN) 
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'this order is not  exists or not belongs to your account.'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $result = $service->detail($id);
        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update order-----------
    public function update(Request $request, OrderService $service)
    {
        $data = $request->only([
            'id',
            'status',
            'short_code',
            'address',
            'province_id' ,
            'district_id' ,
            'ward_id' ,
            'customer_id' ,
            'customer_name',
            'customer_phone' ,
            'total_amount',
            'discount_amount',
            'grand_total',
            'delivery_type',
            'delivery_price' ,
            'delivery_discount' ,
            'delivery_time' ,
            'delivery',
            'customer_latitude',
            'customer_longitude',
            'payment_method' ,
            'notes',
            'items',
            'shop_id',
            'images',
            'image_delete',
        ]);
        $userId = Auth::user()->id;
        if(!GeneralService::checkShopOwnerOrder($userId, $data['id']) && !GeneralService::checkAgentShopRelate($userId, $data['id'])  && Auth::user()->role != User::ROLE_ADMIN)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'This order does not exist or does not belong to your account.'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $result = $service->update($data);

        if(isset($result['error']) || !$result)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => $result['error'] ?? ''
                ]
            ];
        } else {
            $response = [
                'status' => JsonResponse::HTTP_OK,
                'body' => [
                    'data' => $result
                ]
            ];
        }

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------remove order----------------
    public function remove(Request $request, OrderService $service)
    {
        $data = $request->only(['id']);
        $userId = Auth::user()->id;
        if(!GeneralService::checkShopOwnerOrder($userId, $data['id'])  && !GeneralService::checkAgentShopRelate($userId, $data['id']))
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'this order is not exists or not belongs to your account.'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $result = $service->remove($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------delete order--------------
    public function delete(Request $request, OrderService $service)
    {
       $data = $request->only(['id']);
       $userId = Auth::user()->id;
       if(!GeneralService::checkShopOwnerOrder($userId, $data['id']) && !GeneralService::checkAgentShopRelate($userId, $data['id']) )
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => [
                   'message' => 'this order is not  exists or not belongs to your account.'
               ]
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }
       $result = $service->delete($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list order by shop id-----------------
    public function listOrderByShopId(Request $request, OrderService $service)
    {
        $arrays = $request->only(['shop_id', 'offset', 'limit', 'status', 'search_text', 'start_date', 'end_date']);
        if(!GeneralService::checkShopOwner(Auth::user()->id, $arrays['shop_id']))
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => [
                    'message' => 'this Shop is not exists or not belongs to your account.'
                ]
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }
        $result = $service->listOrderByShopId($arrays);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }
    //--------list order by shop id-----------------
    public function listOrderByCustomerId(Request $request, OrderService $service)
    {
        $arrays = $request->only(['customer_id', 'offset', 'limit', 'status', 'search_text', 'start_date', 'end_date']);

        $result = $service->listOrder($arrays);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

     //--------list order by shop id-----------------
     public function countOrderByStatus(Request $request, OrderService $service)
     {
         $arrays = $request->only(['shop_id']);

         $result = $service->countOrderByStatus($arrays);

         $response = [
             'status' => JsonResponse::HTTP_OK,
             'body' => [
                 'data' => $result
             ]
         ];

         return response()->json($response, JsonResponse::HTTP_OK);
     }

    //List orders
    function listOrders(OrderListRequest $request, OrderService $service)
    {
        $arrays = $request->only(['shop_id', 'customer_id', 'offset', 'limit', 'status', 'search_text', 'start_date', 'end_date', 'mode']);
        if($arrays['mode'] == 'shop') {
            if(!GeneralService::checkShopOwner(Auth::user()->id, $arrays['shop_id']))
            {
                $response = [
                    'status' => JsonResponse::HTTP_BAD_REQUEST,
                    'body' => [
                        'message' => 'this Shop is not exists or not belongs to your account.'
                    ]
                ];

                return response()->json($response, JsonResponse::HTTP_OK);
            }
        }

        $result = $service->listOrder($arrays);

        return response()->json([
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ], JsonResponse::HTTP_OK);
    }
}
