<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\Product\ProductService;
use App\Http\Requests\Product\ProductCheckIdRequest;

class ProductController extends Controller
{
    // -------create product---------------
    public function create(Request $request)
    {        $data = $request->only([
            'name',
            'is_main',
            'brand_id',
            'profile_picture',
            'latitude',
            'longitude',
            'notes',
            'description',
            'shop_id',
            'price',
            'price_root',
            'price_off',
            'is_feature',
            'language',
            'translation',
            'stock',
            'images',
            'slug',
            'commission_percent',
        ]);

        $service = new ProductService(Auth::user()->id);
        $result = $service->create($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    // -------create private product---------------
    public function createPrivateProduct(Request $request)
    {        $data = $request->only([
            'name',
            'is_main',
            'brand_id',
            'profile_picture',
            'latitude',
            'longitude',
            'notes',
            'shop_id',
            'price',
            'price_root',
            'price_off',
            'is_feature',
            'category_ids',
            'translation',
            'stock',
            'slug',
            'commission_percent',
        ]);

        $service = new ProductService(Auth::user()->id);
        $result = $service->createPrivateProduct($data);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------list product-----------------
    public function list($all = false, ProductService $service)
    {
        $result = $service->list($all);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------detail product-------------
    public function detail($id = null, ProductService $service)
    {
        $result = $service->detail($id, false);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------detail product-------------
    public function relate($id = null, ProductService $service)
    {
        $result = $service->relate($id);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------update product-----------
    public function update(Request $request, ProductService $service)
    {        $data = $request->only([
            'id',
            'name',
            'is_main',
            'brand_id',
            'profile_picture',
            'latitude',
            'longitude',
            'notes',
            'shop_id',
            'price',
            'price_root',
            'price_off',
            'category_ids',
            'unaccent_name',
            'is_feature',
            'enable',
            'translation',
            'stock',
            'image_delete',
            'images',
            'slug',
            'commission_percent',
        ]);

        // Check if the product belongs to the authenticated user's shop
        if (!$service->checkProductOwnership(Auth::user()->id, $data['id'])) {
            return response()->json([
                'status' => JsonResponse::HTTP_FORBIDDEN,
                'body' => [
                    'message' => 'You do not have permission to update this product.'
                ]
            ], JsonResponse::HTTP_OK);
        }

        $result = $service->update($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_BAD_REQUEST);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------remove product----------------
    public function remove(ProductCheckIdRequest $request, ProductService $service)
    {
        $data = $request->only(['id']);

        $result = $service->remove($data);

        if($result == false)
        {
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => []
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => []
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //-------delete product--------------
    public function delete(ProductCheckIdRequest $request, ProductService $service)
    {
       $data = $request->only(['id']);

       $result = $service->delete($data);

       if($result == false)
       {
           $response = [
               'status' => JsonResponse::HTTP_BAD_REQUEST,
               'body' => []
           ];

           return response()->json($response, JsonResponse::HTTP_OK);
       }

       $response = [
           'status' => JsonResponse::HTTP_OK,
           'body' => []
       ];
       return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------search product-----------------
    public function searchBase(Request $request, ProductService $service)
    {
        $search = $request->only([
            'search',
            'limit',
            'offset'
        ]);
        $limit = isset($search['limit'])? $search['limit'] : 25;
        $offset = isset($search['offset'])? $search['offset'] : 0;
        $searchText = isset($search['search'])? $search['search'] : '';
        $result = $service->searchBase($searchText, $limit, $offset);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //--------search product-----------------
    public function searchByName(Request $request, ProductService $service)
    {
        $search = $request->only([
            'search',
            'search_name'
        ]);
        $result = $service->searchByName($search);

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

    //---------add product from system to shop---------------
    public function addProductFromSystem(Request $request)
    {
        $data = $request->only(
            'list_product',
            'shop_id'
        );

        $service = new ProductService(Auth::user()->id);
        $result = $service->addProductFromSystem($data);

        if($result === false){
            $response = [
                'status' => JsonResponse::HTTP_BAD_REQUEST,
                'body' => ['This shop is not your own']
            ];

            return response()->json($response, JsonResponse::HTTP_OK);
        }

        $response = [
            'status' => JsonResponse::HTTP_OK,
            'body' => [
                'data' => $result
            ]
        ];

        return response()->json($response, JsonResponse::HTTP_OK);
    }

}
