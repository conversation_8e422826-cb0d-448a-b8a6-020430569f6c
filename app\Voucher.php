<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Shop;

class Voucher extends Model
{
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'name', 'type', 'value', 'usage_limit', 'valid_from', 'valid_until', 'is_active', 'shop_id'
    ];

    protected $casts = [
        'id' => 'string',
        'value' => 'decimal:2',
        'usage_limit' => 'integer',
        'usage_count' => 'integer',
        'valid_from' => 'datetime',
        'valid_until' => 'datetime',
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }

    public function setNameAttribute($value)
    {
        $this->attributes['name'] = Str::upper(Str::limit($value, 10, ''));
    }

    public function isValid()
    {
        $now = now();
        return $this->is_active &&
               ($this->valid_from === null || $now >= $this->valid_from) &&
               ($this->valid_until === null || $now <= $this->valid_until) &&
               ($this->usage_limit === null || $this->usage_count < $this->usage_limit);
    }

    public function incrementUsage()
    {
        $this->increment('usage_count');
    }
    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }
}
