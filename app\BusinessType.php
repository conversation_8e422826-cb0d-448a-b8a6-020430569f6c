<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class BusinessType extends Model
{
    protected $fillable = [
        'name',
        'description',
        'enable',
        'created_by',
        'thumbnail'
    ];
    protected $primaryKey = 'id';
    protected $table = 'business_types';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
    protected $hidden = ['updated_at', 'created_at'];

    public function created_by()
    {
        return $this->belongsTo(User::class,'created_by');
    }
}
